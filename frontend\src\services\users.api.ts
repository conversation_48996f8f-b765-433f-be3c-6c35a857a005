import { ApiService } from './api'
import type { UserInfo } from '@/types/api.types'

// 用户列表API响应类型（匹配后端实际返回格式）
export interface UserListResponse {
  data: UserInfo[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface CreateUserDto {
  username: string
  email: string
  fullName: string
  phone?: string
  role: string
  password: string
  department?: string
}

export interface UpdateUserDto {
  email?: string
  fullName?: string
  phone?: string
  role?: string
  department?: string
  status?: 'active' | 'inactive' | 'locked'
}

export interface ChangePasswordDto {
  currentPassword: string
  newPassword: string
}

export interface ResetPasswordDto {
  userId: string
  newPassword: string
}

export interface UserListQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: 'active' | 'inactive' | 'suspended' | 'deleted'
  role_id?: string
  department?: string
  created_start?: string
  created_end?: string
  sort_by?: 'username' | 'email' | 'full_name' | 'created_at' | 'last_login_at'
  sort_order?: 'ASC' | 'DESC'
}

export interface UserStatsResponse {
  totalUsers: number
  activeUsers: number
  adminUsers: number
  editorUsers: number
  viewerUsers: number
  newUsersThisMonth: number
}

/**
 * 用户管理API服务
 */
export class UsersApiService {
  /**
   * 获取用户列表
   */
  static async getUsers(params?: UserListQueryParams): Promise<UserListResponse> {
    return ApiService.get<UserListResponse>('/users', { params })
  }

  /**
   * 获取用户详情
   */
  static async getUserById(id: string): Promise<UserInfo> {
    return ApiService.get<UserInfo>(`/users/${id}`)
  }

  /**
   * 创建用户
   */
  static async createUser(data: CreateUserDto): Promise<UserInfo> {
    return ApiService.post<UserInfo>('/users', data)
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: string, data: UpdateUserDto): Promise<UserInfo> {
    return ApiService.put<UserInfo>(`/users/${id}`, data)
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<void> {
    return ApiService.delete<void>(`/users/${id}`)
  }

  /**
   * 修改用户密码
   */
  static async changePassword(id: string, data: ChangePasswordDto): Promise<void> {
    return ApiService.post<void>(`/users/${id}/change-password`, data)
  }

  /**
   * 重置用户密码（管理员操作）
   */
  static async resetPassword(data: ResetPasswordDto): Promise<void> {
    return ApiService.post<void>('/users/reset-password', data)
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<UserInfo> {
    return ApiService.get<UserInfo>('/users/profile/me')
  }

  /**
   * 更新当前用户信息
   */
  static async updateCurrentUser(data: Partial<UpdateUserDto>): Promise<UserInfo> {
    return ApiService.put<UserInfo>('/users/profile/me', data)
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<UserStatsResponse> {
    return ApiService.get<UserStatsResponse>('/users/stats')
  }
}

export default UsersApiService
