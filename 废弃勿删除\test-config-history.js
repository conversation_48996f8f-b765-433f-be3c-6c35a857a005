const http = require('http');

// 测试配置更新功能
function testUpdateConfig() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      setting_value: 'test-value-' + Date.now(),
      description: '测试配置更新 - ' + new Date().toLocaleString()
    });

    const options = {
      hostname: '*************',
      port: 3000,
      path: '/api/system-config/cache.ttl',
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('🔧 1. 测试配置更新...');
    console.log(`更新配置: cache.ttl`);
    console.log(`新值: ${JSON.parse(postData).setting_value}`);

    const req = http.request(options, (res) => {
      let data = '';
      
      console.log(`📊 响应状态码: ${res.statusCode}`);

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ 配置更新成功!');
            console.log('响应:', jsonData);
            resolve(jsonData);
          } else {
            console.log('❌ 配置更新失败');
            console.log('响应:', jsonData);
            reject(new Error(`Update failed: ${res.statusCode}`));
          }
        } catch (error) {
          console.log('❌ JSON解析失败:', error.message);
          console.log('原始响应:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ 请求失败:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 测试获取历史记录
function testGetHistory() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: '*************',
      port: 3000,
      path: '/api/system-config/history?limit=10',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    console.log('\n🔍 2. 测试获取配置历史...');

    const req = http.request(options, (res) => {
      let data = '';
      
      console.log(`📊 响应状态码: ${res.statusCode}`);

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log('✅ 获取历史记录成功!');
          console.log(`📈 返回了 ${jsonData.data ? jsonData.data.length : 0} 条历史记录`);
          
          if (jsonData.data && jsonData.data.length > 0) {
            console.log('\n📋 历史记录详情:');
            jsonData.data.forEach((record, index) => {
              console.log(`${index + 1}. ID: ${record.id}`);
              console.log(`   配置键: ${record.setting_key}`);
              console.log(`   新值: ${record.new_value}`);
              console.log(`   旧值: ${record.old_value}`);
              console.log(`   操作者: ${record.changed_by}`);
              console.log(`   时间: ${record.changed_at}`);
              console.log(`   操作: ${record.operation}`);
              console.log('');
            });
          } else {
            console.log('⚠️ 没有找到历史记录');
          }
          
          resolve(jsonData);
        } catch (error) {
          console.log('❌ JSON解析失败:', error.message);
          console.log('原始响应:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ 请求失败:', error.message);
      reject(error);
    });

    req.end();
  });
}

// 主测试函数
async function runTest() {
  console.log('🚀 开始配置历史记录功能测试...\n');
  
  try {
    // 1. 先更新一个配置
    await testUpdateConfig();
    
    // 2. 等待一秒确保数据库更新完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 获取历史记录
    await testGetHistory();
    
    console.log('\n🎉 测试完成!');
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
  }
}

runTest(); 