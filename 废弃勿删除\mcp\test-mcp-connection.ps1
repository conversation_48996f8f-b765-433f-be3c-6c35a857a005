# 测试MCP连接脚本
# 验证MCP MySQL服务器是否正常工作

Write-Host "MCP MySQL 连接测试" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "onlyfile_user"
$env:MYSQL_PASS = "0nlyF!le`$ecure#123"
$env:MYSQL_DB = "onlyfile"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"
$env:MYSQL_ENABLE_LOGGING = "true"

Write-Host "1. 测试MCP服务器启动..." -ForegroundColor Yellow

# 创建一个临时的测试脚本
$testScript = @"
const { spawn } = require('child_process');

console.log('正在测试MCP MySQL服务器...');

// 启动MCP服务器进程
const mcpProcess = spawn('npx', ['@benborla29/mcp-server-mysql'], {
  env: {
    ...process.env,
    MYSQL_HOST: '*************',
    MYSQL_PORT: '3306',
    MYSQL_USER: 'onlyfile_user',
    MYSQL_PASS: '0nlyF!le\$ecure#123',
    MYSQL_DB: 'onlyfile',
    MYSQL_ENABLE_LOGGING: 'true'
  }
});

let output = '';
let hasError = false;

mcpProcess.stdout.on('data', (data) => {
  output += data.toString();
  console.log('输出:', data.toString().trim());
});

mcpProcess.stderr.on('data', (data) => {
  console.error('错误:', data.toString().trim());
  hasError = true;
});

// 5秒后停止测试
setTimeout(() => {
  mcpProcess.kill();
  if (!hasError && output.length > 0) {
    console.log('✓ MCP服务器启动成功');
    process.exit(0);
  } else {
    console.log('✗ MCP服务器启动失败');
    process.exit(1);
  }
}, 5000);
"@

# 写入临时文件
$testScript | Out-File -FilePath "temp-mcp-test.js" -Encoding UTF8

try {
  Write-Host "   正在运行连接测试..." -ForegroundColor Cyan
  node temp-mcp-test.js
  
  if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✓ MCP服务器测试通过" -ForegroundColor Green
  } else {
    Write-Host "   ✗ MCP服务器测试失败" -ForegroundColor Red
  }
}
catch {
  Write-Host "   ✗ 测试执行失败: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
  # 清理临时文件
  if (Test-Path "temp-mcp-test.js") {
    Remove-Item "temp-mcp-test.js" -Force
  }
}

Write-Host ""
Write-Host "2. 检查Cursor配置..." -ForegroundColor Yellow

if (Test-Path ".cursor\mcp.json") {
  Write-Host "   ✓ Cursor MCP配置文件存在" -ForegroundColor Green
  
  $mcpConfig = Get-Content ".cursor\mcp.json" | ConvertFrom-Json
  if ($mcpConfig.mcpServers.MySQL) {
    Write-Host "   ✓ MySQL服务器配置存在" -ForegroundColor Green
    Write-Host "   配置详情:" -ForegroundColor Cyan
    Write-Host "     命令: $($mcpConfig.mcpServers.MySQL.command)" -ForegroundColor White
    Write-Host "     主机: $($mcpConfig.mcpServers.MySQL.env.MYSQL_HOST)" -ForegroundColor White
    Write-Host "     数据库: $($mcpConfig.mcpServers.MySQL.env.MYSQL_DB)" -ForegroundColor White
  } else {
    Write-Host "   ✗ MySQL服务器配置不存在" -ForegroundColor Red
  }
} else {
  Write-Host "   ✗ Cursor MCP配置文件不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "下一步:" -ForegroundColor Yellow
Write-Host "1. 重启Cursor IDE" -ForegroundColor White
Write-Host "2. 在Cursor中使用 @MySQL 来访问数据库" -ForegroundColor White
Write-Host "3. 或者运行 .\start-mcp-mysql.ps1 来手动启动服务器" -ForegroundColor White
