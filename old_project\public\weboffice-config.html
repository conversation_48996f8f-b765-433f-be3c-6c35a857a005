<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice编辑器配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: normal;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #219a52;
        }

        .btn-warning {
            background-color: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e67e22;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .config-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 120px);
        }

        .config-sidebar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .config-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            padding: 20px;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
        }

        .nav-item {
            border-bottom: 1px solid #ecf0f1;
        }

        .nav-item:last-child {
            border-bottom: none;
        }

        .nav-link {
            display: block;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        .nav-link:hover,
        .nav-link.active {
            background-color: #3498db;
            color: white;
        }

        .config-section {
            display: none;
        }

        .config-section.active {
            display: block;
        }

        .section-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .checkbox-item label {
            margin: 0;
            cursor: pointer;
            flex: 1;
        }

        .checkbox-description {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .nested-config {
            margin-left: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s;
        }

        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }

        .notification.success {
            background-color: #27ae60;
        }

        .notification.error {
            background-color: #e74c3c;
        }

        .notification.warning {
            background-color: #f39c12;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .config-actions {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 20px 0;
            border-top: 1px solid #ddd;
            margin-top: 30px;
        }

        .row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .col {
            flex: 1;
        }

        .col-2 {
            flex: 0 0 200px;
        }

        @media (max-width: 1200px) {
            .config-layout {
                grid-template-columns: 1fr;
                height: auto;
            }

            .config-sidebar {
                order: 2;
            }

            .config-content {
                order: 1;
            }

            .nav-menu {
                display: flex;
                overflow-x: auto;
            }

            .nav-item {
                border-bottom: none;
                border-right: 1px solid #ecf0f1;
                flex: 0 0 auto;
            }

            .nav-link {
                white-space: nowrap;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="header-content">
            <h1>OnlyOffice编辑器配置管理</h1>
            <div class="header-actions">
                <a href="/navigation.html" class="btn btn-secondary">返回导航</a>
                <a href="/" class="btn btn-primary">文档管理</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="config-layout">
            <div class="config-sidebar">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a class="nav-link active" data-section="permissions">文档权限</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-section="interface">界面设置</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-section="features">功能控制</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-section="layout">布局设置</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-section="advanced">高级设置</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-section="user">用户设置</a>
                    </li>
                </ul>
            </div>

            <div class="config-content">
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在加载配置...</p>
                </div>

                <!-- 文档权限配置 -->
                <div class="config-section active" id="permissions-section">
                    <h2 class="section-title">文档权限配置</h2>
                    <p style="margin-bottom: 20px; color: #666;">控制用户在编辑器中的权限和操作能力</p>

                    <div class="checkbox-group" id="permissions-checkboxes">
                        <!-- 权限选项将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 界面设置 -->
                <div class="config-section" id="interface-section">
                    <h2 class="section-title">界面设置</h2>
                    <p style="margin-bottom: 20px; color: #666;">自定义编辑器的外观和界面布局</p>

                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">UI主题</label>
                                <select class="form-control" id="uiTheme">
                                    <option value="theme-light">浅色主题</option>
                                    <option value="theme-dark">深色主题</option>
                                </select>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">语言</label>
                                <select class="form-control" id="lang">
                                    <option value="zh">中文</option>
                                    <option value="en">英文</option>
                                    <option value="zh-CN">中文(简体)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">缩放比例 (%)</label>
                                <input type="number" class="form-control" id="zoom" min="25" max="500" step="25">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">测量单位</label>
                                <select class="form-control" id="unit">
                                    <option value="cm">厘米 (cm)</option>
                                    <option value="pt">磅 (pt)</option>
                                    <option value="inch">英寸 (inch)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="checkbox-group" id="interface-checkboxes">
                        <!-- 界面选项将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 功能控制 -->
                <div class="config-section" id="features-section">
                    <h2 class="section-title">功能控制</h2>
                    <p style="margin-bottom: 20px; color: #666;">启用或禁用编辑器的各种功能特性</p>

                    <div class="checkbox-group" id="features-checkboxes">
                        <!-- 功能选项将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 布局设置 -->
                <div class="config-section" id="layout-section">
                    <h2 class="section-title">布局设置</h2>
                    <p style="margin-bottom: 20px; color: #666;">配置编辑器各个区域的显示和布局</p>

                    <div id="layout-config">
                        <!-- 布局配置将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 高级设置 -->
                <div class="config-section" id="advanced-section">
                    <h2 class="section-title">高级设置</h2>
                    <p style="margin-bottom: 20px; color: #666;">高级功能和专业配置选项</p>

                    <div class="form-group">
                        <label class="form-label">协作模式</label>
                        <select class="form-control" id="coEditingMode">
                            <option value="fast">快速模式</option>
                            <option value="strict">严格模式</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">宏安全模式</label>
                        <select class="form-control" id="macrosMode">
                            <option value="warn">警告</option>
                            <option value="enable">启用</option>
                            <option value="disable">禁用</option>
                        </select>
                    </div>

                    <div class="checkbox-group" id="advanced-checkboxes">
                        <!-- 高级选项将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 用户设置 -->
                <div class="config-section" id="user-section">
                    <h2 class="section-title">用户设置</h2>
                    <p style="margin-bottom: 20px; color: #666;">配置默认用户信息和行为</p>

                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">用户ID</label>
                                <input type="text" class="form-control" id="userId">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-control" id="userName">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">用户组</label>
                        <input type="text" class="form-control" id="userGroup" placeholder="例如: editors,reviewers">
                    </div>
                </div>

                <div class="config-actions">
                    <div style="display: flex; gap: 10px; justify-content: space-between;">
                        <div>
                            <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                            <button class="btn btn-primary" onclick="previewConfig()">预览配置</button>
                            <button class="btn btn-warning" onclick="loadDefaultConfig()">加载默认配置</button>
                            <button class="btn btn-secondary" onclick="testCurrentConfig()">测试当前配置</button>
                        </div>
                        <div>
                            <button class="btn btn-danger" onclick="resetConfig()">重置配置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知 -->
    <div class="notification" id="notification"></div>

    <script>
        let currentConfig = {};
        let configDescriptions = {};

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadConfig();
            setupNavigation();
        });

        // 设置导航
        function setupNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.config-section');

            navLinks.forEach(link => {
                link.addEventListener('click', function () {
                    const sectionId = this.getAttribute('data-section') + '-section';

                    // 更新导航状态
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');

                    // 显示对应的配置区域
                    sections.forEach(s => s.classList.remove('active'));
                    document.getElementById(sectionId).classList.add('active');
                });
            });
        }

        // 加载配置
        async function loadConfig() {
            showLoading(true);
            try {
                const response = await fetch('/api/onlyoffice-config');
                const data = await response.json();

                if (data.success) {
                    currentConfig = data.config;
                    configDescriptions = data.descriptions;
                    updateUI();
                    showNotification('配置加载成功', 'success');
                } else {
                    showNotification('加载配置失败: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                showNotification('加载配置失败: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 更新UI
        function updateUI() {
            updatePermissions();
            updateInterface();
            updateFeatures();
            updateLayout();
            updateAdvanced();
            updateUser();
        }

        // 更新权限配置UI
        function updatePermissions() {
            const container = document.getElementById('permissions-checkboxes');
            container.innerHTML = '';

            if (currentConfig.permissions) {
                Object.keys(currentConfig.permissions).forEach(key => {
                    const description = configDescriptions.permissions ? configDescriptions.permissions[key] : key;
                    const checkboxItem = createCheckboxItem(key, currentConfig.permissions[key], description, 'permissions');
                    container.appendChild(checkboxItem);
                });
            }
        }

        // 更新界面设置UI
        function updateInterface() {
            // 设置下拉框值
            if (currentConfig.customization) {
                setSelectValue('uiTheme', currentConfig.customization.uiTheme);
                setSelectValue('unit', currentConfig.customization.unit);
                setInputValue('zoom', currentConfig.customization.zoom);
            }

            setSelectValue('lang', currentConfig.lang);

            // 生成界面相关的复选框
            const container = document.getElementById('interface-checkboxes');
            container.innerHTML = '';

            if (currentConfig.customization) {
                const interfaceOptions = [
                    'compactHeader', 'compactToolbar', 'hideRightMenu', 'hideRulers',
                    'showHorizontalScroll', 'showVerticalScroll', 'toolbarHideFileName'
                ];

                interfaceOptions.forEach(key => {
                    if (currentConfig.customization.hasOwnProperty(key)) {
                        const description = configDescriptions.customization ? configDescriptions.customization[key] : key;
                        const checkboxItem = createCheckboxItem(key, currentConfig.customization[key], description, 'customization');
                        container.appendChild(checkboxItem);
                    }
                });
            }
        }

        // 更新功能控制UI
        function updateFeatures() {
            const container = document.getElementById('features-checkboxes');
            container.innerHTML = '';

            if (currentConfig.customization) {
                const featureOptions = [
                    'about', 'autosave', 'comments', 'forcesave', 'help', 'hideNotes',
                    'macros', 'mentionShare', 'plugins'
                ];

                featureOptions.forEach(key => {
                    if (currentConfig.customization.hasOwnProperty(key)) {
                        const description = configDescriptions.customization ? configDescriptions.customization[key] : key;
                        const checkboxItem = createCheckboxItem(key, currentConfig.customization[key], description, 'customization');
                        container.appendChild(checkboxItem);
                    }
                });
            }
        }

        // 更新布局设置UI
        function updateLayout() {
            const container = document.getElementById('layout-config');
            container.innerHTML = '';

            if (currentConfig.customization && currentConfig.customization.layout) {
                const layout = currentConfig.customization.layout;

                // 头部设置
                if (layout.header) {
                    const headerDiv = createLayoutSection('头部设置', layout.header, 'customization.layout.header');
                    container.appendChild(headerDiv);
                }

                // 左侧菜单设置
                if (layout.leftMenu) {
                    const leftMenuDiv = createLayoutSection('左侧菜单', layout.leftMenu, 'customization.layout.leftMenu');
                    container.appendChild(leftMenuDiv);
                }

                // 右侧菜单设置
                if (layout.rightMenu) {
                    const rightMenuDiv = createLayoutSection('右侧菜单', layout.rightMenu, 'customization.layout.rightMenu');
                    container.appendChild(rightMenuDiv);
                }

                // 状态栏设置
                if (layout.statusBar) {
                    const statusBarDiv = createLayoutSection('状态栏', layout.statusBar, 'customization.layout.statusBar');
                    container.appendChild(statusBarDiv);
                }

                // 工具栏设置
                if (layout.toolbar) {
                    const toolbarDiv = createLayoutSection('工具栏', layout.toolbar, 'customization.layout.toolbar');
                    container.appendChild(toolbarDiv);
                }
            }
        }

        // 更新高级设置UI
        function updateAdvanced() {
            if (currentConfig.coEditing) {
                setSelectValue('coEditingMode', currentConfig.coEditing.mode);
            }

            if (currentConfig.customization) {
                setSelectValue('macrosMode', currentConfig.customization.macrosMode);
            }

            const container = document.getElementById('advanced-checkboxes');
            container.innerHTML = '';

            if (currentConfig.customization) {
                const advancedOptions = ['compatibleFeatures'];

                advancedOptions.forEach(key => {
                    if (currentConfig.customization.hasOwnProperty(key)) {
                        const description = configDescriptions.customization ? configDescriptions.customization[key] : key;
                        const checkboxItem = createCheckboxItem(key, currentConfig.customization[key], description, 'customization');
                        container.appendChild(checkboxItem);
                    }
                });
            }
        }

        // 更新用户设置UI
        function updateUser() {
            if (currentConfig.user) {
                setInputValue('userId', currentConfig.user.id);
                setInputValue('userName', currentConfig.user.name);
                setInputValue('userGroup', currentConfig.user.group);
            }
        }

        // 创建复选框项
        function createCheckboxItem(key, value, description, path) {
            const div = document.createElement('div');
            div.className = 'checkbox-item';

            div.innerHTML = `
                <input type="checkbox" id="${path}_${key}" ${value ? 'checked' : ''} 
                       onchange="updateConfigValue('${path}', '${key}', this.checked)">
                <label for="${path}_${key}">
                    ${description}
                    <div class="checkbox-description">${key}</div>
                </label>
            `;

            return div;
        }

        // 创建布局配置区域
        function createLayoutSection(title, config, path) {
            const div = document.createElement('div');
            div.className = 'nested-config';

            let html = `<h4 style="margin-bottom: 15px;">${title}</h4><div class="checkbox-group">`;

            Object.keys(config).forEach(key => {
                if (typeof config[key] === 'boolean') {
                    html += `
                        <div class="checkbox-item">
                            <input type="checkbox" id="${path}_${key}" ${config[key] ? 'checked' : ''} 
                                   onchange="updateNestedConfigValue('${path}', '${key}', this.checked)">
                            <label for="${path}_${key}">
                                ${key}
                                <div class="checkbox-description">启用${key}功能</div>
                            </label>
                        </div>
                    `;
                } else if (typeof config[key] === 'object' && config[key] !== null) {
                    // 处理嵌套对象
                    html += `<div style="grid-column: 1 / -1;"><h5>${key}</h5>`;
                    Object.keys(config[key]).forEach(subKey => {
                        if (typeof config[key][subKey] === 'boolean') {
                            html += `
                                <div class="checkbox-item" style="margin-left: 20px;">
                                    <input type="checkbox" id="${path}_${key}_${subKey}" ${config[key][subKey] ? 'checked' : ''} 
                                           onchange="updateNestedConfigValue('${path}.${key}', '${subKey}', this.checked)">
                                    <label for="${path}_${key}_${subKey}">
                                        ${subKey}
                                        <div class="checkbox-description">启用${subKey}功能</div>
                                    </label>
                                </div>
                            `;
                        }
                    });
                    html += '</div>';
                }
            });

            html += '</div>';
            div.innerHTML = html;

            return div;
        }

        // 更新配置值
        function updateConfigValue(path, key, value) {
            const pathArray = path.split('.');
            let obj = currentConfig;

            // 导航到正确的对象
            for (let i = 0; i < pathArray.length; i++) {
                if (!obj[pathArray[i]]) {
                    obj[pathArray[i]] = {};
                }
                obj = obj[pathArray[i]];
            }

            obj[key] = value;
        }

        // 更新嵌套配置值
        function updateNestedConfigValue(path, key, value) {
            const pathArray = path.split('.');
            let obj = currentConfig;

            // 导航到正确的对象
            for (let i = 0; i < pathArray.length; i++) {
                if (!obj[pathArray[i]]) {
                    obj[pathArray[i]] = {};
                }
                obj = obj[pathArray[i]];
            }

            obj[key] = value;
        }

        // 保存配置
        async function saveConfig() {
            // 更新配置值
            updateConfigFromUI();

            try {
                const response = await fetch('/api/onlyoffice-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ config: currentConfig })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('配置保存成功', 'success');
                } else {
                    showNotification('保存配置失败: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showNotification('保存配置失败: ' + error.message, 'error');
            }
        }

        // 从UI更新配置
        function updateConfigFromUI() {
            // 更新语言
            currentConfig.lang = getSelectValue('lang');

            // 更新界面设置
            if (!currentConfig.customization) currentConfig.customization = {};
            currentConfig.customization.uiTheme = getSelectValue('uiTheme');
            currentConfig.customization.unit = getSelectValue('unit');
            currentConfig.customization.zoom = parseInt(getInputValue('zoom')) || 100;

            // 更新协作模式
            if (!currentConfig.coEditing) currentConfig.coEditing = {};
            currentConfig.coEditing.mode = getSelectValue('coEditingMode');

            // 更新宏模式
            currentConfig.customization.macrosMode = getSelectValue('macrosMode');

            // 更新用户设置
            if (!currentConfig.user) currentConfig.user = {};
            currentConfig.user.id = getInputValue('userId');
            currentConfig.user.name = getInputValue('userName');
            currentConfig.user.group = getInputValue('userGroup');
        }

        // 预览配置
        async function previewConfig() {
            updateConfigFromUI();

            try {
                const response = await fetch('/api/onlyoffice-config/preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ config: currentConfig })
                });

                const data = await response.json();

                if (data.success) {
                    if (data.valid) {
                        showNotification('配置验证通过，可以保存', 'success');
                    } else {
                        showNotification('配置验证失败: ' + data.errors.join(', '), 'warning');
                    }
                } else {
                    showNotification('预览配置失败: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('预览配置失败:', error);
                showNotification('预览配置失败: ' + error.message, 'error');
            }
        }

        // 加载默认配置
        async function loadDefaultConfig() {
            if (confirm('确定要加载默认配置吗？这将覆盖当前的配置。')) {
                try {
                    const response = await fetch('/api/onlyoffice-config/default');
                    const data = await response.json();

                    if (data.success) {
                        currentConfig = data.config;
                        updateUI();
                        showNotification('默认配置已加载', 'success');
                    } else {
                        showNotification('加载默认配置失败: ' + data.message, 'error');
                    }
                } catch (error) {
                    console.error('加载默认配置失败:', error);
                    showNotification('加载默认配置失败: ' + error.message, 'error');
                }
            }
        }

        // 重置配置
        async function resetConfig() {
            if (confirm('确定要重置配置吗？这将删除所有自定义设置并恢复为默认值。')) {
                try {
                    const response = await fetch('/api/onlyoffice-config/reset', {
                        method: 'POST'
                    });

                    const data = await response.json();

                    if (data.success) {
                        await loadConfig(); // 重新加载配置
                        showNotification('配置已重置为默认值', 'success');
                    } else {
                        showNotification('重置配置失败: ' + data.message, 'error');
                    }
                } catch (error) {
                    console.error('重置配置失败:', error);
                    showNotification('重置配置失败: ' + error.message, 'error');
                }
            }
        }

        // 测试当前配置
        async function testCurrentConfig() {
            try {
                const response = await fetch('/api/onlyoffice-config/current');
                const data = await response.json();

                if (data.success) {
                    console.log('当前生效的配置:', data.config);

                    // 显示配置对比信息
                    const userConfig = data.config.user;
                    const customization = data.config.customization;

                    let message = `当前生效配置:\n`;
                    message += `用户ID: ${userConfig.id}\n`;
                    message += `用户名: ${userConfig.name}\n`;
                    message += `语言: ${data.config.lang}\n`;
                    message += `主题: ${customization.uiTheme}\n`;
                    message += `缩放: ${customization.zoom}%\n`;
                    message += `强制保存: ${customization.forcesave ? '启用' : '禁用'}\n`;
                    message += `更新时间: ${data.timestamp}`;

                    alert(message);
                    showNotification('配置测试完成，请查看控制台和弹窗信息', 'success');
                } else {
                    showNotification('测试配置失败: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('测试配置失败:', error);
                showNotification('测试配置失败: ' + error.message, 'error');
            }
        }

        // 辅助函数
        function showLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        function setSelectValue(id, value) {
            const select = document.getElementById(id);
            if (select && value !== undefined) {
                select.value = value;
            }
        }

        function getSelectValue(id) {
            const select = document.getElementById(id);
            return select ? select.value : '';
        }

        function setInputValue(id, value) {
            const input = document.getElementById(id);
            if (input && value !== undefined) {
                input.value = value;
            }
        }

        function getInputValue(id) {
            const input = document.getElementById(id);
            return input ? input.value : '';
        }
    </script>
</body>

</html>