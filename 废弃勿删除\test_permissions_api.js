const https = require('https');
const http = require('http');

// 测试权限API
const testPermissionsAPI = () => {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NTEyNDk5NzksImV4cCI6MTc1MTMzNjM3OSwiYXVkIjoib25seW9mZmljZS1jbGllbnQiLCJpc3MiOiJvbmx5b2ZmaWNlLW5lc3RqcyJ9.YSl7yxRnAD2PSyP2CQPNXe-3jF_xF2dI-BmYZYcJ85g';
  
  const options = {
    hostname: '*************',
    port: 3000,
    path: '/api/permissions/user/my',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Response:');
      try {
        const jsonData = JSON.parse(data);
        console.log(JSON.stringify(jsonData, null, 2));
      } catch (error) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('Error:', error);
  });

  req.end();
};

testPermissionsAPI(); 