/**
 * 类型定义统一导出
 * @description 统一导出所有类型定义，方便在项目中使用
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

// API相关类型 - 使用命名空间导出避免冲突
export * as ApiTypes from './api.types'

// 表单相关类型
export * as FormTypes from './form.types'

// OnlyOffice相关类型
export * as OnlyOfficeTypes from './onlyoffice.types'

// Ant Design组件类型
export * as AntdTypes from './antd.types'

// 重新导出常用类型，避免冲突
export type {
  ApiResponse,
  DocumentInfo,
  TemplateInfo,
  PaginationParams,
  PaginationResponse,
} from './api.types'

export type { ValidateErrorEntity, LoginFormData, FormValidationRule } from './form.types'

export type {
  DocEditor,
  OnlyOfficeConfig,
  ErrorEvent as OnlyOfficeErrorEvent,
} from './onlyoffice.types'

export type { TableColumns, PaginationConfig, FormInstance } from './antd.types'

// 常用的类型别名
export type Recordable<T = unknown> = Record<string, T>
export type Nullable<T> = T | null
export type NonNullable<T> = T extends null | undefined ? never : T
export type Arrayable<T> = T | T[]
export type Awaitable<T> = T | Promise<T>

// 事件处理器类型
export type EventHandler<T = Event> = (event: T) => void | Promise<void>

// 组件Props类型
export type ComponentProps<T = Record<string, unknown>> = T & {
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, string | number>
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'

// 文件类型
export type FileType = 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other'

// 状态类型
export type Status = 'success' | 'error' | 'warning' | 'info' | 'loading'

// 尺寸类型
export type Size = 'small' | 'middle' | 'large'

// 主题类型
export type Theme = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR'

// 权限级别
export type PermissionLevel = 'read' | 'write' | 'admin' | 'owner'

// 排序方向
export type SortOrder = 'asc' | 'desc'

// 布局方向
export type Direction = 'horizontal' | 'vertical'

// 位置类型
export type Position = 'top' | 'right' | 'bottom' | 'left' | 'center'

// 对齐方式
export type Align = 'start' | 'center' | 'end' | 'stretch'

// 时间格式
export type TimeFormat = 'YYYY-MM-DD' | 'YYYY-MM-DD HH:mm:ss' | 'HH:mm:ss' | 'YYYY/MM/DD'
