import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  Query,
  UseInterceptors,
  UploadedFile,
  HttpException,
  HttpStatus,
  Res
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { Response, Express } from 'express';
import { UploadService } from '../services/upload.service';
import { FilenetService } from '../../filenet/services/filenet.service';
import { DocumentService } from '../../documents/services/document.service';
import * as fs from 'fs';

/**
 * 文件上传控制器
 * 提供文件上传和管理的RESTful API
 */
@ApiTags('文件上传')
@Controller('uploads')
export class UploadController {
  constructor(
    private readonly uploadService: UploadService,
    private readonly filenetService: FilenetService,
    private readonly documentService: DocumentService,
  ) {}

  /**
   * 上传文件到FileNet
   */
  @Post()
  @ApiOperation({
    summary: '上传文件',
    description: '上传文档文件到FileNet系统并保存到数据库'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '文档文件',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        templateId: {
          type: 'string',
          description: '配置模板ID'
        },
        uploadedBy: {
          type: 'string',
          description: '上传者'
        }
      },
      required: ['file']
    },
  })
  @ApiResponse({ status: 201, description: '文件上传成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('templateId') templateId?: string,
    @Body('uploadedBy') uploadedBy?: string
  ) {
    try {
      if (!file) {
        throw new HttpException('请选择要上传的文件', HttpStatus.BAD_REQUEST);
      }

      console.log(`[UploadController] 开始上传文件到FileNet: ${file.originalname}`);

      // 使用FilenetService上传到FileNet系统
      const result = await this.filenetService.uploadDocument(
        file,  // 传递整个file对象
        file.originalname,
        uploadedBy || 'system'
      );

      return {
        success: true,
        message: '文件上传成功',
        data: {
          documentId: result.dbId,  // 数据库ID
          fnDocId: result.docId,    // FileNet ID
          originalName: result.originalName,
          fileSize: file.size,
          fileHash: result.fileHash,
          contentWasReused: result.contentWasReused,
          downloadUrl: `/api/uploads/${result.dbId}/download`
        },
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[UploadController] 上传文件到FileNet失败:', error.message);
      throw new HttpException(
        error.message || '上传文件失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 下载文件
   */
  @Get(':documentId/download')
  @ApiOperation({
    summary: '下载文件',
    description: '下载指定ID的文件（从FileNet或本地存储）'
  })
  @ApiResponse({ status: 200, description: '文件下载成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async downloadFile(@Param('documentId') documentId: string, @Res() res: Response) {
    try {
      console.log(`[UploadController] 下载文件: ${documentId}`);

      // 首先尝试从 filenet_documents 表查找（新的FileNet上传）
      try {
        const filenetDoc = await this.documentService.getDocumentById(documentId);
        if (filenetDoc && filenetDoc.fnDocId) {
          console.log(`[UploadController] 找到FileNet文档: ${filenetDoc.fnDocId}`);
          
          const downloadResult = await this.filenetService.downloadDocument(filenetDoc.fnDocId);
          
          if (downloadResult && downloadResult.stream) {
            // 设置响应头
            res.setHeader('Content-Type', filenetDoc.mimeType || 'application/octet-stream');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filenetDoc.name)}"`);
            
            // 直接将FileNet的流pipe到响应
            downloadResult.stream.pipe(res);
            return;
          }
        }
      } catch (error) {
        console.log(`[UploadController] FileNet查找失败，尝试本地文件: ${error.message}`);
      }

      // 如果FileNet中没有，尝试从本地文件系统查找（旧的本地上传）
      const fileInfo = await this.uploadService.downloadFile(documentId);

      // 设置响应头
      res.setHeader('Content-Type', fileInfo.mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileInfo.originalName)}"`);

      // 创建文件流并发送
      const fileStream = fs.createReadStream(fileInfo.filePath);
      fileStream.pipe(res);
    } catch (error) {
      console.error('[UploadController] 下载文件失败:', error.message);
      throw new HttpException(
        error.message || '下载文件失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文件信息
   */
  @Get(':documentId/info')
  @ApiOperation({
    summary: '获取文件信息',
    description: '获取指定ID文件的详细信息'
  })
  @ApiResponse({ status: 200, description: '获取文件信息成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getFileInfo(@Param('documentId') documentId: string) {
    try {
      console.log(`[UploadController] 获取文件信息: ${documentId}`);

      const fileInfo = await this.uploadService.getFileInfo(documentId);

      return {
        success: true,
        message: '获取文件信息成功',
        data: fileInfo,
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[UploadController] 获取文件信息失败:', error.message);
      throw new HttpException(
        error.message || '获取文件信息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除文件
   */
  @Delete(':documentId')
  @ApiOperation({
    summary: '删除文件',
    description: '软删除指定ID的文件'
  })
  @ApiResponse({ status: 200, description: '文件删除成功' })
  @ApiResponse({ status: 404, description: '文件不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async deleteFile(@Param('documentId') documentId: string) {
    try {
      console.log(`[UploadController] 删除文件: ${documentId}`);

      await this.uploadService.deleteFile(documentId);

      return {
        success: true,
        message: '文件删除成功',
        data: { documentId },
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[UploadController] 删除文件失败:', error.message);
      throw new HttpException(
        error.message || '删除文件失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文件列表
   */
  @Get()
  @ApiOperation({
    summary: '获取文件列表',
    description: '获取上传文件的列表，支持分页和搜索'
  })
  @ApiResponse({ status: 200, description: '获取文件列表成功' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getFileList(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('extension') extension?: string,
    @Query('uploadedBy') uploadedBy?: string
  ) {
    try {
      // 安全地转换查询参数
      const pageNum = page ? parseInt(page, 10) || 1 : 1;
      const limitNum = limit ? parseInt(limit, 10) || 20 : 20;

      console.log(`[UploadController] 获取文件列表, 页码: ${pageNum}, 每页: ${limitNum}`);

      const options = {
        page: pageNum,
        limit: limitNum,
        search,
        extension,
        uploadedBy
      };

      const result = await this.uploadService.getFileList(options);

      return {
        success: true,
        message: '获取文件列表成功',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[UploadController] 获取文件列表失败:', error.message);
      throw new HttpException(
        error.message || '获取文件列表失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 