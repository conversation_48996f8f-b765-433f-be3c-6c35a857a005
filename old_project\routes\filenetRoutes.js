const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const filenetService = require('../services/filenetService');
const config = require('../config');

const router = express.Router();

// 配置 Multer 进行文件上传
//确保临时上传目录存在
const tempUploadDir = path.resolve(__dirname, '../', config.storage && config.storage.tmpDir ? config.storage.tmpDir : 'uploads/tmp');
// console.log('[DEBUG] filenetRoutes.js - Calculated tempUploadDir:', tempUploadDir);
fs.ensureDirSync(tempUploadDir);

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, tempUploadDir); // 保存到临时目录
    },
    filename: function (req, file, cb) {
        // 使用UUID生成唯一文件名，避免使用原始文件名可能带来的编码问题
        const uniqueId = uuidv4();
        const extension = path.extname(file.originalname);
        cb(null, `${Date.now()}-${uniqueId}${extension}`);
    }
});
const upload = multer({ storage: storage });

/**
 * @swagger
 * /api/filenet/upload:
 *   post:
 *     summary: 上传文件到 FileNet
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file: 
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: 文件上传成功，返回 FileNet docId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: 
 *                   type: boolean
 *                 docId: 
 *                   type: string
 *                 message:
 *                   type: string
 *       500:
 *         description: 文件上传失败
 */
router.post('/upload', upload.single('file'), async (req, res) => {
    if (!req.file) {
        return res.status(400).json({ success: false, message: '没有选择文件' });
    }

    try {
        // 文件名编码处理
        const { sanitizeFileName } = require('../middleware/upload');
        const sanitizedOriginalName = sanitizeFileName(req.file.originalname);

        console.log('接收到文件上传请求到 /api/filenet/upload:');
        console.log('原始文件名:', req.file.originalname);
        console.log('处理后文件名 (净化后):', sanitizedOriginalName);

        // 创建一个新的文件数据对象，使用净化后的文件名
        const fileDataForUpload = {
            ...req.file, // 复制 req.file 的其他属性 (如 path, size 等)
            originalname: sanitizedOriginalName // 明确使用净化后的文件名
        };

        // 使用处理后的文件名上传
        // 将修改后的 fileDataForUpload 作为第一个参数传递
        // 第二个参数 (sanitizedOriginalName) 在此场景下由于 fileDataForUpload.originalname 已被设置，
        // 在 uploadToFileNet 内部逻辑中不会被优先使用，但保持接口一致性传入也无妨。
        const result = await filenetService.uploadToFileNet(fileDataForUpload, sanitizedOriginalName);

        // 上传到 FileNet 成功后，可以选择删除本地临时文件
        fs.unlink(req.file.path, (err) => {
            if (err) console.error('清理 FileNet 上传后的临时文件失败:', req.file.path, err);
            else console.log('已清理 FileNet 上传后的临时文件:', req.file.path);
        });

        res.json(result); // result 现在包含 { success: true, docId: '...', message: '...', dbId: ..., originalName: ... }
    } catch (error) {
        console.error('FileNet 上传路由处理错误:', error.message);
        // 确保即使 filenetService.uploadToFileNet 内部已删除，这里也尝试删除以防万一
        if (req.file && req.file.path && await fs.pathExists(req.file.path)) {
            fs.unlink(req.file.path, (err) => {
                if (err) console.error('错误处理中清理临时文件失败:', req.file.path, err);
            });
        }
        res.status(500).json({ success: false, message: error.message || '上传到 FileNet 失败' });
    }
});

/**
 * @swagger
 * /api/filenet/download/{docId}:
 *   get:
 *     summary: 从 FileNet 下载文件
 *     parameters:
 *       - in: path
 *         name: docId
 *         required: true
 *         schema:
 *           type: string
 *         description: FileNet 文档 ID
 *     responses:
 *       200:
 *         description: 文件流
 *         content:
 *           application/octet-stream: {}
 *       404:
 *         description: 未找到文档或下载失败
 *       500:
 *         description: 服务器错误
 */
router.get('/download/:docId', async (req, res) => {
    const { docId } = req.params;
    if (!docId) {
        return res.status(400).json({ success: false, message: '缺少 docId' });
    }

    try {
        console.log(`接收到 FileNet 下载请求: /api/filenet/download/${docId}`);
        const { success, fileName, stream } = await filenetService.downloadFromFileNet(docId);

        if (success && stream) {
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
            res.setHeader('Content-Type', 'application/octet-stream'); // 或者更具体的文件类型
            stream.pipe(res);
        } else {
            res.status(404).json({ success: false, message: '无法从 FileNet 下载文件或文件未找到' });
        }
    } catch (error) {
        console.error('FileNet 下载路由处理错误:', error.message);
        res.status(500).json({ success: false, message: error.message || '从 FileNet 下载失败' });
    }
});

// 新增：获取 FileNet 文档列表 (从数据库)
/**
 * @swagger
 * /api/filenet/documents:
 *   get:
 *     summary: 从数据库获取 FileNet 文档列表
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: uploaded_at
 *         description: 排序字段 (id, fn_doc_id, original_name, uploaded_at)
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           default: DESC
 *           enum: [ASC, DESC]
 *         description: 排序顺序
 *     responses:
 *       200:
 *         description: FileNet 文档列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: 
 *                   type: boolean
 *                 documents: 
 *                   type: array
 *                   items: 
 *                     type: object # Define structure for a filenet document entry
 *                 pagination: 
 *                   type: object # Define structure for pagination info
 *       500:
 *         description: 服务器错误
 */
router.get('/documents', async (req, res) => {
    try {
        console.log('收到 FileNet 文档列表请求, 查询参数:', req.query);
        const options = req.query; // 获取查询参数 page, limit, sortBy, sortOrder
        const result = await filenetService.getFileNetDocumentsFromDB(options);
        console.log('FileNet 文档列表查询结果:', {
            success: result.success,
            documentCount: result.documents ? result.documents.length : 0,
            pagination: result.pagination
        });

        // 完整数据太多，只记录第一条作为示例
        if (result.documents && result.documents.length > 0) {
            console.log('文档示例 (第一条):', result.documents[0]);
        }

        res.json(result);
    } catch (error) {
        console.error('获取 FileNet 文档列表路由处理错误:', error.message);
        console.error('错误堆栈:', error.stack);
        res.status(500).json({ success: false, message: error.message || '获取 FileNet 文档列表失败' });
    }
});

module.exports = router; 