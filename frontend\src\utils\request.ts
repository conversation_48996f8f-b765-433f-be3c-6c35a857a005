/**
 * HTTP请求工具
 * 基于axios封装的统一请求接口
 */

import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response

    // 如果响应包含 success 字段，检查业务状态
    if (typeof data === 'object' && data !== null && 'success' in data) {
      if (data.success === false) {
        const errorMessage = data.message || '请求失败'
        message.error(errorMessage)
        return Promise.reject(new Error(errorMessage))
      }
    }

    return data
  },
  error => {
    console.error('响应拦截器错误:', error)

    // 处理网络错误
    if (!error.response) {
      message.error('网络错误，请检查网络连接')
      return Promise.reject(error)
    }

    // 处理HTTP状态码错误
    const { status, data } = error.response
    let errorMessage = '请求失败'

    switch (status) {
      case 401:
        errorMessage = '未授权，请重新登录'
        // 清除token并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        window.location.href = '/login'
        break
      case 403:
        errorMessage = '权限不足'
        break
      case 404:
        errorMessage = '请求的资源不存在'
        break
      case 500:
        errorMessage = '服务器内部错误'
        break
      default:
        errorMessage = data?.message || `请求失败 (${status})`
    }

    message.error(errorMessage)
    return Promise.reject(new Error(errorMessage))
  }
)

export default request
