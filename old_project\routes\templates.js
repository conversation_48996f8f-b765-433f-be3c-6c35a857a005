const express = require('express');
const router = express.Router();
const templateService = require('../services/templateService');
const { apiError } = require('../middleware/error'); // 假设有统一的错误处理

/**
 * @swagger
 * tags:
 *   name: Templates
 *   description: 模板管理
 */

/**
 * @swagger
 * /api/templates:
 *   get:
 *     summary: 获取模板列表
 *     tags: [Templates]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: 偏移量
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: string
 *         description: 分类ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [enabled, disabled]
 *           default: enabled
 *         description: 模板状态
 *     responses:
 *       200:
 *         description: 成功获取模板列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data: 
 *                   type: object
 *                   properties:
 *                     templates: { type: array, items: { $ref: '#/components/schemas/TemplateOutput' } }
 *                     total: { type: integer }
 *                     limit: { type: integer }
 *                     offset: { type: integer }
 *       500:
 *         description: 服务器错误
 */
router.get('/', /* isAuthenticated, */ async (req, res, next) => {
    try {
        console.log('收到获取模板列表请求，查询参数:', req.query);
        const options = {
            limit: parseInt(req.query.limit) || 10,
            offset: parseInt(req.query.offset) || 0,
            categoryId: req.query.categoryId,
            status: req.query.status || 'enabled',
            sortBy: req.query.sortBy,
            order: req.query.order
        };
        console.log('查询选项:', options);
        const result = await templateService.getTemplates(options);
        console.log('查询结果:', JSON.stringify(result).substring(0, 200) + '...');
        console.log(`查询到 ${result.templates ? result.templates.length : '未知数量'} 条模板记录`);
        res.json({ success: true, data: result });
    } catch (error) {
        console.error('获取模板列表出错:', error);
        next(apiError('获取模板列表失败', 500, error));
    }
});

/**
 * @swagger
 * /api/templates/{templateId}:
 *   get:
 *     summary: 获取单个模板详情
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     responses:
 *       200:
 *         description: 成功获取模板详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data: { $ref: '#/components/schemas/TemplateOutput' }
 *       404:
 *         description: 模板未找到
 *       500:
 *         description: 服务器错误
 */
router.get('/:templateId', /* isAuthenticated, */ async (req, res, next) => {
    try {
        const template = await templateService.getTemplateById(req.params.templateId);
        if (!template) {
            return next(apiError('模板未找到', 404));
        }
        res.json({ success: true, data: template });
    } catch (error) {
        next(apiError('获取模板详情失败', 500, error));
    }
});

/**
 * @swagger
 * /api/templates:
 *   post:
 *     summary: 创建新模板
 *     tags: [Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TemplateInput'
 *     responses:
 *       201:
 *         description: 模板创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data: { $ref: '#/components/schemas/TemplateOutput' }
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/', /* isAuthenticated, authorizeRole(['admin', 'editor']), */ async (req, res, next) => {
    try {
        // TODO: 添加创建者ID (req.user.id)
        const templateData = { ...req.body, created_by: req.user ? req.user.id : 'system' };
        const newTemplate = await templateService.createTemplate(templateData);
        res.status(201).json({ success: true, data: newTemplate });
    } catch (error) {
        if (error.message.includes('不能为空') || error.message.includes('不存在')) {
            return next(apiError(error.message, 400));
        }
        next(apiError('创建模板失败', 500, error));
    }
});

/**
 * @swagger
 * /api/templates/{templateId}:
 *   put:
 *     summary: 更新模板信息
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TemplateUpdateInput'
 *     responses:
 *       200:
 *         description: 模板更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data: { $ref: '#/components/schemas/TemplateOutput' }
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 模板未找到
 *       500:
 *         description: 服务器错误
 */
router.put('/:templateId', /* isAuthenticated, authorizeRole(['admin', 'editor']), */ async (req, res, next) => {
    try {
        // TODO: 添加更新者ID (req.user.id)
        const updateData = { ...req.body, updated_by: req.user ? req.user.id : 'system' };
        const updatedTemplate = await templateService.updateTemplate(req.params.templateId, updateData);
        if (!updatedTemplate) {
            return next(apiError('模板未找到或无法更新', 404));
        }
        res.json({ success: true, data: updatedTemplate });
    } catch (error) {
        if (error.message.includes('更新字段') || error.message.includes('不存在')) {
            return next(apiError(error.message, 400));
        }
        next(apiError('更新模板失败', 500, error));
    }
});

/**
 * @swagger
 * /api/templates/{templateId}:
 *   delete:
 *     summary: 删除模板 (软删除)
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     responses:
 *       200:
 *         description: 模板删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 message: { type: string }
 *       404:
 *         description: 模板未找到
 *       500:
 *         description: 服务器错误
 */
router.delete('/:templateId', /* isAuthenticated, authorizeRole(['admin']), */ async (req, res, next) => {
    try {
        const success = await templateService.deleteTemplate(req.params.templateId);
        if (!success) {
            return next(apiError('模板未找到或无法删除', 404));
        }
        res.json({ success: true, message: '模板已成功删除' });
    } catch (error) {
        next(apiError('删除模板失败', 500, error));
    }
});


/**
 * @swagger
 * components:
 *   schemas:
 *     TemplateBase:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: 模板名称
 *           example: "月度报告模板"
 *         category_id:
 *           type: string
 *           format: uuid
 *           description: 分类ID
 *           nullable: true
 *         description:
 *           type: string
 *           description: 模板描述
 *           nullable: true
 *         status:
 *           type: string
 *           enum: [enabled, disabled]
 *           description: 模板状态
 * 
 *     TemplateInput:
 *       allOf:
 *         - $ref: '#/components/schemas/TemplateBase'
 *         - type: object
 *           required:
 *             - name
 *             - doc_id
 *           properties:
 *             doc_id:
 *               type: string
 *               format: uuid
 *               description: 源文档ID (来自 filenet_documents 表)
 *               example: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
 * 
 *     TemplateUpdateInput:
 *       allOf:
 *         - $ref: '#/components/schemas/TemplateBase'
 *         # 在更新时，所有字段都是可选的
 * 
 *     TemplateOutput:
 *       allOf:
 *         - $ref: '#/components/schemas/TemplateBase'
 *         - type: object
 *           properties:
 *             id:
 *               type: string
 *               format: uuid
 *               description: 模板ID
 *             doc_id:
 *               type: string
 *               format: uuid
 *               description: 源文档ID
 *             category_name:
 *               type: string
 *               description: 分类名称
 *               nullable: true
 *             source_document_name:
 *                type: string
 *                description: 模板源文档的原始名称
 *                nullable: true
 *             source_document_extension:
 *                type: string
 *                description: 模板源文档的扩展名
 *                nullable: true
 *             created_at:
 *               type: string
 *               format: date-time
 *             updated_at:
 *               type: string
 *               format: date-time
 *             created_by:
 *               type: string
 *             updated_by:
 *               type: string
 *               nullable: true
 *             is_deleted:
 *               type: boolean
 */

module.exports = router; 