# OnlyOffice集成系统 - 项目初始化指南

> **版本**: v1.0  
> **更新时间**: 2024年12月19日  
> **适用阶段**: 架构过渡前期准备

## 🎯 初始化策略

### 项目架构设计思路
```yaml
当前阶段: 前后端整合开发 (便于调试和部署)
  - 单一启动命令
  - 共享依赖和配置
  - 简化开发流程

未来升级: 前后端完全分离 (企业级部署)
  - 独立部署和扩展
  - 微服务架构支持
  - CDN静态资源分离
```

## 📁 目录结构规划

### 推荐的混合架构 (前期整合 + 后期分离)
```
OnlyOffice/
├── backend/                    # 后端源码目录
│   ├── src/
│   │   ├── controllers/        # API控制器
│   │   ├── services/          # 业务逻辑 (现有services迁移)
│   │   ├── middleware/        # 中间件
│   │   ├── routes/           # 路由定义
│   │   ├── types/            # TypeScript类型定义
│   │   ├── utils/            # 工具函数
│   │   └── config/           # 配置文件
│   ├── dist/                 # TypeScript编译输出
│   ├── package.json         # 后端依赖
│   ├── tsconfig.json        # TypeScript配置
│   └── nodemon.json         # 开发环境配置
│
├── frontend/                  # 前端源码目录
│   ├── src/
│   │   ├── components/       # 公共组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── types/           # TypeScript类型
│   │   ├── utils/           # 工具函数
│   │   └── styles/          # 样式文件
│   ├── public/              # 静态资源
│   ├── dist/                # 构建输出 (暂时由backend托管)
│   ├── package.json         # 前端依赖
│   ├── vite.config.ts       # Vite配置
│   └── tsconfig.json        # TypeScript配置
│
├── shared/                   # 共享代码 (可选)
│   ├── types/               # 共享类型定义
│   └── constants/           # 共享常量
│
├── config/                  # 保留现有配置 (过渡期)
├── services/               # 保留现有服务 (过渡期)
├── uploads/                # 文件存储目录
├── tmp/                    # 临时文件目录
├── package.json            # 根目录依赖管理
├── .env.example            # 环境变量模板
└── README.md              # 项目说明
```

## 🛠️ 环境准备和依赖安装

### 1. 全局工具安装
```bash
# TypeScript编译器 (如果没有)
npm install -g typescript

# Vue CLI (可选，我们主要用Vite)
npm install -g @vue/cli

# PM2进程管理器 (生产环境)
npm install -g pm2

# 代码格式化工具 (可选)
npm install -g prettier eslint
```

### 2. 后端依赖安装

首先创建backend的package.json：

```bash
cd backend
npm init -y
```

安装后端核心依赖：
```bash
# 基础框架 (保持现有Express)
npm install express cors body-parser multer

# TypeScript环境
npm install -D typescript @types/node @types/express ts-node nodemon
npm install -D @types/cors @types/body-parser @types/multer

# API文档
npm install swagger-jsdoc swagger-ui-express
npm install -D @types/swagger-jsdoc @types/swagger-ui-express

# 日志系统
npm install winston

# 验证中间件
npm install joi express-validator

# 缓存 (暂时用内存缓存)
npm install node-cache

# 其他工具
npm install dotenv fs-extra
npm install -D @types/fs-extra
```

### 3. 前端依赖安装

创建Vue 3 + Ant Design Pro项目：
```bash
cd ../frontend

# 使用Vite创建Vue 3项目
npm create vue@latest . --template typescript

# 安装Ant Design Vue
npm install ant-design-vue

# 安装Ant Design Pro组件
npm install @ant-design/pro-components

# 路由和状态管理
npm install vue-router@4 pinia

# HTTP请求
npm install axios

# 工具库
npm install dayjs lodash-es
npm install -D @types/lodash-es

# 开发工具
npm install -D vite @vitejs/plugin-vue
npm install -D eslint prettier @typescript-eslint/parser
```

## ⚙️ 配置文件创建

### 1. 后端TypeScript配置

创建 `backend/tsconfig.json`：
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/types/*": ["./src/types/*"],
      "@/services/*": ["./src/services/*"],
      "@/controllers/*": ["./src/controllers/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

创建 `backend/nodemon.json`：
```json
{
  "watch": ["src"],
  "ext": "ts,js",
  "ignore": ["dist"],
  "exec": "ts-node src/app.ts"
}
```

### 2. 前端Vite配置

创建 `frontend/vite.config.ts`：
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: '../backend/public', // 构建到后端static目录
    emptyOutDir: true,
  },
})
```

### 3. 环境变量配置

创建根目录 `.env.example`：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=onlyoffice_system
DB_USER=your_username
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# OnlyOffice配置
ONLYOFFICE_SERVER_URL=http://localhost/
ONLYOFFICE_DOCUMENT_SERVER_URL=http://localhost:8000/

# FileNet配置  
FILENET_URL=your-filenet-url
FILENET_USERNAME=your-username
FILENET_PASSWORD=your-password

# 应用配置
NODE_ENV=development
PORT=3000
CORS_ORIGIN=http://localhost:8080

# 文件存储
UPLOAD_PATH=./uploads
TMP_PATH=./tmp
MAX_FILE_SIZE=100MB

# 缓存配置 (当前使用内存缓存)
CACHE_TYPE=memory
CACHE_TTL=3600

# Redis配置 (预留升级)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs
```

## 🚀 初始化命令

### 1. 一键初始化脚本

创建根目录 `package.json`：
```json
{
  "name": "onlyoffice-integration-system",
  "version": "1.0.0",
  "description": "OnlyOffice集成系统 - 架构过渡版本",
  "scripts": {
    "setup": "npm run setup:backend && npm run setup:frontend",
    "setup:backend": "cd backend && npm install",
    "setup:frontend": "cd frontend && npm install",
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "cd backend && npm run dev",
    "dev:frontend": "cd frontend && npm run dev",
    "build": "npm run build:frontend && npm run build:backend",
    "build:frontend": "cd frontend && npm run build",
    "build:backend": "cd backend && npm run build",
    "start": "cd backend && npm start"
  },
  "devDependencies": {
    "concurrently": "^7.6.0"
  }
}
```

### 2. 执行初始化

```bash
# 1. 安装根目录依赖
npm install

# 2. 初始化前后端项目
npm run setup

# 3. 复制环境变量文件
cp .env.example .env

# 4. 编辑配置文件 (填入实际配置)
# 修改 .env 文件中的数据库连接等信息
```

## 🔧 开发环境配置

### 1. backend/package.json 脚本配置
```json
{
  "scripts": {
    "dev": "nodemon",
    "build": "tsc",
    "start": "node dist/app.js",
    "test": "jest",
    "lint": "eslint src/**/*.ts",
    "format": "prettier --write src/**/*.ts"
  }
}
```

### 2. frontend/package.json 脚本配置
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc --noEmit && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "lint": "eslint src/**/*.{vue,ts}",
    "format": "prettier --write src/**/*.{vue,ts}"
  }
}
```

## 🎯 关键优势

### ✅ 前期整合开发
1. **单一启动**: `npm run dev` 同时启动前后端
2. **共享配置**: 环境变量、代理设置统一管理
3. **简化部署**: 前端构建到后端static目录
4. **快速调试**: 无需处理跨域，开发效率高

### ✅ 后期分离升级
1. **独立构建**: 前端可独立构建到CDN
2. **独立部署**: 后端API服务可独立扩展
3. **微服务化**: 后端可拆分为多个服务
4. **容器化**: 前后端可分别容器化部署

### ✅ 技术栈现代化
1. **类型安全**: 全栈TypeScript覆盖
2. **企业级UI**: Ant Design Pro专业管理界面
3. **API规范**: Swagger文档自动生成
4. **扩展预留**: Redis、RabbitMQ接口预留

## 📋 初始化检查清单

### 开发环境检查
- [ ] Node.js >= 16.0.0 ✅ (您的版本: v22.15.0)
- [ ] npm >= 8.0.0 ✅ (您的版本: 10.9.2)
- [ ] Git环境配置 ✅
- [ ] 数据库连接确认 ⏳
- [ ] OnlyOffice服务器确认 ⏳

### 项目初始化检查
- [ ] 目录结构创建 ✅
- [ ] 后端依赖安装 ⏳
- [ ] 前端依赖安装 ⏳
- [ ] 环境变量配置 ⏳
- [ ] TypeScript配置 ⏳

### 功能测试检查
- [ ] 后端API启动测试 ⏳
- [ ] 前端界面访问测试 ⏳
- [ ] 数据库连接测试 ⏳
- [ ] 文件上传功能测试 ⏳
- [ ] OnlyOffice集成测试 ⏳

## 🚀 下一步行动

1. **立即执行**: 运行上述初始化命令
2. **配置调整**: 修改 `.env` 文件中的实际配置
3. **现有代码迁移**: 将services目录内容迁移到backend/src/services
4. **第一个API**: 创建health check接口测试环境
5. **第一个页面**: 创建登录页面测试前端环境

准备好开始了吗？我可以帮您执行这些初始化步骤！ 