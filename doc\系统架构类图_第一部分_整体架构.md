# OnlyOffice系统架构类图 - 第一部分：整体架构

## 系统分层架构概览

```mermaid
graph TB
    subgraph "外部系统"
        OO[OnlyOffice Document Server]
        FN[FileNet Content Engine]
        DB[(MySQL Database)]
        FS[File System]
    end

    subgraph "应用入口层"
        SERVER[server.js]
        APP[app.js]
    end

    subgraph "路由层 (Routes)"
        IR[index.js - 主路由聚合器]
        ER[editor.js - 编辑器路由]
        DR[documents.js - 文档路由]
        CTR[config-templates.js - 配置模板路由]
        TR[templates.js - 模板路由]
        FR[filenetRoutes.js - FileNet路由]
        CR[config.js - 配置路由]
        AR[api.js - API路由]
        DBR[debug.js - 调试路由]
    end

    subgraph "中间件层 (Middleware)"
        ERR[error.js - 错误处理]
        UPL[upload.js - 文件上传]
    end

    subgraph "服务层 (Services)"
        DS[document.js - 文档服务]
        CTS[configTemplateService.js - 配置模板服务]
        DBS[database.js - 数据库服务]
        FS_SVC[fileStorage.js - 文件存储服务]
        FNS[filenetService.js - FileNet服务]
        CS[configService.js - 配置服务]
        TS[templateService.js - 模板服务]
        JWT[jwt.js - JWT服务]
    end

    subgraph "配置层 (Config)"
        CFG_IDX[config/index.js - 主配置]
        CFG_DEF[config/default.js - 默认配置]
        CFG_DB[config/database.js - 数据库配置]
    end

    subgraph "视图层 (Views)"
        ED_VIEW[editor.ejs - 编辑器视图]
        CT_VIEW[config-templates.ejs - 配置模板视图]
        ERR_VIEW[error.ejs - 错误视图]
    end

    subgraph "静态资源层 (Public)"
        HTML[HTML页面]
        CSS[CSS样式]
        JS[JavaScript文件]
    end

    %% 依赖关系
    SERVER --> APP
    APP --> IR
    APP --> CTR
    APP --> FR
    APP --> CR
    APP --> DBR
    APP --> ERR
    
    IR --> ER
    IR --> DR
    IR --> TR
    
    %% 路由层到服务层
    ER --> DS
    ER --> CTS
    ER --> JWT
    ER --> FS_SVC
    
    DR --> DS
    DR --> FS_SVC
    DR --> FNS
    
    CTR --> CTS
    CTR --> DBS
    
    TR --> TS
    FR --> FNS
    
    %% 服务层内部依赖
    DS --> DBS
    DS --> FS_SVC
    DS --> JWT
    DS --> CS
    
    CTS --> DBS
    CTS --> CS
    
    FNS --> DBS
    FS_SVC --> DBS
    TS --> DBS
    
    %% 服务层到外部系统
    DS --> OO
    FNS --> FN
    DBS --> DB
    FS_SVC --> FS
    
    %% 配置依赖
    DS --> CFG_IDX
    CTS --> CFG_IDX
    DBS --> CFG_DB
    
    %% 视图渲染
    ER --> ED_VIEW
    CTR --> CT_VIEW
    IR --> ERR_VIEW
    
    %% 中间件使用
    APP --> UPL
    DR --> UPL

    classDef routeClass fill:#e1f5fe
    classDef serviceClass fill:#f3e5f5
    classDef configClass fill:#fff3e0
    classDef viewClass fill:#e8f5e8
    classDef externalClass fill:#fce4ec
    classDef entryClass fill:#fff9c4

    class IR,ER,DR,CTR,TR,FR,CR,AR,DBR routeClass
    class DS,CTS,DBS,FS_SVC,FNS,CS,TS,JWT serviceClass
    class CFG_IDX,CFG_DEF,CFG_DB configClass
    class ED_VIEW,CT_VIEW,ERR_VIEW viewClass
    class OO,FN,DB,FS externalClass
    class SERVER,APP entryClass
```

## 核心组件说明

### 🚀 应用入口层
- **server.js**: 应用启动入口，负责服务器初始化和启动
- **app.js**: Express应用配置，中间件和路由注册

### 🛣️ 路由层 (API接口层)
- **index.js**: 主路由聚合器，统一管理所有路由
- **editor.js**: 编辑器相关路由，处理文档编辑功能
- **documents.js**: 文档管理路由，处理文档CRUD操作
- **config-templates.js**: 配置模板管理路由
- **filenetRoutes.js**: FileNet集成相关路由

### 🔧 服务层 (业务逻辑层)
- **document.js**: 核心文档服务，处理OnlyOffice集成
- **configTemplateService.js**: 配置模板服务，管理编辑器配置
- **database.js**: 数据库抽象层
- **fileStorage.js**: 文件存储服务
- **filenetService.js**: FileNet集成服务

### ⚙️ 配置层
- **config/index.js**: 主配置文件，整合所有配置
- **config/default.js**: 默认配置项
- **config/database.js**: 数据库专用配置

### 🖼️ 视图层
- **editor.ejs**: 文档编辑器界面模板
- **config-templates.ejs**: 配置模板管理界面
- **error.ejs**: 错误页面模板

### 🌐 外部系统集成
- **OnlyOffice Document Server**: 文档编辑服务器
- **FileNet Content Engine**: 企业内容管理系统
- **MySQL Database**: 关系型数据库
- **File System**: 本地文件系统

## 数据流向

1. **请求处理流程**: HTTP请求 → 路由层 → 服务层 → 数据库/外部系统
2. **文档编辑流程**: 用户请求 → 编辑器路由 → 文档服务 → OnlyOffice服务器
3. **配置管理流程**: 管理员配置 → 配置模板路由 → 配置模板服务 → 数据库
4. **文件存储流程**: 文件上传 → 文档路由 → 文件存储服务 → 文件系统

## 架构特点

- **分层架构**: 清晰的分层设计，职责分离
- **服务化**: 业务逻辑封装在服务层
- **可配置**: 灵活的配置管理系统
- **可扩展**: 模块化设计，易于扩展
- **集成性**: 良好的外部系统集成能力 