/**
 * 调试路由
 */
const express = require('express');
const router = express.Router();

// 检查服务导出的函数
router.get('/check-functions', async (req, res) => {
    try {
        const documentService = require('../services/document');

        const result = {
            getFileNetDocumentConfig: documentService.getFileNetDocumentConfig.toString(),
            getDocumentConfig: documentService.getDocumentConfig.toString()
        };

        res.json(result);
    } catch (error) {
        res.status(500).json({
            error: error.message,
            stack: error.stack
        });
    }
});

module.exports = router; 