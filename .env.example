# OnlyOffice集成系统 - 环境变量配置
# 复制此文件为 .env 并填入实际配置值

# ================================
# 数据库配置
# ================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=onlyoffice_system
DB_USER=your_username
DB_PASSWORD=your_password

# ================================
# 应用基础配置
# ================================
NODE_ENV=development
PORT=3000
FRONTEND_PORT=8080

# ================================
# JWT认证配置
# ================================
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h

# ================================
# CORS安全配置
# ================================
CORS_ORIGIN=http://localhost:8080
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# ================================
# OnlyOffice服务器配置
# ================================
ONLYOFFICE_SERVER_URL=http://localhost/
ONLYOFFICE_DOCUMENT_SERVER_URL=http://localhost:8000/
ONLYOFFICE_SECRET_KEY=your-onlyoffice-secret-key

# ================================
# FileNet企业集成配置
# ================================
FILENET_URL=your-filenet-server-url
FILENET_USERNAME=your-filenet-username
FILENET_PASSWORD=your-filenet-password
FILENET_OBJECT_STORE=your-object-store-name

# ================================
# 文件存储配置
# ================================
UPLOAD_PATH=./uploads
TMP_PATH=./tmp
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt

# ================================
# 缓存配置 (当前使用内存缓存)
# ================================
CACHE_TYPE=memory
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# ================================
# Redis配置 (预留升级使用)
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ================================
# 日志配置
# ================================
LOG_LEVEL=info
LOG_DIR=./logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7

# ================================
# 性能监控配置
# ================================
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1000
REQUEST_TIMEOUT=30000

# ================================
# 安全配置
# ================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
ENABLE_SECURITY_HEADERS=true

# ================================
# 开发环境配置
# ================================
HOT_RELOAD=true
DEBUG_MODE=true
API_DOCS_ENABLED=true
SWAGGER_ENDPOINT=/api-docs 