import type { Router } from 'vue-router'
import { message } from 'ant-design-vue'
import { usePermissions } from '@/composables/usePermissions'

/**
 * 路由权限配置
 * @description 定义每个路由需要的权限
 */
const ROUTE_PERMISSIONS: Record<string, string[]> = {
  '/': [], // 首页无需权限
  '/dashboard': [], // 仪表盘无需权限
  '/login': [], // 登录页无需权限

  // 文档管理
  '/documents': ['documents.read'],
  '/documents/create': ['documents.create'],
  '/documents/edit': ['documents.update'],

  // 模板管理
  '/templates': ['templates.read'],
  '/templates/create': ['templates.create'],
  '/templates/edit': ['templates.update'],

  // 用户管理
  '/users': ['users.read'],
  '/users/create': ['users.create'],
  '/users/edit': ['users.update'],

  // 权限管理
  '/permissions': ['permissions.read', 'roles.read'],

  // 系统配置
  '/system-config': ['system.read', 'config.read'],
}

/**
 * 检查路由权限
 * @param path 路由路径
 * @returns 是否有权限访问
 */
const checkRoutePermission = (path: string): boolean => {
  const { hasPermission, hasRole } = usePermissions()

  // 超级管理员拥有所有权限
  if (hasRole('super_admin')) {
    return true
  }

  // 获取路由所需权限
  const requiredPermissions = ROUTE_PERMISSIONS[path]

  // 如果没有配置权限要求，则允许访问
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true
  }

  // 检查是否拥有任意一个所需权限
  return hasPermission(requiredPermissions)
}

/**
 * 安装路由权限守卫
 * @param router Vue Router实例
 */
export function setupPermissionGuard(router: Router) {
  const { loadPermissionsFromStorage } = usePermissions()

  // 前置守卫：权限检查
  router.beforeEach(async (to, from, next) => {
    console.log('🔐 [路由守卫] 检查路由权限:', to.path)

    // 登录页和首页直接放行
    if (['/login', '/', '/dashboard'].includes(to.path)) {
      next()
      return
    }

    // 检查是否有token
    const token = localStorage.getItem('token')
    if (!token) {
      console.warn('⚠️ [路由守卫] 未找到token，重定向到登录页')
      message.warning('请先登录')
      next('/login')
      return
    }

    // 尝试从localStorage加载权限信息
    const hasPermissionData = loadPermissionsFromStorage()
    if (!hasPermissionData) {
      console.warn('⚠️ [路由守卫] 未找到权限信息，重定向到登录页')
      message.warning('权限信息不完整，请重新登录')
      next('/login')
      return
    }

    // 检查路由权限
    const hasAccess = checkRoutePermission(to.path)
    if (!hasAccess) {
      console.warn('⚠️ [路由守卫] 无权限访问:', to.path)
      message.error('您没有权限访问该页面')

      // 重定向到有权限的默认页面
      const { permissionState } = usePermissions()
      if (permissionState.value.canManageDocuments) {
        next('/documents')
      } else if (permissionState.value.canManageUsers) {
        next('/users')
      } else {
        next('/dashboard')
      }
      return
    }

    console.log('✅ [路由守卫] 权限检查通过:', to.path)
    next()
  })

  // 后置守卫：路由跳转完成后的处理
  router.afterEach(to => {
    console.log('📍 [路由守卫] 路由跳转完成:', to.path)

    // 设置页面标题
    const routeTitles: Record<string, string> = {
      '/dashboard': '系统首页',
      '/documents': '文档管理',
      '/templates': '模板管理',
      '/users': '用户管理',
      '/permissions': '权限管理',
      '/system-config': '系统配置',
    }

    const title = routeTitles[to.path] || 'OnlyOffice集成系统'
    document.title = `${title} - OnlyOffice集成系统`
  })
}

/**
 * 动态添加路由权限配置
 * @param path 路由路径
 * @param permissions 所需权限列表
 */
export function addRoutePermission(path: string, permissions: string[]) {
  ROUTE_PERMISSIONS[path] = permissions
  console.log('🔐 [路由守卫] 添加路由权限配置:', { path, permissions })
}

/**
 * 获取所有路由权限配置
 * @returns 路由权限配置对象
 */
export function getRoutePermissions(): Record<string, string[]> {
  return { ...ROUTE_PERMISSIONS }
}
