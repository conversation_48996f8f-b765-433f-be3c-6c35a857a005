import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, MinLength, MaxLength } from 'class-validator';

/**
 * 登录请求DTO
 */
export class LoginDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    minLength: 3,
    maxLength: 50,
  })
  @IsString({ message: '用户名必须是字符串' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @MinLength(3, { message: '用户名长度至少3个字符' })
  @MaxLength(50, { message: '用户名长度不能超过50个字符' })
  username: string;

  @ApiProperty({
    description: '密码',
    example: 'password123',
    minLength: 6,
  })
  @IsString({ message: '密码必须是字符串' })
  @IsNotEmpty({ message: '密码不能为空' })
  @MinLength(6, { message: '密码长度至少6个字符' })
  password: string;
}

/**
 * 刷新令牌请求DTO
 */
export class RefreshTokenDto {
  @ApiProperty({
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString({ message: '刷新令牌必须是字符串' })
  @IsNotEmpty({ message: '刷新令牌不能为空' })
  refreshToken: string;
}

/**
 * 用户信息嵌套对象
 */
export class AuthUserDto {
  @ApiProperty({
    description: '用户ID',
    example: 'user-123-456',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  username: string;

  @ApiProperty({
    description: '用户角色',
    example: 'admin',
  })
  role: string;
}

/**
 * 认证响应DTO
 */
export class AuthResponseDto {
  @ApiProperty({
    description: '访问令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: '令牌类型',
    example: 'Bearer',
  })
  tokenType: string;

  @ApiProperty({
    description: '过期时间(秒)',
    example: 86400,
  })
  expiresIn: number;

  @ApiProperty({
    description: '用户信息',
    type: AuthUserDto,
  })
  user: AuthUserDto;
}

/**
 * 用户信息DTO
 */
export class UserInfoDto {
  @ApiProperty({
    description: '用户ID',
    example: 'user-123',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  username: string;

  @ApiProperty({
    description: '用户角色',
    example: 'admin',
    enum: ['admin', 'user', 'readonly'],
  })
  role: string;

  @ApiProperty({
    description: '用户状态',
    example: 'active',
    enum: ['active', 'inactive', 'suspended'],
  })
  status: string;

  @ApiPropertyOptional({
    description: '创建时间',
    example: '2024-12-19T10:30:00Z',
  })
  createdAt?: string;

  @ApiPropertyOptional({
    description: '最后登录时间',
    example: '2024-12-19T10:30:00Z',
  })
  lastLoginAt?: string;
} 