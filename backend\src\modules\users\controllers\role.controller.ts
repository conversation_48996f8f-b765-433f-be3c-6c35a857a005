import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RoleService } from '../services/role.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import {
  CreateRoleDto,
  UpdateRoleDto,
  RoleQueryDto,
  RoleResponseDto,
  RoleListResponseDto,
  AssignPermissionsDto,
} from '../dto/role.dto';

/**
 * 角色管理控制器API响应类型
 */
interface ApiSuccessResponse<T = unknown> {
  success: true;
  message: string;
  data?: T;
}

interface ApiErrorResponse {
  success: false;
  message: string;
  error?: string;
}

type RoleControllerResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * 角色管理控制器
 * 
 * 提供完整的角色CRUD操作和权限分配功能
 * 
 * @controller RoleController
 * <AUTHOR> Team
 * @since 2024-12-19
 */
@ApiTags('角色管理')
@Controller('roles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  /**
   * 获取角色列表
   */
  @Get()
  @ApiOperation({ summary: '获取角色列表' })
  @ApiResponse({
    status: 200,
    description: '获取角色列表成功',
    type: RoleListResponseDto,
  })
  async getRoles(@Query() query: RoleQueryDto): Promise<RoleControllerResponse<RoleListResponseDto>> {
    try {
      const result = await this.roleService.findMany(query);
      
      return {
        success: true,
        message: '获取角色列表成功',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取角色列表失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取角色详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取角色详情' })
  @ApiResponse({
    status: 200,
    description: '获取角色详情成功',
    type: RoleResponseDto,
  })
  async getRoleById(@Param('id') id: string): Promise<RoleControllerResponse<RoleResponseDto>> {
    try {
      const role = await this.roleService.findById(id);
      if (!role) {
        return {
          success: false,
          message: '角色不存在',
        };
      }

      return {
        success: true,
        message: '获取角色详情成功',
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取角色详情失败',
        error: error.message,
      };
    }
  }

  /**
   * 创建角色
   */
  @Post()
  @ApiOperation({ summary: '创建角色' })
  @ApiResponse({
    status: 201,
    description: '创建角色成功',
    type: RoleResponseDto,
  })
  async createRole(@Body() createRoleDto: CreateRoleDto, @Request() req): Promise<RoleControllerResponse<RoleResponseDto>> {
    try {
      const currentUserId = req.user?.sub;
      const role = await this.roleService.createRole(createRoleDto, currentUserId);

      return {
        success: true,
        message: '创建角色成功',
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        message: '创建角色失败',
        error: error.message,
      };
    }
  }

  /**
   * 更新角色
   */
  @Put(':id')
  @ApiOperation({ summary: '更新角色信息' })
  @ApiResponse({
    status: 200,
    description: '更新角色信息成功',
    type: RoleResponseDto,
  })
  async updateRole(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Request() req,
  ): Promise<RoleControllerResponse<RoleResponseDto>> {
    try {
      const currentUserId = req.user?.sub;
      const role = await this.roleService.updateRole(id, updateRoleDto, currentUserId);

      return {
        success: true,
        message: '更新角色信息成功',
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        message: '更新角色信息失败',
        error: error.message,
      };
    }
  }

  /**
   * 删除角色
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除角色' })
  @ApiResponse({
    status: 204,
    description: '删除角色成功',
  })
  async deleteRole(@Param('id') id: string, @Request() req): Promise<RoleControllerResponse> {
    try {
      const currentUserId = req.user?.sub;
      await this.roleService.deleteRole(id, currentUserId);
      
      return {
        success: true,
        message: '删除角色成功',
      };
    } catch (error) {
      return {
        success: false,
        message: '删除角色失败',
        error: error.message,
      };
    }
  }

  /**
   * 为角色分配权限
   */
  @Put(':id/permissions')
  @ApiOperation({ summary: '为角色分配权限' })
  @ApiResponse({
    status: 200,
    description: '权限分配成功',
    type: RoleResponseDto,
  })
  async assignPermissions(
    @Param('id') id: string,
    @Body() assignPermissionsDto: AssignPermissionsDto,
    @Request() req,
  ): Promise<RoleControllerResponse<RoleResponseDto>> {
    try {
      const currentUserId = req.user?.sub;
      const role = await this.roleService.assignPermissions(id, assignPermissionsDto.permissions, currentUserId);

      return {
        success: true,
        message: '权限分配成功',
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        message: '权限分配失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取角色的用户数量
   */
  @Get(':id/users/count')
  @ApiOperation({ summary: '获取角色的用户数量' })
  @ApiResponse({
    status: 200,
    description: '获取用户数量成功',
  })
  async getRoleUserCount(@Param('id') id: string): Promise<RoleControllerResponse> {
    try {
      const count = await this.roleService.getRoleUserCount(id);

      return {
        success: true,
        message: '获取用户数量成功',
        data: { count },
      };
    } catch (error) {
      return {
        success: false,
        message: '获取用户数量失败',
        error: error.message,
      };
    }
  }

  /**
   * 复制角色
   */
  @Post(':id/copy')
  @ApiOperation({ summary: '复制角色' })
  @ApiResponse({
    status: 201,
    description: '复制角色成功',
    type: RoleResponseDto,
  })
  async copyRole(@Param('id') id: string, @Request() req): Promise<RoleControllerResponse<RoleResponseDto>> {
    try {
      const currentUserId = req.user?.sub;
      const role = await this.roleService.copyRole(id, currentUserId);

      return {
        success: true,
        message: '复制角色成功',
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        message: '复制角色失败',
        error: error.message,
      };
    }
  }

  /**
   * 启用/禁用角色
   */
  @Put(':id/toggle-status')
  @ApiOperation({ summary: '启用/禁用角色' })
  @ApiResponse({
    status: 200,
    description: '角色状态切换成功',
    type: RoleResponseDto,
  })
  async toggleRoleStatus(@Param('id') id: string, @Request() req): Promise<RoleControllerResponse<RoleResponseDto>> {
    try {
      const currentUserId = req.user?.sub;
      const role = await this.roleService.toggleRoleStatus(id, currentUserId);

      return {
        success: true,
        message: '角色状态切换成功',
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        message: '角色状态切换失败',
        error: error.message,
      };
    }
  }
} 