<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice表格填报系统 - 电子报价单</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
      color: #333;
      line-height: 1.6;
    }

    .container {
      display: flex;
      min-height: 100vh;
    }

    /* 左侧导航栏 */
    .sidebar {
      width: 260px;
      background: #ffffff;
      border-right: 1px solid #e8eaec;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 1000;
    }

    .logo {
      padding: 20px;
      border-bottom: 1px solid #e8eaec;
    }

    .logo h2 {
      color: #1890ff;
      font-size: 20px;
      font-weight: 600;
    }

    .nav-menu {
      padding: 20px 0;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: #666;
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .nav-item:hover {
      background: #f0f8ff;
      color: #1890ff;
    }

    .nav-item.active {
      background: #e6f7ff;
      color: #1890ff;
      border-right: 3px solid #1890ff;
    }

    .nav-icon {
      margin-right: 12px;
      font-size: 18px;
    }

    /* 主内容区 */
    .main-content {
      flex: 1;
      margin-left: 260px;
      background: #ffffff;
    }

    .header {
      background: #ffffff;
      padding: 16px 24px;
      border-bottom: 1px solid #e8eaec;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }

    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .btn {
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 6px;
    }

    .btn-primary {
      background: #1890ff;
      color: white;
    }

    .btn-primary:hover {
      background: #40a9ff;
    }

    .btn-secondary {
      background: #f5f5f5;
      color: #666;
      border: 1px solid #d9d9d9;
    }

    .btn-secondary:hover {
      background: #fafafa;
      border-color: #40a9ff;
      color: #40a9ff;
    }

    .btn-success {
      background: #52c41a;
      color: white;
    }

    .btn-success:hover {
      background: #73d13d;
    }

    .content {
      padding: 24px;
    }

    /* 表格容器 */
    .form-container {
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 24px;
    }

    .form-header {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      padding: 20px 24px;
      text-align: center;
    }

    .form-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .form-subtitle {
      font-size: 14px;
      opacity: 0.9;
    }

    /* 基本信息区域 */
    .basic-info {
      padding: 24px;
      background: #f8f9fa;
      border-bottom: 1px solid #e8eaec;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .info-item {
      display: flex;
      align-items: center;
    }

    .info-label {
      min-width: 100px;
      font-weight: 500;
      color: #262626;
    }

    .info-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .info-input:focus {
      outline: none;
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    /* 表格样式 */
    .table-wrapper {
      overflow-x: auto;
      background: #ffffff;
    }

    .quote-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .quote-table th {
      background: #fafafa;
      color: #262626;
      font-weight: 600;
      padding: 12px 8px;
      text-align: center;
      border: 1px solid #e8eaec;
      white-space: nowrap;
    }

    .quote-table td {
      padding: 8px;
      border: 1px solid #e8eaec;
      text-align: center;
      position: relative;
    }

    .table-input {
      width: 100%;
      border: none;
      outline: none;
      padding: 6px 8px;
      text-align: center;
      font-size: 13px;
      background: transparent;
      transition: background-color 0.3s ease;
    }

    .table-input:focus {
      background: #f0f8ff;
      border-radius: 4px;
    }

    .table-input.calculated {
      background: #f6ffed;
      color: #52c41a;
      font-weight: 500;
    }

    .table-input.error {
      background: #fff2f0;
      color: #ff4d4f;
    }

    .row-number {
      background: #f0f2f5;
      font-weight: 500;
      color: #666;
      width: 40px;
    }

    .action-column {
      width: 80px;
    }

    .delete-btn {
      background: #ff4d4f;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.3s ease;
    }

    .delete-btn:hover {
      background: #ff7875;
    }

    /* 统计区域 */
    .summary-section {
      padding: 24px;
      background: #f8f9fa;
      border-top: 1px solid #e8eaec;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
    }

    .summary-item {
      background: #ffffff;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .summary-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .summary-value {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }

    /* 工具栏 */
    .toolbar {
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #e8eaec;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    /* 状态指示器 */
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #52c41a;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }

      .sidebar.open {
        transform: translateX(0);
      }

      .main-content {
        margin-left: 0;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .summary-grid {
        grid-template-columns: 1fr 1fr;
      }

      .toolbar {
        flex-direction: column;
        gap: 12px;
      }
    }

    /* 通知样式 */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.95);
      color: #333;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10px);
      z-index: 2000;
      transform: translateX(400px);
      transition: transform 0.3s ease;
      max-width: 300px;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      border-left: 4px solid #52c41a;
    }

    .notification.error {
      border-left: 4px solid #ff4d4f;
    }

    .notification.warning {
      border-left: 4px solid #faad14;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 左侧导航栏 -->
    <nav class="sidebar">
      <div class="logo">
        <h2>📋 OnlyOffice</h2>
      </div>
      <div class="nav-menu">
        <a href="#" class="nav-item">
          <span class="nav-icon">🏠</span>
          首页
        </a>
        <a href="#" class="nav-item">
          <span class="nav-icon">📄</span>
          文档管理
        </a>
        <a href="#" class="nav-item active">
          <span class="nav-icon">📋</span>
          表格填报
        </a>
        <a href="#" class="nav-item">
          <span class="nav-icon">📊</span>
          数据分析
        </a>
        <a href="#" class="nav-item">
          <span class="nav-icon">👥</span>
          用户管理
        </a>
        <a href="#" class="nav-item">
          <span class="nav-icon">⚙️</span>
          系统设置
        </a>
      </div>
    </nav>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部标题栏 -->
      <header class="header">
        <h1 class="header-title">表格填报系统 - 电子报价单</h1>
        <div class="header-actions">
          <button class="btn btn-secondary" onclick="newForm()">
            <span>📄</span>
            新建表格
          </button>
          <button class="btn btn-secondary" onclick="loadTemplate()">
            <span>📋</span>
            加载模板
          </button>
          <button class="btn btn-primary" onclick="saveForm()">
            <span>💾</span>
            保存
          </button>
        </div>
      </header>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <button class="btn btn-secondary" onclick="addRow()">
            <span>➕</span>
            添加行
          </button>
          <button class="btn btn-secondary" onclick="importData()">
            <span>📥</span>
            导入数据
          </button>
          <button class="btn btn-secondary" onclick="exportData()">
            <span>📤</span>
            导出Excel
          </button>
        </div>
        <div class="toolbar-right">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span>自动保存已启用</span>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content">
        <!-- 表格容器 -->
        <div class="form-container">
          <!-- 表头 -->
          <div class="form-header">
            <h2 class="form-title">XX公司电子报价单</h2>
            <p class="form-subtitle">专业的企业级报价单填报系统</p>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">客户名称：</span>
                <input type="text" class="info-input" placeholder="请输入客户名称" value="深圳科技有限公司">
              </div>
              <div class="info-item">
                <span class="info-label">报价日期：</span>
                <input type="date" class="info-input" value="2024-12-19">
              </div>
              <div class="info-item">
                <span class="info-label">联系人：</span>
                <input type="text" class="info-input" placeholder="请输入联系人" value="张经理">
              </div>
              <div class="info-item">
                <span class="info-label">有效期：</span>
                <input type="date" class="info-input" value="2024-12-26">
              </div>
            </div>
          </div>

          <!-- 表格区域 -->
          <div class="table-wrapper">
            <table class="quote-table" id="quoteTable">
              <thead>
                <tr>
                  <th class="row-number">序号</th>
                  <th style="min-width: 180px;">产品名称</th>
                  <th style="min-width: 100px;">规格型号</th>
                  <th style="width: 80px;">单位</th>
                  <th style="width: 80px;">数量</th>
                  <th style="width: 100px;">单价(元)</th>
                  <th style="width: 100px;">金额(元)</th>
                  <th style="min-width: 150px;">备注</th>
                  <th class="action-column">操作</th>
                </tr>
              </thead>
              <tbody id="tableBody">
                <tr>
                  <td class="row-number">1</td>
                  <td><input type="text" class="table-input" placeholder="请输入产品名称" value="Dell OptiPlex 7090"></td>
                  <td><input type="text" class="table-input" placeholder="规格型号" value="Intel i7 16G 512G SSD"></td>
                  <td><input type="text" class="table-input" placeholder="单位" value="台"></td>
                  <td><input type="number" class="table-input quantity" placeholder="数量" value="5" onchange="calculateAmount(this)"></td>
                  <td><input type="number" class="table-input price" placeholder="单价" value="6800" step="0.01" onchange="calculateAmount(this)"></td>
                  <td><input type="text" class="table-input calculated amount" placeholder="金额" value="34000.00" readonly></td>
                  <td><input type="text" class="table-input" placeholder="备注信息" value="含三年保修"></td>
                  <td><button class="delete-btn" onclick="deleteRow(this)">删除</button></td>
                </tr>
                <tr>
                  <td class="row-number">2</td>
                  <td><input type="text" class="table-input" placeholder="请输入产品名称" value="HP LaserJet Pro M404n"></td>
                  <td><input type="text" class="table-input" placeholder="规格型号" value="黑白激光打印机"></td>
                  <td><input type="text" class="table-input" placeholder="单位" value="台"></td>
                  <td><input type="number" class="table-input quantity" placeholder="数量" value="2" onchange="calculateAmount(this)"></td>
                  <td><input type="number" class="table-input price" placeholder="单价" value="1200" step="0.01" onchange="calculateAmount(this)"></td>
                  <td><input type="text" class="table-input calculated amount" placeholder="金额" value="2400.00" readonly></td>
                  <td><input type="text" class="table-input" placeholder="备注信息" value="包含安装调试"></td>
                  <td><button class="delete-btn" onclick="deleteRow(this)">删除</button></td>
                </tr>
                <tr>
                  <td class="row-number">3</td>
                  <td><input type="text" class="table-input" placeholder="请输入产品名称" value="网络交换机"></td>
                  <td><input type="text" class="table-input" placeholder="规格型号" value="24口千兆交换机"></td>
                  <td><input type="text" class="table-input" placeholder="单位" value="台"></td>
                  <td><input type="number" class="table-input quantity" placeholder="数量" value="1" onchange="calculateAmount(this)"></td>
                  <td><input type="number" class="table-input price" placeholder="单价" value="800" step="0.01" onchange="calculateAmount(this)"></td>
                  <td><input type="text" class="table-input calculated amount" placeholder="金额" value="800.00" readonly></td>
                  <td><input type="text" class="table-input" placeholder="备注信息" value="含配置服务"></td>
                  <td><button class="delete-btn" onclick="deleteRow(this)">删除</button></td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 统计汇总 -->
          <div class="summary-section">
            <div class="summary-grid">
              <div class="summary-item">
                <div class="summary-label">总项目数</div>
                <div class="summary-value" id="totalItems">3</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">总数量</div>
                <div class="summary-value" id="totalQuantity">8</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">小计金额</div>
                <div class="summary-value" id="subtotal">¥37,200.00</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">税率</div>
                <div class="summary-value">
                  <input type="number" value="13" style="width:50px;border:none;text-align:center;font-size:18px;font-weight:600;color:#1890ff;" onchange="updateTax(this)">%
                </div>
              </div>
              <div class="summary-item">
                <div class="summary-label">税额</div>
                <div class="summary-value" id="taxAmount">¥4,836.00</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">合计金额</div>
                <div class="summary-value" id="totalAmount" style="color: #52c41a; font-size: 20px;">¥42,036.00</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    let rowCounter = 3;
    let autoSaveInterval;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      updateSummary();
      startAutoSave();
      showNotification('表格填报系统已加载完成', 'success');
    });

    // 添加新行
    function addRow() {
      rowCounter++;
      const tableBody = document.getElementById('tableBody');
      const newRow = document.createElement('tr');
      
      newRow.innerHTML = `
        <td class="row-number">${rowCounter}</td>
        <td><input type="text" class="table-input" placeholder="请输入产品名称"></td>
        <td><input type="text" class="table-input" placeholder="规格型号"></td>
        <td><input type="text" class="table-input" placeholder="单位"></td>
        <td><input type="number" class="table-input quantity" placeholder="数量" onchange="calculateAmount(this)"></td>
        <td><input type="number" class="table-input price" placeholder="单价" step="0.01" onchange="calculateAmount(this)"></td>
        <td><input type="text" class="table-input calculated amount" placeholder="金额" readonly></td>
        <td><input type="text" class="table-input" placeholder="备注信息"></td>
        <td><button class="delete-btn" onclick="deleteRow(this)">删除</button></td>
      `;
      
      tableBody.appendChild(newRow);
      updateRowNumbers();
      updateSummary();
      showNotification('已添加新行', 'success');
    }

    // 删除行
    function deleteRow(button) {
      if (document.querySelectorAll('#tableBody tr').length <= 1) {
        showNotification('至少需要保留一行', 'warning');
        return;
      }
      
      button.closest('tr').remove();
      updateRowNumbers();
      updateSummary();
      showNotification('已删除行', 'success');
    }

    // 更新行号
    function updateRowNumbers() {
      const rows = document.querySelectorAll('#tableBody tr');
      rows.forEach((row, index) => {
        const rowNumber = row.querySelector('.row-number');
        if (rowNumber) {
          rowNumber.textContent = index + 1;
        }
      });
    }

    // 计算金额
    function calculateAmount(input) {
      const row = input.closest('tr');
      const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
      const price = parseFloat(row.querySelector('.price').value) || 0;
      const amount = quantity * price;
      
      const amountInput = row.querySelector('.amount');
      amountInput.value = amount.toFixed(2);
      
      updateSummary();
    }

    // 更新汇总信息
    function updateSummary() {
      const rows = document.querySelectorAll('#tableBody tr');
      let totalItems = 0;
      let totalQuantity = 0;
      let subtotal = 0;

      rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
        const amount = parseFloat(row.querySelector('.amount').value) || 0;
        
        if (quantity > 0) {
          totalItems++;
          totalQuantity += quantity;
        }
        subtotal += amount;
      });

      document.getElementById('totalItems').textContent = totalItems;
      document.getElementById('totalQuantity').textContent = totalQuantity;
      document.getElementById('subtotal').textContent = `¥${subtotal.toLocaleString('zh-CN', {minimumFractionDigits: 2})}`;
      
      updateTax();
    }

    // 更新税额计算
    function updateTax(taxInput) {
      const subtotalText = document.getElementById('subtotal').textContent;
      const subtotal = parseFloat(subtotalText.replace(/[¥,]/g, '')) || 0;
      const taxRate = parseFloat(taxInput ? taxInput.value : document.querySelector('input[onchange="updateTax(this)"]').value) / 100;
      
      const taxAmount = subtotal * taxRate;
      const totalAmount = subtotal + taxAmount;
      
      document.getElementById('taxAmount').textContent = `¥${taxAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2})}`;
      document.getElementById('totalAmount').textContent = `¥${totalAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2})}`;
    }

    // 保存表格
    function saveForm() {
      // 模拟保存操作
      showNotification('正在保存表格...', 'success');
      setTimeout(() => {
        showNotification('表格保存成功', 'success');
      }, 1000);
    }

    // 新建表格
    function newForm() {
      if (confirm('确定要新建表格吗？当前内容将被清空。')) {
        // 清空基本信息
        document.querySelectorAll('.info-input').forEach(input => {
          if (input.type !== 'date') {
            input.value = '';
          }
        });
        
        // 清空表格内容，保留一行
        const tableBody = document.getElementById('tableBody');
        tableBody.innerHTML = `
          <tr>
            <td class="row-number">1</td>
            <td><input type="text" class="table-input" placeholder="请输入产品名称"></td>
            <td><input type="text" class="table-input" placeholder="规格型号"></td>
            <td><input type="text" class="table-input" placeholder="单位"></td>
            <td><input type="number" class="table-input quantity" placeholder="数量" onchange="calculateAmount(this)"></td>
            <td><input type="number" class="table-input price" placeholder="单价" step="0.01" onchange="calculateAmount(this)"></td>
            <td><input type="text" class="table-input calculated amount" placeholder="金额" readonly></td>
            <td><input type="text" class="table-input" placeholder="备注信息"></td>
            <td><button class="delete-btn" onclick="deleteRow(this)">删除</button></td>
          </tr>
        `;
        
        rowCounter = 1;
        updateSummary();
        showNotification('已创建新表格', 'success');
      }
    }

    // 加载模板
    function loadTemplate() {
      showNotification('模板加载功能开发中...', 'warning');
    }

    // 导入数据
    function importData() {
      showNotification('数据导入功能开发中...', 'warning');
    }

    // 导出Excel
    function exportData() {
      showNotification('正在导出Excel文件...', 'success');
      setTimeout(() => {
        showNotification('Excel文件导出成功', 'success');
      }, 1500);
    }

    // 自动保存
    function startAutoSave() {
      autoSaveInterval = setInterval(() => {
        // 模拟自动保存
        console.log('自动保存执行中...');
      }, 30000); // 每30秒自动保存
    }

    // 显示通知
    function showNotification(message, type = 'success') {
      // 移除已存在的通知
      const existingNotification = document.querySelector('.notification');
      if (existingNotification) {
        existingNotification.remove();
      }

      // 创建新通知
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;
      document.body.appendChild(notification);

      // 显示通知
      setTimeout(() => {
        notification.classList.add('show');
      }, 100);

      // 3秒后隐藏通知
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
          notification.remove();
        }, 300);
      }, 3000);
    }

    // 键盘快捷键
    document.addEventListener('keydown', function(e) {
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveForm();
      }
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        newForm();
      }
    });

    // 表格输入验证
    document.addEventListener('input', function(e) {
      if (e.target.classList.contains('table-input')) {
        // 移除错误样式
        e.target.classList.remove('error');
        
        // 数字验证
        if (e.target.type === 'number' && e.target.value < 0) {
          e.target.classList.add('error');
          showNotification('数值不能为负数', 'error');
        }
      }
    });
  </script>
</body>
</html> 