<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档管理 - 现代极简风格 (版本A)</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #fafafa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 24px;
    }

    /* 页面头部 */
    .page-header {
      background: white;
      border-radius: 12px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      border: 1px solid #f0f0f0;
    }

    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #1d1d1f;
      margin-bottom: 8px;
    }

    .page-subtitle {
      font-size: 16px;
      color: #86868b;
      margin-bottom: 24px;
    }

    /* 操作栏 */
    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
    }

    .search-section {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
      max-width: 600px;
    }

    .search-input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e5e5e7;
      border-radius: 24px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: #f9f9f9;
    }

    .search-input:focus {
      outline: none;
      border-color: #007aff;
      background: white;
      box-shadow: 0 0 0 4px rgba(0,122,255,0.1);
    }

    .filter-btn {
      padding: 12px 20px;
      border: 2px solid #e5e5e7;
      border-radius: 20px;
      background: white;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .filter-btn:hover {
      border-color: #007aff;
      color: #007aff;
    }

    .primary-actions {
      display: flex;
      gap: 12px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 24px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0,122,255,0.3);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,122,255,0.4);
    }

    .btn-secondary {
      background: white;
      color: #007aff;
      border: 2px solid #007aff;
      padding: 10px 20px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background: #007aff;
      color: white;
    }

    /* 文档列表 */
    .document-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 32px;
    }

    .document-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      border: 1px solid #f0f0f0;
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .document-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #007aff, #34c759, #ff9500);
    }

    .document-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 24px rgba(0,0,0,0.1);
      border-color: #007aff;
    }

    .doc-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-bottom: 16px;
    }

    .doc-icon.word { background: linear-gradient(135deg, #2b5797 0%, #1e3a6f 100%); }
    .doc-icon.excel { background: linear-gradient(135deg, #217346 0%, #0f5132 100%); }
    .doc-icon.ppt { background: linear-gradient(135deg, #d24726 0%, #a73513 100%); }
    .doc-icon.pdf { background: linear-gradient(135deg, #dc3545 0%, #a71e2a 100%); }

    .doc-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d1d1f;
      margin-bottom: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .doc-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .doc-size {
      font-size: 14px;
      color: #86868b;
    }

    .doc-date {
      font-size: 14px;
      color: #86868b;
    }

    .doc-status {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .status-published { background: #d1f2eb; color: #00875a; }
    .status-draft { background: #fff3cd; color: #856404; }
    .status-archived { background: #f8d7da; color: #721c24; }

    .doc-actions {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      padding: 8px 12px;
      border: 1px solid #e5e5e7;
      border-radius: 8px;
      background: #f9f9f9;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: #007aff;
      color: white;
      border-color: #007aff;
    }

    /* 统计卡片 */
    .stats-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 32px;
    }

    .stat-card {
      background: white;
      padding: 24px;
      border-radius: 16px;
      border: 1px solid #f0f0f0;
      text-align: center;
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      background: linear-gradient(135deg, #007aff 0%, #34c759 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #86868b;
      font-weight: 500;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .container {
        padding: 16px;
      }
      
      .action-bar {
        flex-direction: column;
        align-items: stretch;
      }
      
      .search-section {
        max-width: none;
      }
      
      .document-grid {
        grid-template-columns: 1fr;
      }
      
      .page-header {
        padding: 24px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">📄 文档管理</h1>
      <p class="page-subtitle">管理您的文档，支持在线编辑和协作</p>
      
      <!-- 统计数据 -->
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-number">156</div>
          <div class="stat-label">总文档数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">24</div>
          <div class="stat-label">今日新增</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">8</div>
          <div class="stat-label">正在编辑</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">98%</div>
          <div class="stat-label">同步成功率</div>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="search-section">
          <input type="text" class="search-input" placeholder="🔍 搜索文档名称、类型或内容...">
          <button class="filter-btn">📊 筛选</button>
          <button class="filter-btn">📁 分类</button>
        </div>
        <div class="primary-actions">
          <button class="btn-secondary">📤 批量导出</button>
          <button class="btn-primary">➕ 创建文档</button>
        </div>
      </div>
    </div>

    <!-- 文档网格 -->
    <div class="document-grid">
      <!-- Word文档 -->
      <div class="document-card">
        <div class="doc-icon word">📄</div>
        <div class="doc-title">项目需求文档.docx</div>
        <div class="doc-meta">
          <span class="doc-size">2.4 MB</span>
          <span class="doc-date">2小时前</span>
        </div>
        <div class="doc-status status-published">已发布</div>
        <div class="doc-actions">
          <button class="action-btn">👁️ 预览</button>
          <button class="action-btn">✏️ 编辑</button>
          <button class="action-btn">📤 分享</button>
          <button class="action-btn">⚙️ 更多</button>
        </div>
      </div>

      <!-- Excel文档 -->
      <div class="document-card">
        <div class="doc-icon excel">📊</div>
        <div class="doc-title">财务报表Q4.xlsx</div>
        <div class="doc-meta">
          <span class="doc-size">1.8 MB</span>
          <span class="doc-date">1天前</span>
        </div>
        <div class="doc-status status-draft">草稿</div>
        <div class="doc-actions">
          <button class="action-btn">👁️ 预览</button>
          <button class="action-btn">✏️ 编辑</button>
          <button class="action-btn">📤 分享</button>
          <button class="action-btn">⚙️ 更多</button>
        </div>
      </div>

      <!-- PowerPoint文档 -->
      <div class="document-card">
        <div class="doc-icon ppt">📽️</div>
        <div class="doc-title">产品发布会.pptx</div>
        <div class="doc-meta">
          <span class="doc-size">15.2 MB</span>
          <span class="doc-date">3天前</span>
        </div>
        <div class="doc-status status-published">已发布</div>
        <div class="doc-actions">
          <button class="action-btn">👁️ 预览</button>
          <button class="action-btn">✏️ 编辑</button>
          <button class="action-btn">📤 分享</button>
          <button class="action-btn">⚙️ 更多</button>
        </div>
      </div>

      <!-- PDF文档 -->
      <div class="document-card">
        <div class="doc-icon pdf">📋</div>
        <div class="doc-title">用户手册v2.0.pdf</div>
        <div class="doc-meta">
          <span class="doc-size">8.9 MB</span>
          <span class="doc-date">1周前</span>
        </div>
        <div class="doc-status status-archived">已归档</div>
        <div class="doc-actions">
          <button class="action-btn">👁️ 预览</button>
          <button class="action-btn">📥 下载</button>
          <button class="action-btn">📤 分享</button>
          <button class="action-btn">⚙️ 更多</button>
        </div>
      </div>

      <!-- 更多文档... -->
      <div class="document-card">
        <div class="doc-icon word">📄</div>
        <div class="doc-title">合同模板_标准版.docx</div>
        <div class="doc-meta">
          <span class="doc-size">956 KB</span>
          <span class="doc-date">2周前</span>
        </div>
        <div class="doc-status status-published">已发布</div>
        <div class="doc-actions">
          <button class="action-btn">👁️ 预览</button>
          <button class="action-btn">✏️ 编辑</button>
          <button class="action-btn">📤 分享</button>
          <button class="action-btn">⚙️ 更多</button>
        </div>
      </div>

      <div class="document-card">
        <div class="doc-icon excel">📊</div>
        <div class="doc-title">员工绩效统计.xlsx</div>
        <div class="doc-meta">
          <span class="doc-size">3.2 MB</span>
          <span class="doc-date">3周前</span>
        </div>
        <div class="doc-status status-draft">草稿</div>
        <div class="doc-actions">
          <button class="action-btn">👁️ 预览</button>
          <button class="action-btn">✏️ 编辑</button>
          <button class="action-btn">📤 分享</button>
          <button class="action-btn">⚙️ 更多</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 简单的交互效果
    document.querySelectorAll('.document-card').forEach(card => {
      card.addEventListener('click', (e) => {
        if (!e.target.closest('.action-btn')) {
          console.log('打开文档:', card.querySelector('.doc-title').textContent);
        }
      });
    });

    document.querySelectorAll('.action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('执行操作:', btn.textContent);
      });
    });
  </script>
</body>
</html> 