{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "MySQL": {"command": "npx", "args": ["-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "*************", "MYSQL_PORT": "3306", "MYSQL_USER": "onlyfile_user", "MYSQL_PASS": "0nlyF!le$ecure#123", "MYSQL_DB": "onlyfile", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "false", "MYSQL_ENABLE_LOGGING": "true", "MYSQL_POOL_SIZE": "10", "MYSQL_QUERY_TIMEOUT": "30000", "MYSQL_CACHE_TTL": "60000", "MYSQL_RATE_LIMIT": "100", "MYSQL_SSL": "false", "PATH": "D:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Windows\\System32", "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "shrimp-task-manager": {"command": "node", "args": ["/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "/mcp-shrimp-task-manager/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}}}