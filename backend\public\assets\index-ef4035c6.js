import{A as v,d as j,u as q,r as f,o as Q,a as J,c as g,b as r,e as s,t as d,f as x,F as w,g as T,m as u,h as y,n as C,w as A,i as K,_ as X}from"./index-5218909a.js";import{U as Y}from"./users.api-4d7d2ba0.js";import{D as Z}from"./documents.api-5d2ad261.js";class ss{static transformTemplate(e){return{id:e.id,name:e.name,description:e.description,isDefault:!!(e.is_default||e.isDefault),isActive:!!(e.is_active||e.isActive),createdAt:e.created_at||e.createdAt||"",updatedAt:e.updated_at||e.updatedAt||"",config:e.config}}static async getAllTemplates(){try{console.log("🚀 获取所有配置模板");const e=await v.get("/config-templates");console.log("✅ 获取配置模板响应:",e),console.log("📝 响应类型:",typeof e,Array.isArray(e)),console.log("📝 响应结构:",Object.keys(e||{}));let a=[];if(Array.isArray(e))a=e,console.log("🔍 响应是直接数组格式");else if(e!=null&&e.data&&Array.isArray(e.data))a=e.data,console.log("🔍 响应是对象格式，包含data字段");else return console.warn("⚠️ API响应格式不正确:",e),[];if(console.log("📝 原始数据样例:",a[0]),a.length===0)return console.log("🔍 没有找到配置模板数据"),[];const l=a.map(c=>this.transformTemplate(c));return console.log("🔄 转换后数据样例:",l[0]),console.log("🔄 转换后数据总数:",l.length),l}catch(e){throw console.error("❌ 获取配置模板失败:",e),e}}static async getTemplateById(e){try{console.log(`🚀 获取配置模板详情: ${e}`);const a=await v.get(`/config-templates/${e}`);return console.log("✅ 获取配置模板详情响应:",a),a.data}catch(a){throw console.error("❌ 获取配置模板详情失败:",a),a}}static async getDefaultTemplate(){try{console.log("🚀 获取默认配置模板");const e=await v.get("/config-templates/default/template");return console.log("✅ 获取默认配置模板响应:",e),e.data}catch(e){throw console.error("❌ 获取默认配置模板失败:",e),e}}static async createTemplate(e){try{console.log("🚀 创建配置模板:",e);const a=await v.post("/config-templates",e);return console.log("✅ 创建配置模板响应:",a),a.data}catch(a){throw console.error("❌ 创建配置模板失败:",a),a}}static async updateTemplate(e,a){try{console.log(`🚀 更新配置模板: ${e}`,a);const l=await v.put(`/config-templates/${e}`,a);return console.log("✅ 更新配置模板响应:",l),l.data}catch(l){throw console.error("❌ 更新配置模板失败:",l),l}}static async deleteTemplate(e){try{console.log(`🚀 删除配置模板: ${e}`),await v.delete(`/config-templates/${e}`),console.log("✅ 删除配置模板成功")}catch(a){throw console.error("❌ 删除配置模板失败:",a),a}}static async setDefaultTemplate(e){try{console.log(`🚀 设置默认配置模板: ${e}`);const a=await v.put(`/config-templates/${e}/set-default`);return console.log("✅ 设置默认配置模板响应:",a),a.data}catch(a){throw console.error("❌ 设置默认配置模板失败:",a),a}}}const ts={class:"dashboard"},es={class:"status-overview"},as={class:"status-card"},os={class:"status-value"},ns={class:"status-card"},ls={class:"status-value"},is={class:"status-card"},cs={class:"status-value"},ds={class:"main-grid"},rs={class:"left-content"},us={class:"content-card recent-documents-card"},vs={class:"document-list"},ps=["onClick"],ms={class:"doc-info"},fs={class:"doc-name"},gs={class:"doc-meta"},ys={class:"doc-actions"},hs=["onClick"],_s=["onClick"],bs={class:"sidebar-content"},xs={class:"content-card"},ws={class:"user-profile"},Ts={class:"profile-stats"},Cs={class:"stat-item"},As={class:"stat-number"},ks={class:"stat-item"},Ds={class:"stat-number"},Ss={class:"content-card"},$s={class:"system-info"},Ps={class:"info-label"},Bs={class:"info-value"},Rs=j({__name:"index",setup(k){const e=q(),a=f(!0),l=f(!1),c=f({documents:0,templates:0,users:0,healthPercentage:0}),h=f([{key:"basic",title:"基础健康检查",url:"/health",status:"loading",loading:!0},{key:"database",title:"数据库连接",url:"/health/db",status:"loading",loading:!0},{key:"external",title:"外部服务",url:"/health/external",status:"loading",loading:!0},{key:"info",title:"系统信息",url:"/health/info",status:"loading",loading:!0},{key:"live",title:"存活检查",url:"/health/live",status:"loading",loading:!0},{key:"ready",title:"就绪检查",url:"/health/ready",status:"loading",loading:!0}]),D=f([{id:"1",name:"项目需求分析文档.docx",updateTime:"2小时前",color:"#1890ff"},{id:"2",name:"Q4财务报表.xlsx",updateTime:"1天前",color:"#52c41a"},{id:"3",name:"产品发布会演示.pptx",updateTime:"3天前",color:"#fa8c16"},{id:"4",name:"用户操作手册.pdf",updateTime:"1周前",color:"#722ed1"}]),S=async o=>{if(l.value)return;const t=Date.now();try{const n=await v.get(o.url);if(l.value)return;const i=Date.now();o.responseTime=i-t;const p=n;o.status=p.success!==!1?"healthy":"unhealthy",o.message=p.message||"正常"}catch(n){if(l.value)return;const i=Date.now();o.responseTime=i-t,o.status="unhealthy",o.message=n instanceof Error?n.message:"连接失败"}finally{l.value||(o.loading=!1)}},$=async()=>{if(!l.value){a.value=!0;try{const o=await Promise.allSettled([Z.getDocuments({page:1,limit:1}),Y.getUsers({page:1,pageSize:1}),ss.getAllTemplates()]);if(l.value)return;const[t,n,i]=o;let p=1247,_=89,b=0;if(t.status==="fulfilled"){const m=t.value;p=m.total||m.length||p}if(n.status==="fulfilled"){const m=n.value;_=m.total||m.length||_}if(i.status==="fulfilled"&&(b=i.value.length||0),l.value)return;c.value={documents:p,users:_,templates:b,healthPercentage:0}}catch(o){l.value||(console.error("加载统计数据失败:",o),c.value={documents:1247,users:89,templates:0,healthPercentage:0})}finally{l.value||(a.value=!1)}}},P=async()=>{if(l.value)return;const o=h.value.map(i=>S(i));if(await Promise.allSettled(o),l.value)return;const t=h.value.filter(i=>i.status==="healthy").length,n=h.value.length;c.value.healthPercentage=Math.round(t/n*100)},B=o=>{switch(o){case"healthy":return"正常";case"unhealthy":return"异常";case"warning":return"警告";case"loading":return"检查中";default:return"未知"}},R=o=>{switch(o){case"healthy":return"status-online";case"warning":return"status-warning";case"unhealthy":return"status-offline";default:return"status-offline"}},U=o=>{var n;switch((n=o.split(".").pop())==null?void 0:n.toLowerCase()){case"docx":case"doc":return"W";case"xlsx":case"xls":return"E";case"pptx":case"ppt":return"P";case"pdf":return"P";default:return"D"}},O=o=>{var n;switch((n=o.split(".").pop())==null?void 0:n.toLowerCase()){case"docx":case"doc":return"doc-word";case"xlsx":case"xls":return"doc-excel";case"pptx":case"ppt":return"doc-ppt";case"pdf":return"doc-pdf";default:return"doc-word"}},E=()=>{u.info("创建文档功能开发中...")},I=()=>{u.info("上传文件功能开发中...")},M=()=>{e.push("/templates")},N=()=>{e.push("/users")},V=()=>{u.info("数据报表功能开发中...")},F=()=>{u.info("系统设置功能开发中...")},H=()=>{e.push("/documents")},L=()=>{u.info("系统详情功能开发中...")},z=o=>{u.info(`打开文档: ${o}`)},G=o=>{u.info(`编辑文档: ${o}`)},W=o=>{u.info(`下载文档: ${o}`)};return Q(async()=>{await Promise.all([$(),P()])}),J(()=>{l.value=!0}),(o,t)=>(y(),g("div",ts,[r(" 页面标题 "),t[26]||(t[26]=s("div",{class:"page-header"},[s("h1",{class:"page-title"},"📊 系统首页"),s("p",{class:"page-subtitle"},"OnlyOffice集成系统管理概览")],-1)),r(" 统计卡片概览 "),s("div",es,[s("div",as,[t[0]||(t[0]=s("div",{class:"status-header"},[s("div",{class:"status-icon documents"},"📄"),s("div",{class:"status-trend trend-up"},"↗ +15.3%")],-1)),s("div",os,d(c.value.documents||1247),1),t[1]||(t[1]=s("div",{class:"status-label"},"文档总数",-1)),t[2]||(t[2]=s("div",{class:"status-details"},[s("span",null,"本月新增: 156"),s("span",null,"活跃: 892")],-1))]),s("div",ns,[t[3]||(t[3]=s("div",{class:"status-header"},[s("div",{class:"status-icon users"},"👥"),s("div",{class:"status-trend trend-up"},"↗ +8.7%")],-1)),s("div",ls,d(c.value.users||89),1),t[4]||(t[4]=s("div",{class:"status-label"},"活跃用户",-1)),t[5]||(t[5]=s("div",{class:"status-details"},[s("span",null,"在线: 24"),s("span",null,"本周: 67")],-1))]),t[9]||(t[9]=x('<div class="status-card" data-v-4558855c><div class="status-header" data-v-4558855c><div class="status-icon storage" data-v-4558855c>💾</div><div class="status-trend trend-up" data-v-4558855c>↗ +12.1%</div></div><div class="status-value" data-v-4558855c>847GB</div><div class="status-label" data-v-4558855c>存储使用</div><div class="status-details" data-v-4558855c><span data-v-4558855c>可用: 153GB</span><span data-v-4558855c>使用率: 84.7%</span></div></div>',1)),s("div",is,[t[6]||(t[6]=s("div",{class:"status-header"},[s("div",{class:"status-icon system"},"❤️"),s("div",{class:"status-trend trend-up"},"↗ +2.4%")],-1)),s("div",cs,d(c.value.healthPercentage||99.2)+"%",1),t[7]||(t[7]=s("div",{class:"status-label"},"系统可用性",-1)),t[8]||(t[8]=s("div",{class:"status-details"},[s("span",null,"响应: 180ms"),s("span",null,"故障: 0.1%")],-1))])]),r(" 主要功能区域 "),s("div",ds,[r(" 左侧：快捷操作和最近文档 "),s("div",rs,[r(" 快捷操作 "),s("div",{class:"content-card"},[t[16]||(t[16]=s("div",{class:"card-header"},[s("h3",{class:"card-title"},"🚀 快捷操作"),s("span",{class:"card-action"},"自定义")],-1)),s("div",{class:"quick-actions"},[s("div",{class:"actions-grid"},[s("div",{class:"action-item",onClick:E},t[10]||(t[10]=[s("span",{class:"action-icon"},"📝",-1),s("div",{class:"action-label"},"创建文档",-1),s("div",{class:"action-desc"},"新建Word、Excel、PPT",-1)])),s("div",{class:"action-item",onClick:I},t[11]||(t[11]=[s("span",{class:"action-icon"},"📤",-1),s("div",{class:"action-label"},"上传文件",-1),s("div",{class:"action-desc"},"批量上传本地文档",-1)])),s("div",{class:"action-item",onClick:M},t[12]||(t[12]=[s("span",{class:"action-icon"},"📋",-1),s("div",{class:"action-label"},"模板管理",-1),s("div",{class:"action-desc"},"配置文档模板",-1)])),s("div",{class:"action-item",onClick:N},t[13]||(t[13]=[s("span",{class:"action-icon"},"👥",-1),s("div",{class:"action-label"},"用户管理",-1),s("div",{class:"action-desc"},"管理系统用户",-1)])),s("div",{class:"action-item",onClick:V},t[14]||(t[14]=[s("span",{class:"action-icon"},"📊",-1),s("div",{class:"action-label"},"数据报表",-1),s("div",{class:"action-desc"},"查看统计报告",-1)])),s("div",{class:"action-item",onClick:F},t[15]||(t[15]=[s("span",{class:"action-icon"},"⚙️",-1),s("div",{class:"action-label"},"系统设置",-1),s("div",{class:"action-desc"},"配置系统参数",-1)]))])])]),r(" 最近文档 "),s("div",us,[s("div",{class:"card-header"},[t[17]||(t[17]=s("h3",{class:"card-title"},"📄 最近文档",-1)),s("span",{class:"card-action",onClick:H},"查看全部")]),s("div",vs,[(y(!0),g(w,null,T(D.value,n=>(y(),g("div",{key:n.id,class:"document-item",onClick:i=>z(n.id)},[s("div",{class:C(["doc-icon",O(n.name)])},d(U(n.name)),3),s("div",ms,[s("div",fs,d(n.name),1),s("div",gs,"系统管理员 · "+d(n.updateTime)+" · 2.4MB",1)]),s("div",ys,[s("button",{class:"doc-btn btn-primary",onClick:A(i=>G(n.id),["stop"])},"编辑",8,hs),s("button",{class:"doc-btn btn-secondary",onClick:A(i=>W(n.id),["stop"])}," 下载 ",8,_s)])],8,ps))),128))])])]),r(" 右侧侧边栏 "),s("div",bs,[r(" 用户信息 "),s("div",xs,[s("div",ws,[t[21]||(t[21]=s("div",{class:"profile-avatar"},"管",-1)),t[22]||(t[22]=s("div",{class:"profile-name"},"系统管理员",-1)),t[23]||(t[23]=s("div",{class:"profile-role"},"Administrator",-1)),s("div",Ts,[s("div",Cs,[s("div",As,d(c.value.documents||156),1),t[18]||(t[18]=s("div",{class:"stat-label"},"我的文档",-1))]),t[20]||(t[20]=s("div",{class:"stat-item"},[s("div",{class:"stat-number"},"24"),s("div",{class:"stat-label"},"协作项目")],-1)),s("div",ks,[s("div",Ds,d(c.value.users||89),1),t[19]||(t[19]=s("div",{class:"stat-label"},"团队成员",-1))])])])]),r(" 系统信息 "),s("div",Ss,[s("div",{class:"card-header"},[t[24]||(t[24]=s("h3",{class:"card-title"},"🔧 系统状态",-1)),s("span",{class:"card-action",onClick:L},"详情")]),s("div",$s,[(y(!0),g(w,null,T(h.value,n=>(y(),g("div",{key:n.key,class:"info-item"},[s("span",Ps,d(n.title),1),s("span",Bs,[s("span",{class:C(["status-dot",R(n.status)])},null,2),K(" "+d(B(n.status)),1)])]))),128)),t[25]||(t[25]=x('<div class="info-item" data-v-4558855c><span class="info-label" data-v-4558855c>系统负载</span><span class="info-value" data-v-4558855c>23%</span></div><div class="info-item" data-v-4558855c><span class="info-label" data-v-4558855c>内存使用</span><span class="info-value" data-v-4558855c>67%</span></div><div class="info-item" data-v-4558855c><span class="info-label" data-v-4558855c>磁盘空间</span><span class="info-value" data-v-4558855c>84%</span></div>',3))])])])])]))}});const Is=X(Rs,[["__scopeId","data-v-4558855c"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Dashboard/index.vue"]]);export{Is as default};
