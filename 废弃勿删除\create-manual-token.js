const jwt = require('jsonwebtoken');

// 使用和NestJS相同的JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_ISSUER = process.env.JWT_ISSUER || 'onlyoffice-api';
const JWT_AUDIENCE = process.env.JWT_AUDIENCE || 'onlyoffice-users';

// 创建payload
const payload = {
  sub: 'user-admin',      // 用户ID
  username: 'admin',      // 用户名
  role: 'super_admin',    // 角色
  type: 'access',         // token类型
  iat: Math.floor(Date.now() / 1000),           // 签发时间
  exp: Math.floor(Date.now() / 1000) + 3600,    // 1小时后过期
  iss: JWT_ISSUER,        // 签发者
  aud: JWT_AUDIENCE       // 受众
};

// 生成token
const token = jwt.sign(payload, JWT_SECRET);

console.log('🎫 手动生成的JWT令牌:');
console.log(token);
console.log('\n📋 令牌信息:');
console.log('- 用户ID:', payload.sub);
console.log('- 用户名:', payload.username);
console.log('- 角色:', payload.role);
console.log('- 有效期:', new Date(payload.exp * 1000).toLocaleString());

console.log('\n🧪 测试令牌...');

// 测试令牌
const axios = require('axios');

async function testToken() {
  try {
    const response = await axios.get('http://localhost:3000/api/users', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      timeout: 5000
    });
    
    console.log('✅ 令牌测试成功！');
    console.log('API响应:', response.data.message);
    
    console.log('\n📋 使用方法:');
    console.log('1. 打开浏览器访问: http://localhost:3000/api-docs');
    console.log('2. 点击右上角的"Authorize"按钮');
    console.log('3. 在弹出框中输入以下令牌（无需Bearer前缀）:');
    console.log(token);
    console.log('4. 点击"Authorize"按钮完成认证');
    
  } catch (error) {
    console.error('❌ 令牌测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('网络错误:', error.message);
    }
  }
}

testToken(); 