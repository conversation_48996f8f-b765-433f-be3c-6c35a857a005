<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 工作台风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
      background: #fafafa;
      color: #2c3e50;
      line-height: 1.6;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
    }

    /* 顶部工作台标题 */
    .workspace-header {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 24px;
      color: white;
      position: relative;
      overflow: hidden;
    }

    .workspace-header::before {
      content: '';
      position: absolute;
      top: -30px;
      right: -30px;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      border-radius: 50%;
    }

    .header-main {
      position: relative;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .workspace-info h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .workspace-info p {
      font-size: 16px;
      opacity: 0.9;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .user-avatar {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 600;
    }

    .user-details h3 {
      font-size: 16px;
      margin-bottom: 4px;
    }

    .user-details p {
      font-size: 14px;
      opacity: 0.8;
    }

    /* 工作流程状态 */
    .workflow-status {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 24px;
    }

    .status-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-left: 4px solid #3498db;
      position: relative;
      transition: all 0.3s ease;
    }

    .status-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .status-card:nth-child(1) { border-left-color: #3498db; }
    .status-card:nth-child(2) { border-left-color: #2ecc71; }
    .status-card:nth-child(3) { border-left-color: #f39c12; }
    .status-card:nth-child(4) { border-left-color: #e74c3c; }

    .status-icon {
      position: absolute;
      top: 20px;
      right: 20px;
      font-size: 24px;
      opacity: 0.7;
    }

    .status-number {
      font-size: 32px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .status-label {
      font-size: 14px;
      color: #7f8c8d;
      margin-bottom: 12px;
    }

    .status-progress {
      width: 100%;
      height: 6px;
      background: #ecf0f1;
      border-radius: 3px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .progress-blue { background: linear-gradient(90deg, #3498db, #5dade2); }
    .progress-green { background: linear-gradient(90deg, #2ecc71, #58d68d); }
    .progress-orange { background: linear-gradient(90deg, #f39c12, #f8c471); }
    .progress-red { background: linear-gradient(90deg, #e74c3c, #ec7063); }

    /* 主要内容区域 */
    .main-content {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
    }

    /* 左侧工作区 */
    .work-area {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .section-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .section-header {
      background: #f8f9fa;
      padding: 16px 20px;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: between;
      align-items: center;
    }

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-actions {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      padding: 6px 12px;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      background: white;
      font-size: 12px;
      color: #6c757d;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: #f8f9fa;
      border-color: #adb5bd;
    }

    .action-btn.primary {
      background: #3498db;
      color: white;
      border-color: #3498db;
    }

    .action-btn.primary:hover {
      background: #2980b9;
      border-color: #2980b9;
    }

    /* 任务看板 */
    .kanban-board {
      padding: 20px;
    }

    .kanban-columns {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
    }

    .kanban-column {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      min-height: 300px;
    }

    .column-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .column-title {
      font-size: 14px;
      font-weight: 600;
      color: #495057;
    }

    .column-count {
      background: #6c757d;
      color: white;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 12px;
    }

    .task-card {
      background: white;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .task-card:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    }

    .task-title {
      font-size: 14px;
      font-weight: 500;
      color: #212529;
      margin-bottom: 8px;
    }

    .task-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #6c757d;
    }

    .task-priority {
      padding: 2px 6px;
      border-radius: 10px;
      font-weight: 500;
      font-size: 10px;
    }

    .priority-high { background: #fee; color: #e74c3c; }
    .priority-medium { background: #ffa; color: #f39c12; }
    .priority-low { background: #efe; color: #27ae60; }

    /* 项目进度 */
    .project-list {
      padding: 20px;
    }

    .project-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #f1f3f4;
    }

    .project-item:last-child {
      border-bottom: none;
    }

    .project-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: white;
    }

    .project-info {
      flex: 1;
    }

    .project-name {
      font-size: 14px;
      font-weight: 500;
      color: #212529;
      margin-bottom: 4px;
    }

    .project-desc {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 8px;
    }

    .project-progress {
      width: 100%;
      height: 4px;
      background: #e9ecef;
      border-radius: 2px;
      overflow: hidden;
    }

    .project-progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    .project-stats {
      text-align: right;
    }

    .project-deadline {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 4px;
    }

    .project-completion {
      font-size: 14px;
      font-weight: 600;
      color: #2c3e50;
    }

    /* 右侧面板 */
    .sidebar-panel {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .panel-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .panel-header {
      background: linear-gradient(135deg, #495057, #343a40);
      color: white;
      padding: 16px 20px;
      font-size: 14px;
      font-weight: 600;
    }

    .panel-content {
      padding: 20px;
    }

    /* 今日任务 */
    .task-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .task-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .task-item:hover {
      background: #e9ecef;
    }

    .task-checkbox {
      width: 18px;
      height: 18px;
      border: 2px solid #dee2e6;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .task-checkbox.checked {
      background: #28a745;
      border-color: #28a745;
      position: relative;
    }

    .task-checkbox.checked::after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: 600;
    }

    .task-content {
      flex: 1;
    }

    .task-name {
      font-size: 14px;
      color: #212529;
      margin-bottom: 2px;
    }

    .task-time {
      font-size: 12px;
      color: #6c757d;
    }

    /* 团队动态 */
    .activity-feed {
      max-height: 300px;
      overflow-y: auto;
    }

    .activity-item {
      display: flex;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f1f3f4;
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-avatar {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      color: white;
      flex-shrink: 0;
    }

    .activity-content {
      flex: 1;
    }

    .activity-text {
      font-size: 13px;
      color: #212529;
      margin-bottom: 4px;
    }

    .activity-time {
      font-size: 11px;
      color: #6c757d;
    }

    /* 日历组件 */
    .calendar-mini {
      text-align: center;
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .calendar-month {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }

    .calendar-nav {
      background: none;
      border: none;
      font-size: 16px;
      color: #6c757d;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
    }

    .calendar-nav:hover {
      background: #f8f9fa;
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 4px;
    }

    .calendar-day {
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .calendar-day.header {
      font-weight: 600;
      color: #6c757d;
      cursor: default;
    }

    .calendar-day.today {
      background: #3498db;
      color: white;
    }

    .calendar-day.event {
      background: #e3f2fd;
      color: #1976d2;
    }

    .calendar-day:hover:not(.header) {
      background: #f8f9fa;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .workflow-status {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .main-content {
        grid-template-columns: 1fr;
      }
      
      .kanban-columns {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 16px;
      }
      
      .workflow-status {
        grid-template-columns: 1fr;
      }
      
      .header-main {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 工作台标题 -->
    <div class="workspace-header">
      <div class="header-main">
        <div class="workspace-info">
          <h1>🚀 我的工作台</h1>
          <p>高效协作，智能管理，让工作更简单</p>
        </div>
        <div class="user-profile">
          <div class="user-avatar">管</div>
          <div class="user-details">
            <h3>系统管理员</h3>
            <p>今天是个好日子，开始工作吧！</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作流程状态 -->
    <div class="workflow-status">
      <div class="status-card">
        <div class="status-icon">📋</div>
        <div class="status-number">24</div>
        <div class="status-label">待处理任务</div>
        <div class="status-progress">
          <div class="progress-fill progress-blue" style="width: 75%;"></div>
        </div>
      </div>
      <div class="status-card">
        <div class="status-icon">✅</div>
        <div class="status-number">18</div>
        <div class="status-label">已完成任务</div>
        <div class="status-progress">
          <div class="progress-fill progress-green" style="width: 90%;"></div>
        </div>
      </div>
      <div class="status-card">
        <div class="status-icon">⏰</div>
        <div class="status-number">6</div>
        <div class="status-label">即将到期</div>
        <div class="status-progress">
          <div class="progress-fill progress-orange" style="width: 45%;"></div>
        </div>
      </div>
      <div class="status-card">
        <div class="status-icon">🔥</div>
        <div class="status-number">3</div>
        <div class="status-label">紧急任务</div>
        <div class="status-progress">
          <div class="progress-fill progress-red" style="width: 60%;"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧工作区 -->
      <div class="work-area">
        <!-- 任务看板 -->
        <div class="section-card">
          <div class="section-header">
            <div class="section-title">📊 任务看板</div>
            <div class="section-actions">
              <button class="action-btn">筛选</button>
              <button class="action-btn primary">新建任务</button>
            </div>
          </div>
          <div class="kanban-board">
            <div class="kanban-columns">
              <!-- 待处理 -->
              <div class="kanban-column">
                <div class="column-header">
                  <div class="column-title">📋 待处理</div>
                  <div class="column-count">8</div>
                </div>
                <div class="task-card">
                  <div class="task-title">审核项目需求文档</div>
                  <div class="task-meta">
                    <span>张三</span>
                    <span class="task-priority priority-high">高</span>
                  </div>
                </div>
                <div class="task-card">
                  <div class="task-title">更新系统配置模板</div>
                  <div class="task-meta">
                    <span>李四</span>
                    <span class="task-priority priority-medium">中</span>
                  </div>
                </div>
                <div class="task-card">
                  <div class="task-title">准备月度工作报告</div>
                  <div class="task-meta">
                    <span>王五</span>
                    <span class="task-priority priority-low">低</span>
                  </div>
                </div>
              </div>

              <!-- 进行中 -->
              <div class="kanban-column">
                <div class="column-header">
                  <div class="column-title">🔄 进行中</div>
                  <div class="column-count">5</div>
                </div>
                <div class="task-card">
                  <div class="task-title">文档管理系统优化</div>
                  <div class="task-meta">
                    <span>赵六</span>
                    <span class="task-priority priority-high">高</span>
                  </div>
                </div>
                <div class="task-card">
                  <div class="task-title">用户权限配置更新</div>
                  <div class="task-meta">
                    <span>钱七</span>
                    <span class="task-priority priority-medium">中</span>
                  </div>
                </div>
              </div>

              <!-- 已完成 -->
              <div class="kanban-column">
                <div class="column-header">
                  <div class="column-title">✅ 已完成</div>
                  <div class="column-count">12</div>
                </div>
                <div class="task-card">
                  <div class="task-title">数据库备份脚本更新</div>
                  <div class="task-meta">
                    <span>孙八</span>
                    <span class="task-priority priority-low">低</span>
                  </div>
                </div>
                <div class="task-card">
                  <div class="task-title">新员工权限配置</div>
                  <div class="task-meta">
                    <span>周九</span>
                    <span class="task-priority priority-medium">中</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目进度 -->
        <div class="section-card">
          <div class="section-header">
            <div class="section-title">📈 项目进度</div>
            <div class="section-actions">
              <button class="action-btn">查看全部</button>
              <button class="action-btn primary">新建项目</button>
            </div>
          </div>
          <div class="project-list">
            <div class="project-item">
              <div class="project-icon" style="background: linear-gradient(135deg, #3498db, #2980b9);">📝</div>
              <div class="project-info">
                <div class="project-name">文档管理系统升级</div>
                <div class="project-desc">升级到最新版本，优化用户体验和性能</div>
                <div class="project-progress">
                  <div class="project-progress-fill" style="width: 85%;"></div>
                </div>
              </div>
              <div class="project-stats">
                <div class="project-deadline">2024/12/25</div>
                <div class="project-completion">85%</div>
              </div>
            </div>
            <div class="project-item">
              <div class="project-icon" style="background: linear-gradient(135deg, #2ecc71, #27ae60);">👥</div>
              <div class="project-info">
                <div class="project-name">团队协作流程优化</div>
                <div class="project-desc">建立高效的团队协作机制和沟通流程</div>
                <div class="project-progress">
                  <div class="project-progress-fill" style="width: 60%;"></div>
                </div>
              </div>
              <div class="project-stats">
                <div class="project-deadline">2024/12/30</div>
                <div class="project-completion">60%</div>
              </div>
            </div>
            <div class="project-item">
              <div class="project-icon" style="background: linear-gradient(135deg, #f39c12, #e67e22);">🔒</div>
              <div class="project-info">
                <div class="project-name">安全策略更新</div>
                <div class="project-desc">完善系统安全机制，提升数据保护水平</div>
                <div class="project-progress">
                  <div class="project-progress-fill" style="width: 40%;"></div>
                </div>
              </div>
              <div class="project-stats">
                <div class="project-deadline">2025/01/10</div>
                <div class="project-completion">40%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="sidebar-panel">
        <!-- 今日任务 -->
        <div class="panel-card">
          <div class="panel-header">📅 今日任务</div>
          <div class="panel-content">
            <div class="task-list">
              <div class="task-item">
                <div class="task-checkbox"></div>
                <div class="task-content">
                  <div class="task-name">检查系统备份状态</div>
                  <div class="task-time">09:00 - 09:30</div>
                </div>
              </div>
              <div class="task-item">
                <div class="task-checkbox checked"></div>
                <div class="task-content">
                  <div class="task-name">团队晨会</div>
                  <div class="task-time">10:00 - 10:30</div>
                </div>
              </div>
              <div class="task-item">
                <div class="task-checkbox"></div>
                <div class="task-content">
                  <div class="task-name">审核文档模板</div>
                  <div class="task-time">14:00 - 15:00</div>
                </div>
              </div>
              <div class="task-item">
                <div class="task-checkbox"></div>
                <div class="task-content">
                  <div class="task-name">项目进度评估</div>
                  <div class="task-time">16:00 - 17:00</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队动态 -->
        <div class="panel-card">
          <div class="panel-header">🔔 团队动态</div>
          <div class="panel-content">
            <div class="activity-feed">
              <div class="activity-item">
                <div class="activity-avatar">张</div>
                <div class="activity-content">
                  <div class="activity-text">张三完成了"项目需求分析"任务</div>
                  <div class="activity-time">2分钟前</div>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-avatar">李</div>
                <div class="activity-content">
                  <div class="activity-text">李四创建了新文档"系统配置手册"</div>
                  <div class="activity-time">15分钟前</div>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-avatar">王</div>
                <div class="activity-content">
                  <div class="activity-text">王五更新了项目进度</div>
                  <div class="activity-time">1小时前</div>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-avatar">赵</div>
                <div class="activity-content">
                  <div class="activity-text">赵六分享了文档"技术规范v2.0"</div>
                  <div class="activity-time">2小时前</div>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-avatar">钱</div>
                <div class="activity-content">
                  <div class="activity-text">钱七加入了"文档管理优化"项目</div>
                  <div class="activity-time">3小时前</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 迷你日历 -->
        <div class="panel-card">
          <div class="panel-header">📅 工作日历</div>
          <div class="panel-content">
            <div class="calendar-mini">
              <div class="calendar-header">
                <button class="calendar-nav">‹</button>
                <div class="calendar-month">2024年12月</div>
                <button class="calendar-nav">›</button>
              </div>
              <div class="calendar-grid">
                <div class="calendar-day header">日</div>
                <div class="calendar-day header">一</div>
                <div class="calendar-day header">二</div>
                <div class="calendar-day header">三</div>
                <div class="calendar-day header">四</div>
                <div class="calendar-day header">五</div>
                <div class="calendar-day header">六</div>
                <div class="calendar-day">1</div>
                <div class="calendar-day">2</div>
                <div class="calendar-day">3</div>
                <div class="calendar-day">4</div>
                <div class="calendar-day">5</div>
                <div class="calendar-day">6</div>
                <div class="calendar-day">7</div>
                <div class="calendar-day">8</div>
                <div class="calendar-day">9</div>
                <div class="calendar-day">10</div>
                <div class="calendar-day">11</div>
                <div class="calendar-day">12</div>
                <div class="calendar-day">13</div>
                <div class="calendar-day">14</div>
                <div class="calendar-day">15</div>
                <div class="calendar-day">16</div>
                <div class="calendar-day">17</div>
                <div class="calendar-day">18</div>
                <div class="calendar-day today">19</div>
                <div class="calendar-day">20</div>
                <div class="calendar-day">21</div>
                <div class="calendar-day">22</div>
                <div class="calendar-day event">23</div>
                <div class="calendar-day">24</div>
                <div class="calendar-day event">25</div>
                <div class="calendar-day">26</div>
                <div class="calendar-day">27</div>
                <div class="calendar-day">28</div>
                <div class="calendar-day">29</div>
                <div class="calendar-day">30</div>
                <div class="calendar-day">31</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 任务复选框交互
    document.querySelectorAll('.task-checkbox').forEach(checkbox => {
      checkbox.addEventListener('click', () => {
        checkbox.classList.toggle('checked');
      });
    });

    // 看板卡片拖拽模拟
    document.querySelectorAll('.task-card').forEach(card => {
      card.addEventListener('click', () => {
        console.log('编辑任务:', card.querySelector('.task-title').textContent);
      });
    });

    // 项目点击
    document.querySelectorAll('.project-item').forEach(item => {
      item.addEventListener('click', () => {
        console.log('查看项目:', item.querySelector('.project-name').textContent);
      });
    });

    // 操作按钮
    document.querySelectorAll('.action-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        console.log('操作:', btn.textContent);
      });
    });

    // 日历导航
    document.querySelectorAll('.calendar-nav').forEach(nav => {
      nav.addEventListener('click', () => {
        console.log('切换月份:', nav.textContent);
      });
    });

    // 模拟进度更新动画
    function animateProgress() {
      const progressBars = document.querySelectorAll('.progress-fill, .project-progress-fill');
      progressBars.forEach(bar => {
        const currentWidth = parseInt(bar.style.width);
        bar.style.width = '0%';
        setTimeout(() => {
          bar.style.width = currentWidth + '%';
        }, 100);
      });
    }

    // 页面加载时播放动画
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(animateProgress, 500);
    });

    // 模拟实时更新
    setInterval(() => {
      const numbers = document.querySelectorAll('.status-number');
      numbers.forEach(num => {
        if (Math.random() > 0.9) {
          num.style.transform = 'scale(1.1)';
          setTimeout(() => {
            num.style.transform = 'scale(1)';
          }, 200);
        }
      });
    }, 10000);
  </script>
</body>
</html> 