import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { configModuleOptions } from './config/app.config';

// 核心模块导入
import { DatabaseModule } from './modules/database/database.module';
import { HealthModule } from './modules/health/health.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';

// 业务模块导入
import { DocumentsModule } from './modules/documents/documents.module';
import { ConfigModule as OnlyOfficeConfigModule } from './modules/config/config.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { UploadModule } from './modules/upload/upload.module';
import { FilenetModule } from './modules/filenet/filenet.module';
import { EditorModule } from './modules/editor/editor.module';

// 应用控制器
import { AppController } from './app.controller';

/**
 * 应用根模块
 * 
 * 整合所有功能模块，使用统一的环境变量配置系统
 * 
 * @module AppModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (统一配置版)
 */
@Module({
  imports: [
    // 配置模块 - 使用统一配置系统
    ConfigModule.forRoot(configModuleOptions),
    
    // 核心基础模块
    DatabaseModule,
    HealthModule,
    
    // 认证和用户管理模块
    AuthModule,
    UsersModule,
    
    // 业务功能模块
    DocumentsModule,
    OnlyOfficeConfigModule,
    TemplatesModule,
    UploadModule,
    FilenetModule,
    EditorModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {} 