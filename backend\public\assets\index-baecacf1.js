import{j as l,k as R,l as et,p as tt,d as Se,r as b,o as ze,q as d,h as k,c as Q,s as a,b as D,v as j,E as ke,i as p,e as _,S as nt,D as Me,F as De,g as lt,x as V,t as B,m as g,_ as Fe,y as at,u as ot,z as rt,P as it,R as st,B as ut,C as dt,G as ct,H as _e,I as ge,J as he,K as ft}from"./index-5218909a.js";import{D as pt}from"./documents.api-5d2ad261.js";import{o as ye,a as le}from"./editor-utils-5bc6f677.js";import{U as mt}from"./UploadOutlined-25037a42.js";var vt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM514.1 580.1l-61.8-102.4c-2.2-3.6-6.1-5.8-10.3-5.8h-38.4c-2.3 0-4.5.6-6.4 1.9-5.6 3.5-7.3 10.9-3.7 16.6l82.3 130.4-83.4 132.8a12.04 12.04 0 0010.2 18.4h34.5c4.2 0 8-2.2 10.2-5.7L510 664.8l62.3 101.4c2.2 3.6 6.1 5.7 10.2 5.7H620c2.3 0 4.5-.7 6.5-1.9 5.6-3.6 7.2-11 3.6-16.6l-84-130.4 85.3-132.5a12.04 12.04 0 00-10.1-18.5h-35.7c-4.2 0-8.1 2.2-10.3 5.8l-61.2 102.3z"}}]},name:"file-excel",theme:"outlined"};const _t=vt;function Oe(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){gt(o,s,t[s])})}return o}function gt(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var ae=function(e,t){var i=Oe({},e,t.attrs);return l(R,Oe({},i,{icon:_t}),null)};ae.displayName="FileExcelOutlined";ae.inheritAttrs=!1;const ht=ae;var yt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M553.1 509.1l-77.8 99.2-41.1-52.4a8 8 0 00-12.6 0l-99.8 127.2a7.98 7.98 0 006.3 12.9H696c6.7 0 10.4-7.7 6.3-12.9l-136.5-174a8.1 8.1 0 00-12.7 0zM360 442a40 40 0 1080 0 40 40 0 10-80 0zm494.6-153.4L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-image",theme:"outlined"};const Ot=yt;function be(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){bt(o,s,t[s])})}return o}function bt(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var oe=function(e,t){var i=be({},e,t.attrs);return l(R,be({},i,{icon:Ot}),null)};oe.displayName="FileImageOutlined";oe.inheritAttrs=!1;const wt=oe;var xt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M531.3 574.4l.3-1.4c5.8-23.9 13.1-53.7 7.4-80.7-3.8-21.3-19.5-29.6-32.9-30.2-15.8-.7-29.9 8.3-33.4 21.4-6.6 24-.7 56.8 10.1 98.6-13.6 32.4-35.3 79.5-51.2 107.5-29.6 15.3-69.3 38.9-75.2 68.7-1.2 5.5.2 12.5 3.5 18.8 3.7 7 9.6 12.4 16.5 15 3 1.1 6.6 2 10.8 2 17.6 0 46.1-14.2 84.1-79.4 5.8-1.9 11.8-3.9 17.6-5.9 27.2-9.2 55.4-18.8 80.9-23.1 28.2 15.1 60.3 24.8 82.1 24.8 21.6 0 30.1-12.8 33.3-20.5 5.6-13.5 2.9-30.5-6.2-39.6-13.2-13-45.3-16.4-95.3-10.2-24.6-15-40.7-35.4-52.4-65.8zM421.6 726.3c-13.9 20.2-24.4 30.3-30.1 34.7 6.7-12.3 19.8-25.3 30.1-34.7zm87.6-235.5c5.2 8.9 4.5 35.8.5 49.4-4.9-19.9-5.6-48.1-2.7-51.4.8.1 1.5.7 2.2 2zm-1.6 120.5c10.7 18.5 24.2 34.4 39.1 46.2-21.6 4.9-41.3 13-58.9 20.2-4.2 1.7-8.3 3.4-12.3 5 13.3-24.1 24.4-51.4 32.1-71.4zm155.6 65.5c.1.2.2.5-.4.9h-.2l-.2.3c-.8.5-9 5.3-44.3-8.6 40.6-1.9 45 7.3 45.1 7.4zm191.4-388.2L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-pdf",theme:"outlined"};const Pt=xt;function we(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){Ct(o,s,t[s])})}return o}function Ct(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var re=function(e,t){var i=we({},e,t.attrs);return l(R,we({},i,{icon:Pt}),null)};re.displayName="FilePdfOutlined";re.inheritAttrs=!1;const $t=re;var St={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M424 476c-4.4 0-8 3.6-8 8v276c0 4.4 3.6 8 8 8h32.5c4.4 0 8-3.6 8-8v-95.5h63.3c59.4 0 96.2-38.9 96.2-94.1 0-54.5-36.3-94.3-96-94.3H424zm150.6 94.3c0 43.4-26.5 54.3-71.2 54.3h-38.9V516.2h56.2c33.8 0 53.9 19.7 53.9 54.1zm280-281.7L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-ppt",theme:"outlined"};const zt=St;function xe(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){kt(o,s,t[s])})}return o}function kt(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var ie=function(e,t){var i=xe({},e,t.attrs);return l(R,xe({},i,{icon:zt}),null)};ie.displayName="FilePptOutlined";ie.inheritAttrs=!1;const Mt=ie;var Dt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM528.1 472h-32.2c-5.5 0-10.3 3.7-11.6 9.1L434.6 680l-46.1-198.7c-1.3-5.4-6.1-9.3-11.7-9.3h-35.4a12.02 12.02 0 00-11.6 15.1l74.2 276c1.4 5.2 6.2 8.9 11.6 8.9h32c5.4 0 10.2-3.6 11.6-8.9l52.8-197 52.8 197c1.4 5.2 6.2 8.9 11.6 8.9h31.8c5.4 0 10.2-3.6 11.6-8.9l74.4-276a12.04 12.04 0 00-11.6-15.1H647c-5.6 0-10.4 3.9-11.7 9.3l-45.8 199.1-49.8-199.3c-1.3-5.4-6.1-9.1-11.6-9.1z"}}]},name:"file-word",theme:"outlined"};const Ft=Dt;function Pe(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){Lt(o,s,t[s])})}return o}function Lt(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var se=function(e,t){var i=Pe({},e,t.attrs);return l(R,Pe({},i,{icon:Ft}),null)};se.displayName="FileWordOutlined";se.inheritAttrs=!1;const jt=se;var Et={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"};const At=Et;function Ce(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){Tt(o,s,t[s])})}return o}function Tt(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var ue=function(e,t){var i=Ce({},e,t.attrs);return l(R,Ce({},i,{icon:At}),null)};ue.displayName="InboxOutlined";ue.inheritAttrs=!1;const It=ue;var Vt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};const Bt=Vt;function $e(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},i=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.forEach(function(s){Ht(o,s,t[s])})}return o}function Ht(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}var de=function(e,t){var i=$e({},e,t.attrs);return l(R,$e({},i,{icon:Bt}),null)};de.displayName="LinkOutlined";de.inheritAttrs=!1;const Nt=de;var Le={exports:{}};(function(o,e){(function(t,i){o.exports=i()})(et,function(){return function(t,i,s){t=t||{};var w=i.prototype,P={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function C(c,y,M,L){return w.fromToBase(c,y,M,L)}s.en.relativeTime=P,w.fromToBase=function(c,y,M,L,z){for(var U,f,r,$=M.$locale().relativeTime||P,O=t.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],q=O.length,T=0;T<q;T+=1){var F=O[T];F.d&&(U=L?s(c).diff(M,F.d,!0):M.diff(c,F.d,!0));var E=(t.rounding||Math.round)(Math.abs(U));if(r=U>0,E<=F.r||!F.r){E<=1&&T>0&&(F=O[T-1]);var H=$[F.l];z&&(E=z(""+E)),f=typeof H=="string"?H.replace("%d",E):H(E,y,F.l,r);break}}if(y)return f;var W=r?$.future:$.past;return typeof W=="function"?W(f):W.replace("%s",f)},w.to=function(c,y){return C(c,y,this,!0)},w.from=function(c,y){return C(c,y,this)};var S=function(c){return c.$u?s.utc():s()};w.toNow=function(c){return this.to(S(this),c)},w.fromNow=function(c){return this.from(S(this),c)}}})})(Le);var Ut=Le.exports;const Wt=tt(Ut),Rt={class:"document-actions"},qt={class:"config-template-selector"},Gt={class:"template-description"},Qt={class:"template-list"},Jt={class:"template-options"},Xt={class:"template-info"},Yt={class:"template-header"},Zt={class:"template-name"},Kt={class:"template-description"},en={class:"template-meta"},tn={class:"editor-mode-selector",style:{"margin-top":"16px"}},nn=Se({__name:"DocumentActions",props:{documentId:{type:String,required:!0}},setup(o){const e=o,t=b(!1),i=b(!1),s=b(!1),w=b(!1),P=b([]),C=b(""),S=b("embedded"),c=f=>{try{return new Date(f).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return f}},y=async()=>{s.value=!0;try{const r=await(await fetch("/api/config-templates")).json();if(r.success&&r.data){P.value=r.data.filter(O=>O.isActive);const $=P.value.find(O=>O.isDefault);$?C.value=$.id:P.value.length>0&&(C.value=P.value[0].id),console.log("✅ [DocumentActions] 配置模板加载成功:",P.value)}else console.warn("⚠️ [DocumentActions] 配置模板加载失败:",r),g.warning("加载配置模板失败，将使用默认配置")}catch(f){console.error("❌ [DocumentActions] 配置模板加载错误:",f),g.error("加载配置模板失败")}finally{s.value=!1}},M=async()=>{t.value=!0;try{await ye(e.documentId),g.success("正在打开编辑器...")}catch(f){console.error("打开内嵌编辑器失败:",f),g.error("打开编辑器失败")}finally{t.value=!1}},L=async()=>{i.value=!0;try{const f=await le(e.documentId);if(f){g.success("编辑器已在新窗口中打开");const r=setInterval(()=>{f.closed&&(clearInterval(r),g.info("编辑器窗口已关闭"))},1e3)}}catch(f){console.error("打开弹窗编辑器失败:",f),g.error(`打开编辑器失败: ${f instanceof Error?f.message:"未知错误"}`)}finally{i.value=!1}},z=async f=>{const $={small:{width:800,height:600},medium:{width:1200,height:800},large:{width:1600,height:1e3},fullscreen:{width:window.screen.availWidth,height:window.screen.availHeight,left:0,top:0}}[f];if(!$){g.error("无效的窗口配置");return}try{await le(e.documentId,void 0,$)&&g.success(`编辑器已在${f==="small"?"小":f==="medium"?"中":f==="large"?"大":"全屏"}窗口中打开`)}catch(O){console.error("打开自定义弹窗编辑器失败:",O),g.error(`打开编辑器失败: ${O instanceof Error?O.message:"未知错误"}`)}},U=async()=>{if(!C.value){g.warning("请选择一个配置模板");return}const f={template:C.value};try{console.log("🔧 [DocumentActions] 使用配置模板:",C.value),console.log("🔧 [DocumentActions] 编辑器模式:",S.value),S.value==="embedded"?(await ye(e.documentId,f),g.success("正在使用所选配置模板打开编辑器...")):await le(e.documentId,f)&&g.success("编辑器已在新窗口中打开，使用所选配置模板"),w.value=!1}catch(r){console.error("使用配置模板打开编辑器失败:",r),g.error(`打开编辑器失败: ${r instanceof Error?r.message:"未知错误"}`)}};return ze(()=>{y()}),(f,r)=>{const $=d("a-button"),O=d("a-menu-item"),q=d("a-menu-item-group"),T=d("a-menu-divider"),F=d("a-menu"),E=d("a-dropdown"),H=d("a-space"),W=d("a-alert"),J=d("a-tag"),G=d("a-radio"),X=d("a-radio-group"),K=d("a-spin"),ee=d("a-divider"),te=d("a-modal");return k(),Q("div",Rt,[l(H,null,{default:a(()=>[D(" 内嵌编辑按钮 "),l($,{type:"primary",onClick:M,loading:t.value},{icon:a(()=>[l(j(ke))]),default:a(()=>[r[9]||(r[9]=p(" 内嵌编辑 "))]),_:1,__:[9]},8,["loading"]),D(" 弹窗编辑按钮 "),l($,{type:"default",onClick:L,loading:i.value},{icon:a(()=>[l(j(Nt))]),default:a(()=>[r[10]||(r[10]=p(" 新窗口编辑 "))]),_:1,__:[10]},8,["loading"]),D(" 自定义弹窗编辑按钮 "),l(E,null,{overlay:a(()=>[l(F,null,{default:a(()=>[D(" 窗口尺寸选项 "),l(q,{title:"窗口大小"},{default:a(()=>[l(O,{key:"small",onClick:r[0]||(r[0]=h=>z("small"))},{default:a(()=>r[11]||(r[11]=[_("span",null,"小窗口 (800x600)",-1)])),_:1,__:[11]}),l(O,{key:"medium",onClick:r[1]||(r[1]=h=>z("medium"))},{default:a(()=>r[12]||(r[12]=[_("span",null,"中窗口 (1200x800)",-1)])),_:1,__:[12]}),l(O,{key:"large",onClick:r[2]||(r[2]=h=>z("large"))},{default:a(()=>r[13]||(r[13]=[_("span",null,"大窗口 (1600x1000)",-1)])),_:1,__:[13]}),l(O,{key:"fullscreen",onClick:r[3]||(r[3]=h=>z("fullscreen"))},{default:a(()=>r[14]||(r[14]=[_("span",null,"全屏窗口",-1)])),_:1,__:[14]})]),_:1}),l(T),D(" 配置模板选择 "),l(q,{title:"配置模板"},{default:a(()=>[l(O,{key:"config-template",onClick:r[4]||(r[4]=h=>w.value=!0)},{default:a(()=>[l(j(nt)),r[15]||(r[15]=_("span",null,"选择配置模板",-1))]),_:1,__:[15]})]),_:1})]),_:1})]),default:a(()=>[l($,null,{default:a(()=>[r[16]||(r[16]=p(" 自定义窗口 ")),l(j(Me))]),_:1,__:[16]})]),_:1})]),_:1}),D(" 配置模板选择弹窗 "),l(te,{open:w.value,"onUpdate:open":r[7]||(r[7]=h=>w.value=h),title:"选择OnlyOffice配置模板",width:"600px",onOk:U,onCancel:r[8]||(r[8]=h=>w.value=!1),"confirm-loading":s.value},{default:a(()=>[_("div",qt,[_("div",Gt,[l(W,{message:"配置模板说明",description:"不同的配置模板会影响OnlyOffice编辑器的功能和权限设置，请根据需要选择合适的模板。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}})]),l(K,{spinning:s.value,tip:"加载配置模板..."},{default:a(()=>[_("div",Qt,[l(X,{value:C.value,"onUpdate:value":r[5]||(r[5]=h=>C.value=h),style:{width:"100%"}},{default:a(()=>[_("div",Jt,[(k(!0),Q(De,null,lt(P.value,h=>(k(),V(G,{key:h.id,value:h.id,class:"template-option"},{default:a(()=>[_("div",Xt,[_("div",Yt,[_("span",Zt,B(h.name),1),h.isDefault?(k(),V(J,{key:0,color:"blue",size:"small"},{default:a(()=>[...r[17]||(r[17]=[p("默认")])]),_:1,__:[17]})):D("v-if",!0),h.isActive?D("v-if",!0):(k(),V(J,{key:1,color:"red",size:"small"},{default:a(()=>[...r[18]||(r[18]=[p("禁用")])]),_:1,__:[18]}))]),_("div",Kt,B(h.description),1),_("div",en,[_("span",null,"更新时间: "+B(c(h.updatedAt)),1)])])]),_:2},1032,["value"]))),128))])]),_:1},8,["value"])])]),_:1},8,["spinning"]),_("div",tn,[l(ee,null,{default:a(()=>r[19]||(r[19]=[p("编辑器打开方式")])),_:1,__:[19]}),l(X,{value:S.value,"onUpdate:value":r[6]||(r[6]=h=>S.value=h)},{default:a(()=>[l(G,{value:"embedded"},{default:a(()=>r[20]||(r[20]=[p("内嵌模式")])),_:1,__:[20]}),l(G,{value:"popup"},{default:a(()=>r[21]||(r[21]=[p("弹窗模式")])),_:1,__:[21]})]),_:1},8,["value"])])])]),_:1},8,["open","confirm-loading"])])}}});const ln=Fe(nn,[["__scopeId","data-v-1092a6d1"],["__file","D:/Code/OnlyOffice/frontend/src/components/DocumentActions.vue"]]),an={class:"documents-page"},on={class:"content-wrapper"},rn={class:"search-section"},sn=["onClick"],un={class:"name-info"},dn={class:"name"},cn={class:"path"},fn={class:"ant-upload-drag-icon"},pn=Se({__name:"index",setup(o){at.extend(Wt);const e=ot(),t=b(!1),i=b(""),s=b(),w=b(),P=b(!1),C=b(!1),S=b([]),c=b({current:1,pageSize:20,total:0}),y=b({fileList:[],folder:""}),M=b({name:"",id:""}),L=b([]),z=async()=>{t.value=!0;try{const u={page:c.value.current,limit:c.value.pageSize,search:i.value||void 0,extension:s.value};console.log("📄 获取文档列表，参数:",u);const n=await pt.getDocuments(u);console.log("📄 文档API响应:",n),Array.isArray(n)?(L.value=n.map(m=>{const A=m.name||m.title||"",N=F(A);return{id:m.id,title:A,type:N,size:m.size||0,status:"published",createdAt:m.createdAt,updatedAt:m.lastModified||m.updatedAt,createdBy:m.createdBy||"",lastEditor:m.modifiedBy||m.createdBy||"",url:m.url,isPublic:!0,category:"",tags:[]}}),c.value.total=n.length):(console.warn("⚠️ 意外的文档API响应格式:",n),L.value=[],c.value.total=0),console.log(`✅ 成功获取 ${L.value.length} 个文档`)}catch(u){console.error("❌ 获取文档列表失败:",u),g.error("获取文档列表失败"),L.value=[],c.value.total=0}finally{t.value=!1}},U=b([{title:"根目录",value:"/",children:[{title:"项目文档",value:"/projects/"},{title:"财务文档",value:"/finance/"},{title:"营销文档",value:"/marketing/"},{title:"技术文档",value:"/tech/"}]}]),f=[{title:"文档名称",key:"name",width:"35%",sorter:!0},{title:"类型",key:"type",width:"10%",filters:[{text:"Word文档",value:"word"},{text:"Excel表格",value:"excel"},{text:"PowerPoint",value:"powerpoint"},{text:"PDF文件",value:"pdf"},{text:"图片文件",value:"image"},{text:"其他文件",value:"other"}]},{title:"状态",key:"status",width:"12%",filters:[{text:"草稿",value:"draft"},{text:"已发布",value:"published"},{text:"已归档",value:"archived"}]},{title:"大小",key:"size",width:"10%",sorter:!0},{title:"创建者",dataIndex:"createdBy",key:"creator",width:"10%"},{title:"更新时间",key:"updateTime",width:"15%",sorter:!0},{title:"操作",key:"actions",width:"15%"}],r={selectedRowKeys:S,onChange:u=>{S.value=u}},$=rt(()=>{let u=L.value;return i.value&&(u=u.filter(n=>n.title.toLowerCase().includes(i.value.toLowerCase()))),s.value&&(u=u.filter(n=>n.type===s.value)),w.value&&(u=u.filter(n=>n.status===w.value)),u}),O=u=>{if(u===0)return"0 B";const n=1024,m=["B","KB","MB","GB"],A=Math.floor(Math.log(u)/Math.log(n));return parseFloat((u/Math.pow(n,A)).toFixed(2))+" "+m[A]},q=u=>{const n=new Date,m=new Date(u),A=n.getTime()-m.getTime(),N=Math.floor(A/(1e3*60*60));if(N<1)return"刚刚";if(N<24)return`${N}小时前`;const Y=Math.floor(N/24);return Y<30?`${Y}天前`:m.toLocaleDateString()},T=u=>new Date(u).toLocaleString(),F=u=>{var m;if(!u)return"word";const n=((m=u.split(".").pop())==null?void 0:m.toLowerCase())||"";return["doc","docx","odt","rtf","txt"].includes(n)?"word":["xls","xlsx","ods","csv"].includes(n)?"excel":["ppt","pptx","odp"].includes(n)?"powerpoint":["pdf"].includes(n)?"pdf":["jpg","jpeg","png","gif","bmp","svg","webp"].includes(n)?"image":"other"},E=u=>({word:"Word文档",excel:"Excel表格",powerpoint:"PowerPoint",pdf:"PDF文件",image:"图片文件",other:"其他文件"})[u]||u,H=u=>({word:"#1890ff",excel:"#52c41a",powerpoint:"#fa8c16",pdf:"#f5222d",image:"#722ed1",other:"#d9d9d9"})[u]||"#d9d9d9",W=u=>({word:jt,excel:ht,powerpoint:Mt,pdf:$t,image:wt,other:ge})[u]||ge,J=u=>({draft:"草稿",published:"已发布",archived:"已归档"})[u]||u,G=u=>({draft:"default",published:"success",archived:"warning"})[u]||"default",X=u=>({draft:ke,published:ft,archived:he})[u]||he,K=()=>{c.value.current=1,z()},ee=()=>{c.value.current=1,z()},te=()=>{c.value.current=1,z()},h=()=>{c.value.current=1,z(),g.success("刷新成功")},je=(u,n,m)=>{c.value.current=u.current,c.value.pageSize=u.pageSize,z(),console.log("Table filters:",n),console.log("Table sorter:",m)},Ee=()=>{P.value=!0},Ae=()=>{e.push("/templates")},Te=u=>{e.push(`/documents/editor/${u.id}`)},Ie=()=>{if(S.value.length===0){g.warning("请先选择要下载的文档");return}g.info(`批量下载 ${S.value.length} 个文档`)},Ve=()=>{if(S.value.length===0){g.warning("请先选择要删除的文档");return}g.info(`批量删除 ${S.value.length} 个文档`)},Be=u=>{y.value.fileList=u.fileList},He=()=>{if(y.value.fileList.length===0){g.warning("请选择要上传的文件");return}g.success("文档上传成功！"),P.value=!1,y.value={fileList:[],folder:""}},Ne=()=>{P.value=!1,y.value={fileList:[],folder:""}},Ue=()=>{if(!M.value.name.trim()){g.warning("请输入文档名称");return}g.success("文档重命名成功！"),C.value=!1},We=()=>{C.value=!1,M.value={name:"",id:""}};return ze(()=>{z()}),(u,n)=>{const m=d("a-button"),A=d("a-space"),N=d("a-page-header"),Y=d("a-input-search"),Z=d("a-col"),I=d("a-select-option"),ce=d("a-select"),fe=d("a-menu-item"),Re=d("a-menu"),qe=d("a-dropdown"),Ge=d("a-row"),Qe=d("a-avatar"),pe=d("a-tag"),Je=d("a-tooltip"),Xe=d("a-table"),Ye=d("a-upload-dragger"),ne=d("a-form-item"),Ze=d("a-tree-select"),me=d("a-form"),ve=d("a-modal"),Ke=d("a-input");return k(),Q("div",an,[l(N,{title:"文档管理","sub-title":"管理和编辑您的文档"},{extra:a(()=>[l(A,null,{default:a(()=>[l(m,{type:"primary",onClick:Ee},{icon:a(()=>[l(j(mt))]),default:a(()=>[n[8]||(n[8]=p(" 上传文档 "))]),_:1,__:[8]}),l(m,{onClick:Ae},{icon:a(()=>[l(j(it))]),default:a(()=>[n[9]||(n[9]=p(" 从模板创建 "))]),_:1,__:[9]})]),_:1})]),_:1}),_("div",on,[D(" 搜索和筛选区域 "),_("div",rn,[l(Ge,{gutter:16},{default:a(()=>[l(Z,{span:8},{default:a(()=>[l(Y,{value:i.value,"onUpdate:value":n[0]||(n[0]=v=>i.value=v),placeholder:"搜索文档名称、内容...",size:"large",onSearch:K},null,8,["value"])]),_:1}),l(Z,{span:4},{default:a(()=>[l(ce,{value:s.value,"onUpdate:value":n[1]||(n[1]=v=>s.value=v),placeholder:"文档类型",size:"large","allow-clear":"",onChange:ee},{default:a(()=>[l(I,{value:"word"},{default:a(()=>n[10]||(n[10]=[p(" Word文档 ")])),_:1,__:[10]}),l(I,{value:"excel"},{default:a(()=>n[11]||(n[11]=[p(" Excel表格 ")])),_:1,__:[11]}),l(I,{value:"powerpoint"},{default:a(()=>n[12]||(n[12]=[p(" PowerPoint ")])),_:1,__:[12]}),l(I,{value:"pdf"},{default:a(()=>n[13]||(n[13]=[p(" PDF文件 ")])),_:1,__:[13]}),l(I,{value:"image"},{default:a(()=>n[14]||(n[14]=[p(" 图片文件 ")])),_:1,__:[14]}),l(I,{value:"other"},{default:a(()=>n[15]||(n[15]=[p(" 其他文件 ")])),_:1,__:[15]})]),_:1},8,["value"])]),_:1}),l(Z,{span:4},{default:a(()=>[l(ce,{value:w.value,"onUpdate:value":n[2]||(n[2]=v=>w.value=v),placeholder:"状态",size:"large","allow-clear":"",onChange:te},{default:a(()=>[l(I,{value:"draft"},{default:a(()=>n[16]||(n[16]=[p(" 草稿 ")])),_:1,__:[16]}),l(I,{value:"published"},{default:a(()=>n[17]||(n[17]=[p(" 已发布 ")])),_:1,__:[17]}),l(I,{value:"archived"},{default:a(()=>n[18]||(n[18]=[p(" 已归档 ")])),_:1,__:[18]})]),_:1},8,["value"])]),_:1}),l(Z,{span:8},{default:a(()=>[l(A,null,{default:a(()=>[l(m,{onClick:h},{icon:a(()=>[l(j(st))]),default:a(()=>[n[19]||(n[19]=p(" 刷新 "))]),_:1,__:[19]}),l(qe,null,{overlay:a(()=>[l(Re,null,{default:a(()=>[l(fe,{onClick:Ie},{default:a(()=>[l(j(ut)),n[20]||(n[20]=p(" 批量下载 "))]),_:1,__:[20]}),l(fe,{class:"danger-item",onClick:Ve},{default:a(()=>[l(j(dt)),n[21]||(n[21]=p(" 批量删除 "))]),_:1,__:[21]})]),_:1})]),default:a(()=>[l(m,null,{default:a(()=>[n[22]||(n[22]=p(" 批量操作 ")),l(j(Me))]),_:1,__:[22]})]),_:1})]),_:1})]),_:1})]),_:1})]),D(" 文档列表 "),l(Xe,{columns:f,"data-source":$.value,loading:t.value,pagination:{current:c.value.current,pageSize:c.value.pageSize,total:c.value.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(v,x)=>`第 ${x[0]}-${x[1]} 条，共 ${v} 个文档`},"row-selection":r,"row-key":"id",onChange:je},{bodyCell:a(({column:v,record:x})=>[v.key==="name"?(k(),Q("div",{key:0,class:"document-name",onClick:mn=>Te(x)},[l(Qe,{shape:"square",style:ct({backgroundColor:H(x.type)})},{icon:a(()=>[(k(),V(_e(W(x.type))))]),_:2},1032,["style"]),_("div",un,[_("div",dn,B(x.title),1),_("div",cn,B(x.path),1)])],8,sn)):v.key==="type"?(k(),V(pe,{key:1,color:H(x.type)},{default:a(()=>[p(B(E(x.type)),1)]),_:2},1032,["color"])):v.key==="status"?(k(),V(pe,{key:2,color:G(x.status)},{default:a(()=>[(k(),V(_e(X(x.status)))),p(" "+B(J(x.status)),1)]),_:2},1032,["color"])):v.key==="size"?(k(),Q(De,{key:3},[p(B(O(x.size)),1)],64)):v.key==="updateTime"?(k(),V(Je,{key:4,title:T(x.updatedAt)},{default:a(()=>[p(B(q(x.updatedAt)),1)]),_:2},1032,["title"])):v.key==="actions"?(k(),V(ln,{key:5,"document-id":x.id},null,8,["document-id"])):D("v-if",!0)]),_:1},8,["data-source","loading","pagination"])]),D(" 上传文档弹窗 "),l(ve,{open:P.value,"onUpdate:open":n[5]||(n[5]=v=>P.value=v),title:"上传文档",width:"600px",onOk:He,onCancel:Ne},{default:a(()=>[l(me,{model:y.value,layout:"vertical"},{default:a(()=>[l(ne,{label:"选择文件",required:""},{default:a(()=>[l(Ye,{"file-list":y.value.fileList,"onUpdate:fileList":n[3]||(n[3]=v=>y.value.fileList=v),multiple:!0,"before-upload":()=>!1,accept:".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf",onChange:Be},{default:a(()=>[_("p",fn,[l(j(It))]),n[23]||(n[23]=_("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),n[24]||(n[24]=_("p",{class:"ant-upload-hint"}," 支持单个或批量上传。支持 Word、Excel、PowerPoint、PDF 格式 ",-1))]),_:1,__:[23,24]},8,["file-list"])]),_:1}),l(ne,{label:"上传目录"},{default:a(()=>[l(Ze,{value:y.value.folder,"onUpdate:value":n[4]||(n[4]=v=>y.value.folder=v),"tree-data":U.value,placeholder:"选择上传目录","allow-clear":"","tree-default-expand-all":""},null,8,["value","tree-data"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),D(" 重命名弹窗 "),l(ve,{open:C.value,"onUpdate:open":n[7]||(n[7]=v=>C.value=v),title:"重命名文档",onOk:Ue,onCancel:We},{default:a(()=>[l(me,{model:M.value,layout:"vertical"},{default:a(()=>[l(ne,{label:"文档名称",required:""},{default:a(()=>[l(Ke,{value:M.value.name,"onUpdate:value":n[6]||(n[6]=v=>M.value.name=v),placeholder:"请输入新的文档名称"},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])])}}});const yn=Fe(pn,[["__scopeId","data-v-02e3d10a"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Documents/index.vue"]]);export{yn as default};
