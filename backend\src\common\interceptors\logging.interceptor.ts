import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

/**
 * 扩展的请求接口，包含requestId
 */
interface RequestWithId extends Request {
  requestId?: string;
}

/**
 * 日志拦截器
 * 
 * 记录所有HTTP请求和响应的详细信息，用于调试和监控
 * 
 * @class LoggingInterceptor
 * @implements {NestInterceptor}
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<RequestWithId>();
    const response = ctx.getResponse<Response>();

    const { method, url, headers, body } = request;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // 生成请求ID
    const requestId = this.generateRequestId();
    request.requestId = requestId;

    // 记录请求开始
    console.log(`[${this.getTimestamp()}] [${requestId}] --> ${method} ${url}`, {
      method,
      url,
      userAgent,
      contentType: headers['content-type'],
      contentLength: headers['content-length'],
      body: this.sanitizeBody(body),
    });

    return next.handle().pipe(
      tap({
        next: (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // 记录成功响应
          console.log(`[${this.getTimestamp()}] [${requestId}] <-- ${method} ${url} ${response.statusCode} ${duration}ms`, {
            method,
            url,
            statusCode: response.statusCode,
            duration: `${duration}ms`,
            responseSize: this.getResponseSize(data),
          });
        },
        error: (error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // 记录错误响应
          console.error(`[${this.getTimestamp()}] [${requestId}] <-- ${method} ${url} ERROR ${duration}ms`, {
            method,
            url,
            duration: `${duration}ms`,
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
          });
        },
      }),
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取时间戳字符串
   */
  private getTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * 清理请求体中的敏感信息
   */
  private sanitizeBody(body: unknown): unknown {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    
    // 隐藏敏感字段
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * 获取响应大小
   */
  private getResponseSize(data: unknown): string {
    if (!data) return '0B';
    
    try {
      const size = JSON.stringify(data).length;
      if (size < 1024) {
        return `${size}B`;
      } else if (size < 1024 * 1024) {
        return `${(size / 1024).toFixed(1)}KB`;
      } else {
        return `${(size / (1024 * 1024)).toFixed(1)}MB`;
      }
    } catch {
      return 'unknown';
    }
  }
} 