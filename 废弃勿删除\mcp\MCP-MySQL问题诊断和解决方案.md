# MCP MySQL 问题诊断和解决方案

## 📋 当前状况分析

根据测试结果，您的配置基本正确：

### ✅ 已确认正常的部分
- **Node.js环境**: v22.15.0 (满足要求)
- **MCP配置文件**: `.cursor/mcp.json` 格式正确
- **MySQL网络连接**: *************:3306 连接成功
- **MCP包可用性**: @benborla29/mcp-server-mysql 可以下载和运行

### ⚠️ 潜在问题点
1. **远程MySQL连接权限**
2. **Cursor IDE的MCP集成**
3. **环境变量传递**
4. **数据库权限设置**

## 🔧 解决方案

### 方案1: 检查MySQL远程连接权限

```sql
-- 连接到MySQL服务器，检查用户权限
SHOW GRANTS FOR 'onlyfile_user'@'%';
SHOW GRANTS FOR 'onlyfile_user'@'*************';

-- 如果用户不存在或权限不足，创建/修改用户
CREATE USER IF NOT EXISTS 'onlyfile_user'@'%' IDENTIFIED BY '0nlyF!le$ecure#123';
GRANT ALL PRIVILEGES ON onlyfile.* TO 'onlyfile_user'@'%';
FLUSH PRIVILEGES;

-- 检查数据库是否存在
SHOW DATABASES LIKE 'onlyfile';
```

### 方案2: 修改MCP配置以启用详细日志

将您的 `.cursor/mcp.json` 更新为：

```json
{
  "mcpServers": {
    "MySQL": {
      "command": "npx",
      "args": [
        "-y",
        "@benborla29/mcp-server-mysql"
      ],
      "env": {
        "MYSQL_HOST": "*************",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "onlyfile_user",
        "MYSQL_PASS": "0nlyF!le$ecure#123",
        "MYSQL_DB": "onlyfile",
        "ALLOW_INSERT_OPERATION": "true",
        "ALLOW_UPDATE_OPERATION": "true",
        "ALLOW_DELETE_OPERATION": "false",
        "MYSQL_ENABLE_LOGGING": "true",
        "MYSQL_LOG_LEVEL": "debug",
        "MYSQL_POOL_SIZE": "5",
        "MYSQL_QUERY_TIMEOUT": "30000"
      }
    }
  }
}
```

### 方案3: 使用本地MySQL测试

如果远程连接有问题，可以先用本地MySQL测试：

```json
{
  "mcpServers": {
    "MySQL": {
      "command": "npx",
      "args": [
        "-y",
        "@benborla29/mcp-server-mysql"
      ],
      "env": {
        "MYSQL_HOST": "127.0.0.1",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "root",
        "MYSQL_PASS": "your_local_password",
        "MYSQL_DB": "test",
        "ALLOW_INSERT_OPERATION": "true",
        "ALLOW_UPDATE_OPERATION": "true",
        "ALLOW_DELETE_OPERATION": "false",
        "MYSQL_ENABLE_LOGGING": "true"
      }
    }
  }
}
```

### 方案4: 手动测试MCP连接

创建测试脚本验证MCP是否能连接到数据库：

```powershell
# 手动测试连接
$env:MYSQL_HOST="*************"
$env:MYSQL_PORT="3306"
$env:MYSQL_USER="onlyfile_user"
$env:MYSQL_PASS="0nlyF!le`$ecure#123"
$env:MYSQL_DB="onlyfile"
$env:MYSQL_ENABLE_LOGGING="true"

# 运行MCP服务器并查看输出
npx @benborla29/mcp-server-mysql
```

## 🚀 Cursor IDE 中的MCP使用步骤

### 1. 重启Cursor IDE
```bash
# 完全关闭Cursor
# 重新打开项目
```

### 2. 检查MCP状态
在Cursor中：
- 按 `Ctrl+Shift+P` (Windows) 
- 输入 "MCP" 查看相关命令
- 检查底部状态栏是否显示MCP连接状态

### 3. 在聊天中测试MCP
```
@MySQL 显示数据库中的所有表
```

```
@MySQL 查询用户表的结构
```

### 4. 查看MCP日志
检查以下位置的日志文件：
- Windows: `%APPDATA%\Cursor\logs\`
- 或者在Cursor中: `Help > Toggle Developer Tools` > `Console`

## 🐛 常见问题和解决方法

### 问题1: "MCP server not found"
**解决方法**:
1. 确保 `.cursor/mcp.json` 在项目根目录
2. 重启Cursor IDE
3. 检查npx是否在PATH中

### 问题2: "Database connection failed"
**解决方法**:
1. 检查MySQL服务是否运行
2. 验证用户名密码
3. 确认远程连接权限
4. 检查防火墙设置

### 问题3: "Permission denied"
**解决方法**:
1. 检查MySQL用户权限
2. 确认数据库存在
3. 验证表的访问权限

### 问题4: "Timeout connecting to server"
**解决方法**:
1. 增加连接超时时间
2. 检查网络连接
3. 验证MySQL配置

## 📝 调试检查清单

- [ ] **MySQL服务运行**: 在*************:3306端口
- [ ] **用户权限正确**: onlyfile_user 可以连接并访问onlyfile数据库
- [ ] **防火墙开放**: 3306端口允许从您的机器访问
- [ ] **MCP配置文件**: `.cursor/mcp.json` 在正确位置且格式正确
- [ ] **Cursor重启**: 修改配置后重启了IDE
- [ ] **数据库存在**: onlyfile数据库已创建且有数据表
- [ ] **环境变量**: npx和node在PATH中可用
- [ ] **MCP包版本**: 使用最新版本的@benborla29/mcp-server-mysql

## 🔍 进一步调试步骤

如果以上方案都不能解决问题，请：

1. **启用详细日志记录**
2. **使用本地MySQL进行测试**
3. **检查Cursor Developer Tools的控制台输出**
4. **尝试使用不同的MCP服务器进行测试**
5. **联系MCP MySQL包的维护者**

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：
- Cursor IDE版本
- MCP MySQL包版本
- 完整的错误日志
- MySQL版本和配置
- 网络环境详情 