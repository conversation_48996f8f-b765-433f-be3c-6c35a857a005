import { ref, computed } from 'vue'

/**
 * 通知管理 Composable
 *
 * @description 提供应用内通知的显示和管理功能
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

/**
 * 通知类型
 */
export type NotificationType = 'info' | 'success' | 'warning' | 'error'

/**
 * 通知信息
 */
export interface NotificationInfo {
  /** 是否显示 */
  show: boolean
  /** 通知类型 */
  type: NotificationType
  /** 通知消息 */
  message: string
  /** 显示持续时间（毫秒） */
  duration: number
}

/**
 * 通知管理 Hook
 */
export function useNotification() {
  // 通知状态
  const notification = ref<NotificationInfo>({
    show: false,
    type: 'info',
    message: '',
    duration: 5000,
  })

  // 自动隐藏定时器
  let hideTimer: number | null = null

  /**
   * 显示通知
   * @param message 通知消息
   * @param type 通知类型
   * @param duration 显示持续时间（毫秒），默认5秒
   */
  const showNotification = (
    message: string,
    type: NotificationType = 'info',
    duration: number = 5000
  ): void => {
    // 清除现有定时器
    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }

    // 设置通知内容
    notification.value = {
      show: true,
      type,
      message,
      duration,
    }

    // 设置自动隐藏
    if (duration > 0) {
      hideTimer = window.setTimeout(() => {
        hideNotification()
      }, duration)
    }

    console.log(`显示通知 [${type.toUpperCase()}]: ${message}`)
  }

  /**
   * 隐藏通知
   */
  const hideNotification = (): void => {
    notification.value.show = false

    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }

    console.log('隐藏通知')
  }

  /**
   * 显示成功通知
   * @param message 通知消息
   * @param duration 显示持续时间（毫秒）
   */
  const showSuccess = (message: string, duration?: number): void => {
    showNotification(message, 'success', duration)
  }

  /**
   * 显示错误通知
   * @param message 通知消息
   * @param duration 显示持续时间（毫秒）
   */
  const showError = (message: string, duration?: number): void => {
    showNotification(message, 'error', duration)
  }

  /**
   * 显示警告通知
   * @param message 通知消息
   * @param duration 显示持续时间（毫秒）
   */
  const showWarning = (message: string, duration?: number): void => {
    showNotification(message, 'warning', duration)
  }

  /**
   * 显示信息通知
   * @param message 通知消息
   * @param duration 显示持续时间（毫秒）
   */
  const showInfo = (message: string, duration?: number): void => {
    showNotification(message, 'info', duration)
  }

  /**
   * 获取通知图标
   */
  const getNotificationIcon = computed(() => {
    switch (notification.value.type) {
      case 'success':
        return '✓'
      case 'error':
        return '✗'
      case 'warning':
        return '⚠'
      case 'info':
      default:
        return 'ℹ'
    }
  })

  /**
   * 获取通知颜色
   */
  const getNotificationColor = computed(() => {
    switch (notification.value.type) {
      case 'success':
        return '#2ecc71'
      case 'error':
        return '#e74c3c'
      case 'warning':
        return '#f39c12'
      case 'info':
      default:
        return '#3498db'
    }
  })

  /**
   * 获取通知CSS类名
   */
  const getNotificationClass = computed(() => {
    return `notification notification-${notification.value.type}`
  })

  /**
   * 检查是否正在显示通知
   */
  const isShowing = computed((): boolean => {
    return notification.value.show
  })

  return {
    // 状态
    notification: computed(() => notification.value),
    isShowing,

    // 样式
    getNotificationIcon,
    getNotificationColor,
    getNotificationClass,

    // 方法
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  }
}
