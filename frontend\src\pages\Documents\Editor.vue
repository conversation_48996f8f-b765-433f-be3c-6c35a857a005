<template>
  <div class="document-editor-page">
    <div class="editor-header">
      <a-page-header :title="documentTitle" sub-title="OnlyOffice文档编辑器" @back="handleBack">
        <template #extra>
          <a-space>
            <a-button :loading="saving" @click="handleSave">
              <template #icon>
                <save-outlined />
              </template>
              保存
            </a-button>
            <a-button @click="handleDownload">
              <template #icon>
                <download-outlined />
              </template>
              下载
            </a-button>
            <a-button @click="handleShare">
              <template #icon>
                <share-alt-outlined />
              </template>
              分享
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <div class="editor-container">
      <!-- OnlyOffice编辑器容器 -->
      <div id="onlyoffice-editor" ref="editorContainer" class="editor-content" />

      <!-- 加载状态 -->
      <div v-if="loading" class="editor-loading">
        <a-spin size="large" tip="正在加载文档编辑器...">
          <div class="loading-placeholder" />
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { SaveOutlined, DownloadOutlined, ShareAltOutlined } from '@ant-design/icons-vue'
import type { DocEditor, OnlyOfficeConfig, ErrorEvent } from '@/types/onlyoffice.types'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const saving = ref(false)
const documentTitle = ref('文档加载中...')
const editorContainer = ref<HTMLElement>()

// 文档ID
const documentId = route.params.id as string

// OnlyOffice编辑器实例
let docEditor: DocEditor | null = null

// 返回处理
const handleBack = () => {
  router.push('/documents')
}

// 保存处理
const handleSave = async () => {
  if (!docEditor) return

  saving.value = true
  try {
    // TODO: 调用OnlyOffice保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('文档保存成功！')
  } catch (error) {
    message.error('文档保存失败')
    console.error('保存错误:', error)
  } finally {
    saving.value = false
  }
}

// 下载处理
const handleDownload = () => {
  if (!docEditor) return

  // TODO: 实现文档下载
  message.info('下载功能开发中...')
}

// 分享处理
const handleShare = () => {
  // TODO: 实现文档分享
  message.info('分享功能开发中...')
}

// 初始化OnlyOffice编辑器
const initOnlyOfficeEditor = () => {
  if (!editorContainer.value) return

  // 模拟OnlyOffice配置
  const config: OnlyOfficeConfig = {
    document: {
      fileType: 'docx',
      key: `doc_${documentId}_${Date.now()}`,
      title: documentTitle.value,
      url: `/api/documents/${documentId}/download`,
    },
    documentType: 'word',
    editorConfig: {
      callbackUrl: `/api/documents/callback`,
      lang: 'zh-CN',
      mode: 'edit',
      user: {
        id: 'user1',
        name: '当前用户',
      },
    },
    width: '100%',
    height: '100%',
    events: {
      onDocumentReady: () => {
        loading.value = false
        console.log('文档编辑器加载完成')
      },
      onError: (event: ErrorEvent) => {
        loading.value = false
        message.error('编辑器加载失败')
        console.error('OnlyOffice错误:', event)
      },
    },
  }

  // 检查OnlyOffice API是否可用
  if (typeof window !== 'undefined' && window.DocsAPI) {
    docEditor = new window.DocsAPI.DocEditor('onlyoffice-editor', config)
  } else {
    // 如果OnlyOffice API不可用，显示占位内容
    loading.value = false
    editorContainer.value!.innerHTML = `
      <div class="editor-placeholder">
        <div class="placeholder-content">
          <h3>OnlyOffice编辑器</h3>
          <p>文档ID: ${documentId}</p>
          <p>编辑器正在开发中，需要集成OnlyOffice Document Server</p>
          <div class="placeholder-features">
            <div class="feature">📝 在线文档编辑</div>
            <div class="feature">👥 协同编辑</div>
            <div class="feature">💾 实时保存</div>
            <div class="feature">📤 文档分享</div>
          </div>
        </div>
      </div>
    `
  }
}

// 加载文档信息
const loadDocumentInfo = async () => {
  try {
    // TODO: 调用API获取文档信息
    await new Promise(resolve => setTimeout(resolve, 500))
    documentTitle.value = `文档_${documentId}.docx`
  } catch (error) {
    message.error('加载文档信息失败')
    console.error('加载错误:', error)
  }
}

onMounted(async () => {
  await loadDocumentInfo()
  initOnlyOfficeEditor()
})

onUnmounted(() => {
  if (docEditor) {
    try {
      docEditor.destroyEditor()
    } catch (error) {
      console.warn('销毁编辑器失败:', error)
    }
  }
})
</script>

<style scoped>
.document-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.editor-header {
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.editor-container {
  flex: 1;
  position: relative;
  background: #f5f5f5;
}

.editor-content {
  width: 100%;
  height: 100%;
  background: white;
}

.editor-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-placeholder {
  width: 200px;
  height: 100px;
}

/* OnlyOffice编辑器占位样式 */
:deep(.editor-placeholder) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f9f9f9;
}

:deep(.placeholder-content) {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

:deep(.placeholder-content h3) {
  color: #1890ff;
  font-size: 24px;
  margin-bottom: 16px;
}

:deep(.placeholder-content p) {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

:deep(.placeholder-features) {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 24px;
}

:deep(.feature) {
  padding: 12px;
  background: #f0f2f5;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .editor-header {
    padding: 0 16px;
  }

  :deep(.placeholder-features) {
    grid-template-columns: 1fr;
  }
}
</style>
