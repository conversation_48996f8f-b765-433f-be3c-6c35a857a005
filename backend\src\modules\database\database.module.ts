import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseService } from './services/database.service';

/**
 * 数据库模块
 * 
 * 提供MySQL数据库连接和服务
 * 保持与原有系统的完全兼容性
 * 
 * @class DatabaseModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Module({
  imports: [ConfigModule],
  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {} 