/**
 * Ant Design组件相关类型定义
 * @description 定义Ant Design组件的TypeScript类型
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

// 表格相关类型
export interface TableColumns {
  key: string
  title: string
  dataIndex: string
  width?: number | string
  fixed?: 'left' | 'right'
  align?: 'left' | 'center' | 'right'
  sorter?: boolean | ((a: unknown, b: unknown) => number)
  sortOrder?: 'ascend' | 'descend' | null
  filters?: TableFilter[]
  filterDropdown?: boolean
  onFilter?: (value: string | number | boolean, record: unknown) => boolean
  customRender?: (params: TableRenderParams) => unknown
  ellipsis?: boolean
  resizable?: boolean
}

export interface TableFilter {
  text: string
  value: string | number | boolean
}

export interface TableRenderParams {
  text: unknown
  record: unknown
  index: number
  column: TableColumns
}

// 分页器类型
export interface PaginationConfig {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
  pageSizeOptions?: string[]
  size?: 'default' | 'small'
}

// 表格排序参数
export interface TableSorter {
  field?: string
  order?: 'ascend' | 'descend'
  column?: TableColumns
  columnKey?: string
}

// 表格过滤参数
export interface TableFilters {
  [key: string]: (string | number)[] | null
}

// 表格变化事件参数
export interface TableChangeInfo {
  action: 'paginate' | 'sort' | 'filter'
}

// 表单实例类型
export interface FormInstance {
  validateFields: () => Promise<Record<string, unknown>>
  resetFields: () => void
  setFieldsValue: (values: Record<string, unknown>) => void
  getFieldsValue: () => Record<string, unknown>
  scrollToField: (name: string) => void
  submit: () => void
}

// 表单项规则
export interface FormRule {
  required?: boolean
  message?: string
  type?:
    | 'string'
    | 'number'
    | 'boolean'
    | 'method'
    | 'regexp'
    | 'integer'
    | 'float'
    | 'array'
    | 'object'
    | 'enum'
    | 'date'
    | 'url'
    | 'hex'
    | 'email'
  pattern?: RegExp
  min?: number
  max?: number
  len?: number
  validator?: (rule: FormRule, value: unknown, callback: (error?: string) => void) => void
  trigger?: string | string[]
  whitespace?: boolean
  transform?: (value: unknown) => unknown
}

// 表单布局
export interface FormLayout {
  labelCol?: {
    span?: number
    offset?: number
  }
  wrapperCol?: {
    span?: number
    offset?: number
  }
}

// Modal配置
export interface ModalConfig {
  title?: string
  width?: number | string
  height?: number | string
  centered?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  footer?: null | unknown[]
  destroyOnClose?: boolean
  forceRender?: boolean
  getContainer?: () => HTMLElement
}

// 消息提示配置
export interface MessageConfig {
  content: string
  duration?: number
  onClose?: () => void
  icon?: unknown
  key?: string
}

// 通知配置
export interface NotificationConfig {
  message: string
  description?: string
  duration?: number
  icon?: unknown
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
  style?: Record<string, string>
  className?: string
  onClick?: () => void
  onClose?: () => void
  key?: string
}

// 上传文件信息
export interface UploadFileInfo {
  uid: string
  name: string
  status?: 'uploading' | 'done' | 'error' | 'removed'
  response?: unknown
  linkProps?: Record<string, unknown>
  type?: string
  size?: number
  percent?: number
  thumbUrl?: string
  url?: string
  preview?: string
  originFileObj?: File
  error?: unknown
}

// 上传变化参数
export interface UploadChangeParam {
  file: UploadFileInfo
  fileList: UploadFileInfo[]
  event?: Event
}

// 日期选择器值类型
export type DatePickerValue = string | Date | null

// 时间范围选择器值类型
export type RangePickerValue = [DatePickerValue, DatePickerValue] | null

// 选择器选项
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  children?: SelectOption[]
}

// 树形选择器数据
export interface TreeSelectData {
  title: string
  value: string | number
  key: string | number
  disabled?: boolean
  selectable?: boolean
  children?: TreeSelectData[]
}

// 菜单项类型
export interface MenuItem {
  key: string
  label: string
  icon?: unknown
  disabled?: boolean
  children?: MenuItem[]
  type?: 'group' | 'divider'
}

// 步骤条项目
export interface StepItem {
  title: string
  description?: string
  status?: 'wait' | 'process' | 'finish' | 'error'
  icon?: unknown
  subTitle?: string
}

// 标签页项目
export interface TabItem {
  key: string
  label: string
  disabled?: boolean
  closable?: boolean
  icon?: unknown
  children?: unknown
}

// 抽屉配置
export interface DrawerConfig {
  title?: string
  width?: number | string
  height?: number | string
  placement?: 'top' | 'right' | 'bottom' | 'left'
  closable?: boolean
  maskClosable?: boolean
  mask?: boolean
  keyboard?: boolean
  destroyOnClose?: boolean
  forceRender?: boolean
  getContainer?: () => HTMLElement
}
