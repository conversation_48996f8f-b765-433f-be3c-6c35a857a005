/**
 * 获取FileNet文档列表
 */
router.get('/filenet/documents', async (req, res) => {
    try {
        const filenetService = require('../services/filenetService');

        // 从查询参数获取分页和排序选项
        const options = {
            page: req.query.page,
            limit: req.query.limit,
            sortBy: req.query.sortBy,
            sortOrder: req.query.sortOrder,
            showDeleted: req.query.showDeleted
        };

        const results = await filenetService.getFileNetDocumentsFromDB(options);
        res.json(results);
    } catch (error) {
        console.error('获取FileNet文档列表失败:', error);
        res.status(500).json({
            success: false,
            message: `获取FileNet文档列表失败: ${error.message}`
        });
    }
}); 