<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置模板管理 - OnlyOffice文档管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/config-templates.css" rel="stylesheet">
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-file-alt me-2"></i>OnlyOffice文档管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/navigation.html">
                    <i class="fas fa-compass me-1"></i>导航
                </a>
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧模板列表 -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>配置模板
                        </h5>
                        <button class="btn btn-sm btn-success" onclick="createNewTemplate()">
                            <i class="fas fa-plus"></i> 新建
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div id="templatesList" class="list-group list-group-flush">
                            <!-- 模板列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧配置编辑区 -->
            <div class="col-md-9">
                <div id="configEditor" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0" id="currentTemplateName">配置模板编辑</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate()">
                                            <i class="fas fa-eye"></i> 预览
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="copyTemplate()">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="exportTemplate()">
                                            <i class="fas fa-download"></i> 导出
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="saveTemplate()">
                                            <i class="fas fa-save"></i> 保存
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 基本信息 -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">模板名称 *</label>
                                    <input type="text" class="form-control" id="templateName" placeholder="输入模板名称">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">模板描述</label>
                                    <input type="text" class="form-control" id="templateDescription"
                                        placeholder="输入模板描述">
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isDefaultTemplate">
                                        <label class="form-check-label" for="isDefaultTemplate">
                                            设为默认模板
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isActiveTemplate" checked>
                                        <label class="form-check-label" for="isActiveTemplate">
                                            启用此模板
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 配置项编辑区 -->
                            <div id="configItemsContainer">
                                <!-- 配置项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 欢迎页面 -->
                <div id="welcomePage" class="text-center py-5">
                    <i class="fas fa-cogs fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">配置模板管理</h3>
                    <p class="text-muted">选择左侧的模板进行编辑，或创建新的配置模板</p>
                    <button class="btn btn-primary" onclick="createNewTemplate()">
                        <i class="fas fa-plus me-2"></i>创建新模板
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-3">正在处理...</p>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="previewContent" class="bg-light p-3" style="max-height: 500px; overflow-y: auto;"></pre>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTemplateId = null;
        let configGroups = {};
        let templates = [];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadConfigGroups();
            loadTemplates();
        });

        // 加载配置分组元数据
        async function loadConfigGroups() {
            try {
                const response = await fetch('/api/config-templates/meta/groups');
                const data = await response.json();
                if (data.success) {
                    configGroups = data.data;
                }
            } catch (error) {
                console.error('加载配置分组失败:', error);
                showAlert('加载配置分组失败', 'danger');
            }
        }

        // 加载模板列表
        async function loadTemplates() {
            try {
                showLoading(true);
                const response = await fetch('/api/config-templates');
                const data = await response.json();

                if (data.success) {
                    templates = data.data;
                    renderTemplatesList();
                } else {
                    showAlert('加载模板列表失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('加载模板列表失败:', error);
                showAlert('加载模板列表失败', 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 渲染模板列表
        function renderTemplatesList() {
            const container = document.getElementById('templatesList');
            container.innerHTML = '';

            templates.forEach(template => {
                const item = document.createElement('a');
                item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
                item.href = '#';
                item.onclick = (e) => {
                    e.preventDefault();
                    selectTemplate(template.id);
                };

                const badges = [];
                if (template.is_default) {
                    badges.push('<span class="badge bg-primary">默认</span>');
                }
                if (!template.is_active) {
                    badges.push('<span class="badge bg-secondary">禁用</span>');
                }

                item.innerHTML = `
                    <div>
                        <div class="fw-bold">${escapeHtml(template.name)}</div>
                        <small class="text-muted">${escapeHtml(template.description || '')}</small>
                    </div>
                    <div>
                        ${badges.join(' ')}
                    </div>
                `;

                container.appendChild(item);
            });
        }

        // 选择模板
        async function selectTemplate(templateId) {
            try {
                showLoading(true);
                currentTemplateId = templateId;

                // 更新选中状态
                document.querySelectorAll('#templatesList .list-group-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.target.closest('.list-group-item').classList.add('active');

                // 加载模板详情
                const response = await fetch(`/api/config-templates/${templateId}`);
                const data = await response.json();

                if (data.success) {
                    renderTemplateEditor(data.data);
                    document.getElementById('welcomePage').style.display = 'none';
                    document.getElementById('configEditor').style.display = 'block';
                } else {
                    showAlert('加载模板详情失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('加载模板详情失败:', error);
                showAlert('加载模板详情失败', 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 渲染模板编辑器
        function renderTemplateEditor(template) {
            // 填充基本信息
            document.getElementById('currentTemplateName').textContent = template.name;
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateDescription').value = template.description || '';
            document.getElementById('isDefaultTemplate').checked = template.is_default;
            document.getElementById('isActiveTemplate').checked = template.is_active;

            // 渲染配置项
            renderConfigItems(template.items);
        }

        // 渲染配置项
        function renderConfigItems(items) {
            const container = document.getElementById('configItemsContainer');
            container.innerHTML = '';

            // 按分组组织配置项
            const itemsByGroup = {};
            items.forEach(item => {
                if (!itemsByGroup[item.config_group]) {
                    itemsByGroup[item.config_group] = [];
                }
                itemsByGroup[item.config_group].push(item);
            });

            // 为每个分组创建界面
            Object.keys(configGroups).forEach(groupKey => {
                const group = configGroups[groupKey];
                const groupItems = itemsByGroup[groupKey] || [];

                const groupDiv = document.createElement('div');
                groupDiv.className = 'config-group mb-4';

                groupDiv.innerHTML = `
                    <div class="config-group-header">
                        <h6 class="config-group-title">${group.description}</h6>
                        <button type="button" class="config-group-toggle" onclick="toggleGroup('${groupKey}')">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div id="group-${groupKey}" class="config-group-content">
                        <!-- 配置项将在这里生成 -->
                    </div>
                `;

                container.appendChild(groupDiv);

                // 渲染该分组的配置项
                renderGroupItems(groupKey, group, groupItems);
            });
        }

        // 渲染分组内的配置项
        function renderGroupItems(groupKey, group, existingItems) {
            const container = document.getElementById(`group-${groupKey}`);
            container.innerHTML = '';

            Object.keys(group.items).forEach(itemKey => {
                const itemConfig = group.items[itemKey];
                const existingItem = existingItems.find(item => item.config_key === itemKey);

                const itemDiv = document.createElement('div');
                itemDiv.className = `config-item ${!existingItem || !existingItem.is_enabled ? 'disabled' : ''}`;

                const isEnabled = existingItem ? existingItem.is_enabled : true;
                const isRequired = existingItem ? existingItem.is_required : false;
                const currentValue = existingItem ? existingItem.config_value : itemConfig.default;

                itemDiv.innerHTML = `
                    <div class="config-item-main">
                        <div class="config-item-label">
                            <strong>${itemKey}</strong>
                            <div class="config-item-description text-muted">${itemConfig.description}</div>
                        </div>
                        <div class="config-value-input">
                            ${renderValueInput(groupKey, itemKey, itemConfig, currentValue)}
                        </div>
                    </div>
                    <div class="config-item-controls mt-2">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="enabled_${groupKey}_${itemKey}" ${isEnabled ? 'checked' : ''} 
                                           onchange="toggleConfigItem('${groupKey}', '${itemKey}', this.checked)">
                                    <label class="form-check-label" for="enabled_${groupKey}_${itemKey}">
                                        <small>包含此配置项</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="required_${groupKey}_${itemKey}" ${isRequired ? 'checked' : ''}>
                                    <label class="form-check-label" for="required_${groupKey}_${itemKey}">
                                        <small>必填项</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="config-status-badges">
                                    ${isEnabled ? '<span class="badge bg-success">生效</span>' : '<span class="badge bg-secondary">禁用</span>'}
                                    ${isRequired ? '<span class="badge bg-warning">必填</span>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                container.appendChild(itemDiv);
            });
        }

        // 渲染值输入控件
        function renderValueInput(groupKey, itemKey, itemConfig, currentValue) {
            const inputId = `value_${groupKey}_${itemKey}`;

            switch (itemConfig.type) {
                case 'boolean':
                    return `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="${inputId}" 
                                   ${currentValue === 'true' || currentValue === true ? 'checked' : ''}>
                            <label class="form-check-label" for="${inputId}">启用</label>
                        </div>
                    `;
                case 'number':
                    return `
                        <input type="number" class="form-control form-control-sm" id="${inputId}" 
                               value="${currentValue || itemConfig.default || ''}"
                               ${itemConfig.min ? `min="${itemConfig.min}"` : ''}
                               ${itemConfig.max ? `max="${itemConfig.max}"` : ''}>
                    `;
                case 'string':
                    if (itemConfig.options) {
                        let optionsHtml = '';
                        itemConfig.options.forEach(option => {
                            optionsHtml += `<option value="${option}" ${currentValue === option ? 'selected' : ''}>${option}</option>`;
                        });
                        return `<select class="form-select form-select-sm" id="${inputId}">${optionsHtml}</select>`;
                    } else {
                        return `<input type="text" class="form-control form-control-sm" id="${inputId}" value="${currentValue || itemConfig.default || ''}">`;
                    }
                default:
                    return `<input type="text" class="form-control form-control-sm" id="${inputId}" value="${currentValue || itemConfig.default || ''}">`;
            }
        }

        // 切换配置项启用状态
        function toggleConfigItem(groupKey, itemKey, enabled) {
            const itemDiv = document.querySelector(`#group-${groupKey} .config-item:has(#enabled_${groupKey}_${itemKey})`);
            if (itemDiv) {
                if (enabled) {
                    itemDiv.classList.remove('disabled');
                } else {
                    itemDiv.classList.add('disabled');
                }
            }

            // 更新状态徽章
            const statusContainer = itemDiv.querySelector('.config-status-badges');
            if (statusContainer) {
                const isRequired = document.getElementById(`required_${groupKey}_${itemKey}`).checked;
                statusContainer.innerHTML = `
                    ${enabled ? '<span class="badge bg-success">生效</span>' : '<span class="badge bg-secondary">禁用</span>'}
                    ${isRequired ? '<span class="badge bg-warning">必填</span>' : ''}
                `;
            }
        }

        // 切换分组展开/折叠
        function toggleGroup(groupKey) {
            const content = document.getElementById(`group-${groupKey}`);
            const toggle = document.querySelector(`[onclick="toggleGroup('${groupKey}')"] i`);

            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.className = 'fas fa-chevron-down';
            } else {
                content.style.display = 'none';
                toggle.className = 'fas fa-chevron-right';
            }
        }

        // 保存模板
        async function saveTemplate() {
            try {
                showLoading(true);

                const templateData = {
                    name: document.getElementById('templateName').value,
                    description: document.getElementById('templateDescription').value,
                    is_default: document.getElementById('isDefaultTemplate').checked,
                    is_active: document.getElementById('isActiveTemplate').checked,
                    items: collectConfigItems()
                };

                const url = currentTemplateId ? `/api/config-templates/${currentTemplateId}` : '/api/config-templates';
                const method = currentTemplateId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(templateData)
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadTemplates(); // 重新加载模板列表
                    if (!currentTemplateId) {
                        currentTemplateId = data.data.id;
                    }
                } else {
                    showAlert('保存失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('保存模板失败:', error);
                showAlert('保存模板失败', 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 收集配置项数据
        function collectConfigItems() {
            const items = [];

            Object.keys(configGroups).forEach(groupKey => {
                const group = configGroups[groupKey];

                Object.keys(group.items).forEach(itemKey => {
                    const itemConfig = group.items[itemKey];
                    const isEnabled = document.getElementById(`enabled_${groupKey}_${itemKey}`).checked;
                    const isRequired = document.getElementById(`required_${groupKey}_${itemKey}`).checked;

                    let value;
                    const valueInput = document.getElementById(`value_${groupKey}_${itemKey}`);

                    if (itemConfig.type === 'boolean') {
                        value = valueInput.checked.toString();
                    } else {
                        value = valueInput.value;
                    }

                    items.push({
                        config_group: groupKey,
                        config_key: itemKey,
                        config_value: value,
                        value_type: itemConfig.type,
                        is_enabled: isEnabled,
                        is_required: isRequired,
                        description: itemConfig.description
                    });
                });
            });

            return items;
        }

        // 创建新模板
        function createNewTemplate() {
            currentTemplateId = null;
            document.getElementById('currentTemplateName').textContent = '新建配置模板';
            document.getElementById('templateName').value = '';
            document.getElementById('templateDescription').value = '';
            document.getElementById('isDefaultTemplate').checked = false;
            document.getElementById('isActiveTemplate').checked = true;

            // 渲染空的配置项（使用默认值）
            renderConfigItems([]);

            document.getElementById('welcomePage').style.display = 'none';
            document.getElementById('configEditor').style.display = 'block';

            // 清除模板列表选中状态
            document.querySelectorAll('#templatesList .list-group-item').forEach(item => {
                item.classList.remove('active');
            });
        }

        // 预览模板
        async function previewTemplate() {
            if (!currentTemplateId) {
                showAlert('请先保存模板', 'warning');
                return;
            }

            try {
                const response = await fetch(`/api/config-templates/${currentTemplateId}/preview`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fileId: 'sample-file-id-12345'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('previewContent').textContent = JSON.stringify(data.data, null, 2);
                    new bootstrap.Modal(document.getElementById('previewModal')).show();
                } else {
                    showAlert('预览失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('预览失败:', error);
                showAlert('预览失败', 'danger');
            }
        }

        // 复制模板
        async function copyTemplate() {
            if (!currentTemplateId) {
                showAlert('请先选择要复制的模板', 'warning');
                return;
            }

            const newName = prompt('请输入新模板名称:');
            if (!newName) return;

            try {
                showLoading(true);
                const response = await fetch(`/api/config-templates/${currentTemplateId}/copy`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        newName: newName
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    await loadTemplates();
                } else {
                    showAlert('复制失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('复制失败:', error);
                showAlert('复制失败', 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 导出模板
        function exportTemplate() {
            if (!currentTemplateId) {
                showAlert('请先选择要导出的模板', 'warning');
                return;
            }

            window.open(`/api/config-templates/${currentTemplateId}/export`, '_blank');
        }

        // 工具函数
        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        function showAlert(message, type) {
            // 简单的提示实现，可以替换为更好的通知组件
            alert(message);
        }

        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
    </script>
</body>

</html>