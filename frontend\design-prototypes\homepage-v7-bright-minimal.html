<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 明亮简约风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #fafbfc;
      color: #202124;
      line-height: 1.5;
    }

    .app-layout {
      display: flex;
      min-height: 100vh;
    }

    /* 左侧导航栏 */
    .navigation-sidebar {
      width: 240px;
      background: #ffffff;
      border-right: 1px solid #e8eaed;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 1000;
    }

    .brand-section {
      padding: 24px 20px;
      border-bottom: 1px solid #e8eaed;
    }

    .brand-logo {
      font-size: 22px;
      font-weight: 600;
      color: #1a73e8;
      margin-bottom: 6px;
    }

    .brand-tagline {
      font-size: 13px;
      color: #5f6368;
    }

    .nav-menu {
      padding: 16px 0;
    }

    .nav-category {
      margin-bottom: 24px;
    }

    .nav-category-label {
      padding: 8px 20px;
      font-size: 11px;
      color: #80868b;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.8px;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 10px 20px;
      color: #3c4043;
      text-decoration: none;
      transition: all 0.2s ease;
      position: relative;
    }

    .nav-item:hover {
      background: #f1f3f4;
    }

    .nav-item.active {
      background: #e8f0fe;
      color: #1a73e8;
      border-right: 3px solid #1a73e8;
    }

    .nav-item-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-item-text {
      font-size: 14px;
      font-weight: 400;
    }

    .nav-item-badge {
      margin-left: auto;
      background: #ea4335;
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 8px;
      min-width: 16px;
      text-align: center;
      font-weight: 500;
    }

    /* 主要内容区域 */
    .main-content {
      margin-left: 240px;
      flex: 1;
    }

    /* 顶部栏 */
    .top-bar {
      background: #ffffff;
      height: 64px;
      border-bottom: 1px solid #e8eaed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32px;
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .page-header {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .page-title {
      font-size: 22px;
      font-weight: 400;
      color: #202124;
    }

    .page-breadcrumb {
      font-size: 14px;
      color: #5f6368;
    }

    .top-bar-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .search-box {
      display: flex;
      align-items: center;
      background: #f1f3f4;
      border-radius: 24px;
      padding: 8px 16px;
      min-width: 280px;
    }

    .search-box input {
      border: none;
      background: none;
      outline: none;
      font-size: 14px;
      color: #202124;
      flex: 1;
      margin-left: 8px;
    }

    .search-box input::placeholder {
      color: #5f6368;
    }

    .user-menu {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 6px 12px;
      border-radius: 20px;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .user-menu:hover {
      background: #f1f3f4;
    }

    .user-avatar-small {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
      font-size: 14px;
    }

    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: #202124;
    }

    /* 内容区域 */
    .content-area {
      padding: 32px;
    }

    /* 概览卡片 */
    .overview-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-bottom: 32px;
    }

    .overview-card {
      background: #ffffff;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      padding: 24px;
      transition: all 0.2s ease;
    }

    .overview-card:hover {
      box-shadow: 0 1px 3px rgba(60, 64, 67, 0.3);
      border-color: #dadce0;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    .card-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
    }

    .card-icon.blue { background: #e8f0fe; color: #1a73e8; }
    .card-icon.green { background: #e6f4ea; color: #137333; }
    .card-icon.orange { background: #fef7e0; color: #ea8600; }
    .card-icon.red { background: #fce8e6; color: #d33b01; }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 500;
      padding: 4px 8px;
      border-radius: 12px;
    }

    .trend-positive {
      background: #e6f4ea;
      color: #137333;
    }

    .trend-negative {
      background: #fce8e6;
      color: #d33b01;
    }

    .card-number {
      font-size: 32px;
      font-weight: 400;
      color: #202124;
      margin-bottom: 8px;
    }

    .card-label {
      font-size: 14px;
      color: #5f6368;
      margin-bottom: 12px;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #80868b;
    }

    /* 主要内容网格 */
    .content-grid {
      display: grid;
      grid-template-columns: 1fr 320px;
      gap: 32px;
    }

    .main-panel {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .content-section {
      background: #ffffff;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      overflow: hidden;
    }

    .section-header {
      padding: 20px 24px;
      border-bottom: 1px solid #e8eaed;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #202124;
    }

    .section-action {
      color: #1a73e8;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background 0.2s ease;
    }

    .section-action:hover {
      background: #e8f0fe;
    }

    /* 快捷操作 */
    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      padding: 24px;
    }

    .quick-action {
      background: #fafbfc;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .quick-action:hover {
      background: #f8f9fa;
      box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
      transform: translateY(-1px);
    }

    .action-icon {
      width: 48px;
      height: 48px;
      margin: 0 auto 12px;
      background: #e8f0fe;
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #1a73e8;
    }

    .action-title {
      font-size: 14px;
      font-weight: 500;
      color: #202124;
      margin-bottom: 4px;
    }

    .action-desc {
      font-size: 12px;
      color: #5f6368;
      line-height: 1.4;
    }

    /* 文档列表 */
    .document-list {
      padding: 0 24px 24px;
    }

    .document-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 12px 0;
      border-bottom: 1px solid #f1f3f4;
      transition: background 0.2s ease;
    }

    .document-item:hover {
      background: #f8f9fa;
      margin: 0 -24px;
      padding: 12px 24px;
      border-radius: 4px;
    }

    .document-item:last-child {
      border-bottom: none;
    }

    .doc-icon {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: white;
      font-weight: 500;
    }

    .doc-word { background: #4285f4; }
    .doc-excel { background: #0f9d58; }
    .doc-ppt { background: #ea4335; }
    .doc-pdf { background: #9c27b0; }

    .doc-details {
      flex: 1;
    }

    .doc-name {
      font-size: 14px;
      font-weight: 500;
      color: #202124;
      margin-bottom: 2px;
    }

    .doc-info {
      font-size: 12px;
      color: #5f6368;
    }

    .doc-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-editing {
      background: #fff3e0;
      color: #ea8600;
    }

    .status-shared {
      background: #e8f0fe;
      color: #1a73e8;
    }

    .status-completed {
      background: #e6f4ea;
      color: #137333;
    }

    .doc-menu {
      color: #5f6368;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background 0.2s ease;
    }

    .doc-menu:hover {
      background: #f1f3f4;
    }

    /* 侧边栏 */
    .sidebar-panels {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .sidebar-panel {
      background: #ffffff;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      overflow: hidden;
    }

    /* 用户信息面板 */
    .user-info-panel {
      padding: 24px;
      text-align: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .user-avatar-large {
      width: 72px;
      height: 72px;
      margin: 0 auto 16px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      font-weight: 500;
      color: white;
    }

    .user-display-name {
      font-size: 18px;
      font-weight: 500;
      color: #202124;
      margin-bottom: 4px;
    }

    .user-role {
      font-size: 14px;
      color: #5f6368;
      margin-bottom: 20px;
    }

    .user-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      padding-top: 16px;
      border-top: 1px solid #e8eaed;
    }

    .user-stat {
      text-align: center;
    }

    .stat-value {
      font-size: 16px;
      font-weight: 500;
      color: #202124;
    }

    .stat-label {
      font-size: 12px;
      color: #5f6368;
    }

    /* 系统监控面板 */
    .monitoring-panel {
      padding: 20px;
    }

    .monitor-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid #f1f3f4;
    }

    .monitor-item:last-child {
      border-bottom: none;
    }

    .monitor-label {
      font-size: 14px;
      color: #5f6368;
    }

    .monitor-value {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .status-healthy { background: #34a853; }
    .status-warning { background: #fbbc04; }
    .status-critical { background: #ea4335; }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .navigation-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .navigation-sidebar.open {
        transform: translateX(0);
      }
      
      .main-content {
        margin-left: 0;
      }
      
      .content-grid {
        grid-template-columns: 1fr;
      }
      
      .overview-cards {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .content-area {
        padding: 20px;
      }
      
      .overview-cards {
        grid-template-columns: 1fr;
      }
      
      .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .user-stats {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .quick-actions-grid {
        grid-template-columns: 1fr;
      }
      
      .search-box {
        min-width: 200px;
      }
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <!-- 左侧导航栏 -->
    <nav class="navigation-sidebar">
      <div class="brand-section">
        <div class="brand-logo">OnlyOffice</div>
        <div class="brand-tagline">企业文档管理系统</div>
      </div>
      
      <div class="nav-menu">
        <div class="nav-category">
          <div class="nav-category-label">主要功能</div>
          <a href="#" class="nav-item active">
            <span class="nav-item-icon">🏠</span>
            <span class="nav-item-text">首页</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">📄</span>
            <span class="nav-item-text">文档管理</span>
            <span class="nav-item-badge">8</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">📋</span>
            <span class="nav-item-text">模板管理</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">👥</span>
            <span class="nav-item-text">用户管理</span>
          </a>
        </div>
        
        <div class="nav-category">
          <div class="nav-category-label">系统管理</div>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">⚙️</span>
            <span class="nav-item-text">系统配置</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">🔐</span>
            <span class="nav-item-text">权限管理</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">🔔</span>
            <span class="nav-item-text">OnlyOffice配置</span>
          </a>
        </div>
        
        <div class="nav-category">
          <div class="nav-category-label">数据分析</div>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">📊</span>
            <span class="nav-item-text">数据报表</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">📈</span>
            <span class="nav-item-text">统计分析</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-item-icon">📋</span>
            <span class="nav-item-text">系统日志</span>
          </a>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="top-bar">
        <div class="page-header">
          <h1 class="page-title">首页</h1>
          <span class="page-breadcrumb">/ 仪表板</span>
        </div>
        <div class="top-bar-actions">
          <div class="search-box">
            <span style="color: #5f6368;">🔍</span>
            <input type="text" placeholder="搜索文档、用户或设置">
          </div>
          <div class="user-menu">
            <div class="user-avatar-small">管</div>
            <span class="user-name">管理员</span>
          </div>
        </div>
      </header>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 概览卡片 -->
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-header">
              <div class="card-icon blue">📄</div>
              <div class="card-trend trend-positive">↗ +12%</div>
            </div>
            <div class="card-number">1,234</div>
            <div class="card-label">文档总数</div>
            <div class="card-footer">
              <span>本月: +145</span>
              <span>活跃: 892</span>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-header">
              <div class="card-icon green">👥</div>
              <div class="card-trend trend-positive">↗ +8%</div>
            </div>
            <div class="card-number">87</div>
            <div class="card-label">活跃用户</div>
            <div class="card-footer">
              <span>在线: 23</span>
              <span>本周: 65</span>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-header">
              <div class="card-icon orange">💾</div>
              <div class="card-trend trend-positive">↗ +15%</div>
            </div>
            <div class="card-number">823GB</div>
            <div class="card-label">存储使用</div>
            <div class="card-footer">
              <span>可用: 177GB</span>
              <span>82.3%</span>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-header">
              <div class="card-icon red">❤️</div>
              <div class="card-trend trend-positive">↗ +2%</div>
            </div>
            <div class="card-number">99.1%</div>
            <div class="card-label">系统正常运行时间</div>
            <div class="card-footer">
              <span>响应: 165ms</span>
              <span>正常</span>
            </div>
          </div>
        </div>

        <!-- 主要内容网格 -->
        <div class="content-grid">
          <!-- 主面板 -->
          <div class="main-panel">
            <!-- 快捷操作 -->
            <div class="content-section">
              <div class="section-header">
                <h2 class="section-title">快捷操作</h2>
                <span class="section-action">自定义</span>
              </div>
              <div class="quick-actions-grid">
                <div class="quick-action">
                  <div class="action-icon">📝</div>
                  <div class="action-title">创建文档</div>
                  <div class="action-desc">快速创建新的文档</div>
                </div>
                <div class="quick-action">
                  <div class="action-icon">📤</div>
                  <div class="action-title">上传文件</div>
                  <div class="action-desc">上传本地文档</div>
                </div>
                <div class="quick-action">
                  <div class="action-icon">📋</div>
                  <div class="action-title">模板库</div>
                  <div class="action-desc">使用文档模板</div>
                </div>
                <div class="quick-action">
                  <div class="action-icon">👥</div>
                  <div class="action-title">团队协作</div>
                  <div class="action-desc">邀请团队成员</div>
                </div>
                <div class="quick-action">
                  <div class="action-icon">📊</div>
                  <div class="action-title">数据分析</div>
                  <div class="action-desc">查看使用统计</div>
                </div>
                <div class="quick-action">
                  <div class="action-icon">⚙️</div>
                  <div class="action-title">系统设置</div>
                  <div class="action-desc">配置系统参数</div>
                </div>
              </div>
            </div>

            <!-- 最近文档 -->
            <div class="content-section">
              <div class="section-header">
                <h2 class="section-title">最近文档</h2>
                <span class="section-action">查看全部</span>
              </div>
              <div class="document-list">
                <div class="document-item">
                  <div class="doc-icon doc-word">W</div>
                  <div class="doc-details">
                    <div class="doc-name">产品需求文档.docx</div>
                    <div class="doc-info">张三 · 2小时前 · 2.1MB</div>
                  </div>
                  <div class="doc-status status-editing">编辑中</div>
                  <div class="doc-menu">⋮</div>
                </div>
                <div class="document-item">
                  <div class="doc-icon doc-excel">E</div>
                  <div class="doc-details">
                    <div class="doc-name">财务报表Q4.xlsx</div>
                    <div class="doc-info">李四 · 1天前 · 1.5MB</div>
                  </div>
                  <div class="doc-status status-shared">已共享</div>
                  <div class="doc-menu">⋮</div>
                </div>
                <div class="document-item">
                  <div class="doc-icon doc-ppt">P</div>
                  <div class="doc-details">
                    <div class="doc-name">产品发布演示.pptx</div>
                    <div class="doc-info">王五 · 3天前 · 12.8MB</div>
                  </div>
                  <div class="doc-status status-completed">已完成</div>
                  <div class="doc-menu">⋮</div>
                </div>
                <div class="document-item">
                  <div class="doc-icon doc-pdf">P</div>
                  <div class="doc-details">
                    <div class="doc-name">用户手册.pdf</div>
                    <div class="doc-info">赵六 · 1周前 · 5.6MB</div>
                  </div>
                  <div class="doc-status status-completed">已完成</div>
                  <div class="doc-menu">⋮</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 侧边栏 -->
          <div class="sidebar-panels">
            <!-- 用户信息 -->
            <div class="sidebar-panel">
              <div class="user-info-panel">
                <div class="user-avatar-large">管</div>
                <div class="user-display-name">系统管理员</div>
                <div class="user-role">Administrator</div>
                <div class="user-stats">
                  <div class="user-stat">
                    <div class="stat-value">145</div>
                    <div class="stat-label">我的文档</div>
                  </div>
                  <div class="user-stat">
                    <div class="stat-value">23</div>
                    <div class="stat-label">协作项目</div>
                  </div>
                  <div class="user-stat">
                    <div class="stat-value">87</div>
                    <div class="stat-label">团队成员</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统监控 -->
            <div class="sidebar-panel">
              <div class="section-header">
                <h3 class="section-title">系统状态</h3>
                <span class="section-action">详情</span>
              </div>
              <div class="monitoring-panel">
                <div class="monitor-item">
                  <span class="monitor-label">数据库</span>
                  <div class="monitor-value">
                    <span class="status-indicator status-healthy"></span>
                    <span style="color: #34a853;">正常</span>
                  </div>
                </div>
                <div class="monitor-item">
                  <span class="monitor-label">OnlyOffice</span>
                  <div class="monitor-value">
                    <span class="status-indicator status-healthy"></span>
                    <span style="color: #34a853;">运行中</span>
                  </div>
                </div>
                <div class="monitor-item">
                  <span class="monitor-label">FileNet</span>
                  <div class="monitor-value">
                    <span class="status-indicator status-warning"></span>
                    <span style="color: #fbbc04;">连接缓慢</span>
                  </div>
                </div>
                <div class="monitor-item">
                  <span class="monitor-label">CPU使用率</span>
                  <div class="monitor-value">
                    <span style="color: #202124;">25%</span>
                  </div>
                </div>
                <div class="monitor-item">
                  <span class="monitor-label">内存使用率</span>
                  <div class="monitor-value">
                    <span style="color: #202124;">68%</span>
                  </div>
                </div>
                <div class="monitor-item">
                  <span class="monitor-label">磁盘空间</span>
                  <div class="monitor-value">
                    <span style="color: #fbbc04;">82%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // 导航菜单交互
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
        item.classList.add('active');
      });
    });

    // 快捷操作交互
    document.querySelectorAll('.quick-action').forEach(action => {
      action.addEventListener('click', () => {
        const title = action.querySelector('.action-title').textContent;
        console.log('执行操作:', title);
      });
    });

    // 文档项交互
    document.querySelectorAll('.document-item').forEach(item => {
      item.addEventListener('click', () => {
        const docName = item.querySelector('.doc-name').textContent;
        console.log('打开文档:', docName);
      });
    });

    // 搜索框交互
    document.querySelector('.search-box input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        console.log('搜索:', e.target.value);
      }
    });

    // 移动端侧边栏切换
    function toggleSidebar() {
      const sidebar = document.querySelector('.navigation-sidebar');
      sidebar.classList.toggle('open');
    }

    // 响应式处理
    window.addEventListener('resize', () => {
      if (window.innerWidth > 1024) {
        document.querySelector('.navigation-sidebar').classList.remove('open');
      }
    });
  </script>
</body>
</html> 