import{L as q,r as C,z as p,d as I,c as O,e as r,t as T,n as N,G as j,h as b,_ as D,Z as P,ab as z,o as M,M as A,b as k,x as B,s as L,ac as H}from"./index-5218909a.js";import{i as Q}from"./editor-utils-5bc6f677.js";function E(n,s){const o=new Error(n);return o.name="EditorError",s&&console.error("原始错误:",s),o}function de(n){const s=q(),o=C(null),l=C(null),u=C("加载中..."),t=C(!1),i=C(!1),_=()=>{const e=s.query,c={};return e.template&&typeof e.template=="string"&&(c.template=e.template),e.hideChat!==void 0&&(c.hideChat=e.hideChat==="true"),e.hideComments!==void 0&&(c.hideComments=e.hideComments==="true"),e.readonly!==void 0&&(c.readonly=e.readonly==="true"),e.userId&&typeof e.userId=="string"&&(c.userId=e.userId),e.userName&&typeof e.userName=="string"&&(c.userName=e.userName),console.log("🔧 [Editor] 从路由提取配置参数:",c),c},S=async e=>{const f={..._(),...e};console.log("🔧 [Editor] 最终配置参数:",f);const $=new URLSearchParams;f&&Object.entries(f).forEach(([y,w])=>{w!=null&&$.append(y,String(w))});const g=`/api/editor/${n.value}/config${$.toString()?`?${$.toString()}`:""}`;console.log("🔧 [Editor] 请求配置URL:",g);try{const y=await fetch(g,{method:"GET",headers:{"Content-Type":"application/json"}});if(!y.ok)throw E(`获取编辑器配置失败: ${y.status} ${y.statusText}`);const w=await y.json();if(!w.success||!w.data)throw E(w.message||"获取编辑器配置失败");return console.log("✅ [Editor] 编辑器配置获取成功:",w.data),w.data}catch(y){throw y instanceof Error?y:E("获取编辑器配置时发生未知错误")}},v=async e=>new Promise((c,f)=>{if(window.DocsAPI){c();return}const $=document.querySelector(`script[src="${e}"]`);if($){$.addEventListener("load",()=>c()),$.addEventListener("error",()=>f(new Error("OnlyOffice脚本加载失败")));return}const g=document.createElement("script");g.src=e,g.type="text/javascript",g.async=!0,g.onload=()=>{console.log("OnlyOffice API脚本加载成功"),c()},g.onerror=()=>{console.error("OnlyOffice API脚本加载失败"),f(new Error("OnlyOffice脚本加载失败"))},document.head.appendChild(g)}),a=async e=>{var c;if(i.value){console.warn("编辑器正在初始化中...");return}if(!n.value)throw E("文件ID不能为空");i.value=!0;try{console.log("🚀 [Editor] 开始初始化编辑器，文档ID:",n.value);const f=await S(e);if(f.apiUrl&&await v(f.apiUrl),!window.DocsAPI)throw E("OnlyOffice API未加载，请确保OnlyOffice服务器正常运行");u.value=f.document.title||"未知文档";const g={...f,events:{onDocumentReady:()=>{console.log("✅ [Editor] 文档已准备就绪"),t.value=!0},onDocumentStateChange:y=>{console.log("📝 [Editor] 文档状态变更:",y)},onError:y=>{var R;console.error("❌ [Editor] 编辑器错误:",y),t.value=!1;const w=((R=y.data)==null?void 0:R.errorDescription)||"编辑器发生未知错误";console.error("OnlyOffice编辑器错误:",w)}},height:"100%",width:"100%",editorConfig:{...f.editorConfig,customization:{...(c=f.editorConfig)==null?void 0:c.customization,autosave:!0,forcesave:!1,compactToolbar:!1,toolbarNoTabs:!1,zoom:100,hideRightMenu:!1,hideRulers:!1,showHorizontalScroll:!1,showVerticalScroll:!1,uiTheme:"theme-light",integrationMode:"embed"}}};l.value=g,console.log("🔧 [Editor] 最终编辑器配置:",g),o.value=new window.DocsAPI.DocEditor("editor-container",g),console.log("✅ [Editor] 编辑器初始化完成")}catch(f){throw console.error("❌ [Editor] 编辑器初始化失败:",f),f instanceof Error?f:E("编辑器初始化失败：未知错误")}finally{i.value=!1}},d=()=>{if(o.value&&typeof o.value.destroyEditor=="function")try{o.value.destroyEditor(),console.log("编辑器已销毁")}catch(e){console.error("销毁编辑器失败:",e)}o.value=null,l.value=null,t.value=!1,u.value=""},m=async()=>{if(!t.value)throw E("编辑器未就绪，无法执行保存操作");try{const e=await fetch(`/api/editor/save/${n.value}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({force:!0,timestamp:new Date().toISOString()})});if(!e.ok)throw E(`强制保存失败: ${e.status} ${e.statusText}`);const c=await e.json();if(!c.success)throw E(c.message||"强制保存失败")}catch(e){throw e instanceof Error?e:E("强制保存时发生未知错误")}},h=async e=>{d(),await a(e)};return{docEditor:p(()=>o.value),editorConfig:p(()=>l.value),docTitle:p(()=>u.value),isEditorReady:p(()=>t.value),isInitializing:p(()=>i.value),initializeEditor:a,destroyEditor:d,forceSave:m,reloadEditor:h,fetchEditorConfig:S}}function fe(n){const s=C({status:"saved",message:"已保存",lastSaveTime:new Date});let o=null;const l=(v,a)=>{s.value={status:v,message:a,lastSaveTime:v==="saved"?new Date:s.value.lastSaveTime}},u=async()=>{try{const v=await fetch(`/api/editor/save-status/${n.value}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(v.ok){const a=await v.json();if(a.success&&a.data){const{lastSaved:d}=a.data;if(d){const m=new Date(d),h=Math.abs(new Date().getTime()-m.getTime());if(h<6e4)l("saved","已保存");else{const e=Math.floor(h/6e4);l("saved",`已保存 (${e}分钟前)`)}}}}}catch(v){console.error("检查保存状态失败:",v)}},t=()=>{o&&clearInterval(o),o=window.setInterval(u,3e4)},i=()=>{o&&(clearInterval(o),o=null)},_=p(()=>{switch(s.value.status){case"saved":return"status-saved";case"saving":return"status-saving";case"editing":return"status-editing";case"error":return"status-error";default:return"status-saved"}}),S=p(()=>{switch(s.value.status){case"saved":return"#2ecc71";case"saving":return"#f39c12";case"editing":return"#3498db";case"error":return"#e74c3c";default:return"#2ecc71"}});return{saveStatus:p(()=>s.value),getStatusClass:_,getStatusColor:S,updateSaveStatus:l,checkSaveStatus:u,startAutoCheck:t,stopAutoCheck:i}}function ve(){const n=C({show:!1,type:"info",message:"",duration:5e3});let s=null;const o=(m,h="info",e=5e3)=>{s&&(clearTimeout(s),s=null),n.value={show:!0,type:h,message:m,duration:e},e>0&&(s=window.setTimeout(()=>{l()},e)),console.log(`显示通知 [${h.toUpperCase()}]: ${m}`)},l=()=>{n.value.show=!1,s&&(clearTimeout(s),s=null),console.log("隐藏通知")},u=(m,h)=>{o(m,"success",h)},t=(m,h)=>{o(m,"error",h)},i=(m,h)=>{o(m,"warning",h)},_=(m,h)=>{o(m,"info",h)},S=p(()=>{switch(n.value.type){case"success":return"✓";case"error":return"✗";case"warning":return"⚠";case"info":default:return"ℹ"}}),v=p(()=>{switch(n.value.type){case"success":return"#2ecc71";case"error":return"#e74c3c";case"warning":return"#f39c12";case"info":default:return"#3498db"}}),a=p(()=>`notification notification-${n.value.type}`),d=p(()=>n.value.show);return{notification:p(()=>n.value),isShowing:d,getNotificationIcon:S,getNotificationColor:v,getNotificationClass:a,showNotification:o,hideNotification:l,showSuccess:u,showError:t,showWarning:i,showInfo:_}}const U={class:"editor-header"},G={class:"doc-title"},V={class:"header-actions"},W=["disabled"],x=["disabled"],F={class:"save-status"},J={class:"status-text"},Z=["disabled"],K={key:1,href:"/",class:"back-link"},X=I({__name:"EditorHeader",props:{docTitle:{type:String,required:!0},saveStatus:{type:Object,required:!0},isReady:{type:Boolean,required:!1,default:!1}},emits:["force-save","lock-document","unlock-document"],setup(n){const s=n,o=p(()=>Q()),l=()=>{window.opener?window.close():window.location.href="/"},u=()=>{switch(s.saveStatus.status){case"saved":return"#2ecc71";case"saving":return"#f39c12";case"editing":return"#3498db";case"error":return"#e74c3c";default:return"#2ecc71"}};return(t,i)=>(b(),O("div",U,[r("h1",G,T(t.docTitle),1),r("div",V,[r("button",{class:"action-button lock-button",onClick:i[0]||(i[0]=_=>t.$emit("lock-document")),disabled:!t.isReady}," 🔒 一键加密 ",8,W),r("button",{class:"action-button unlock-button",onClick:i[1]||(i[1]=_=>t.$emit("unlock-document")),disabled:!t.isReady}," 🔓 一键解锁 ",8,x),r("div",F,[r("span",{class:N(["status-indicator",t.saveStatus.status]),style:j({backgroundColor:u()})},null,6),r("span",J,T(t.saveStatus.message),1)]),r("button",{class:"action-button save-button",onClick:i[2]||(i[2]=_=>t.$emit("force-save")),disabled:!t.isReady||t.saveStatus.status==="saving"}," 💾 强制保存 ",8,Z),o.value?(b(),O("button",{key:0,class:"action-button close-button",onClick:l}," ❌ 关闭窗口 ")):(b(),O("a",K,"返回首页"))])]))}});const pe=D(X,[["__scopeId","data-v-4b502bc3"],["__file","D:/Code/OnlyOffice/frontend/src/pages/editor/components/EditorHeader.vue"]]),Y={class:"editor-container"},ee={key:0,class:"loading-overlay"},te={key:1,class:"error-overlay"},oe={class:"error-content"},se={class:"error-message"},ne=I({__name:"EditorContainer",props:{isReady:{type:Boolean,required:!0},config:{type:[Object,null],required:!0}},emits:["editor-ready","document-state-change","save","error"],setup(n,{expose:s,emit:o}){const l=n,u=o,t=C(!1),i=C(""),_=()=>{t.value=!1,i.value="",u("editor-ready")},S=a=>{t.value=!0,i.value=a},v=()=>{l.isReady&&z(()=>{const a=document.getElementById("editor-container");if(a){const d=a.querySelector("iframe");d&&(d.style.width="100%",d.style.height="100%")}})};return P(()=>l.config,a=>{a&&(t.value=!1,i.value="")}),P(()=>l.isReady,a=>{a&&(t.value=!1,z(()=>{v()}))}),M(()=>{console.log("编辑器容器已挂载"),window.addEventListener("resize",v)}),A(()=>{console.log("编辑器容器已卸载"),window.removeEventListener("resize",v)}),s({setError:S,handleResize:v}),(a,d)=>(b(),O("div",Y,[k(" 加载状态 "),a.isReady?k("v-if",!0):(b(),O("div",ee,d[0]||(d[0]=[r("div",{class:"loading-content"},[r("div",{class:"loading-spinner"}),r("p",{class:"loading-text"},"正在加载编辑器...")],-1)]))),k(" OnlyOffice编辑器容器 "),r("div",{id:"editor-container",class:N(["editor-iframe-container",{"editor-ready":a.isReady}])},null,2),k(" 错误状态 "),t.value?(b(),O("div",te,[r("div",oe,[d[1]||(d[1]=r("div",{class:"error-icon"},"⚠️",-1)),d[2]||(d[2]=r("h3",{class:"error-title"},"编辑器加载失败",-1)),r("p",se,T(i.value),1),r("button",{class:"retry-button",onClick:_},"重试")])])):k("v-if",!0)]))}});const he=D(ne,[["__scopeId","data-v-d32f7f71"],["__file","D:/Code/OnlyOffice/frontend/src/pages/editor/components/EditorContainer.vue"]]),ae={class:"notification-content"},ie={class:"notification-icon"},re={class:"notification-message"},ce=I({__name:"NotificationPanel",props:{notification:{type:Object,required:!0}},emits:["hide"],setup(n){const s=n,o=p(()=>{switch(s.notification.type){case"success":return"✓";case"error":return"✗";case"warning":return"⚠";case"info":default:return"ℹ"}}),l=p(()=>`notification-${s.notification.type}`);return(u,t)=>(b(),B(H,{name:"notification"},{default:L(()=>[u.notification.show?(b(),O("div",{key:0,class:N(["notification-panel",l.value])},[r("div",ae,[r("span",ie,T(o.value),1),r("span",re,T(u.notification.message),1),r("button",{class:"notification-close",onClick:t[0]||(t[0]=i=>u.$emit("hide")),"aria-label":"关闭通知"},"×")]),k(" 进度条 "),u.notification.duration>0?(b(),O("div",{key:0,class:"notification-progress",style:j({animationDuration:`${u.notification.duration}ms`})},null,4)):k("v-if",!0)],2)):k("v-if",!0)]),_:1}))}});const me=D(ce,[["__scopeId","data-v-7979ddd6"],["__file","D:/Code/OnlyOffice/frontend/src/pages/editor/components/NotificationPanel.vue"]]);export{pe as E,me as N,fe as a,ve as b,he as c,de as u};
