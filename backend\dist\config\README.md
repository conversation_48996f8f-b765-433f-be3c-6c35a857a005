# 🔧 统一环境变量配置管理系统

## 📋 概述

本系统实现了统一的环境变量配置管理，所有项目模块都使用根目录的 `.env` 文件，提供类型安全的配置访问和优雅的管理机制。

## 🏗️ 架构设计

```
project-root/
├── .env                           # 👑 统一环境变量文件
├── backend/
│   └── src/
│       └── config/
│           ├── env.config.ts      # 🔧 核心配置管理
│           ├── app.config.ts      # 🚀 NestJS应用配置
│           └── README.md          # 📖 本文档
└── frontend/                      # 前端项目(未来)
```

## 📁 核心文件说明

### 1. `env.config.ts` - 核心配置管理器

```typescript
// 自动加载根目录 .env 文件
import envConfig from './config/env.config';

// 类型安全的配置访问
console.log(envConfig.database.host);     // 数据库主机
console.log(envConfig.jwt.secret);        // JWT密钥
console.log(envConfig.onlyoffice.serverUrl); // OnlyOffice服务器
```

### 2. `app.config.ts` - NestJS集成配置

```typescript
// NestJS模块中使用
import { configModuleOptions } from './config/app.config';

@Module({
  imports: [ConfigModule.forRoot(configModuleOptions)],
})
export class AppModule {}
```

## 🚀 使用方法

### 基本使用

```typescript
// 在任何 TypeScript 文件中导入配置
import envConfig from '../config/env.config';

// 直接访问配置值
const dbHost = envConfig.database.host;
const jwtSecret = envConfig.jwt.secret;
const port = envConfig.port;
```

### 在服务中使用

```typescript
import { Injectable } from '@nestjs/common';
import envConfig from '../config/env.config';

@Injectable()
export class DatabaseService {
  private async connect() {
    const config = {
      host: envConfig.database.host,
      port: envConfig.database.port,
      user: envConfig.database.user,
      password: envConfig.database.password,
      database: envConfig.database.name,
    };
    // 使用配置连接数据库
  }
}
```

### 在控制器中使用

```typescript
import { Controller } from '@nestjs/common';
import envConfig from '../config/env.config';

@Controller('api')
export class ApiController {
  getServerInfo() {
    return {
      host: envConfig.server.host,
      port: envConfig.port,
      environment: envConfig.node_env,
    };
  }
}
```

## 📝 配置项分类

### 🔧 应用基础配置
```typescript
envConfig.node_env          // 运行环境
envConfig.port              // 服务端口
envConfig.frontend_port     // 前端端口
```

### 🗄️ 数据库配置
```typescript
envConfig.database.host     // 数据库主机
envConfig.database.port     // 数据库端口
envConfig.database.name     // 数据库名称
envConfig.database.user     // 用户名
envConfig.database.password // 密码
```

### 🔐 JWT认证配置
```typescript
envConfig.jwt.secret        // JWT密钥
envConfig.jwt.expiresIn     // 过期时间
```

### 🌐 CORS配置
```typescript
envConfig.cors.origin       // 允许的域名
envConfig.cors.allowedOrigins // 允许的域名数组
```

### 📄 OnlyOffice配置
```typescript
envConfig.onlyoffice.serverUrl       // 服务器地址
envConfig.onlyoffice.documentServerUrl // 文档服务器地址
envConfig.onlyoffice.secretKey       // 密钥
```

### 🗂️ FileNet配置
```typescript
envConfig.filenet.host      // FileNet主机
envConfig.filenet.port      // 端口
envConfig.filenet.username  // 用户名
envConfig.filenet.password  // 密码
```

### 📁 文件存储配置
```typescript
envConfig.storage.uploadPath        // 上传路径
envConfig.storage.tmpPath           // 临时路径
envConfig.storage.maxFileSize       // 最大文件大小
envConfig.storage.allowedFileTypes  // 允许的文件类型
```

### 🔄 缓存配置
```typescript
envConfig.cache.type        // 缓存类型 (memory/redis)
envConfig.cache.ttl         // 缓存时间
envConfig.cache.maxSize     // 最大缓存数量
```

### 📊 监控配置
```typescript
envConfig.monitoring.enabled              // 是否启用监控
envConfig.monitoring.slowQueryThreshold   // 慢查询阈值
envConfig.monitoring.requestTimeout       // 请求超时时间
```

## 🔒 安全特性

### 1. 敏感信息保护
```typescript
// 配置打印时自动隐藏敏感信息
printConfig(); // 密码会显示为 "***隐藏***"
```

### 2. 配置验证
```typescript
// 应用启动时自动验证必需配置项
validateConfig(); // 缺少必需配置会抛出错误
```

### 3. 类型安全
```typescript
// TypeScript 接口保证类型安全
interface AppConfig {
  database: {
    host: string;    // 字符串类型
    port: number;    // 数字类型
  };
}
```

## 📚 最佳实践

### 1. 配置项命名规范
```bash
# 环境变量命名: 大写 + 下划线
DB_HOST=*************
JWT_SECRET=your-secret-key

# 配置对象访问: 小写 + 驼峰
envConfig.database.host
envConfig.jwt.secret
```

### 2. 默认值设置
```typescript
// 始终提供合理的默认值
port: getEnvNumber('PORT', 3000),
debug: getEnvBoolean('DEBUG_MODE', false),
```

### 3. 配置分组
```typescript
// 按功能分组配置项
database: { ... },    // 数据库相关
jwt: { ... },         // 认证相关
onlyoffice: { ... },  // OnlyOffice相关
```

## 🛠️ 扩展配置

### 添加新配置项

1. **在根目录 `.env` 添加环境变量**
```bash
# 新功能配置
NEW_FEATURE_ENABLED=true
NEW_FEATURE_API_KEY=your-api-key
```

2. **在 `env.config.ts` 接口中添加定义**
```typescript
export interface AppConfig {
  // ... 其他配置 ...
  
  newFeature: {
    enabled: boolean;
    apiKey: string;
  };
}
```

3. **在配置对象中添加解析**
```typescript
export const envConfig: AppConfig = {
  // ... 其他配置 ...
  
  newFeature: {
    enabled: getEnvBoolean('NEW_FEATURE_ENABLED', false),
    apiKey: getEnvValue('NEW_FEATURE_API_KEY', ''),
  },
};
```

### 配置验证规则

```typescript
// 在 validateConfig() 中添加验证
const requiredFields = [
  'database.host',
  'jwt.secret',
  'newFeature.apiKey', // 新增验证
];
```

## 🔍 调试和故障排除

### 1. 查看当前配置
```typescript
import { printConfig } from './config/env.config';

// 打印当前配置(隐藏敏感信息)
printConfig();
```

### 2. 验证配置加载
```typescript
import { initializeConfig } from './config/env.config';

// 手动初始化和验证配置
initializeConfig();
```

### 3. 检查环境变量文件路径
```bash
# 配置文件应该在项目根目录
project-root/.env  ✅ 正确
backend/.env       ❌ 错误(已删除)
```

## 🚀 迁移指南

### 从旧配置系统迁移

1. **删除模块级别的 `.env` 文件**
2. **更新导入语句**
   ```typescript
   // 旧方式
   import { ConfigService } from '@nestjs/config';
   
   // 新方式
   import envConfig from '../config/env.config';
   ```

3. **更新配置访问方式**
   ```typescript
   // 旧方式
   this.configService.get('DB_HOST')
   
   // 新方式
   envConfig.database.host
   ```

## 🔄 版本历史

- **v2.0.0** - 统一配置管理系统发布
- **v1.0.0** - 原始分散配置系统

---

💡 **提示**: 使用统一配置系统可以确保项目配置的一致性和可维护性，同时提供类型安全和优雅的访问接口。 