import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsInt,
  IsEnum,
  IsUUID,
  Min<PERSON>ength,
  <PERSON><PERSON>ength,
  <PERSON>,
  <PERSON>,
  Matches,
} from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * 创建权限DTO
 * @description 用于创建新权限的数据传输对象
 * <AUTHOR> Team
 * @since 2024-12-19
 */
export class CreatePermissionDto {
  @ApiProperty({
    description: '权限代码',
    example: 'documents.read',
    pattern: '^[a-z_]+\\.[a-z_]+$',
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  @Matches(/^[a-z_]+\.[a-z_]+$/, {
    message: '权限代码格式必须为：模块.操作（如：documents.read）',
  })
  code: string;

  @ApiProperty({
    description: '权限名称',
    example: '查看文档',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({
    description: '权限描述',
    example: '允许用户查看和浏览文档内容',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: '所属模块',
    example: 'documents',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  module: string;

  @ApiPropertyOptional({
    description: '资源标识',
    example: 'document',
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  resource?: string;

  @ApiPropertyOptional({
    description: '操作类型',
    example: 'read',
    enum: ['create', 'read', 'update', 'delete', 'manage', 'execute'],
  })
  @IsEnum(['create', 'read', 'update', 'delete', 'manage', 'execute'])
  @IsOptional()
  action?: string;

  @ApiPropertyOptional({
    description: '权限条件JSON',
    example: { department: ['IT', 'Finance'] },
  })
  @IsOptional()
  conditions?: Record<string, unknown>;

  @ApiPropertyOptional({
    description: '权限是否启用',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: '排序序号',
    example: 10,
    minimum: 0,
    maximum: 999,
  })
  @IsInt()
  @Min(0)
  @Max(999)
  @IsOptional()
  sort_order?: number;
}

/**
 * 更新权限DTO
 * @description 用于更新权限信息的数据传输对象
 */
export class UpdatePermissionDto {
  @ApiPropertyOptional({
    description: '权限代码',
    example: 'documents.read',
    pattern: '^[a-z_]+\\.[a-z_]+$',
  })
  @IsString()
  @IsOptional()
  @MinLength(3)
  @MaxLength(100)
  @Matches(/^[a-z_]+\.[a-z_]+$/, {
    message: '权限代码格式必须为：模块.操作（如：documents.read）',
  })
  code?: string;

  @ApiPropertyOptional({
    description: '权限名称',
    example: '查看文档',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: '权限描述',
    example: '允许用户查看和浏览文档内容',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    description: '所属模块',
    example: 'documents',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @MaxLength(50)
  module?: string;

  @ApiPropertyOptional({
    description: '资源标识',
    example: 'document',
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  resource?: string;

  @ApiPropertyOptional({
    description: '操作类型',
    example: 'read',
    enum: ['create', 'read', 'update', 'delete', 'manage', 'execute'],
  })
  @IsEnum(['create', 'read', 'update', 'delete', 'manage', 'execute'])
  @IsOptional()
  action?: string;

  @ApiPropertyOptional({
    description: '权限条件JSON',
    example: { department: ['IT', 'Finance'] },
  })
  @IsOptional()
  conditions?: Record<string, unknown>;

  @ApiPropertyOptional({
    description: '权限是否启用',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: '排序序号',
    example: 10,
    minimum: 0,
    maximum: 999,
  })
  @IsInt()
  @Min(0)
  @Max(999)
  @IsOptional()
  sort_order?: number;
}

/**
 * 权限查询DTO
 * @description 用于查询权限列表的参数
 */
export class PermissionQueryDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
  })
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 1000,
  })
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(1000)
  @IsOptional()
  pageSize?: number;

  @ApiPropertyOptional({
    description: '搜索关键词',
    example: '文档',
  })
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: '模块筛选',
    example: 'documents',
  })
  @IsString()
  @IsOptional()
  module?: string;

  @ApiPropertyOptional({
    description: '操作类型筛选',
    example: 'read',
    enum: ['create', 'read', 'update', 'delete', 'manage', 'execute'],
  })
  @IsEnum(['create', 'read', 'update', 'delete', 'manage', 'execute'])
  @IsOptional()
  action?: string;

  @ApiPropertyOptional({
    description: '权限状态',
    example: 'active',
    enum: ['active', 'inactive'],
  })
  @IsEnum(['active', 'inactive'])
  @IsOptional()
  status?: 'active' | 'inactive';

  @ApiPropertyOptional({
    description: '排序字段',
    example: 'sort_order',
    enum: ['code', 'name', 'module', 'created_at', 'sort_order'],
  })
  @IsEnum(['code', 'name', 'module', 'created_at', 'sort_order'])
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({
    description: '排序方向',
    example: 'ASC',
    enum: ['ASC', 'DESC'],
  })
  @IsEnum(['ASC', 'DESC'])
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * 批量操作权限DTO
 * @description 用于批量操作权限的数据传输对象
 */
export class BatchPermissionDto {
  @ApiProperty({
    description: '权限ID列表',
    type: [String],
    example: ['perm-uuid-1', 'perm-uuid-2'],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  permissionIds: string[];

  @ApiPropertyOptional({
    description: '操作类型',
    example: 'activate',
    enum: ['activate', 'deactivate', 'delete'],
  })
  @IsEnum(['activate', 'deactivate', 'delete'])
  @IsOptional()
  operation?: 'activate' | 'deactivate' | 'delete';
}

/**
 * 权限响应DTO
 * @description 权限信息的响应格式
 */
export class PermissionResponseDto {
  @ApiProperty({
    description: '权限ID',
    example: 'perm-uuid-123',
  })
  id: string;

  @ApiProperty({
    description: '权限代码',
    example: 'documents.read',
  })
  code: string;

  @ApiProperty({
    description: '权限名称',
    example: '查看文档',
  })
  name: string;

  @ApiPropertyOptional({
    description: '权限描述',
    example: '允许用户查看和浏览文档内容',
  })
  description?: string;

  @ApiProperty({
    description: '所属模块',
    example: 'documents',
  })
  module: string;

  @ApiPropertyOptional({
    description: '资源标识',
    example: 'document',
  })
  resource?: string;

  @ApiPropertyOptional({
    description: '操作类型',
    example: 'read',
  })
  action?: string;

  @ApiPropertyOptional({
    description: '权限条件',
    example: { department: ['IT', 'Finance'] },
  })
  conditions?: Record<string, unknown>;

  @ApiProperty({
    description: '是否启用',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: '排序序号',
    example: 10,
  })
  sort_order: number;

  @ApiPropertyOptional({
    description: '创建者ID',
    example: 'user-uuid-123',
  })
  created_by?: string;

  @ApiProperty({
    description: '创建时间',
    example: '2024-12-19T10:30:00Z',
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: '更新者ID',
    example: 'user-uuid-123',
  })
  updated_by?: string;

  @ApiProperty({
    description: '更新时间',
    example: '2024-12-19T10:30:00Z',
  })
  updated_at: Date;

  @ApiPropertyOptional({
    description: '使用此权限的角色数量',
    example: 3,
  })
  role_count?: number;

  @ApiPropertyOptional({
    description: '使用此权限的用户数量',
    example: 15,
  })
  user_count?: number;
}

/**
 * 权限列表响应DTO
 * @description 权限列表查询的响应格式
 */
export class PermissionListResponseDto {
  @ApiProperty({
    description: '权限列表',
    type: [PermissionResponseDto],
  })
  data: PermissionResponseDto[];

  @ApiProperty({
    description: '总数',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: '总页数',
    example: 5,
  })
  totalPages: number;
}

/**
 * 权限统计响应DTO
 * @description 权限统计信息的响应格式
 */
export class PermissionStatsResponseDto {
  @ApiProperty({
    description: '总权限数量',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: '启用的权限数量',
    example: 45,
  })
  active: number;

  @ApiProperty({
    description: '禁用的权限数量',
    example: 5,
  })
  inactive: number;

  @ApiProperty({
    description: '按模块分组的权限统计',
    example: {
      documents: 15,
      users: 12,
      templates: 8,
      config: 10,
    },
  })
  by_module: Record<string, number>;

  @ApiProperty({
    description: '按操作类型分组的权限统计',
    example: {
      create: 10,
      read: 15,
      update: 12,
      delete: 8,
      manage: 5,
    },
  })
  by_action: Record<string, number>;
}

/**
 * 模块权限树DTO
 * @description 模块权限树结构的响应格式
 */
export class ModulePermissionTreeDto {
  @ApiProperty({
    description: '模块名称',
    example: 'documents',
  })
  module: string;

  @ApiProperty({
    description: '模块显示名称',
    example: '文档管理',
  })
  module_name: string;

  @ApiProperty({
    description: '模块权限列表',
    type: [PermissionResponseDto],
  })
  permissions: PermissionResponseDto[];

  @ApiProperty({
    description: '权限数量',
    example: 15,
  })
  permission_count: number;
}

/**
 * 检查权限DTO
 * @description 用于检查用户是否具有指定权限
 */
export class CheckPermissionDto {
  @ApiProperty({
    description: '用户ID',
    example: 'user-uuid-123',
  })
  @IsUUID(4)
  userId: string;

  @ApiProperty({
    description: '权限代码列表',
    type: [String],
    example: ['documents.read', 'documents.create'],
  })
  @IsArray()
  @IsString({ each: true })
  permissions: string[];

  @ApiPropertyOptional({
    description: '资源上下文',
    example: { documentId: 'doc-123', departmentId: 'dept-456' },
  })
  @IsOptional()
  context?: Record<string, unknown>;
}

/**
 * 权限检查结果DTO
 * @description 权限检查的结果
 */
export class PermissionCheckResultDto {
  @ApiProperty({
    description: '权限检查结果',
    example: {
      'documents.read': true,
      'documents.create': false,
    },
  })
  results: Record<string, boolean>;

  @ApiProperty({
    description: '是否拥有所有权限',
    example: false,
  })
  has_all: boolean;

  @ApiProperty({
    description: '是否拥有任意权限',
    example: true,
  })
  has_any: boolean;

  @ApiProperty({
    description: '缺失的权限列表',
    type: [String],
    example: ['documents.create'],
  })
  missing_permissions: string[];
} 