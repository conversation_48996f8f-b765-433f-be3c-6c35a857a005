-- OnlyOffice配置系统数据库初始化脚本
-- 将原本在环境变量中的配置迁移到数据库中
-- 执行时间：2024-12-19

-- ================================
-- JWT配置设置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('jwt.onlyoffice.secret', 'OnlyOffice-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV', 'OnlyOffice文档服务器JWT密钥', NOW(), NOW()),
('jwt.onlyoffice.header', 'Authorization', 'OnlyOffice JWT头部名称', NOW(), NOW()),
('jwt.onlyoffice.in_body', 'true', '是否在请求体中包含OnlyOffice JWT', NOW(), NOW()),
('jwt.onlyoffice.algorithm', 'HS256', 'OnlyOffice JWT算法', NOW(), NOW());

-- ================================
-- OnlyOffice服务器配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('onlyoffice.server_url', 'http://*************/', 'OnlyOffice服务器地址', NOW(), NOW()),
('onlyoffice.document_server_url', 'http://*************/', 'OnlyOffice文档服务器地址', NOW(), NOW()),
('onlyoffice.document_port', '80', 'OnlyOffice文档服务器端口', NOW(), NOW()),
('onlyoffice.api_url_suffix', '/web-apps/apps/api/documents/api.js', 'OnlyOffice API JS路径', NOW(), NOW());

-- ================================
-- FileNet配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('filenet.host', '*************', 'FileNet服务器主机地址', NOW(), NOW()),
('filenet.port', '8090', 'FileNet服务器端口', NOW(), NOW()),
('filenet.username', 'your-filenet-username', 'FileNet用户名', NOW(), NOW()),
('filenet.password', 'YourFileNetPassword123!', 'FileNet密码（加密存储）', NOW(), NOW()),
('filenet.default_folder', '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}', 'FileNet默认文件夹ID', NOW(), NOW()),
('filenet.default_doc_class', 'SimpleDocument', 'FileNet默认文档类', NOW(), NOW()),
('filenet.default_source_type', 'MaxOffice', 'FileNet默认源类型', NOW(), NOW()),
('filenet.default_biz_tag', 'office_file', 'FileNet默认业务标签', NOW(), NOW());

-- ================================
-- 存储配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('storage.upload_path', './uploads', '文件上传路径', NOW(), NOW()),
('storage.tmp_path', './tmp', '临时文件路径', NOW(), NOW()),
('storage.max_file_size', '52428800', '最大文件大小（字节）50MB', NOW(), NOW()),
('storage.allowed_file_types', '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt', '允许的文件类型', NOW(), NOW());

-- ================================
-- 缓存配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('cache.type', 'memory', '缓存类型', NOW(), NOW()),
('cache.ttl', '3600', '缓存生存时间（秒）', NOW(), NOW()),
('cache.max_size', '100', '缓存最大条目数', NOW(), NOW());

-- ================================
-- Redis配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('redis.host', 'localhost', 'Redis主机地址', NOW(), NOW()),
('redis.port', '6379', 'Redis端口', NOW(), NOW()),
('redis.password', '', 'Redis密码', NOW(), NOW()),
('redis.db', '0', 'Redis数据库索引', NOW(), NOW());

-- ================================
-- 日志配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('logging.level', 'info', '日志级别', NOW(), NOW()),
('logging.dir', './logs', '日志目录', NOW(), NOW()),
('logging.max_size', '10m', '单个日志文件最大大小', NOW(), NOW()),
('logging.max_files', '7', '保留的日志文件数量', NOW(), NOW());

-- ================================
-- 监控配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('monitoring.enabled', 'true', '是否启用监控', NOW(), NOW()),
('monitoring.slow_query_threshold', '1000', '慢查询阈值（毫秒）', NOW(), NOW()),
('monitoring.request_timeout', '30000', '请求超时时间（毫秒）', NOW(), NOW());

-- ================================
-- 安全配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('security.rate_limit_window_ms', '900000', '限流窗口时间（毫秒）', NOW(), NOW()),
('security.rate_limit_max_requests', '100', '限流窗口内最大请求数', NOW(), NOW()),
('security.enable_security_headers', 'true', '是否启用安全头', NOW(), NOW());

-- ================================
-- 开发环境配置
-- ================================
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
('development.hot_reload', 'true', '是否启用热重载', NOW(), NOW()),
('development.debug_mode', 'true', '是否启用调试模式', NOW(), NOW()),
('development.api_docs_enabled', 'true', '是否启用API文档', NOW(), NOW()),
('development.swagger_endpoint', '/api-docs', 'Swagger文档端点', NOW(), NOW());

-- ================================
-- 验证配置是否插入成功
-- ================================
SELECT 
    COUNT(*) as total_settings,
    COUNT(CASE WHEN setting_key LIKE 'jwt.%' THEN 1 END) as jwt_settings,
    COUNT(CASE WHEN setting_key LIKE 'onlyoffice.%' THEN 1 END) as onlyoffice_settings,
    COUNT(CASE WHEN setting_key LIKE 'filenet.%' THEN 1 END) as filenet_settings
FROM system_settings 
WHERE setting_key IN (
    'jwt.onlyoffice.secret', 'jwt.onlyoffice.header', 'jwt.onlyoffice.in_body', 'jwt.onlyoffice.algorithm',
    'onlyoffice.server_url', 'onlyoffice.document_server_url', 'onlyoffice.document_port', 'onlyoffice.api_url_suffix',
    'filenet.host', 'filenet.port', 'filenet.username', 'filenet.password',
    'filenet.default_folder', 'filenet.default_doc_class', 'filenet.default_source_type', 'filenet.default_biz_tag',
    'storage.upload_path', 'storage.tmp_path', 'storage.max_file_size', 'storage.allowed_file_types',
    'cache.type', 'cache.ttl', 'cache.max_size',
    'redis.host', 'redis.port', 'redis.password', 'redis.db',
    'logging.level', 'logging.dir', 'logging.max_size', 'logging.max_files',
    'monitoring.enabled', 'monitoring.slow_query_threshold', 'monitoring.request_timeout',
    'security.rate_limit_window_ms', 'security.rate_limit_max_requests', 'security.enable_security_headers',
    'development.hot_reload', 'development.debug_mode', 'development.api_docs_enabled', 'development.swagger_endpoint'
); 