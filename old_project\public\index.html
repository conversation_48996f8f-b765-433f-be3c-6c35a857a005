<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Landsea在线文档管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            text-align: center;
            margin-bottom: 30px;
        }

        header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .upload-section {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .upload-section h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .file-input-wrapper {
            display: flex;
            margin-bottom: 15px;
        }

        .file-input-wrapper input[type="file"] {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .file-input-wrapper button {
            margin-left: 10px;
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .file-input-wrapper button:hover {
            background-color: #2980b9;
        }

        .documents-section {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .documents-section h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .document-list {
            width: 100%;
            border-collapse: collapse;
        }

        .document-list th,
        .document-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .document-list th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .document-list tr:hover {
            background-color: #f9f9f9;
        }

        .document-list .actions {
            display: flex;
            gap: 10px;
        }

        .document-list .actions button {
            padding: 5px 10px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 5px;
        }

        .document-list .actions button:hover {
            background-color: #2980b9;
        }

        .document-list .actions button.delete {
            background-color: #e74c3c;
        }

        .document-list .actions button.delete:hover {
            background-color: #c0392b;
        }

        .empty-list {
            text-align: center;
            padding: 20px;
            color: #777;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #777;
        }

        .loading:after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }

        @keyframes dots {

            0%,
            20% {
                content: ".";
            }

            40% {
                content: "..";
            }

            60%,
            100% {
                content: "...";
            }
        }

        .document-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }

        footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            color: #777;
            font-size: 14px;
        }

        .hidden {
            display: none;
        }

        .notice-banner {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #f5c6cb;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            /* 默认隐藏 */
            position: fixed;
            z-index: 1001;
            /* 比header低一点，但高于页面内容 */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 60%;
            max-width: 600px;
            border-radius: 5px;
            position: relative;
        }

        .modal-header {
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            color: #2c3e50;
        }

        .close-button {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
        }

        .template-list-container {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 15px;
            border: 1px solid #eee;
            padding: 10px;
        }

        .template-item {
            padding: 8px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .template-item:last-child {
            border-bottom: none;
        }

        .template-item:hover,
        .template-item.selected {
            background-color: #e9ecef;
        }

        .template-item h4 {
            margin: 0 0 5px 0;
            font-size: 1em;
        }

        .template-item p {
            font-size: 0.85em;
            color: #666;
            margin: 0;
        }


        .modal-footer {
            padding-top: 15px;
            border-top: 1px solid #eee;
            margin-top: 20px;
            text-align: right;
        }

        .modal-footer button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-left: 10px;
        }

        .modal-footer button.primary {
            background-color: #3498db;
            color: white;
        }

        .modal-footer button.primary:hover {
            background-color: #2980b9;
        }

        .modal-footer button.secondary {
            background-color: #ecf0f1;
            color: #333;
        }

        .modal-footer button.secondary:hover {
            background-color: #dadedf;
        }

        #newDocumentNameInput {
            width: calc(100% - 22px);
            /* padding and border */
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .actions-container {
            display: flex;
            gap: 10px;
            /* 按钮之间的间距 */
            margin-bottom: 15px;
            /* 与下方内容的间距 */
        }

        /* 编辑下拉菜单样式 */
        .edit-dropdown {
            position: relative;
            display: inline-flex;
        }

        .edit-main-btn {
            border-radius: 3px 0 0 3px !important;
            border-right: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .edit-dropdown-btn {
            background-color: #27ae60 !important;
            color: white;
            border: none;
            padding: 5px 8px;
            border-radius: 0 3px 3px 0;
            cursor: pointer;
            font-size: 12px;
            min-width: 20px;
        }

        .edit-dropdown-btn:hover {
            background-color: #229954 !important;
        }

        .edit-dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            border-radius: 4px;
            border: 1px solid #ddd;
            top: 100%;
            right: 0;
        }

        .edit-dropdown-content a {
            color: #333;
            padding: 8px 12px;
            text-decoration: none;
            display: block;
            font-size: 13px;
        }

        .edit-dropdown-content a:hover {
            background-color: #f1f1f1;
        }

        .edit-dropdown-content a:first-child {
            border-radius: 4px 4px 0 0;
        }

        .edit-dropdown-content a:last-child {
            border-radius: 0 0 4px 4px;
        }

        .edit-dropdown-content a::before {
            content: "🏷️ ";
            margin-right: 4px;
        }

        .edit-dropdown-content a:last-child::before {
            content: "✨ ";
        }

        .edit-dropdown.show .edit-dropdown-content {
            display: block;
        }
    </style>
</head>

<body>
    <header>
        <h1>Landsea Office文档管理系统</h1>
        <p>上传、查看和编辑您的文档</p>
        <div style="margin-top: 10px;">
            <a href="/navigation" style="color: #fff; text-decoration: underline; font-size: 14px;">功能导航</a>
        </div>
    </header>

    <div class="container">
        <!-- 本地上传功能已移除，但在后端保留API支持 -->
        <section class="upload-section hidden">
            <h2>上传新文档 (本地存储)</h2>
            <div class="file-input-wrapper">
                <input type="file" id="fileInput" accept=".docx,.xlsx,.pptx,.txt,.pdf" />
                <button id="uploadButton">上传 (本地)</button>
            </div>
            <div id="uploadProgress" style="display: none;">
                <div class="loading">上传中</div>
            </div>
        </section>

        <section class="upload-section">
            <h2>文档上传与创建</h2>
            <div class="actions-container">
                <div class="file-input-wrapper" style="flex-grow: 1;">
                    <input type="file" id="fileNetInput" accept=".docx,.xlsx,.pptx,.txt,.pdf,.jpeg,.jpg,.png,.xml" />
                    <button id="fileNetUploadButton">上传文档</button>
                </div>
                <button id="createFromTemplateBtn"
                    style="padding: 8px 15px; background-color: #27ae60; color: white; border: none; border-radius: 4px; cursor: pointer;">从模板创建</button>
            </div>
            <div id="fileNetUploadProgress" style="display: none;">
                <div class="loading">上传中</div>
            </div>
        </section>

        <!-- 本地文档列表已隐藏 -->
        <section class="documents-section hidden">
            <h2>我的文档 (本地存储)</h2>
            <div id="documentsList">
                <div class="loading">加载文档列表</div>
            </div>
        </section>

        <section class="documents-section">
            <h2>文档列表</h2>
            <div id="filenet-loading" class="loading">加载文档列表</div>
            <div id="fileNetDocumentsList">
                <!-- FileNet 文档列表将在此渲染 -->
            </div>
        </section>
    </div>

    <!-- 模板选择模态框 -->
    <div id="templateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>从模板创建新文档</h3>
                <span class="close-button" id="closeTemplateModalBtn">&times;</span>
            </div>
            <div id="modal-body">
                <p>请选择一个模板，并为您的新文档命名：</p>
                <div class="template-list-container" id="templateListContainer">
                    <!-- 模板将在此处动态加载 -->
                    <div class="loading">正在加载模板...</div>
                </div>
                <input type="text" id="newDocumentNameInput" placeholder="输入新文档名称 (不含扩展名)">
                <div id="templateError" style="color: red; margin-bottom: 10px; font-size: 0.9em;"></div>

            </div>
            <div class="modal-footer">
                <button id="cancelCreateFromTemplateBtn" class="secondary">取消</button>
                <button id="confirmCreateFromTemplateBtn" class="primary">创建文档</button>
            </div>
        </div>
    </div>

    <footer>
        <p>© 2023 OnlyOffice文档集成项目 | 基于OnlyOffice Document Server</p>
    </footer>

    <script>
        // 添加 escapeHtml 函数定义
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // 添加全局错误处理
        window.onerror = function (message, source, lineno, colno, error) {
            console.error('捕获到全局错误:', message, 'at', source, lineno, colno);
            alert('JavaScript错误: ' + message + ' 行: ' + lineno);
            return false;
        };

        document.addEventListener('DOMContentLoaded', function () {
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .document-list .actions button {
                    padding: 5px 10px;
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 14px;
                    margin-right: 5px;
                }
                
                .document-list .actions button:hover {
                    background-color: #2980b9;
                }
            `;
            document.head.appendChild(style);

            // 加载FileNet文档列表
            const loadingElement = document.getElementById('filenet-loading');
            if (loadingElement) {
                loadingElement.style.display = 'block';
            }

            // 加载FileNet文档列表
            loadFileNetDocuments();

            // 添加上传事件监听器
            const fileNetUploadButton = document.getElementById('fileNetUploadButton');
            if (fileNetUploadButton) {
                fileNetUploadButton.addEventListener('click', uploadToFileNet);
            }

            // 模板功能相关DOM元素
            const templateModal = document.getElementById('templateModal');
            const createFromTemplateBtn = document.getElementById('createFromTemplateBtn');
            const closeTemplateModalBtn = document.getElementById('closeTemplateModalBtn');
            const cancelCreateFromTemplateBtn = document.getElementById('cancelCreateFromTemplateBtn');
            const confirmCreateFromTemplateBtn = document.getElementById('confirmCreateFromTemplateBtn');
            const templateListContainer = document.getElementById('templateListContainer');
            const newDocumentNameInput = document.getElementById('newDocumentNameInput');
            const templateErrorDiv = document.getElementById('templateError');

            let selectedTemplateId = null;

            // 打开模板模态框
            if (createFromTemplateBtn) {
                createFromTemplateBtn.addEventListener('click', () => {
                    templateModal.style.display = 'block';
                    loadTemplates();
                    newDocumentNameInput.value = ''; // 清空之前的输入
                    templateErrorDiv.textContent = ''; // 清空错误信息
                    selectedTemplateId = null; // 重置选择
                });
            }

            // 关闭模板模态框
            function closeTemplateModal() {
                templateModal.style.display = 'none';
            }
            if (closeTemplateModalBtn) closeTemplateModalBtn.addEventListener('click', closeTemplateModal);
            if (cancelCreateFromTemplateBtn) cancelCreateFromTemplateBtn.addEventListener('click', closeTemplateModal);
            window.addEventListener('click', (event) => { // 点击模态框外部关闭
                if (event.target == templateModal) {
                    closeTemplateModal();
                }
            });

            // 加载模板列表
            async function loadTemplates() {
                templateListContainer.innerHTML = '<div class="loading">正在加载模板...</div>';
                try {
                    const response = await fetch('/api/templates?status=enabled'); // 只获取启用的模板
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const result = await response.json();
                    if (result.success && result.data && result.data.templates) {
                        renderTemplates(result.data.templates);
                    } else {
                        templateListContainer.innerHTML = '<p>没有可用的模板，或加载失败。</p>';
                        console.error('Failed to load templates:', result.message || 'Unknown error');
                    }
                } catch (error) {
                    templateListContainer.innerHTML = '<p>加载模板时发生错误。</p>';
                    console.error('Error loading templates:', error);
                }
            }

            // 渲染模板列表到模态框
            function renderTemplates(templates) {
                if (templates.length === 0) {
                    templateListContainer.innerHTML = '<p>当前没有可用的模板。</p>';
                    return;
                }
                templateListContainer.innerHTML = ''; // 清空加载提示
                templates.forEach(template => {
                    const item = document.createElement('div');
                    item.classList.add('template-item');
                    item.dataset.templateId = template.id;
                    item.innerHTML = `
                        <h4>${escapeHtml(template.name)}</h4>
                        <p>${escapeHtml(template.description || '暂无描述')}</p>
                        <small>源文档: ${escapeHtml(template.source_document_name || '未知')}</small>
                    `;
                    item.addEventListener('click', () => {
                        document.querySelectorAll('.template-item.selected').forEach(el => el.classList.remove('selected'));
                        item.classList.add('selected');
                        selectedTemplateId = template.id;
                        templateErrorDiv.textContent = ''; // 清除之前的错误
                    });
                    templateListContainer.appendChild(item);
                });
            }

            // 确认从模板创建文档
            if (confirmCreateFromTemplateBtn) {
                confirmCreateFromTemplateBtn.addEventListener('click', async () => {
                    const newName = newDocumentNameInput.value.trim();
                    if (!selectedTemplateId) {
                        templateErrorDiv.textContent = '请选择一个模板。';
                        return;
                    }
                    if (!newName) {
                        templateErrorDiv.textContent = '请输入新文档的名称。';
                        return;
                    }
                    templateErrorDiv.textContent = ''; // 清空错误

                    confirmCreateFromTemplateBtn.disabled = true;
                    confirmCreateFromTemplateBtn.textContent = '创建中...';

                    try {
                        const response = await fetch('/api/documents/from-template', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                template_id: selectedTemplateId,
                                name: newName
                                // user_id 可以由后端处理或如果前端有用户信息则传入 req.user.id
                            })
                        });
                        const result = await response.json();
                        if (response.ok && result.success && result.data) {
                            alert('文档创建成功！即将跳转到编辑页面。');
                            closeTemplateModal();
                            // fetchAndRenderFileNetDocuments(); // 刷新主列表
                            window.location.href = `/editor/${result.data.id}`; // 跳转到编辑页
                        } else {
                            templateErrorDiv.textContent = `创建失败: ${result.message || '未知错误'}`;
                            console.error('Failed to create document from template:', result);
                        }
                    } catch (error) {
                        templateErrorDiv.textContent = '创建文档时发生网络错误。';
                        console.error('Error creating document from template:', error);
                    } finally {
                        confirmCreateFromTemplateBtn.disabled = false;
                        confirmCreateFromTemplateBtn.textContent = '创建文档';
                    }
                });
            }
        });

        // 获取文档列表函数已保留但不会被调用
        function getDocuments() {
            fetch('/api/documents')
                .then(response => response.json())
                .then(data => {
                    const documentsListElement = document.getElementById('documentsList');

                    if (data.success && data.documents && data.documents.length > 0) {
                        let tableHTML = `
                            <table class="document-list">
                                <thead>
                                    <tr>
                                        <th>文档名称</th>
                                        <th>类型</th>
                                        <th>大小</th>
                                        <th>最后修改</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.documents.forEach(doc => {
                            const docIcon = getDocumentIcon(doc.type);
                            const docSize = formatFileSize(doc.size);
                            const lastModified = new Date(doc.lastModified).toLocaleString('zh-CN');

                            tableHTML += `
                                <tr>
                                    <td>
                                        <img src="${docIcon}" class="document-icon" alt="${doc.type}">
                                        ${doc.name}
                                    </td>
                                    <td>${doc.type.toUpperCase()}</td>
                                    <td>${docSize}</td>
                                    <td>${lastModified}</td>
                                    <td class="actions">
                                        <button onclick="editDocument('${doc.id}')">编辑</button>
                                        <button onclick="testDocument('${doc.id}')" style="background-color: #27ae60;">测试</button>
                                        <button class="delete" onclick="deleteDocument('${doc.id}')">删除</button>
                                    </td>
                                </tr>
                            `;
                        });

                        tableHTML += `
                                </tbody>
                            </table>
                        `;

                        documentsListElement.innerHTML = tableHTML;
                    } else {
                        // 没有文档
                        documentsListElement.innerHTML = `
                            <div class="empty-list">
                                <p>没有找到文档，请上传一个新文档。</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取文档列表失败:', error);
                    document.getElementById('documentsList').innerHTML = `
                        <div class="empty-list">
                            <p>获取文档列表失败，请刷新页面重试。</p>
                        </div>
                    `;
                });
        }

        // 上传文档函数已保留但不会被调用
        function uploadDocument() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择要上传的文件');
                return;
            }

            // 显示进度
            document.getElementById('uploadProgress').style.display = 'block';

            const formData = new FormData();
            formData.append('file', file);

            fetch('/api/documents/upload', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('uploadProgress').style.display = 'none';

                    if (data.success) {
                        console.log('上传成功 (本地)，文件ID:', data.file.id);
                        // 直接使用文件ID，不进行额外编码
                        window.location.href = `/edit/${data.file.id}`;
                    } else {
                        alert('文件上传失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    document.getElementById('uploadProgress').style.display = 'none';
                    console.error('上传文件错误:', error);
                    alert('上传文件时发生错误，请重试');
                });
        }

        // 上传文档到 FileNet
        function uploadToFileNet() {
            const fileInput = document.getElementById('fileNetInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择要上传的文件');
                return;
            }

            document.getElementById('fileNetUploadProgress').style.display = 'block';

            const formData = new FormData();
            formData.append('file', file);

            fetch('/api/filenet/upload', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('fileNetUploadProgress').style.display = 'none';
                    if (data.success && data.docId) {
                        alert(`上传成功!\nDocID: ${data.docId}\nDB ID: ${data.dbId}\n文件名: ${data.originalName}`);
                        fileInput.value = ''; // 清空选择
                        fetchAndRenderFileNetDocuments(); // 上传成功后刷新列表
                    } else {
                        alert('上传失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    document.getElementById('fileNetUploadProgress').style.display = 'none';
                    console.error('上传错误:', error);
                    alert('上传时发生错误，请查看控制台');
                });
        }

        // 渲染 FileNet 文档列表 (从 API 获取)
        function fetchAndRenderFileNetDocuments(page = 1, limit = 10) {
            const fnDocsContainer = document.getElementById('fileNetDocumentsList');
            if (!fnDocsContainer) return;

            fnDocsContainer.innerHTML = '<div class="loading">加载文档列表...</div>';

            console.log('开始获取文档列表，请求: /api/filenet/documents');

            // 在实际应用中，您可能想要添加排序参数等
            fetch(`/api/filenet/documents?page=${page}&limit=${limit}&sortBy=uploaded_at&sortOrder=DESC`)
                .then(response => {
                    console.log('文档列表响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error('API响应异常: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('文档列表数据:', data);
                    if (data.success && data.documents && data.documents.length > 0) {
                        let tableHTML = `
                            <table class="document-list">
                                <thead>
                                    <tr>
                                        <th>文档名称</th>
                                        <th>DocID</th>
                                        <th>上传时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        data.documents.forEach(doc => {
                            // 注意：数据库返回的字段名是 fn_doc_id, original_name, uploaded_at
                            // 确保后端API返回的 doc 对象中包含内部数据库主键ID，例如 doc.id 或 doc.internal_id
                            tableHTML += `
                                <tr>
                                    <td>${escapeHtml(doc.original_name)}</td>
                                    <td>${escapeHtml(doc.fn_doc_id)}</td> 
                                    <td>${new Date(doc.uploaded_at || doc.created_at || doc.updated_at).toLocaleString('zh-CN')}</td>
                                    <td class="actions">
                                        <button onclick="downloadFromFilenet('${doc.fn_doc_id}')">下载</button>
                                        <div class="edit-dropdown">
                                            <button onclick="editDocumentWithInternalId('${doc.id}')" style="background-color: #27ae60;" class="edit-main-btn">编辑</button>
                                            <button onclick="toggleEditDropdown(this)" class="edit-dropdown-btn" title="选择编辑模式">▼</button>
                                            <div class="edit-dropdown-content">
                                                <a href="#" onclick="editWithTemplate('${doc.id}', 'default-edit')">default-edit (默认编辑版)</a>
                                                <a href="#" onclick="editWithTemplate('${doc.id}', 'legal-edit')">legal-edit (法务编辑版)</a>
                                                <a href="#" onclick="editWithTemplate('${doc.id}', 'employee-readonly')">employee-readonly (员工只读版)</a>
                                                <a href="#" onclick="editWithTemplate('${doc.id}', 'simple-edit')">simple-edit (功能简化版)</a>
                                            </div>
                                        </div>
                                        <button class="delete" onclick="removeFileNetDocumentFromView('${doc.fn_doc_id}', this)">从列表移除</button>
                                    </td>
                                </tr>
                            `;
                        });
                        tableHTML += `</tbody></table>`;
                        // 添加分页控件 (简单示例)
                        if (data.pagination && data.pagination.totalPages > 1) {
                            tableHTML += '<div class="pagination-controls">页码: ';
                            for (let i = 1; i <= data.pagination.totalPages; i++) {
                                if (i === data.pagination.page) {
                                    tableHTML += `<span>${i}</span> `;
                                } else {
                                    tableHTML += `<a href="#" onclick="fetchAndRenderFileNetDocuments(${i}, ${data.pagination.limit}); return false;">${i}</a> `;
                                }
                            }
                            tableHTML += '</div>';
                        }
                        fnDocsContainer.innerHTML = tableHTML;
                    } else if (data.success && data.documents && data.documents.length === 0) {
                        fnDocsContainer.innerHTML = '<div class="empty-list"><p>文档列表为空，请上传文档。</p></div>';
                    } else {
                        fnDocsContainer.innerHTML = `<div class="empty-list"><p>获取文档列表失败: ${data.message || "未知错误"}</p></div>`;
                    }
                })
                .catch(error => {
                    console.error('获取文档列表错误:', error);
                    fnDocsContainer.innerHTML = '<div class="empty-list"><p>获取文档列表时发生网络错误。</p></div>';
                });
        }

        // 从FileNet下载文档
        function downloadFromFilenet(docId) {
            console.log(`开始下载文档，DocID: ${docId}`);
            // 创建一个隐藏的<a>元素来触发下载
            const downloadLink = document.createElement('a');
            downloadLink.href = `/api/filenet/download/${encodeURIComponent(docId)}`;
            downloadLink.target = '_blank'; // 可选：在新窗口打开
            downloadLink.download = `filenet_${docId}`; // 文件名会被服务器响应头中的Content-Disposition覆盖

            // 将链接添加到文档中，触发点击，然后移除
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }

        // 编辑FileNet文档 - 此函数现在使用内部数据库ID
        function editDocumentWithInternalId(internalId) {
            if (!internalId) {
                console.error('编辑错误: 未提供内部文档ID');
                alert('无法编辑文档：缺少内部文档ID。');
                return;
            }
            console.log('使用内部DB ID编辑文档:', internalId);
            // 使用新的路径格式 /editor/:internalDbId
            const encodedInternalId = encodeURIComponent(internalId);
            window.location.href = `/editor/${encodedInternalId}`;
        }

        // 旧的 editFileNetDocument 函数，可以保留用于兼容或调试，但新的编辑流程应使用 editDocumentWithInternalId
        function editFileNetDocument(docId) {
            console.warn('调用了旧的 editFileNetDocument, DocID (FileNet ID):', docId, '。请考虑迁移到使用内部ID的编辑链接。');
            // 为了避免完全破坏现有功能（如果其他地方还在调用），暂时保留，但理想情况下应该被 editDocumentWithInternalId 替代
            const encodedDocId = encodeURIComponent(docId);
            window.location.href = `/editor/filenet/${encodedDocId}`;
        }

        // 从前端列表移除 FileNet 文档记录 (不从服务器删除)
        function removeFileNetDocumentFromView(docId, buttonElement) {
            // 这个函数现在只是从视图中移除行，因为列表是从服务器动态加载的。
            // 更完善的做法是调用API删除数据库记录，然后刷新列表。
            if (confirm('仅从当前视图隐藏该文档记录，并不会从数据库删除。确定吗？下次加载时可能再次出现。')) {
                const row = buttonElement.closest('tr');
                if (row) {
                    row.style.display = 'none'; // 或者 row.remove();
                }
            }
        }

        // 编辑本地文档功能已保留但不会被调用
        function editDocument(docId) {
            console.log('编辑文档，文件ID:', docId);
            // 直接使用文件ID，不进行额外编码
            window.location.href = `/editor/${docId}`;
        }

        // 测试本地文档功能已保留但不会被调用
        function testDocument(docId) {
            window.location.href = `/view-file/${docId}`;
        }

        // 删除本地文档功能已保留但不会被调用
        function deleteDocument(docId) {
            if (confirm('确定要删除这个本地存储的文档吗？此操作无法撤销。')) {
                console.log('删除文档，文件ID:', docId);
                fetch(`/api/documents/${docId}`, {
                    method: 'DELETE'
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 刷新文档列表
                            getDocuments();
                        } else {
                            alert('删除文档失败: ' + (data.message || '未知错误'));
                        }
                    })
                    .catch(error => {
                        console.error('删除文档错误:', error);
                        alert('删除文档时发生错误，请重试');
                    });
            }
        }

        // 获取文档图标
        function getDocumentIcon(type) {
            const icons = {
                'docx': '/images/word.png',
                'xlsx': '/images/excel.png',
                'pptx': '/images/powerpoint.png',
                'pdf': '/images/pdf.png',
                'txt': '/images/text.png'
            };

            return icons[type] || '/images/file.png';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';

            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 打开FileNet文档
        function openFileNetDocument(docId) {
            const encodedDocId = encodeURIComponent(docId);
            const url = `/documents/filenet/${encodedDocId}`;
            window.open(url, '_blank');
        }

        // 添加处理文件名的辅助函数
        function fixFileName(fileName) {
            if (!fileName) return '';

            // 尝试修复明显的文件名编码问题
            if (/%[0-9A-F]{2}/.test(fileName)) {
                try {
                    return decodeURIComponent(fileName);
                } catch (e) {
                    console.warn('解码文件名失败:', fileName, e);
                }
            }

            // 返回原始文件名（如果不需要修复）
            return fileName;
        }

        // 显示FileNet文档列表
        function displayFileNetDocuments(documents) {
            const container = document.getElementById('fileNetDocumentsList');
            if (!container) return;

            container.innerHTML = '';

            if (documents.length === 0) {
                container.innerHTML = '<p>暂无文档</p>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'document-list';

            // 创建表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            ['文件名', '上传时间', '版本', '操作'].forEach(text => {
                const th = document.createElement('th');
                th.textContent = text;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // 创建表体
            const tbody = document.createElement('tbody');
            documents.forEach(doc => {
                const row = document.createElement('tr');

                // 文件名单元格 - 使用修复后的文件名
                const nameCell = document.createElement('td');
                nameCell.textContent = fixFileName(doc.original_name);
                row.appendChild(nameCell);

                // 上传时间单元格
                const timeCell = document.createElement('td');
                timeCell.textContent = new Date(doc.uploaded_at).toLocaleString();
                row.appendChild(timeCell);

                // 版本单元格
                const versionCell = document.createElement('td');
                versionCell.textContent = doc.version || '1';
                row.appendChild(versionCell);

                // 操作单元格
                const actionsCell = document.createElement('td');
                actionsCell.className = 'actions';

                // 打开按钮
                const openBtn = document.createElement('button');
                openBtn.textContent = '打开';
                openBtn.onclick = () => downloadFromFilenet(doc.fn_doc_id);
                actionsCell.appendChild(openBtn);

                // 编辑按钮
                const editBtn = document.createElement('button');
                editBtn.textContent = '编辑';
                editBtn.style.backgroundColor = '#27ae60';
                // 确保 doc 对象中有 id 属性（内部数据库主键ID）
                if (doc.id) {
                    editBtn.onclick = () => editDocumentWithInternalId(doc.id);
                } else {
                    // 如果没有内部ID，回退到旧的编辑方式（基于fn_doc_id），或者报错
                    console.warn('文档对象缺少内部ID (doc.id)，编辑按钮可能使用旧逻辑:', doc);
                    editBtn.onclick = () => editFileNetDocument(doc.fn_doc_id);
                    // 或者禁用按钮/提示错误
                    // editBtn.disabled = true;
                    // editBtn.title = '无法编辑：缺少内部文档ID';
                }
                actionsCell.appendChild(editBtn);

                row.appendChild(actionsCell);

                tbody.appendChild(row);
            });

            table.appendChild(tbody);
            container.appendChild(table);
        }

        // 在页面加载FileNet文档列表
        async function loadFileNetDocuments() {
            try {
                const response = await fetch('/api/filenet/documents');
                const data = await response.json();

                if (data.success && data.documents) {
                    displayFileNetDocuments(data.documents);
                } else {
                    document.getElementById('fileNetDocumentsList').innerHTML =
                        `<div class="alert alert-danger">加载文档失败: ${data.message || '未知错误'}</div>`;
                }
            } catch (error) {
                document.getElementById('fileNetDocumentsList').innerHTML =
                    `<div class="alert alert-danger">加载文档失败: ${error.message}</div>`;
            } finally {
                document.getElementById('filenet-loading').style.display = 'none';
            }
        }

        // 切换编辑下拉菜单
        function toggleEditDropdown(buttonElement) {
            // 关闭其他打开的下拉菜单
            document.querySelectorAll('.edit-dropdown.show').forEach(dropdown => {
                if (dropdown !== buttonElement.parentElement) {
                    dropdown.classList.remove('show');
                }
            });

            // 切换当前下拉菜单
            buttonElement.parentElement.classList.toggle('show');
        }

        // 使用指定配置模板编辑文档
        function editWithTemplate(internalId, templateId) {
            if (!internalId) {
                console.error('编辑错误: 未提供内部文档ID');
                alert('无法编辑文档：缺少内部文档ID。');
                return;
            }
            console.log(`使用配置模板 ${templateId} 编辑文档:`, internalId);
            const encodedInternalId = encodeURIComponent(internalId);
            window.location.href = `/editor/${encodedInternalId}?template=${templateId}`;
        }

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function (event) {
            if (!event.target.matches('.edit-dropdown-btn')) {
                document.querySelectorAll('.edit-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    </script>
</body>

</html>