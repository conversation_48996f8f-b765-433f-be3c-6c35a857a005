-- ================================
-- 系统配置数据更新脚本
-- 根据用户提供的数据库配置信息更新system_settings表
-- ================================

-- 更新缓存配置
UPDATE system_settings SET setting_value = '100' WHERE setting_key = 'cache.max_size';
UPDATE system_settings SET setting_value = 'test-value-1751251026368' WHERE setting_key = 'cache.ttl';
UPDATE system_settings SET setting_value = 'memory' WHERE setting_key = 'cache.type';

-- 更新数据库初始化标记
INSERT INTO system_settings (setting_key, setting_value, description, created_at, updated_at) 
VALUES ('database_initialized', 'true', '标记数据库是否已完成初始化', NOW(), NOW())
ON DUPLICATE KEY UPDATE setting_value = 'true', updated_at = NOW();

-- 更新开发配置
UPDATE system_settings SET setting_value = 'true' WHERE setting_key = 'development.api_docs_enabled';
UPDATE system_settings SET setting_value = 'true' WHERE setting_key = 'development.debug_mode';
UPDATE system_settings SET setting_value = 'true' WHERE setting_key = 'development.hot_reload';
UPDATE system_settings SET setting_value = '/api-docs' WHERE setting_key = 'development.swagger_endpoint';

-- 更新FileNet配置
UPDATE system_settings SET setting_value = 'office_file' WHERE setting_key = 'filenet.default_biz_tag';
UPDATE system_settings SET setting_value = 'SimpleDocument' WHERE setting_key = 'filenet.default_doc_class';
UPDATE system_settings SET setting_value = '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}' WHERE setting_key = 'filenet.default_folder';
UPDATE system_settings SET setting_value = 'MaxOffice' WHERE setting_key = 'filenet.default_source_type';
UPDATE system_settings SET setting_value = '*************' WHERE setting_key = 'filenet.host';
UPDATE system_settings SET setting_value = 'YourFileNetPassword123!' WHERE setting_key = 'filenet.password';
UPDATE system_settings SET setting_value = '8090' WHERE setting_key = 'filenet.port';
UPDATE system_settings SET setting_value = 'your-filenet-username' WHERE setting_key = 'filenet.username';

-- 更新JWT配置
UPDATE system_settings SET setting_value = 'HS256' WHERE setting_key = 'jwt.onlyoffice.algorithm';
UPDATE system_settings SET setting_value = 'Authorization' WHERE setting_key = 'jwt.onlyoffice.header';
UPDATE system_settings SET setting_value = 'true' WHERE setting_key = 'jwt.onlyoffice.in_body';
UPDATE system_settings SET setting_value = 'BSaDzHAHA11SHagMzpVdZbeqMvoxMVrG' WHERE setting_key = 'jwt.onlyoffice.secret';

-- 更新日志配置
UPDATE system_settings SET setting_value = './logs' WHERE setting_key = 'logging.dir';
UPDATE system_settings SET setting_value = 'info' WHERE setting_key = 'logging.level';
UPDATE system_settings SET setting_value = '7' WHERE setting_key = 'logging.max_files';
UPDATE system_settings SET setting_value = '10m' WHERE setting_key = 'logging.max_size';

-- 更新监控配置
UPDATE system_settings SET setting_value = 'true' WHERE setting_key = 'monitoring.enabled';
UPDATE system_settings SET setting_value = '30000' WHERE setting_key = 'monitoring.request_timeout';
UPDATE system_settings SET setting_value = '1000' WHERE setting_key = 'monitoring.slow_query_threshold';

-- 更新OnlyOffice配置
UPDATE system_settings SET setting_value = '/web-apps/apps/api/documents/api.js' WHERE setting_key = 'onlyoffice.api_url_suffix';
UPDATE system_settings SET setting_value = '80' WHERE setting_key = 'onlyoffice.document_port';
UPDATE system_settings SET setting_value = 'http://*************/' WHERE setting_key = 'onlyoffice.document_server_url';
UPDATE system_settings SET setting_value = 'http://*************/' WHERE setting_key = 'onlyoffice.server_url';

-- 更新Redis配置
UPDATE system_settings SET setting_value = '0' WHERE setting_key = 'redis.db';
UPDATE system_settings SET setting_value = 'localhost' WHERE setting_key = 'redis.host';
UPDATE system_settings SET setting_value = '' WHERE setting_key = 'redis.password';
UPDATE system_settings SET setting_value = '6379' WHERE setting_key = 'redis.port';

-- 更新安全配置
UPDATE system_settings SET setting_value = 'true' WHERE setting_key = 'security.enable_security_headers';
UPDATE system_settings SET setting_value = '100' WHERE setting_key = 'security.rate_limit_max_requests';
UPDATE system_settings SET setting_value = '900000' WHERE setting_key = 'security.rate_limit_window_ms';

-- 更新服务器配置
UPDATE system_settings SET setting_value = 'localhost' WHERE setting_key = 'server.host';
UPDATE system_settings SET setting_value = 'http://localhost:3000/api/editor/callback' WHERE setting_key = 'server.callback_url';

-- 更新存储配置
UPDATE system_settings SET setting_value = '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt' WHERE setting_key = 'storage.allowed_file_types';
UPDATE system_settings SET setting_value = '52428800' WHERE setting_key = 'storage.max_file_size';
UPDATE system_settings SET setting_value = './tmp' WHERE setting_key = 'storage.tmp_path';
UPDATE system_settings SET setting_value = './uploads' WHERE setting_key = 'storage.upload_path';

-- 插入缺失的配置项（如果不存在）
INSERT IGNORE INTO system_settings (setting_key, setting_value, description, created_at, updated_at) VALUES
-- CORS配置
('cors.origin', 'http://*************:8081', 'CORS允许的源地址', NOW(), NOW()),
('cors.allowed_origins', 'http://*************:3000,http://*************:8080,http://*************:8081,http://localhost:8080,http://localhost:8081,http://localhost:3000', 'CORS允许的所有源地址列表', NOW(), NOW()),

-- 服务器网络配置（根据用户环境）
('server.host', '*************', '服务器主机地址', NOW(), NOW()),
('server.port', '3000', '服务器端口', NOW(), NOW()),
('server.frontend_port', '8080', '前端服务器端口', NOW(), NOW());

-- 显示更新结果
SELECT 'Configuration update completed' as status;
SELECT COUNT(*) as total_configs FROM system_settings;
