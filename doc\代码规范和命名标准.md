# OnlyOffice集成系统 - 代码规范和命名标准

> **更新时间**: 2024年12月19日  
> **适用范围**: 整个OnlyOffice集成系统项目  
> **强制执行**: 通过ESLint + Prettier自动检查  

## 📁 文件和目录命名规范

### 🗂️ 目录命名规范
```bash
# 使用kebab-case (短横线分隔)
backend/src/
├── controllers/           # 控制器层
├── services/             # 业务逻辑层
├── middleware/           # 中间件
├── config/              # 配置文件
├── types/               # TypeScript类型定义
├── utils/               # 工具函数
├── routes/              # 路由定义
├── validators/          # 参数验证器
├── error-handlers/      # 错误处理器
└── database/            # 数据库相关

frontend/src/
├── components/          # Vue组件
├── views/              # 页面视图
├── stores/             # Pinia状态管理
├── composables/        # 组合式函数
├── utils/              # 工具函数
├── api/                # API请求封装
├── types/              # TypeScript类型
├── assets/             # 静态资源
└── router/             # 路由配置
```

### 📄 文件命名规范

#### 后端文件命名 (PascalCase)
```typescript
// 控制器文件 - PascalCase + Controller后缀
DocumentController.ts
UserController.ts
ConfigTemplateController.ts
FilenetController.ts
SystemController.ts

// 服务类文件 - PascalCase + Service后缀
DocumentService.ts
UserService.ts
ConfigTemplateService.ts
FilenetService.ts
CacheService.ts
LoggerService.ts

// 中间件文件 - PascalCase + Middleware后缀
AuthMiddleware.ts
ValidationMiddleware.ts
ErrorHandlingMiddleware.ts
RateLimitMiddleware.ts

// 类型定义文件 - kebab-case + .types.ts后缀
document.types.ts
user.types.ts
config-template.types.ts
api-response.types.ts
filenet.types.ts

// 配置文件 - kebab-case
database.ts
app-config.ts
jwt-config.ts
onlyoffice-config.ts

// 工具函数文件 - kebab-case
date-utils.ts
file-utils.ts
crypto-utils.ts
validation-utils.ts
```

#### 前端文件命名
```vue
<!-- Vue组件 - PascalCase -->
DocumentList.vue
DocumentEditor.vue
UserManagement.vue
ConfigTemplateManager.vue
UploadButton.vue

<!-- 页面视图 - PascalCase -->
DocumentManagement.vue
UserSettings.vue
SystemConfiguration.vue
Dashboard.vue

<!-- 组合式函数 - camelCase + use前缀 -->
useDocuments.ts
useAuth.ts
useFileUpload.ts
useConfigTemplates.ts

<!-- API封装 - kebab-case + -api后缀 -->
document-api.ts
user-api.ts
config-template-api.ts
filenet-api.ts

<!-- 类型定义 - kebab-case + .types.ts后缀 -->
document.types.ts
user.types.ts
api.types.ts
```

---

## 🏷️ 变量和函数命名规范

### 📝 变量命名 (camelCase)
```typescript
// 基础变量 - camelCase
const documentId = "doc_123";
const uploadedFiles = [];
const currentUser = null;
const isAuthenticated = false;
const maxFileSize = 1024 * 1024;

// 常量 - SCREAMING_SNAKE_CASE
const MAX_UPLOAD_SIZE = 100 * 1024 * 1024;
const API_BASE_URL = "http://localhost:3000/api/v1";
const JWT_SECRET_KEY = process.env.JWT_SECRET;
const DEFAULT_PAGE_SIZE = 20;
const SUPPORTED_FILE_TYPES = ['.docx', '.xlsx', '.pptx'];

// 私有变量 - 下划线前缀 + camelCase
private _cachedDocuments = new Map();
private _connectionPool = null;
private _isInitialized = false;

// 布尔变量 - is/has/can/should前缀
const isLoading = true;
const hasPermission = false;
const canEdit = true;
const shouldRefresh = false;
const hasUnsavedChanges = true;
```

### 🔧 函数命名 (camelCase + 动词开头)

#### 控制器方法命名
```typescript
// CRUD操作 - 动词 + 名词
async getDocuments(req: Request, res: Response) {}
async getDocumentById(req: Request, res: Response) {}
async createDocument(req: Request, res: Response) {}
async updateDocument(req: Request, res: Response) {}
async deleteDocument(req: Request, res: Response) {}

// 特定业务操作 - 动词 + 描述
async uploadDocument(req: Request, res: Response) {}
async downloadDocument(req: Request, res: Response) {}
async shareDocument(req: Request, res: Response) {}
async previewDocument(req: Request, res: Response) {}
async publishDocument(req: Request, res: Response) {}

// 状态查询 - get/check + 描述
async getDocumentStatus(req: Request, res: Response) {}
async checkPermissions(req: Request, res: Response) {}
async validateConfig(req: Request, res: Response) {}
```

#### 服务层方法命名
```typescript
// 业务逻辑方法 - 明确的动作描述
async createDocumentWithTemplate(documentData: CreateDocumentDto): Promise<Document> {}
async generateDocumentPreview(documentId: string): Promise<string> {}
async synchronizeWithFilenet(documentId: string): Promise<boolean> {}
async calculateDocumentPermissions(userId: string, documentId: string): Promise<Permission[]> {}

// 数据查询方法 - find/get + 条件描述
async findDocumentsByUser(userId: string): Promise<Document[]> {}
async getActiveDocuments(): Promise<Document[]> {}
async findConfigTemplateByName(name: string): Promise<ConfigTemplate | null> {}
async getUserPermissions(userId: string): Promise<Permission[]> {}

// 验证方法 - validate/verify + 描述
async validateDocumentAccess(userId: string, documentId: string): Promise<boolean> {}
async verifyUserCredentials(username: string, password: string): Promise<User | null> {}
async checkFileTypeSupported(fileExtension: string): Promise<boolean> {}
```

#### 工具函数命名
```typescript
// 转换函数 - to/from + 格式
function toFormattedDate(date: Date): string {}
function fromTimestamp(timestamp: number): Date {}
function toBase64(buffer: Buffer): string {}
function parseJsonSafely(jsonString: string): any {}

// 验证函数 - is/has + 条件
function isValidEmail(email: string): boolean {}
function hasRequiredFields(object: any, fields: string[]): boolean {}
function isFileTypeSupported(filename: string): boolean {}
function isUserAuthorized(user: User, action: string): boolean {}

// 格式化函数 - format + 类型
function formatFileSize(bytes: number): string {}
function formatCurrency(amount: number): string {}
function sanitizeFilename(filename: string): string {}
```

---

## 🏗️ 类和接口命名规范

### 📦 类命名 (PascalCase)
```typescript
// 控制器类 - 名词 + Controller
export class DocumentController {}
export class UserController {}
export class ConfigTemplateController {}
export class SystemController {}

// 服务类 - 名词 + Service
export class DocumentService {}
export class CacheService {}
export class LoggerService {}
export class EmailService {}

// 中间件类 - 名词 + Middleware
export class AuthMiddleware {}
export class ValidationMiddleware {}
export class ErrorHandlingMiddleware {}

// 工具类 - 名词 + Utils/Helper
export class DateUtils {}
export class FileHelper {}
export class CryptoUtils {}

// 数据传输对象 - 名词 + Dto
export class CreateDocumentDto {}
export class UpdateUserDto {}
export class LoginRequestDto {}
export class ApiResponseDto<T> {}

// 实体类 - 名词 (领域对象)
export class Document {}
export class User {}
export class ConfigTemplate {}
export class Permission {}
```

### 🔌 接口命名 (I前缀 + PascalCase)
```typescript
// 服务接口 - I + 名词 + Service
export interface IDocumentService {}
export interface ICacheService {}
export interface ILoggerService {}
export interface INotificationService {}

// 存储库接口 - I + 名词 + Repository
export interface IDocumentRepository {}
export interface IUserRepository {}
export interface IConfigTemplateRepository {}

// 配置接口 - I + 名词 + Config
export interface IDatabaseConfig {}
export interface IOnlyOfficeConfig {}
export interface IFilenetConfig {}
export interface IAppConfig {}

// 响应接口 - I + 描述 + Response
export interface IApiResponse<T> {}
export interface IAuthResponse {}
export interface IUploadResponse {}
export interface IPaginatedResponse<T> {}

// 请求接口 - I + 描述 + Request
export interface ILoginRequest {}
export interface ICreateDocumentRequest {}
export interface IUpdateConfigRequest {}
```

---

## 🗄️ 数据库命名规范

### 📊 表名 (snake_case, 复数形式)
```sql
-- 主要业务表
documents
users
config_templates
permissions
file_storage_records

-- 关联表 - 表名_关联表名
user_permissions
document_shares
template_categories

-- 日志表 - 动作_logs
access_logs
error_logs
audit_logs
```

### 🏷️ 字段名 (snake_case)
```sql
-- 主键 - id
id

-- 外键 - 表名_id
user_id
document_id
template_id

-- 时间字段 - 动作_at
created_at
updated_at
deleted_at
last_accessed_at

-- 状态字段 - is_/has_
is_active
is_deleted
has_permission
is_public

-- 内容字段 - 描述性名称
file_name
file_path
file_size
mime_type
display_name
description
```

---

## 🌐 API 路由命名规范

### 🛣️ RESTful 路由 (kebab-case)
```typescript
// 基础CRUD - 资源名(复数) + HTTP方法
GET    /api/v1/documents              // 获取文档列表
GET    /api/v1/documents/:id          // 获取特定文档
POST   /api/v1/documents              // 创建新文档
PUT    /api/v1/documents/:id          // 更新文档
DELETE /api/v1/documents/:id          // 删除文档

// 嵌套资源 - 父资源/id/子资源
GET    /api/v1/users/:id/documents    // 获取用户的文档
POST   /api/v1/users/:id/documents    // 为用户创建文档
GET    /api/v1/documents/:id/versions // 获取文档版本

// 特殊操作 - 资源/id/动作
POST   /api/v1/documents/:id/share    // 分享文档
POST   /api/v1/documents/:id/publish  // 发布文档
POST   /api/v1/documents/:id/download // 下载文档
PUT    /api/v1/users/:id/activate     // 激活用户

// 集合操作 - 资源/批量动作
POST   /api/v1/documents/bulk-delete  // 批量删除
POST   /api/v1/documents/bulk-share   // 批量分享
POST   /api/v1/users/bulk-invite      // 批量邀请
```

### 🔍 查询参数命名 (camelCase)
```typescript
// 分页参数
?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc

// 过滤参数
?status=active&fileType=pdf&createdAfter=2024-01-01

// 搜索参数
?search=keyword&searchIn=title,content

// 关联参数
?include=user,permissions&exclude=content
```

---

## 📝 注释和文档规范

### 💬 函数注释 (JSDoc)
```typescript
/**
 * 创建新文档并保存到数据库
 * @param documentData - 文档创建数据
 * @param userId - 创建文档的用户ID
 * @returns Promise<Document> 创建的文档对象
 * @throws {ValidationError} 当文档数据无效时抛出
 * @throws {DatabaseError} 当数据库操作失败时抛出
 * @example
 * ```typescript
 * const document = await createDocument({
 *   name: "测试文档.docx",
 *   content: buffer
 * }, "user123");
 * ```
 */
async createDocument(documentData: CreateDocumentDto, userId: string): Promise<Document> {}

/**
 * 验证用户是否有访问指定文档的权限
 * @param userId - 用户ID
 * @param documentId - 文档ID
 * @param action - 要执行的操作 ('read' | 'write' | 'delete')
 * @returns Promise<boolean> 是否有权限
 */
async validateDocumentAccess(userId: string, documentId: string, action: PermissionAction): Promise<boolean> {}
```

### 🏷️ 类注释
```typescript
/**
 * 文档管理服务类
 * 
 * 负责处理文档的CRUD操作、权限验证、OnlyOffice集成等核心业务逻辑
 * 
 * @class DocumentService
 * @implements {IDocumentService}
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0
 */
export class DocumentService implements IDocumentService {
    /**
     * 数据库服务实例
     * @private
     * @readonly
     */
    private readonly databaseService: IDatabaseService;
    
    /**
     * 缓存服务实例
     * @private
     * @readonly
     */
    private readonly cacheService: ICacheService;
}
```

---

## 🎯 Git 提交消息规范

### 📦 提交类型
```bash
# 功能开发
feat: add document sharing functionality
feat(api): implement user authentication endpoints
feat(ui): add document upload component

# Bug修复
fix: resolve file upload timeout issue
fix(auth): correct JWT token validation logic
fix(ui): fix document list pagination

# 重构
refactor: extract document validation logic to service
refactor(db): optimize database query performance
refactor(types): reorganize TypeScript interfaces

# 文档更新
docs: update API documentation
docs(readme): add installation instructions
docs(types): add JSDoc comments to interfaces

# 样式修改
style: format code with prettier
style(ui): improve button component styling

# 测试
test: add unit tests for document service
test(e2e): add document upload flow tests

# 配置修改
chore: update dependencies
chore(build): optimize webpack configuration
chore(env): add new environment variables
```

---

## 🔧 ESLint 和 Prettier 配置

### 📏 ESLint 规则 (.eslintrc.js)
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'prettier'
  ],
  rules: {
    // 命名规范
    '@typescript-eslint/naming-convention': [
      'error',
      // 变量使用camelCase
      {
        selector: 'variable',
        format: ['camelCase', 'UPPER_CASE'],
        leadingUnderscore: 'allow'
      },
      // 函数使用camelCase
      {
        selector: 'function',
        format: ['camelCase']
      },
      // 类使用PascalCase
      {
        selector: 'class',
        format: ['PascalCase']
      },
      // 接口使用PascalCase，I前缀
      {
        selector: 'interface',
        format: ['PascalCase'],
        prefix: ['I']
      },
      // 枚举使用PascalCase
      {
        selector: 'enum',
        format: ['PascalCase']
      },
      // 类型别名使用PascalCase
      {
        selector: 'typeAlias',
        format: ['PascalCase']
      }
    ],
    
    // 其他规则
    '@typescript-eslint/explicit-function-return-type': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/prefer-readonly': 'error'
  }
};
```

### 🎨 Prettier 配置 (.prettierrc)
```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "endOfLine": "lf",
  "arrowParens": "always"
}
```

---

## 📊 命名规范总结表

| 类型 | 规范 | 示例 | 说明 |
|------|------|------|------|
| **目录** | kebab-case | `error-handlers/` | 短横线分隔 |
| **TypeScript文件** | PascalCase | `DocumentController.ts` | 首字母大写 |
| **类型定义文件** | kebab-case + .types.ts | `document.types.ts` | 短横线 + 后缀 |
| **Vue组件** | PascalCase | `DocumentList.vue` | 首字母大写 |
| **变量** | camelCase | `documentId` | 驼峰命名 |
| **常量** | SCREAMING_SNAKE_CASE | `MAX_FILE_SIZE` | 全大写下划线 |
| **函数** | camelCase | `createDocument()` | 动词开头 |
| **类** | PascalCase | `DocumentService` | 首字母大写 |
| **接口** | I + PascalCase | `IDocumentService` | I前缀 |
| **数据库表** | snake_case | `config_templates` | 下划线分隔 |
| **API路由** | kebab-case | `/api/v1/documents` | 短横线分隔 |

---

## 🎯 强制执行策略

### ✅ 自动检查
1. **pre-commit hooks** - 提交前自动格式化和检查
2. **CI/CD pipeline** - 自动化代码质量检查
3. **IDE配置** - 统一的开发环境设置
4. **代码审查** - PR中强制检查命名规范

### 📋 检查清单
- [ ] 文件名符合PascalCase/kebab-case规范
- [ ] 函数名使用camelCase且动词开头
- [ ] 变量名使用camelCase，常量使用SCREAMING_SNAKE_CASE
- [ ] 类名使用PascalCase
- [ ] 接口名使用I前缀
- [ ] API路由使用RESTful规范
- [ ] 数据库表和字段使用snake_case
- [ ] 注释完整且格式正确

---

> **制定时间**: 2024年12月19日  
> **版本**: v1.0  
> **下次更新**: 根据项目实践调整完善 