<template>
  <div class="editor-page">
    <!-- 编辑器头部 -->
    <EditorHeader
      :doc-title="docTitle"
      :save-status="saveStatus"
      :is-ready="isEditorReady"
      @force-save="handleForceSave"
      @lock-document="handleLockDocument"
      @unlock-document="handleUnlockDocument"
      @save-and-close="handleSaveAndClose"
      @direct-close="handleDirectClose"
    />

    <!-- 编辑器容器 -->
    <EditorContainer
      :is-ready="isEditorReady"
      :config="editorConfig"
      @editor-ready="handleEditorReady"
      @document-state-change="
        (event: unknown) => handleDocumentStateChange(event as DocumentStateChangeEvent)
      "
      @save="(event: unknown) => handleSave(event as SaveEvent)"
      @error="(event: unknown) => handleError(event as ErrorEvent)"
    />

    <!-- 保存并关闭进度遮罩 -->
    <div v-if="isSavingAndClosing" class="save-overlay">
      <div class="save-modal">
        <div class="modal-header">
          <h3>正在保存文档</h3>
        </div>
        <div class="modal-body">
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${saveProgress.progress}%` }"></div>
            </div>
            <div class="progress-text">{{ saveProgress.progress }}%</div>
          </div>
          <div class="step-info">
            <div class="step-indicator">
              <span class="step-icon">{{ getStepIcon(saveProgress.step) }}</span>
              <span class="step-text">{{ saveProgress.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知面板 -->
    <NotificationPanel :notification="{ show: false, type: 'info', message: '', duration: 0 }" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useEditor } from './composables/useEditor'
import { useSaveStatus } from './composables/useSaveStatus'
import { useNotification } from './composables/useNotification'
import EditorHeader from './components/EditorHeader.vue'
import EditorContainer from './components/EditorContainer.vue'
import NotificationPanel from './components/NotificationPanel.vue'
import type { DocumentStateChangeEvent, SaveEvent, ErrorEvent } from '@/types/api.types'

/**
 * OnlyOffice编辑器页面组件
 *
 * @description 主编辑器页面，集成以下功能：
 * - OnlyOffice编辑器初始化和管理
 * - 文档保存状态跟踪
 * - 用户操作反馈和通知
 * - 错误处理和通知
 *
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

const route = useRoute()
const router = useRouter()

// 支持普通文档和模板会话两种模式
const fileId = computed(() => {
  if (route.params.sessionId) {
    // 模板会话模式：sessionId
    return route.params.sessionId as string
  } else {
    // 普通文档模式：id
    return route.params.id as string
  }
})

const isTemplateSession = computed(() => !!route.params.sessionId)
const templateId = computed(() => route.query.templateId as string)
const documentName = computed(() => route.query.documentName as string)
const configTemplate = computed(() => (route.query.template as string) || 'default-edit')

// 组合式函数
const { editorConfig, isEditorReady, initializeEditor, forceSave, destroyEditor } =
  useEditor(fileId)

const { saveStatus, startAutoCheck, stopAutoCheck } = useSaveStatus(fileId)

const { showNotification } = useNotification()

// 响应式状态
const docTitle = ref('加载中...')
const isLoading = ref(true)

// 保存并关闭相关状态
const isSavingAndClosing = ref(false)
const saveProgress = ref({
  step: 'init' as 'init' | 'intent' | 'version' | 'save' | 'monitor' | 'complete',
  progress: 0,
  message: '准备保存...',
})

/**
 * 获取步骤图标
 */
const getStepIcon = (step: string): string => {
  const icons = {
    init: '⏳',
    intent: '🎯',
    version: '📊',
    save: '💾',
    monitor: '👀',
    complete: '✅',
  }
  return icons[step as keyof typeof icons] || '⏳'
}

/**
 * 保存并关闭处理
 */
const handleSaveAndClose = async (): Promise<void> => {
  try {
    console.log('🔄 开始保存并关闭流程...')

    // 显示进度遮罩
    isSavingAndClosing.value = true
    saveProgress.value = {
      step: 'intent',
      progress: 10,
      message: '正在设置保存意图...',
    }

    // 1. 设置用户意图为保存
    try {
      const response = await fetch(`/api/documents/${fileId.value}/close-intent`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ intent: 'save' }),
      })

      if (response.ok) {
        console.log('✅ 已设置用户意图为保存')
      } else {
        console.warn('⚠️ 设置用户意图失败，但继续保存流程')
      }
    } catch (intentError) {
      console.warn('⚠️ 设置用户意图出错，但继续保存流程:', intentError)
    }

    // 2. 先获取初始版本号（在强制保存之前）
    saveProgress.value = {
      step: 'version',
      progress: 25,
      message: '正在获取文档版本信息...',
    }

    let initialVersion = 0
    try {
      const initialResponse = await fetch(`/api/documents/${fileId.value}/save-status`)
      if (initialResponse.ok) {
        const initialData = await initialResponse.json()
        console.log('🔍 初始状态完整数据:', JSON.stringify(initialData, null, 2))

        // 修复版本号提取 - 处理嵌套的data结构
        initialVersion =
          initialData?.data?.data?.version ||
          initialData?.data?.version ||
          initialData?.version ||
          0
        console.log(`📊 提取到的初始版本号: ${initialVersion} (类型: ${typeof initialVersion})`)
      } else {
        console.warn('⚠️ 获取初始状态失败，response not ok')
      }
    } catch (error) {
      console.warn('⚠️ 获取初始版本失败:', error)
    }

    // 3. 执行强制保存
    saveProgress.value = {
      step: 'save',
      progress: 40,
      message: '正在执行强制保存...',
    }

    showNotification('正在保存文档...', 'info')
    await forceSave()
    showNotification('文档保存成功', 'success')

    // 4. 监控保存进度
    saveProgress.value = {
      step: 'monitor',
      progress: 40,
      message: '正在监控保存进度...',
    }

    console.log(`👀 开始监控保存进度... 初始版本=${initialVersion}`)

    const startTime = Date.now()
    const timeout = 20000 // 20秒超时
    const pollInterval = 2000 // 每2秒检查一次

    const checkSaveProgress = async (): Promise<boolean> => {
      try {
        const response = await fetch(`/api/documents/${fileId.value}/save-status`)
        if (!response.ok) {
          console.warn('⚠️ 检查保存状态失败')
          return false
        }

        const statusData = await response.json()
        console.log('🔍 当前状态完整数据:', JSON.stringify(statusData, null, 2))

        // 修复版本号提取 - 处理嵌套的data结构
        const currentVersion =
          statusData?.data?.data?.version || statusData?.data?.version || statusData?.version || 0

        console.log(
          `📊 版本比较: 当前=${currentVersion} (${typeof currentVersion}) vs 初始=${initialVersion} (${typeof initialVersion})`
        )

        // 确保数字比较
        const currentVersionNum = Number(currentVersion)
        const initialVersionNum = Number(initialVersion)
        console.log(`📊 数字版本比较: 当前=${currentVersionNum} vs 初始=${initialVersionNum}`)

        if (currentVersionNum > initialVersionNum) {
          console.log('🎉 检测到版本变化，保存完成！')
          return true
        } else {
          const elapsed = Date.now() - startTime
          const progressPercent = Math.min(85, 40 + (elapsed / timeout) * 45)
          saveProgress.value = {
            step: 'monitor',
            progress: progressPercent,
            message: `版本号未变化，继续等待... (${elapsed}ms/${timeout}ms)`,
          }
          console.log(`⏳ 版本号未变化，继续等待... (${elapsed}ms/${timeout}ms)`)
          return false
        }
      } catch (error) {
        console.warn('⚠️ 检查保存状态时出错:', error)
        return false
      }
    }

    return new Promise<void>((resolve, reject) => {
      const pollTimer = setInterval(async () => {
        const isComplete = await checkSaveProgress()

        if (isComplete) {
          clearInterval(pollTimer)
          saveProgress.value = {
            step: 'complete',
            progress: 100,
            message: '保存完成，正在关闭...',
          }

          setTimeout(() => {
            isSavingAndClosing.value = false
            console.log('🚀 保存并关闭流程完成，返回文档列表')
            router.push('/documents')
            resolve()
          }, 800)
          return
        }

        // 检查超时
        const elapsed = Date.now() - startTime
        if (elapsed >= timeout) {
          clearInterval(pollTimer)

          // 超时后做最后一次检查
          console.log('⏰ 达到超时时间，进行最后一次检查...')
          const finalCheck = await checkSaveProgress()

          if (finalCheck) {
            saveProgress.value = {
              step: 'complete',
              progress: 100,
              message: '保存完成，正在关闭...',
            }

            setTimeout(() => {
              isSavingAndClosing.value = false
              console.log('🚀 最终检查成功，返回文档列表')
              router.push('/documents')
              resolve()
            }, 800)
          } else {
            // 真正超时了
            saveProgress.value = {
              step: 'monitor',
              progress: 90,
              message: '保存检测超时，请选择操作...',
            }

            showNotification(
              '保存检测超时，文档可能已保存但无法确认。您可以选择直接关闭或继续等待。',
              'warning'
            )

            setTimeout(() => {
              isSavingAndClosing.value = false
              reject(new Error('保存检测超时'))
            }, 3000)
          }
        }
      }, pollInterval)
    })
  } catch (error) {
    console.error('❌ 保存并关闭失败:', error)
    isSavingAndClosing.value = false
    showNotification(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error')
  }
}

/**
 * 直接关闭处理
 */
const handleDirectClose = async (): Promise<void> => {
  try {
    console.log('🔄 开始直接关闭流程...')

    // 设置用户意图为不保存
    try {
      const response = await fetch(`/api/documents/${fileId.value}/close-intent`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ intent: 'no-save' }),
      })

      if (response.ok) {
        console.log('✅ 已设置用户意图为不保存')
      } else {
        console.warn('⚠️ 设置用户意图失败')
      }
    } catch (intentError) {
      console.warn('⚠️ 设置用户意图出错:', intentError)
    }

    // 直接关闭
    console.log('🚀 直接关闭，返回文档列表')
    router.push('/documents')
  } catch (error) {
    console.error('❌ 直接关闭失败:', error)
    showNotification(`关闭失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error')
  }
}

/**
 * 强制保存处理
 */
const handleForceSave = async (): Promise<void> => {
  try {
    showNotification('正在强制保存...', 'info')
    await forceSave()
    showNotification('强制保存成功', 'success')
  } catch (error) {
    console.error('❌ 强制保存失败:', error)
    showNotification(
      `强制保存失败: ${error instanceof Error ? error.message : '未知错误'}`,
      'error'
    )
  }
}

/**
 * 文档加锁处理
 */
const handleLockDocument = (): void => {
  showNotification('文档加锁功能开发中...', 'info')
}

/**
 * 文档解锁处理
 */
const handleUnlockDocument = (): void => {
  showNotification('文档解锁功能开发中...', 'info')
}

/**
 * 编辑器就绪处理
 */
const handleEditorReady = (): void => {
  console.log('📝 [EditorPage] 编辑器已就绪')
  startAutoCheck()
}

/**
 * 文档状态变化处理
 */
const handleDocumentStateChange = (event: DocumentStateChangeEvent): void => {
  console.log('📝 [EditorPage] 文档状态变更:', event)
}

/**
 * 保存事件处理
 */
const handleSave = (event: SaveEvent): void => {
  console.log('💾 [EditorPage] 保存事件:', event)
  showNotification('文档已保存', 'success')
}

/**
 * 错误处理
 */
const handleError = (event: ErrorEvent): void => {
  console.error('❌ [EditorPage] 编辑器错误:', event)
  showNotification(`编辑器错误: ${event.data}`, 'error')
}

/**
 * 初始化页面
 */
const initializePage = async (): Promise<void> => {
  try {
    isLoading.value = true
    console.log('🔄 [EditorPage] 开始初始化编辑器页面')
    console.log('📋 [EditorPage] 文档ID:', fileId.value)
    console.log('⚙️ [EditorPage] 配置模板:', configTemplate.value)

    await initializeEditor({ template: configTemplate.value })

    // 设置文档标题
    const config = editorConfig.value
    if (isTemplateSession.value) {
      // 模板会话模式：显示特殊标题
      docTitle.value = documentName.value || '基于模板的新文档.docx'
    } else if (config?.document?.title) {
      // 普通文档模式：使用配置中的标题
      docTitle.value = config.document.title
    }

    console.log('✅ [EditorPage] 编辑器页面初始化完成')
  } catch (error) {
    console.error('❌ [EditorPage] 编辑器页面初始化失败:', error)
    showNotification(`初始化失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error')
  } finally {
    isLoading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  initializePage()
})

onUnmounted(() => {
  stopAutoCheck()
  destroyEditor()
})
</script>

<style scoped>
.editor-page {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  flex: 1;
}

/* 保存进度遮罩样式 */
.save-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.save-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  text-align: center;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
}

.modal-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  min-width: 45px;
  text-align: right;
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.step-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.step-icon {
  font-size: 16px;
  animation: stepPulse 2s infinite;
}

@keyframes stepPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.step-text {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .save-modal {
    min-width: 320px;
    margin: 20px;
  }

  .progress-container {
    flex-direction: column;
    align-items: stretch;
  }

  .progress-text {
    text-align: center;
    min-width: auto;
  }
}
</style>
