<template>
  <div class="onlyoffice-config-page">
    <!-- 使用标准的a-page-header组件保持一致性 -->
    <a-page-header title="OnlyOffice配置模板管理" sub-title="管理文档编辑器的权限和功能配置">
      <template #extra>
        <a-space v-if="selectedTemplate">
          <a-button @click="resetConfig">
            <template #icon>
              <undo-outlined />
            </template>
            重置
          </a-button>
          <a-button type="primary" @click="handleSaveCurrentConfig" :loading="saving">
            <template #icon>
              <save-outlined />
            </template>
            保存
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 主要内容 -->
    <div class="content-wrapper">
      <div class="main-layout">
        <!-- 左侧模板列表 -->
        <aside class="templates-section">
          <div class="section-header">
            <div>
              <h2 class="section-title">配置模板</h2>
              <p class="section-subtitle">快速选择和编辑</p>
            </div>
            <div class="header-controls">
              <a-tooltip title="显示全部模板（包括已禁用的）">
                <a-switch
                  v-model:checked="showAllTemplates"
                  size="small"
                  :checked-children="'全部'"
                  :un-checked-children="'启用'"
                />
              </a-tooltip>
              <a-button @click="showCreateModal" type="primary" size="small" class="add-btn">
                <template #icon><plus-outlined /></template>
              </a-button>
            </div>
          </div>

          <div class="search-container">
            <a-input
              v-model:value="searchKeyword"
              placeholder="搜索模板..."
              size="small"
              class="search-input"
              @input="handleSearch"
            >
              <template #prefix><search-outlined class="search-icon" /></template>
            </a-input>
          </div>

          <div class="templates-list">
            <div
              v-for="template in filteredTemplates"
              :key="template.id"
              :class="[
                'template-card',
                {
                  active: selectedTemplateId === template.id,
                  disabled: !template.isActive,
                },
              ]"
              @click="selectTemplate(template)"
            >
              <div class="template-header">
                <div class="template-name">
                  {{ template.name }}
                  <span v-if="!template.isActive" class="disabled-label">（已禁用）</span>
                </div>
                <div class="template-status">
                  <span v-if="template.isDefault" class="status-badge status-default">默认</span>
                  <span
                    :class="[
                      'status-badge',
                      template.isActive ? 'status-active' : 'status-inactive',
                    ]"
                  >
                    {{ template.isActive ? '启用' : '禁用' }}
                  </span>
                </div>
              </div>
              <div class="template-desc">{{ template.description }}</div>
              <div class="template-meta">
                <span>{{ formatDate(template.updatedAt) }}</span>
                <div class="template-actions" @click.stop>
                  <a-dropdown>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item
                          key="setDefault"
                          @click="setDefaultTemplate(template)"
                          v-if="!template.isDefault"
                        >
                          <star-outlined />
                          设为默认
                        </a-menu-item>
                        <a-menu-item key="duplicate" @click="duplicateTemplate(template)">
                          <copy-outlined />
                          复制模板
                        </a-menu-item>
                        <a-menu-item key="export" @click="exportTemplate(template)">
                          <download-outlined />
                          导出模板
                        </a-menu-item>
                        <a-menu-item key="toggle" @click="toggleTemplateStatus(template)">
                          <switcher-outlined />
                          {{ template.isActive ? '禁用模板' : '启用模板' }}
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item
                          key="disable"
                          @click="disableTemplate(template)"
                          class="danger-item"
                          v-if="template.isActive"
                        >
                          <delete-outlined />
                          永久禁用
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <button class="action-btn" title="更多操作">
                      <more-outlined />
                    </button>
                  </a-dropdown>
                </div>
              </div>
            </div>

            <div v-if="filteredTemplates.length === 0 && !loading" class="empty-state">
              <div class="empty-icon">
                <file-text-outlined />
              </div>
              <div class="empty-title">暂无模板</div>
              <div class="empty-desc">点击右上角按钮创建新的配置模板</div>
            </div>
          </div>
        </aside>

        <!-- 右侧配置区域 -->
        <main class="config-section">
          <div v-if="!selectedTemplate" class="empty-config">
            <div class="empty-config-content">
              <file-text-outlined class="empty-config-icon" />
              <h3 class="empty-config-title">请选择配置模板</h3>
              <p class="empty-config-desc">从左侧列表中选择一个模板来查看和编辑配置</p>
            </div>
          </div>

          <div v-else class="config-panel">
            <div class="config-header">
              <h2 class="config-title">{{ selectedTemplate.name }}</h2>
              <p class="config-subtitle">快速配置文档编辑器的权限和功能</p>
            </div>

            <nav class="config-nav">
              <button
                v-for="tab in configTabs"
                :key="tab.key"
                :class="['nav-tab', { active: activeTab === tab.key }]"
                @click="activeTab = tab.key"
              >
                {{ tab.label }}
              </button>
            </nav>

            <div class="config-content" ref="configContentRef">
              <ConfigDetail
                ref="configDetailRef"
                :template-data="selectedTemplate"
                :initial-config="selectedTemplateConfig"
                :initial-config-states="selectedTemplateConfigStates"
                :active-tab="activeTab"
                @save="handleSaveConfig"
                @reset="handleResetConfig"
                @tab-change="activeTab = $event"
              />
            </div>
          </div>
        </main>
      </div>

      <!-- 创建/编辑模板模态框 -->
      <a-modal
        v-model:open="modalVisible"
        :title="editingTemplate ? '编辑模板' : '创建新模板'"
        width="600px"
        @ok="handleCreateTemplate"
        @cancel="handleModalCancel"
        :confirm-loading="saving"
      >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板名称" name="name">
                <a-input v-model:value="formData.name" placeholder="请输入模板名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="模板状态">
                <a-space>
                  <a-switch v-model:checked="formData.isActive" />
                  <span>{{ formData.isActive ? '启用' : '禁用' }}</span>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="模板描述" name="description">
            <a-textarea
              v-model:value="formData.description"
              placeholder="请输入模板描述"
              :rows="3"
            />
          </a-form-item>
          <a-form-item label="设为默认模板">
            <a-switch v-model:checked="formData.isDefault" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 导入配置模态框 -->
      <a-modal
        v-model:open="importModalVisible"
        title="导入配置模板"
        width="600px"
        @ok="handleImportTemplate"
        @cancel="importModalVisible = false"
        :confirm-loading="importing"
      >
        <div class="import-section">
          <a-upload-dragger
            v-model:fileList="importFileList"
            :before-upload="beforeUpload"
            accept=".json"
            :multiple="false"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持JSON格式的配置文件</p>
          </a-upload-dragger>
        </div>
      </a-modal>

      <!-- 复制模板重命名模态框 -->
      <a-modal
        v-model:open="duplicateModalVisible"
        title="复制配置模板"
        width="600px"
        @ok="handleDuplicateTemplate"
        @cancel="handleDuplicateCancel"
        :confirm-loading="saving"
        :ok-text="'确认复制'"
        :cancel-text="'取消'"
      >
        <div class="duplicate-template-form">
          <a-form
            ref="duplicateFormRef"
            :model="duplicateFormData"
            :rules="duplicateFormRules"
            layout="vertical"
          >
            <a-alert
              v-if="duplicatingTemplate"
              :message="`正在复制模板: ${duplicatingTemplate.name}`"
              :description="`将复制所有配置参数和设置，您可以自定义新模板的名称和描述`"
              type="info"
              show-icon
              style="margin-bottom: 20px"
            />

            <a-form-item label="新模板名称" name="name">
              <a-input
                v-model:value="duplicateFormData.name"
                placeholder="请输入新模板的名称"
                allow-clear
              />
              <div class="form-help-text">
                <small>系统已自动生成唯一名称，您可以根据需要修改</small>
              </div>
            </a-form-item>

            <a-form-item label="模板描述" name="description">
              <a-textarea
                v-model:value="duplicateFormData.description"
                placeholder="请输入新模板的描述（可选）"
                :rows="3"
                allow-clear
              />
            </a-form-item>

            <a-divider>
              <span class="divider-text">复制说明</span>
            </a-divider>

            <div class="copy-info">
              <ul class="copy-info-list">
                <li>✅ 所有配置参数和设置将被完整复制</li>
                <li>✅ 配置项的启用状态和必需属性将被保留</li>
                <li>✅ 复制完成后将自动选择新模板供您编辑</li>
                <li>ℹ️ 新模板的状态将默认为"启用"</li>
              </ul>
            </div>
          </a-form>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  DownloadOutlined,
  SaveOutlined,
  SearchOutlined,
  FileTextOutlined,
  CopyOutlined,
  MoreOutlined,
  StarOutlined,
  SwitcherOutlined,
  UndoOutlined,
  InboxOutlined,
} from '@ant-design/icons-vue'
import ConfigDetail, { type OnlyOfficeConfig } from './ConfigDetail.vue'

/**
 * ConfigDetail组件实例类型
 */
interface ConfigDetailInstance {
  saveConfig: () => void
  resetConfig: () => void
  configStates: () => Record<string, Record<string, ConfigItemState>>
}

// 接口定义
interface ConfigTemplate {
  id: string
  name: string
  description: string
  isDefault: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface ConfigItem {
  config_group: string
  config_key: string
  config_value: string
  value_type: string
  is_enabled: boolean
  is_required: boolean
  description: string
}

interface ConfigItemState {
  value: unknown
  enabled: boolean
  required: boolean
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const showAllTemplates = ref(false) // 控制显示全部模板还是仅启用的模板
const templates = ref<ConfigTemplate[]>([])
const selectedTemplateId = ref<string>('')
const selectedTemplate = ref<ConfigTemplate | null>(null)
const selectedTemplateConfig = ref<Partial<OnlyOfficeConfig> | undefined>(undefined)
const selectedTemplateConfigStates = ref<
  Record<string, Record<string, ConfigItemState>> | undefined
>(undefined)
const modalVisible = ref(false)
const editingTemplate = ref<ConfigTemplate | null>(null)
const formRef = ref<FormInstance>()
const importModalVisible = ref(false)
const importing = ref(false)
const importFileList = ref<File[]>([])
const configContentRef = ref<HTMLElement | null>(null)
const configDetailRef = ref<ConfigDetailInstance | null>(null)
const duplicateModalVisible = ref(false)
const duplicateFormRef = ref<FormInstance>()
const duplicatingTemplate = ref<ConfigTemplate | null>(null)
const activeTab = ref<string>('permissions')
const configTabs = ref([
  { key: 'permissions', label: '权限' },
  { key: 'customization', label: '界面' },
  { key: 'features', label: '功能' },
  { key: 'coEditing', label: '协作' },
  { key: 'user', label: '用户' },
  { key: 'layout', label: '布局' },
  { key: 'review', label: '审阅' },
  { key: 'server', label: '服务器' },
  { key: 'mobile', label: '移动端' },
  { key: 'customer', label: '客户' },
])

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  isDefault: false,
  isActive: true,
})

// 复制模板表单数据
const duplicateFormData = reactive({
  name: '',
  description: '',
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入模板描述', trigger: 'blur' }],
}

// 复制模板表单验证规则
const duplicateFormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    {
      validator: (_rule: unknown, value: string) => {
        if (!value || !value.trim()) {
          return Promise.reject('模板名称不能为空')
        }
        const existingNames = templates.value.map(t => t.name)
        if (existingNames.includes(value.trim())) {
          return Promise.reject('模板名称已存在，请选择其他名称')
        }
        return Promise.resolve()
      },
      trigger: 'blur',
    },
  ],
  description: [{ required: false }],
}

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates.value

  // 根据开关过滤启用状态
  if (!showAllTemplates.value) {
    result = result.filter(template => template.isActive)
  }

  // 根据搜索关键词过滤
  if (searchKeyword.value) {
    result = result.filter(
      template =>
        template.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        template.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return result
})

// API调用函数
const apiCall = async <T,>(url: string, options?: RequestInit): Promise<T> => {
  try {
    const response = await fetch(`http://*************:3000/api${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.message || 'API请求失败')
    }

    return data
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  }
}

// 数据加载
const loadTemplates = async () => {
  try {
    loading.value = true
    const response = await apiCall<{ data: ConfigTemplate[] }>('/config-templates')
    templates.value = response.data || []

    // 如果没有选中的模板，选择第一个
    if (templates.value.length > 0 && !selectedTemplateId.value) {
      await selectTemplate(templates.value[0])
    }
  } catch (error) {
    console.error('加载模板列表失败:', error)
    message.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

const loadTemplateConfig = async (templateId: string) => {
  try {
    const response = await apiCall<{
      data: {
        template: ConfigTemplate
        config: Partial<OnlyOfficeConfig>
        configStates: Record<string, Record<string, ConfigItemState>>
      }
    }>(`/config-templates/${templateId}`)

    return response.data
  } catch (error) {
    console.error('加载模板配置失败:', error)
    message.error('加载模板配置失败')
    return null
  }
}

// 模板操作
const selectTemplate = async (template: ConfigTemplate) => {
  selectedTemplateId.value = template.id
  selectedTemplate.value = template

  // 加载配置详情
  const templateData = await loadTemplateConfig(template.id)
  if (templateData) {
    selectedTemplateConfig.value = templateData.config
    // 将configStates传递给ConfigDetail组件
    selectedTemplateConfigStates.value = templateData.configStates
  }
}

const showCreateModal = () => {
  editingTemplate.value = null
  Object.assign(formData, {
    name: '',
    description: '',
    isDefault: false,
    isActive: true,
  })
  modalVisible.value = true
}

// 模板信息编辑功能（暂时未使用）

const handleCreateTemplate = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    const templateData = {
      name: formData.name,
      description: formData.description,
      isDefault: formData.isDefault,
      isActive: formData.isActive,
      configItems: [], // 空配置，后续可以编辑
    }

    if (editingTemplate.value) {
      // 更新模板
      await apiCall(`/config-templates/${editingTemplate.value.id}`, {
        method: 'PUT',
        body: JSON.stringify(templateData),
      })
      message.success('模板更新成功')
    } else {
      // 创建模板
      await apiCall('/config-templates', {
        method: 'POST',
        body: JSON.stringify(templateData),
      })
      message.success('模板创建成功')
    }

    modalVisible.value = false
    await loadTemplates()
  } catch (error) {
    console.error('保存模板失败:', error)
    message.error('保存模板失败')
  } finally {
    saving.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const setDefaultTemplate = async (template: ConfigTemplate) => {
  try {
    await apiCall(`/config-templates/${template.id}/set-default`, {
      method: 'PUT',
    })
    message.success(`已将 "${template.name}" 设为默认模板`)
    await loadTemplates()
  } catch (error) {
    console.error('设置默认模板失败:', error)
    message.error('设置默认模板失败')
  }
}

/**
 * 将configStates转换为后端期望的configItems格式
 */
const convertConfigStatesToItems = (
  configStates: Record<string, Record<string, ConfigItemState>>
) => {
  const configItems: ConfigItem[] = []

  Object.keys(configStates).forEach(groupKey => {
    const group = configStates[groupKey]
    Object.keys(group).forEach(configKey => {
      const itemState = group[configKey]

      // 确定值类型
      let valueType = 'string'
      let configValue = String(itemState.value)

      if (typeof itemState.value === 'boolean') {
        valueType = 'boolean'
        configValue = String(itemState.value)
      } else if (typeof itemState.value === 'number') {
        valueType = 'number'
        configValue = String(itemState.value)
      } else if (typeof itemState.value === 'object') {
        valueType = 'object'
        configValue = JSON.stringify(itemState.value)
      }

      configItems.push({
        config_group: groupKey,
        config_key: configKey,
        config_value: configValue,
        value_type: valueType,
        is_enabled: itemState.enabled !== false,
        is_required: itemState.required || false,
        description: `${groupKey}.${configKey}配置项`,
      })
    })
  })

  return configItems
}

/**
 * 生成唯一的模板名称，避免重复
 */
const generateUniqueName = (baseName: string): string => {
  const existingNames = templates.value.map(t => t.name)
  let newName = `${baseName} - 副本`
  let counter = 1

  while (existingNames.includes(newName)) {
    counter++
    newName = `${baseName} - 副本${counter}`
  }

  return newName
}

/**
 * 显示复制模板的重命名弹窗
 */
const duplicateTemplate = (template: ConfigTemplate) => {
  duplicatingTemplate.value = template

  // 生成建议的名称
  const suggestedName = generateUniqueName(template.name)

  // 预填充表单
  duplicateFormData.name = suggestedName
  duplicateFormData.description = `${template.description} (副本)`

  // 显示弹窗
  duplicateModalVisible.value = true
}

/**
 * 执行实际的模板复制操作
 */
const handleDuplicateTemplate = async () => {
  if (!duplicatingTemplate.value) return

  try {
    // 验证表单
    await duplicateFormRef.value?.validate()

    // 首先获取模板的完整配置
    const templateData = await loadTemplateConfig(duplicatingTemplate.value.id)
    if (!templateData) return

    // 将configStates转换为configItems格式
    const configItems = convertConfigStatesToItems(templateData.configStates || {})

    const newTemplate = {
      name: duplicateFormData.name.trim(),
      description: duplicateFormData.description.trim() || duplicateFormData.name.trim(),
      configItems: configItems, // 使用转换后的configItems格式
    }

    console.log('🔄 复制模板数据:', {
      originalName: duplicatingTemplate.value.name,
      newName: newTemplate.name,
      configItemsCount: configItems.length,
      configStates: templateData.configStates,
      convertedItems: configItems,
      newTemplate: newTemplate,
    })

    // 验证configItems的数据格式
    if (configItems.length === 0) {
      message.warning('原模板没有配置项，将创建空模板')
    } else {
      const sampleItem = configItems[0]
      console.log('📝 配置项样例:', sampleItem)

      // 验证必要字段
      const requiredFields = ['config_group', 'config_key', 'config_value', 'value_type']
      const missingFields = requiredFields.filter(field => !(field in sampleItem))
      if (missingFields.length > 0) {
        console.error('❌ 配置项缺少必要字段:', missingFields)
        message.error(`配置项数据格式错误，缺少字段: ${missingFields.join(', ')}`)
        return
      }
    }

    try {
      await apiCall('/config-templates', {
        method: 'POST',
        body: JSON.stringify(newTemplate),
      })
      message.success(`模板"${newTemplate.name}"复制成功，包含 ${configItems.length} 个配置参数`)

      // 关闭弹窗
      duplicateModalVisible.value = false

      // 重新加载模板列表
      await loadTemplates()

      // 自动选择新创建的副本模板
      setTimeout(async () => {
        const copiedTemplate = templates.value.find(t => t.name === newTemplate.name)
        if (copiedTemplate) {
          await selectTemplate(copiedTemplate)
          message.info('已自动选择复制的模板，您可以查看复制的配置参数')
        }
      }, 500)
    } catch (apiError: unknown) {
      console.error('❌ API调用详细错误:', apiError)

      // 尝试解析错误信息
      const error = apiError as Error
      const errorMessage = error?.message || '未知错误'
      if (errorMessage.includes('不能为空')) {
        message.error('数据验证失败：必要字段不能为空')
      } else if (errorMessage.includes('数据库')) {
        message.error('数据库操作失败，请检查数据库连接')
      } else {
        message.error(`复制模板失败: ${errorMessage}`)
      }
    }
  } catch (error) {
    console.error('复制模板失败:', error)
    message.error('复制模板失败')
  }
}

/**
 * 取消复制模板
 */
const handleDuplicateCancel = () => {
  duplicateModalVisible.value = false
  duplicatingTemplate.value = null
  duplicateFormRef.value?.resetFields()
}

const exportTemplate = async (template: ConfigTemplate) => {
  try {
    const templateData = await loadTemplateConfig(template.id)
    if (!templateData) return

    const exportData = {
      template: template,
      config: templateData.config,
      configStates: templateData.configStates, // 包含配置状态数据
      exportTime: new Date().toISOString(),
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${template.name}-配置模板.json`
    link.click()
    URL.revokeObjectURL(url)
    message.success('模板导出成功')
  } catch (error) {
    console.error('导出模板失败:', error)
    message.error('导出模板失败')
  }
}

// 导出功能已集成到exportTemplate中

const toggleTemplateStatus = async (template: ConfigTemplate) => {
  try {
    await apiCall(`/config-templates/${template.id}`, {
      method: 'PUT',
      body: JSON.stringify({
        isActive: !template.isActive,
      }),
    })
    message.success(`模板已${template.isActive ? '禁用' : '启用'}`)
    await loadTemplates()
  } catch (error) {
    console.error('切换模板状态失败:', error)
    message.error('切换模板状态失败')
  }
}

const disableTemplate = (template: ConfigTemplate) => {
  Modal.confirm({
    title: '确认禁用',
    content: `确定要永久禁用模板 "${template.name}" 吗？禁用后模板将不再显示在列表中（除非开启"显示全部"）。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await apiCall(`/config-templates/${template.id}`, {
          method: 'DELETE',
        })
        message.success('模板已禁用')

        // 如果禁用的是当前选中的模板，清空选择
        if (selectedTemplateId.value === template.id) {
          selectedTemplateId.value = ''
          selectedTemplate.value = null
          selectedTemplateConfig.value = undefined
        }

        await loadTemplates()
      } catch (error) {
        console.error('禁用模板失败:', error)
        message.error('禁用模板失败')
      }
    },
  })
}

const handleSaveConfig = async (configStates: Record<string, Record<string, ConfigItemState>>) => {
  if (!selectedTemplate.value) return

  try {
    saving.value = true
    console.log('🔄 开始保存配置...', { templateId: selectedTemplate.value.id, configStates })

    await apiCall(`/config-templates/${selectedTemplate.value.id}`, {
      method: 'PUT',
      body: JSON.stringify({
        configStates,
      }),
    })

    console.log('✅ 配置保存成功')
    message.success('配置保存成功')

    // ✅ 修复：直接更新本地状态，而不重新从服务器加载
    // 这样可以保持用户刚刚修改的配置
    selectedTemplateConfigStates.value = { ...configStates }
  } catch (error) {
    console.error('❌ 保存配置失败:', error)
    message.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

const handleSaveCurrentConfig = () => {
  // ✅ 修复：从ConfigDetail组件获取最新的配置状态
  if (configDetailRef.value && configDetailRef.value.configStates) {
    const currentStates =
      typeof configDetailRef.value.configStates === 'function'
        ? configDetailRef.value.configStates()
        : configDetailRef.value.configStates
    handleSaveConfig(currentStates)
  } else if (selectedTemplateConfigStates.value) {
    // 备用方案：如果ref不可用，使用本地状态
    handleSaveConfig(selectedTemplateConfigStates.value)
  } else {
    message.warning('没有可保存的配置')
  }
}

const resetConfig = () => {
  Modal.confirm({
    title: '确认重置',
    content: '确定要重置当前模板的配置吗？这将恢复到默认设置。',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      handleResetConfig()
    },
  })
}

const handleResetConfig = () => {
  // ✅ 修复：调用ConfigDetail组件的重置方法
  if (configDetailRef.value && configDetailRef.value.resetConfig) {
    configDetailRef.value.resetConfig()
    message.success('配置已重置')
  } else {
    // 备用方案：重新加载配置
    if (selectedTemplate.value) {
      selectTemplate(selectedTemplate.value)
      message.success('配置已重置')
    }
  }
}

// 配置预览功能（暂时未使用）

// 工具函数
const handleSearch = () => {
  // 搜索在计算属性中处理
}

const formatDate = (dateStr: string): string => {
  if (!dateStr) return '未知'

  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      return '未知'
    }

    const now = new Date()
    const diffInMs = now.getTime() - date.getTime()
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

    if (diffInDays === 0) {
      return '今天'
    } else if (diffInDays === 1) {
      return '昨天'
    } else if (diffInDays < 7) {
      return `${diffInDays}天前`
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7)
      return `${weeks}周前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  } catch (error) {
    return '未知'
  }
}

// 生命周期
onMounted(() => {
  loadTemplates()
})

const handleImportTemplate = () => {
  // 实现导入模板的功能
}

const beforeUpload = () => {
  // 实现上传前的验证逻辑
  return false // 阻止自动上传
}
</script>

<style scoped>
.onlyoffice-config-page {
  /* 保持与其他页面一致的样式 */
  padding: 0;
}

.content-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
}

/* 优化的主布局 - 移除固定高度和外层overflow */
.main-layout {
  display: grid;
  grid-template-columns: 360px 1fr;
  gap: 24px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  height: calc(100vh - 200px); /* 减少高度计算，为页面头部留出更合理的空间 */
  min-height: 600px; /* 设置最小高度确保在小屏幕上的可用性 */
}

/* 左侧模板区域 - 优化滚动控制 */
.templates-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e7;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 只允许内部滚动 */
  min-height: 0;
  height: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.section-subtitle {
  font-size: 11px;
  color: #718096;
  margin-top: 2px;
}

.add-btn {
  width: 28px;
  height: 28px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background: #40a9ff;
  transform: scale(1.05);
}

.search-container {
  position: relative;
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 32px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 12px;
  background: #f7fafc;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
  background: white;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 12px;
  pointer-events: none;
}

.templates-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  padding-bottom: 12px;
  min-height: 0;
}

.template-card {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  animation: slideInCompact 0.2s ease;
}

.template-card:hover {
  border-color: #1890ff;
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.template-card.active {
  border-color: #1890ff;
  background: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.template-card.disabled {
  opacity: 0.6;
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  cursor: not-allowed;
}

.template-card.disabled:hover {
  transform: none;
  box-shadow: none;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.template-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.3;
}

.disabled-label {
  font-size: 12px;
  font-weight: 400;
  color: #999;
  margin-left: 4px;
}

.template-status {
  display: flex;
  gap: 4px;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-default {
  background: #fed7d7;
  color: #c53030;
}

.status-active {
  background: #c6f6d5;
  color: #38a169;
}

.status-inactive {
  background: #e2e8f0;
  color: #718096;
}

.template-desc {
  color: #718096;
  font-size: 11px;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #a0aec0;
}

.template-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: #e2e8f0;
  color: #718096;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 10px;
}

.action-btn:hover {
  background: #cbd5e0;
  color: #4a5568;
}

/* 右侧配置区域 - 优化滚动控制 */
.config-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e7;
  overflow: hidden; /* 只允许内部滚动 */
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.config-header {
  background: white;
  border-bottom: 1px solid #e5e5e7;
  color: #1d1d1f;
  padding: 20px 24px;
  flex-shrink: 0;
}

.config-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1d1d1f;
}

.config-subtitle {
  color: #86868b;
  font-size: 14px;
}

.config-nav {
  background: #fafafa;
  padding: 0 24px;
  display: flex;
  gap: 8px;
  overflow-x: auto;
  flex-shrink: 0;
  border-bottom: 1px solid #e5e5e7;
}

.nav-tab {
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: #86868b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px 6px 0 0;
  transition: all 0.2s ease;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

.nav-tab.active {
  background: white;
  color: #007aff;
  border-bottom-color: #007aff;
}

.nav-tab:hover {
  color: #007aff;
}

.config-content {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  height: 100%; /* 确保充分利用可用空间 */
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn-secondary {
  background: #f2f2f7;
  color: #007aff;
  border: 1px solid #d1d1d6;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-secondary:hover {
  background: #e5e5ea;
}

.btn-primary {
  background: #007aff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-primary:hover {
  background: #0056cc;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
  color: #a0aec0;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #4a5568;
}

.empty-desc {
  font-size: 12px;
  line-height: 1.4;
}

.empty-config {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-config-content {
  text-align: center;
  color: #8c8c8c;
}

.empty-config-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-config-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #595959;
}

.empty-config-desc {
  margin: 0;
  font-size: 14px;
}

/* 滚动条紧凑样式 */
.templates-list::-webkit-scrollbar,
.config-content::-webkit-scrollbar {
  width: 4px;
}

.templates-list::-webkit-scrollbar-track,
.config-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.templates-list::-webkit-scrollbar-thumb,
.config-content::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.templates-list::-webkit-scrollbar-thumb:hover,
.config-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 加载动画 */
@keyframes slideInCompact {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 危险操作样式 */
.danger-item {
  color: #ff4d4f !important;
}

/* 复制模板弹窗样式 */
.duplicate-template-form {
  padding: 8px 0;
}

.form-help-text {
  margin-top: 4px;
}

.form-help-text small {
  color: #8c8c8c;
  font-size: 12px;
}

.divider-text {
  color: #595959;
  font-size: 14px;
  font-weight: 500;
}

.copy-info {
  background: #f6f6f6;
  border-radius: 6px;
  padding: 16px;
  margin: 0;
}

.copy-info-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.copy-info-list li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.copy-info-list li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 - 移除header-content相关样式（已使用标准组件） */
@media (min-width: 1600px) {
  .main-layout {
    grid-template-columns: 400px 1fr;
    gap: 24px;
  }
}

@media (min-width: 1400px) and (max-width: 1599px) {
  .main-layout {
    grid-template-columns: 380px 1fr;
    gap: 20px;
  }
}

@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 320px 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .main-layout {
    grid-template-columns: 1fr;
    gap: 12px;
    height: auto; /* 在移动端允许自适应高度 */
  }

  .templates-section {
    order: 2;
    height: auto;
    max-height: 400px;
  }

  .config-section {
    order: 1;
    height: auto;
    min-height: 500px;
  }
}
</style>
