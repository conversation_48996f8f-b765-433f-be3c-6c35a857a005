<template>
  <div class="fullscreen-editor">
    <!-- 编辑器头部 -->
    <EditorHeader
      :doc-title="docTitle"
      :save-status="saveStatus"
      :is-ready="isEditorReady"
      @force-save="handleForceSave"
      @lock-document="handleLockDocument"
      @unlock-document="handleUnlockDocument"
      @save-and-close="handleSaveAndClose"
      @direct-close="handleDirectClose"
    />

    <!-- 编辑器容器 -->
    <EditorContainer
      :is-ready="isEditorReady"
      :config="editorConfig"
      @editor-ready="handleEditorReady"
      @document-state-change="handleDocumentStateChange"
      @save="handleSave"
      @error="handleError"
    />

    <!-- 保存进度遮罩 -->
    <div v-if="isSavingAndClosing" class="save-progress-overlay">
      <div class="save-progress-modal">
        <div class="progress-header">
          <h3>正在保存文档</h3>
          <p>请勿关闭浏览器，保存完成后将自动关闭编辑器</p>
        </div>

        <div class="progress-content">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${saveProgress.progress}%` }"></div>
          </div>

          <div class="progress-steps">
            <div class="step" :class="{ active: saveProgress.step === 'intent' }">
              ✓ 设置保存意图
            </div>
            <div class="step" :class="{ active: saveProgress.step === 'force' }">
              🔄 执行强制保存
            </div>
            <div class="step" :class="{ active: saveProgress.step === 'waiting' }">
              ⏳ 等待OnlyOffice处理
            </div>
            <div class="step" :class="{ active: saveProgress.step === 'upload' }">
              📤 上传到FileNet
            </div>
            <div class="step" :class="{ active: saveProgress.step === 'complete' }">
              ✅ 保存完成
            </div>
          </div>

          <p class="progress-message">{{ saveProgress.message }}</p>
        </div>
      </div>
    </div>

    <!-- 通知组件 -->
    <NotificationPanel :notification="notification" @hide="hideNotification" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useEditor } from './composables/useEditor'
import { useSaveStatus } from './composables/useSaveStatus'
import { useNotification } from './composables/useNotification'
import EditorHeader from './components/EditorHeader.vue'
import EditorContainer from './components/EditorContainer.vue'
import NotificationPanel from './components/NotificationPanel.vue'
import type { DocumentStateChangeEvent, SaveAsEvent, ErrorEvent } from '@/types/onlyoffice.types'

/**
 * 全屏OnlyOffice编辑器页面组件
 *
 * @description 独立的全屏编辑器页面，用于弹窗模式打开
 * - 不包含导航栏、页头页脚等元素
 * - 支持独立窗口运行
 * - 完整的编辑器功能
 *
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

// 保存进度状态
const isSavingAndClosing = ref(false)
const saveProgress = ref({
  step: '',
  progress: 0,
  message: '',
})

const route = useRoute()
const fileId = computed(() => route.params.id as string)

// 编辑器状态管理
const {
  editorConfig,
  docTitle,
  isEditorReady,
  initializeEditor,
  destroyEditor,
  forceSave: editorForceSave,
} = useEditor(fileId)

// 保存状态管理
const { saveStatus, updateSaveStatus, checkSaveStatus, startAutoCheck, stopAutoCheck } =
  useSaveStatus(fileId)

// 通知管理
const { notification, showNotification, hideNotification } = useNotification()

/**
 * 编辑器就绪处理
 */
const handleEditorReady = () => {
  console.log('全屏编辑器已准备就绪')
  showNotification('编辑器已准备就绪', 'success', 3000)

  // 开始定期检查保存状态
  startAutoCheck()

  // 初始检查保存状态
  checkSaveStatus()
}

/**
 * 文档状态变更处理
 */
const handleDocumentStateChange = (event: unknown) => {
  console.log('文档状态变更:', event)

  const typedEvent = event as DocumentStateChangeEvent
  if (typedEvent.data === true) {
    updateSaveStatus('editing', '有未保存的更改')
  }
}

/**
 * 文档保存处理
 */
const handleSave = (event: unknown) => {
  console.log('文档保存事件:', event)

  const typedEvent = event as SaveAsEvent
  if (typedEvent.data?.url) {
    updateSaveStatus('saved', '已保存')
    showNotification('文档已自动保存', 'success', 2000)
  }
}

/**
 * 编辑器错误处理
 */
const handleError = (event: unknown) => {
  console.error('编辑器错误:', event)

  const typedEvent = event as ErrorEvent
  let errorMessage = '编辑器发生错误'
  if (typedEvent.data) {
    if (typedEvent.data.errorDescription) {
      errorMessage = typedEvent.data.errorDescription
    } else if (typeof typedEvent.data === 'string') {
      errorMessage = typedEvent.data
    }
  }

  showNotification(errorMessage, 'error')
}

/**
 * 强制保存处理
 */
const handleForceSave = async () => {
  if (!isEditorReady.value) {
    showNotification('编辑器未就绪', 'warning')
    return
  }

  try {
    updateSaveStatus('saving', '正在保存...')
    showNotification('正在保存文档...', 'info')

    await editorForceSave()

    updateSaveStatus('saved', '保存成功')
    showNotification('文档保存成功', 'success')
  } catch (error) {
    console.error('强制保存失败:', error)
    updateSaveStatus('error', '保存失败')

    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showNotification(`保存失败: ${errorMessage}`, 'error')
  }
}

/**
 * 锁定文档处理
 */
const handleLockDocument = async () => {
  if (!isEditorReady.value) {
    showNotification('编辑器未就绪', 'warning')
    return
  }

  try {
    showNotification('正在加密文档...', 'info')

    // TODO: 实现文档加密API调用
    // await lockDocument(fileId.value);

    showNotification('文档已加密，只能填写表单', 'success')
  } catch (error) {
    console.error('加密失败:', error)

    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showNotification(`加密失败: ${errorMessage}`, 'error')
  }
}

/**
 * 解锁文档处理
 */
const handleUnlockDocument = async () => {
  if (!isEditorReady.value) {
    showNotification('编辑器未就绪', 'warning')
    return
  }

  try {
    showNotification('正在解锁文档...', 'info')

    // TODO: 实现文档解锁API调用
    // await unlockDocument(fileId.value);

    showNotification('文档已解锁，可以正常编辑', 'success')
  } catch (error) {
    console.error('解锁失败:', error)

    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showNotification(`解锁失败: ${errorMessage}`, 'error')
  }
}

/**
 * 保存并关闭处理
 */
const handleSaveAndClose = async (): Promise<void> => {
  try {
    console.log('🔄 开始保存并关闭流程...')

    // 显示进度遮罩
    isSavingAndClosing.value = true
    saveProgress.value = {
      step: 'intent',
      progress: 10,
      message: '正在设置保存意图...',
    }

    // 1. 设置用户意图为保存
    try {
      const response = await fetch(`/api/documents/${fileId.value}/close-intent`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ intent: 'save' }),
      })

      if (response.ok) {
        console.log('✅ 已设置用户意图为保存')
      } else {
        console.warn('⚠️ 设置用户意图失败，但继续保存流程')
      }
    } catch (intentError) {
      console.warn('⚠️ 设置用户意图出错，但继续保存流程:', intentError)
    }

    // 2. 先获取初始版本号（在强制保存之前）
    saveProgress.value = {
      step: 'waiting',
      progress: 25,
      message: '获取初始版本信息...',
    }

    let initialVersion = 0
    try {
      const initialResponse = await fetch(`/api/documents/${fileId.value}/save-status`)
      if (initialResponse.ok) {
        const initialData = await initialResponse.json()
        console.log('🔍 初始状态完整数据:', JSON.stringify(initialData, null, 2))

        // 修复版本号提取 - 处理嵌套的data结构
        initialVersion =
          initialData?.data?.data?.version ||
          initialData?.data?.version ||
          initialData?.version ||
          0
        console.log(`📊 提取到的初始版本号: ${initialVersion} (类型: ${typeof initialVersion})`)
      } else {
        console.warn('⚠️ 获取初始状态失败，response not ok')
      }
    } catch (error) {
      console.warn('⚠️ 获取初始版本失败:', error)
    }

    // 3. 执行强制保存
    saveProgress.value = {
      step: 'force',
      progress: 40,
      message: '正在执行强制保存...',
    }

    await handleForceSave()

    // 4. 简化的保存检测逻辑
    const maxWaitTime = 20000 // 减少到20秒
    const checkInterval = 2000 // 每2秒检查一次，减少频率
    let waitTime = 0
    let saveCompleted = false

    console.log(`👀 开始监控保存进度... 初始版本=${initialVersion}`)

    while (waitTime < maxWaitTime && !saveCompleted) {
      await new Promise(resolve => setTimeout(resolve, checkInterval))
      waitTime += checkInterval

      // 更新进度（40% -> 85%）
      const waitProgress = 40 + (waitTime / maxWaitTime) * 45
      saveProgress.value = {
        step: 'waiting',
        progress: Math.min(waitProgress, 85),
        message: `等待保存完成... ${Math.floor(waitTime / 1000)}秒`,
      }

      // 检查文档保存状态
      try {
        const statusResponse = await fetch(`/api/documents/${fileId.value}/save-status`)
        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          console.log('🔍 当前状态完整数据:', JSON.stringify(statusData, null, 2))

          // 修复版本号提取 - 处理嵌套的data结构
          const currentVersion =
            statusData?.data?.data?.version || statusData?.data?.version || statusData?.version || 0

          console.log(
            `📊 版本比较: 当前=${currentVersion} (${typeof currentVersion}) vs 初始=${initialVersion} (${typeof initialVersion})`
          )

          // 确保数字比较
          const currentVersionNum = Number(currentVersion)
          const initialVersionNum = Number(initialVersion)

          console.log(`📊 数字版本比较: 当前=${currentVersionNum} vs 初始=${initialVersionNum}`)

          // 检查版本号是否增加了
          if (currentVersionNum > initialVersionNum) {
            console.log('✅ 检测到版本号更新，保存完成！')
            saveCompleted = true
            break
          } else {
            console.log(`⏳ 版本号未变化，继续等待... (${waitTime}ms/${maxWaitTime}ms)`)
          }
        } else {
          console.warn('⚠️ 获取状态失败: response not ok')
        }
      } catch (error) {
        console.warn('⚠️ 检查文档保存状态失败:', error)
      }
    }

    if (saveCompleted) {
      // 5. 保存成功
      saveProgress.value = {
        step: 'complete',
        progress: 100,
        message: '保存完成！即将关闭编辑器...',
      }

      await new Promise(resolve => setTimeout(resolve, 2000))

      if (window.opener) {
        window.close()
      } else {
        window.location.href = '/'
      }
    } else {
      // 6. 超时处理 - 很可能保存已经完成了，只是检测失败
      console.log('⚠️ 保存检测超时，但保存可能已完成')

      // 最后再检查一次
      try {
        const finalResponse = await fetch(`/api/documents/${fileId.value}/save-status`)
        if (finalResponse.ok) {
          const finalData = await finalResponse.json()
          console.log('🔍 最后检查完整数据:', JSON.stringify(finalData, null, 2))

          const finalVersion = Number(
            finalData?.data?.data?.version || finalData?.data?.version || finalData?.version || 0
          )
          const initialVersionNum = Number(initialVersion)

          console.log(`📊 最后检查版本比较: ${finalVersion} vs ${initialVersionNum}`)

          if (finalVersion > initialVersionNum) {
            console.log('✅ 最后检查发现保存已完成！')
            saveProgress.value = {
              step: 'complete',
              progress: 100,
              message: '保存已完成！即将关闭编辑器...',
            }

            await new Promise(resolve => setTimeout(resolve, 2000))

            if (window.opener) {
              window.close()
            } else {
              window.location.href = '/'
            }
            return
          } else {
            console.log('❌ 最后检查依然没有发现版本变化')
          }
        }
      } catch (error) {
        console.warn('⚠️ 最后检查失败:', error)
      }

      // 隐藏进度遮罩，让用户选择
      isSavingAndClosing.value = false

      const userChoice = confirm(
        '保存检测超时。建议：\n' +
          '• 检查文档是否已自动保存\n' +
          '• 或者稍后重新编辑查看\n\n' +
          '点击"确定"关闭编辑器，点击"取消"继续编辑'
      )

      if (userChoice) {
        if (window.opener) {
          window.close()
        } else {
          window.location.href = '/'
        }
      }
    }
  } catch (error) {
    console.error('❌ 保存并关闭失败:', error)
    isSavingAndClosing.value = false
    showNotification(
      `保存失败: ${error instanceof Error ? error.message : '未知错误'}`,
      'error',
      5000
    )
  }
}

/**
 * 直接关闭处理
 */
const handleDirectClose = async (): Promise<void> => {
  console.log('🚪 执行直接关闭')

  try {
    // 1. 设置用户意图为不保存
    const response = await fetch(`/api/documents/${fileId.value}/close-intent`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ intent: 'no-save' }),
    })

    if (response.ok) {
      console.log('✅ 已设置用户意图为不保存')
    } else {
      console.warn('⚠️ 设置用户意图失败')
    }
  } catch (intentError) {
    console.warn('⚠️ 设置用户意图出错:', intentError)
  }

  // 2. 直接关闭
  if (window.opener) {
    // 如果有父窗口，关闭当前窗口
    window.close()
  } else {
    // 如果没有父窗口，跳转到首页
    window.location.href = '/'
  }
}

/**
 * 页面卸载前检查未保存的更改
 */
const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  if (saveStatus.value.status === 'editing') {
    const message = '您有未保存的更改，确定要离开吗？'
    e.returnValue = message
    return message
  }
}

// 生命周期管理
onMounted(async () => {
  console.log('初始化全屏编辑器页面:', fileId.value)

  try {
    // ✅ 从路由查询参数中获取配置模板信息
    const query = route.query
    const configQuery: Record<string, unknown> = {}

    if (query.template && typeof query.template === 'string') {
      configQuery.template = query.template
      console.log('🔧 [FullscreenEditor] 从路由获取配置模板:', query.template)
    }
    if (query.hideChat !== undefined) {
      configQuery.hideChat = query.hideChat === 'true'
    }
    if (query.hideComments !== undefined) {
      configQuery.hideComments = query.hideComments === 'true'
    }
    if (query.readonly !== undefined) {
      configQuery.readonly = query.readonly === 'true'
    }
    if (query.userId && typeof query.userId === 'string') {
      configQuery.userId = query.userId
    }
    if (query.userName && typeof query.userName === 'string') {
      configQuery.userName = query.userName
    }

    console.log('🔧 [FullscreenEditor] 最终配置查询参数:', configQuery)

    await initializeEditor(configQuery)
  } catch (error) {
    console.error('初始化编辑器失败:', error)

    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showNotification(`初始化失败: ${errorMessage}`, 'error')
  }

  // 添加页面卸载监听
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
  console.log('销毁全屏编辑器页面')

  // 停止自动检查
  stopAutoCheck()

  // 销毁编辑器
  destroyEditor()

  // 移除页面卸载监听
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<style scoped>
.fullscreen-editor {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

/* 保存进度遮罩样式 */
.save-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.save-progress-modal {
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 500px;
  min-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.progress-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
  text-align: center;
}

.progress-header p {
  margin: 0 0 24px 0;
  color: #666;
  text-align: center;
  font-size: 14px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #81c784);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.step.active {
  background-color: #e8f5e8;
  color: #4caf50;
  font-weight: 500;
}

.progress-message {
  text-align: center;
  color: #333;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}
</style>
