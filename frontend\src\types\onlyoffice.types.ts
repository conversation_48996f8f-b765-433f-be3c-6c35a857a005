/**
 * OnlyOffice文档编辑器相关类型定义
 * @description 定义OnlyOffice API的TypeScript类型
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

// OnlyOffice文档配置
export interface DocumentConfig {
  fileType: string
  key: string
  title: string
  url: string
  permissions?: DocumentPermissions
}

// 文档权限配置
export interface DocumentPermissions {
  edit?: boolean
  download?: boolean
  print?: boolean
  comment?: boolean
  fillForms?: boolean
  modifyFilter?: boolean
  modifyContentControl?: boolean
  review?: boolean
}

// 编辑器配置
export interface EditorConfig {
  callbackUrl: string
  lang: string
  mode: 'edit' | 'view' | 'review' | 'comment'
  user: UserInfo
  customization?: EditorCustomization
  plugins?: EditorPlugin[]
}

// 用户信息
export interface UserInfo {
  id: string
  name: string
  group?: string
}

// 编辑器自定义配置
export interface EditorCustomization {
  autosave?: boolean
  forcesave?: boolean
  commentAuthorOnly?: boolean
  comments?: boolean
  compactToolbar?: boolean
  feedback?: boolean
  help?: boolean
  hideRightMenu?: boolean
  toolbarNoTabs?: boolean
  zoom?: number
  hideRulers?: boolean
  showHorizontalScroll?: boolean
  showVerticalScroll?: boolean
  uiTheme?: 'theme-light' | 'theme-dark'
  integrationMode?: 'embed' | 'desktop' | 'mobile'
  compactHeader?: boolean
  about?: boolean
}

// 编辑器插件
export interface EditorPlugin {
  asc_name: string
  guid: string
  baseUrl: string
}

// OnlyOffice事件处理器
export interface OnlyOfficeEvents {
  onDocumentReady?: () => void
  onDocumentStateChange?: (event: DocumentStateChangeEvent) => void
  onRequestEditRights?: () => void
  onRequestHistory?: () => void
  onRequestHistoryClose?: () => void
  onRequestHistoryData?: (event: HistoryDataEvent) => void
  onRequestRestore?: (event: RestoreEvent) => void
  onError?: (event: ErrorEvent) => void
  onWarning?: (event: WarningEvent) => void
  onInfo?: (event: InfoEvent) => void
  onRequestSaveAs?: (event: SaveAsEvent) => void
  onRequestInsertImage?: (event: InsertImageEvent) => void
  onRequestMailMergeRecipients?: () => void
  onRequestCompareFile?: () => void
  onRequestSharingSettings?: () => void
  onRequestRename?: (event: RenameEvent) => void
  onMetaChange?: (event: MetaChangeEvent) => void
  onRequestClose?: () => void
}

// 事件类型定义
export interface DocumentStateChangeEvent {
  data: boolean
}

export interface HistoryDataEvent {
  data: {
    changeshistory: string
    differ: boolean
    history: HistoryVersion[]
    token: string
  }
}

export interface HistoryVersion {
  changes: Change[]
  created: string
  key: string
  serverVersion: string
  user: UserInfo
  version: number
}

export interface Change {
  created: string
  user: UserInfo
}

export interface RestoreEvent {
  data: {
    fileType: string
    token: string
    url: string
    version: number
  }
}

export interface ErrorEvent {
  data: {
    errorCode: number
    errorDescription: string
  }
}

export interface WarningEvent {
  data: {
    warningCode: number
    warningDescription: string
  }
}

export interface InfoEvent {
  data: {
    mode: string
  }
}

export interface SaveAsEvent {
  data: {
    fileType: string
    title: string
    url: string
  }
}

export interface InsertImageEvent {
  data: {
    c: string
    token: string
    type: 'imageUrl' | 'imageFile'
  }
}

export interface RenameEvent {
  data: string
}

export interface MetaChangeEvent {
  data: {
    favorite?: boolean
    title?: string
  }
}

// OnlyOffice完整配置
export interface OnlyOfficeConfig {
  document: DocumentConfig
  documentType: 'word' | 'cell' | 'slide'
  editorConfig: EditorConfig
  events?: OnlyOfficeEvents
  height: string
  width: string
  token?: string
  apiUrl?: string
}

// OnlyOffice编辑器实例
export interface DocEditor {
  destroyEditor(): void
  downloadAs(fileType: string, title?: string): void
  insertImage(imageConfig: { c?: string; fileType?: string; url?: string }): void
  setHistoryData(data: HistoryVersion[]): void
  refreshHistory(data: { currentVersion: number; history: HistoryVersion[] }): void
  setRevisedFile(data: { fileType: string; token?: string; url: string }): void
  setRequestedDocument(data: { fileType: string; token?: string; url: string }): void
  setUsers(users: UserInfo[]): void
  showMessage(message: string): void
  requestClose(): void
}

// OnlyOffice API声明
export interface DocsAPI {
  DocEditor: new (id: string, config: OnlyOfficeConfig) => DocEditor
}

// Window对象扩展
declare global {
  interface Window {
    DocsAPI?: DocsAPI
  }
}
