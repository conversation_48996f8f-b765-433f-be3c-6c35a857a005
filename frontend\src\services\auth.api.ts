import { ApiService } from './api'
import type { LoginResponse, UserInfo } from '@/types/api.types'

export interface LoginCredentials {
  username: string
  password: string
}

export interface RefreshTokenResponse {
  token: string
  refreshToken?: string
  expiresIn: number
}

/**
 * 认证相关API服务
 */
export class AuthApiService {
  /**
   * 用户登录
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    return ApiService.post<LoginResponse>('/auth/login', credentials)
  }

  /**
   * 用户注销
   */
  static async logout(): Promise<void> {
    return ApiService.post<void>('/auth/logout')
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<UserInfo> {
    return ApiService.get<UserInfo>('/auth/me')
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(): Promise<RefreshTokenResponse> {
    return ApiService.post<RefreshTokenResponse>('/auth/refresh')
  }
}

export default AuthApiService
