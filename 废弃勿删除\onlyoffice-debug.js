const axios = require('axios');
const mysql = require('mysql2/promise');

/**
 * OnlyOffice问题诊断脚本
 * 对比老项目和新项目的配置差异，找出保存失败的原因
 */

const config = {
    // 服务器配置
    newServer: 'http://*************:3000',
    oldServer: 'http://*************:3300',
    onlyOfficeServer: 'http://*************',
    
    // 数据库配置
    database: {
        host: '*************',
        port: 3306,
        user: 'onlyfile_user',
        password: '0nlyF!le$ecure#123',
        database: 'onlyfile'
    }
};

// 颜色输出
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testConnection(url, name) {
    try {
        const response = await axios.get(`${url}/api/health`, { timeout: 5000 });
        log(`✅ ${name} 连接成功 (${response.status})`, 'green');
        return true;
    } catch (error) {
        log(`❌ ${name} 连接失败: ${error.message}`, 'red');
        return false;
    }
}

async function getDocumentFromDB() {
    let connection;
    try {
        connection = await mysql.createConnection(config.database);
        
        // 获取一个测试文档
        const [rows] = await connection.execute(`
            SELECT id, original_name, fn_doc_id, extension 
            FROM filenet_documents 
            WHERE is_deleted = FALSE 
            LIMIT 1
        `);
        
        if (rows.length === 0) {
            log('❌ 数据库中没有找到可用的测试文档', 'red');
            return null;
        }
        
        const doc = rows[0];
        log(`📄 找到测试文档: ${doc.original_name} (ID: ${doc.id})`, 'blue');
        return doc;
        
    } catch (error) {
        log(`❌ 数据库连接失败: ${error.message}`, 'red');
        return null;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

async function testEditorConfig(serverUrl, docId) {
    try {
        log(`\n🔧 测试编辑器配置获取: ${serverUrl}`, 'cyan');
        
        const response = await axios.get(`${serverUrl}/api/editor/${docId}/config`, {
            timeout: 10000
        });
        
        if (response.status === 200) {
            log(`✅ 配置获取成功`, 'green');
            
            const data = response.data;
            const config = data.success ? data.data : data;
            
            // 验证关键配置项
            log(`\n📋 配置验证:`, 'yellow');
            log(`   文档URL: ${config.document?.url}`);
            log(`   回调URL: ${config.editorConfig?.callbackUrl}`);
            log(`   文档密钥: ${config.document?.key}`);
            log(`   API脚本: ${config.apiUrl}`);
            log(`   权限配置: ${JSON.stringify(config.document?.permissions)}`);
            
            // 检查URL有效性
            const issues = [];
            if (!config.document?.url) issues.push('缺少文档URL');
            if (!config.editorConfig?.callbackUrl) issues.push('缺少回调URL');
            if (!config.document?.key) issues.push('缺少文档密钥');
            if (!config.apiUrl) issues.push('缺少API脚本URL');
            
            if (issues.length > 0) {
                log(`\n⚠️  配置问题:`, 'yellow');
                issues.forEach(issue => log(`   - ${issue}`, 'yellow'));
            }
            
            return config;
        }
    } catch (error) {
        log(`❌ 配置获取失败: ${error.response?.status || error.message}`, 'red');
        if (error.response?.data) {
            log(`   错误详情: ${JSON.stringify(error.response.data)}`, 'red');
        }
    }
    return null;
}

async function testDocumentAccess(docUrl) {
    try {
        log(`\n📄 测试文档访问: ${docUrl}`, 'cyan');
        
        const response = await axios.head(docUrl, { timeout: 10000 });
        
        log(`✅ 文档访问成功 (${response.status})`, 'green');
        log(`   Content-Type: ${response.headers['content-type']}`);
        log(`   Content-Length: ${response.headers['content-length']}`);
        return true;
        
    } catch (error) {
        log(`❌ 文档访问失败: ${error.response?.status || error.message}`, 'red');
        return false;
    }
}

async function testCallbackEndpoint(callbackUrl) {
    try {
        log(`\n🔄 测试回调端点: ${callbackUrl}`, 'cyan');
        
        const testPayload = {
            key: 'test-key-' + Date.now(),
            status: 1,
            users: ['test-user']
        };
        
        const response = await axios.post(callbackUrl, testPayload, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        
        log(`✅ 回调端点响应 (${response.status})`, 'green');
        log(`   响应数据: ${JSON.stringify(response.data)}`);
        
        // 检查响应格式
        if (response.data.error === 0) {
            log(`✅ 回调响应格式正确`, 'green');
        } else {
            log(`⚠️  回调响应格式异常: ${JSON.stringify(response.data)}`, 'yellow');
        }
        
        return true;
        
    } catch (error) {
        log(`❌ 回调端点失败: ${error.response?.status || error.message}`, 'red');
        if (error.response?.data) {
            log(`   错误响应: ${JSON.stringify(error.response.data)}`, 'red');
        }
        return false;
    }
}

async function testOnlyOfficeConnectivity() {
    try {
        log(`\n🏢 测试OnlyOffice服务器连接`, 'cyan');
        
        // 测试主页
        const response = await axios.get(config.onlyOfficeServer, { timeout: 5000 });
        log(`✅ OnlyOffice服务器响应 (${response.status})`, 'green');
        
        // 测试API脚本
        const apiUrl = `${config.onlyOfficeServer}/web-apps/apps/api/documents/api.js`;
        const apiResponse = await axios.get(apiUrl, { timeout: 5000 });
        log(`✅ OnlyOffice API脚本可访问 (${apiResponse.status})`, 'green');
        
        return true;
        
    } catch (error) {
        log(`❌ OnlyOffice服务器连接失败: ${error.message}`, 'red');
        return false;
    }
}

async function testNetworkConnectivity() {
    log(`\n🌐 测试网络连接`, 'cyan');
    
    // 从OnlyOffice服务器的角度测试回调URL的可达性
    const callbackTestUrl = `${config.newServer}/api/editor/callback`;
    
    try {
        // 这里我们模拟OnlyOffice服务器发起的请求
        log(`   测试回调URL可达性: ${callbackTestUrl}`, 'blue');
        
        // 简单的ping测试
        const response = await axios.post(callbackTestUrl, {
            status: 0,
            key: 'connectivity-test'
        }, {
            timeout: 5000,
            headers: { 'Content-Type': 'application/json' }
        });
        
        log(`✅ 回调URL从外部可访问`, 'green');
        return true;
        
    } catch (error) {
        log(`❌ 回调URL可能无法从OnlyOffice服务器访问`, 'red');
        log(`   错误: ${error.message}`, 'red');
        return false;
    }
}

async function compareMysqlConfig() {
    let connection;
    try {
        log(`\n🗄️  检查数据库配置`, 'cyan');
        
        connection = await mysql.createConnection(config.database);
        
        // 检查OnlyOffice相关配置
        const [settings] = await connection.execute(`
            SELECT setting_key, setting_value, description 
            FROM system_settings 
            WHERE setting_key LIKE '%onlyoffice%' 
               OR setting_key LIKE '%jwt%'
               OR setting_key LIKE '%callback%'
            ORDER BY setting_key
        `);
        
        log(`\n📋 OnlyOffice相关配置:`, 'yellow');
        settings.forEach(setting => {
            log(`   ${setting.setting_key}: ${setting.setting_value}`, 'blue');
        });
        
        // 检查默认配置模板
        const [templates] = await connection.execute(`
            SELECT id, name, is_default, is_active 
            FROM config_templates 
            WHERE is_active = 1
            ORDER BY is_default DESC, name
        `);
        
        log(`\n📋 可用配置模板:`, 'yellow');
        templates.forEach(template => {
            const marker = template.is_default ? ' (默认)' : '';
            log(`   ${template.id}: ${template.name}${marker}`, 'blue');
        });
        
        return { settings, templates };
        
    } catch (error) {
        log(`❌ 数据库配置检查失败: ${error.message}`, 'red');
        return null;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

async function main() {
    log(`${colors.bold}🔍 OnlyOffice保存问题诊断${colors.reset}`, 'cyan');
    log(`时间: ${new Date().toLocaleString()}\n`);
    
    // 1. 基础连接测试
    log(`${colors.bold}=== 第一步: 基础连接测试 ===${colors.reset}`, 'magenta');
    await testConnection(config.newServer, '新后端服务 (3000端口)');
    await testConnection(config.oldServer, '老项目服务 (3300端口)');
    await testOnlyOfficeConnectivity();
    
    // 2. 数据库配置检查
    log(`\n${colors.bold}=== 第二步: 数据库配置检查 ===${colors.reset}`, 'magenta');
    await compareMysqlConfig();
    
    // 3. 获取测试文档
    log(`\n${colors.bold}=== 第三步: 获取测试文档 ===${colors.reset}`, 'magenta');
    const testDoc = await getDocumentFromDB();
    
    if (!testDoc) {
        log(`❌ 无法继续测试，请确保数据库中有可用文档`, 'red');
        return;
    }
    
    // 4. 测试编辑器配置
    log(`\n${colors.bold}=== 第四步: 编辑器配置测试 ===${colors.reset}`, 'magenta');
    const editorConfig = await testEditorConfig(config.newServer, testDoc.id);
    
    if (!editorConfig) {
        log(`❌ 编辑器配置获取失败，无法继续测试`, 'red');
        return;
    }
    
    // 5. 测试文档访问
    log(`\n${colors.bold}=== 第五步: 文档访问测试 ===${colors.reset}`, 'magenta');
    if (editorConfig.document?.url) {
        await testDocumentAccess(editorConfig.document.url);
    }
    
    // 6. 测试回调端点
    log(`\n${colors.bold}=== 第六步: 回调端点测试 ===${colors.reset}`, 'magenta');
    if (editorConfig.editorConfig?.callbackUrl) {
        await testCallbackEndpoint(editorConfig.editorConfig.callbackUrl);
    }
    
    // 7. 网络连通性测试
    log(`\n${colors.bold}=== 第七步: 网络连通性测试 ===${colors.reset}`, 'magenta');
    await testNetworkConnectivity();
    
    // 8. 总结和建议
    log(`\n${colors.bold}=== 诊断总结 ===${colors.reset}`, 'magenta');
    log(`\n如果上述测试都通过，但OnlyOffice仍提示无法保存，请检查:`, 'yellow');
    log(`1. OnlyOffice服务器的错误日志`, 'blue');
    log(`2. 网络防火墙设置`, 'blue');
    log(`3. JWT配置是否一致`, 'blue');
    log(`4. 文档权限设置`, 'blue');
    
    log(`\n请将测试结果分享给开发团队以便进一步诊断。`, 'green');
}

// 运行诊断
if (require.main === module) {
    main().catch(error => {
        log(`\n💥 诊断过程中发生错误: ${error.message}`, 'red');
        console.error(error);
    });
}

module.exports = { main }; 