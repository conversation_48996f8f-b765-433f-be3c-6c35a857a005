import { Controller, Get, Post } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HybridConfigService } from '../services/hybrid-config.service';

/**
 * 混合配置管理控制器
 * 
 * 提供配置状态查看和管理功能
 * 
 * @class HybridConfigController
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 1.0.0
 */
@ApiTags('配置管理')
@Controller('config/hybrid')
export class HybridConfigController {
  constructor(
    private readonly hybridConfigService: HybridConfigService,
  ) {}

  /**
   * 获取配置来源统计
   */
  @Get('sources')
  @ApiOperation({ 
    summary: '获取配置来源信息',
    description: '查看各个配置项是从数据库还是环境变量读取的'
  })
  @ApiResponse({ 
    status: 200, 
    description: '配置来源信息',
    schema: {
      type: 'object',
      properties: {
        sources: {
          type: 'object',
          additionalProperties: {
            type: 'string',
            enum: ['database', 'env']
          }
        }
      }
    }
  })
  async getConfigSources() {
    const sources = await this.hybridConfigService.getConfigSources();
    
    return {
      success: true,
      message: '配置来源信息获取成功',
      data: {
        sources,
        description: {
          database: '从数据库读取的配置',
          env: '从环境变量读取的配置'
        }
      }
    };
  }

  /**
   * 获取配置统计信息
   */
  @Get('stats')
  @ApiOperation({ 
    summary: '获取配置统计信息',
    description: '查看配置项数量、来源分布、缓存状态等统计信息'
  })
  @ApiResponse({ 
    status: 200, 
    description: '配置统计信息'
  })
  async getConfigStats() {
    const stats = await this.hybridConfigService.getConfigStats();
    
    return {
      success: true,
      message: '配置统计信息获取成功',
      data: {
        ...stats,
        cacheAgeMinutes: Math.floor(stats.cacheAge / (1000 * 60)),
        distribution: {
          databasePercentage: Math.round((stats.fromDatabase / stats.total) * 100),
          envPercentage: Math.round((stats.fromEnv / stats.total) * 100)
        }
      }
    };
  }

  /**
   * 刷新配置缓存
   */
  @Post('refresh-cache')
  @ApiOperation({ 
    summary: '刷新配置缓存',
    description: '手动刷新配置缓存，重新从数据库加载配置'
  })
  @ApiResponse({ 
    status: 200, 
    description: '缓存刷新成功'
  })
  async refreshCache() {
    await this.hybridConfigService.refreshConfigCache();
    
    const stats = await this.hybridConfigService.getConfigStats();
    
    return {
      success: true,
      message: '配置缓存刷新成功',
      data: {
        refreshedAt: new Date().toISOString(),
        loadedConfigs: stats.fromDatabase,
        cacheAge: 0
      }
    };
  }

  /**
   * 获取配置检查报告
   */
  @Get('check')
  @ApiOperation({ 
    summary: '配置检查报告',
    description: '全面检查配置状态，包括必要配置是否存在、配置来源等'
  })
  @ApiResponse({ 
    status: 200, 
    description: '配置检查报告'
  })
  async checkConfiguration() {
    const [sources, stats] = await Promise.all([
      this.hybridConfigService.getConfigSources(),
      this.hybridConfigService.getConfigStats()
    ]);

    // 检查关键配置
    const criticalConfigs = [
      'jwt.onlyoffice.secret',
      'onlyoffice.server_url',
      'filenet.host',
      'filenet.username'
    ];

    const configStatus = criticalConfigs.map(config => ({
      key: config,
      source: sources[config] || 'missing',
      status: sources[config] ? 'ok' : 'missing'
    }));

    const missingConfigs = configStatus.filter(c => c.status === 'missing');
    const healthScore = Math.round(((criticalConfigs.length - missingConfigs.length) / criticalConfigs.length) * 100);

    return {
      success: true,
      message: '配置检查完成',
      data: {
        summary: {
          healthScore,
          totalConfigs: stats.total,
          fromDatabase: stats.fromDatabase,
          fromEnvironment: stats.fromEnv,
          missingCritical: missingConfigs.length
        },
        criticalConfigs: configStatus,
        recommendations: this.generateRecommendations(configStatus, stats),
        cacheInfo: {
          ageMinutes: Math.floor(stats.cacheAge / (1000 * 60)),
          status: stats.cacheAge < 300000 ? 'fresh' : 'stale' // 5分钟内为新鲜
        }
      }
    };
  }

  /**
   * 生成配置建议
   */
  private generateRecommendations(
    configStatus: Array<{ key: string; source: string; status: string }>,
    stats: { fromDatabase: number; fromEnv: number; total: number }
  ): string[] {
    const recommendations: string[] = [];

    // 检查缺失的关键配置
    const missingConfigs = configStatus.filter(c => c.status === 'missing');
    if (missingConfigs.length > 0) {
      recommendations.push(
        `建议在数据库system_settings表中添加以下配置: ${missingConfigs.map(c => c.key).join(', ')}`
      );
    }

    // 检查配置分布
    const dbPercentage = (stats.fromDatabase / stats.total) * 100;
    if (dbPercentage < 50) {
      recommendations.push(
        '建议将更多配置迁移到数据库中以提高配置的灵活性和可管理性'
      );
    }

    // 安全建议
    const envOnlyConfigs = configStatus.filter(c => c.source === 'env');
    if (envOnlyConfigs.some(c => c.key.includes('password') || c.key.includes('secret'))) {
      recommendations.push(
        '敏感配置项(密码、密钥)应保持在环境变量中以确保安全'
      );
    }

    if (recommendations.length === 0) {
      recommendations.push('配置状态良好，无需特别处理');
    }

    return recommendations;
  }
} 