/**
 * 文件存储服务
 * 用于管理文件存储和数据库记录
 */
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const db = require('./database');
const config = require('../config');

/**
 * 生成唯一的存储文件名
 * @param {string} extension 文件扩展名
 * @returns {string} 唯一文件名
 */
function generateStorageName(extension) {
    const timestamp = Date.now();
    const uuid = uuidv4().replace(/-/g, '').substring(0, 12);
    return `${timestamp}-${uuid}${extension ? '.' + extension : ''}`;
}

/**
 * 获取文件扩展名
 * @param {string} fileName 文件名
 * @returns {string} 扩展名（不包含点）
 */
function getFileExtension(fileName) {
    return path.extname(fileName).toLowerCase().substring(1);
}

/**
 * 获取文件MIME类型
 * @param {string} extension 文件扩展名
 * @returns {string} MIME类型
 */
function getMimeType(extension) {
    const mimeTypes = {
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'doc': 'application/msword',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'xls': 'application/vnd.ms-excel',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'ppt': 'application/vnd.ms-powerpoint',
        'pdf': 'application/pdf',
        'txt': 'text/plain',
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif'
    };

    return mimeTypes[extension] || 'application/octet-stream';
}

/**
 * 保存文件到存储系统并记录到数据库
 * @param {Object} fileData 文件数据对象
 * @param {string} fileData.originalName 原始文件名
 * @param {string} fileData.tempPath 临时文件路径
 * @param {number} fileData.size 文件大小
 * @param {string} fileData.userId 用户ID（可选）
 * @returns {Promise<Object>} 文件信息
 */
async function saveFile(fileData) {
    const { originalName, tempPath, size, userId = 'system' } = fileData;

    try {
        // 获取文件扩展名
        const extension = getFileExtension(originalName);

        // 生成唯一的存储文件名
        const storageName = generateStorageName(extension);

        // 文件存储路径
        const storagePath = path.join(config.storage.uploadDir, storageName);

        // 移动文件到存储目录
        await fs.move(tempPath, storagePath, { overwrite: true });

        // 生成唯一ID
        const fileId = uuidv4();

        // 记录文件信息到数据库
        await db.query(`
      INSERT INTO files (
        id, original_name, storage_name, file_size, 
        mime_type, extension, last_modified_by
      ) VALUES (
        :id, :originalName, :storageName, :fileSize,
        :mimeType, :extension, :lastModifiedBy
      )
    `, {
            id: fileId,
            originalName,
            storageName,
            fileSize: size,
            mimeType: getMimeType(extension),
            extension,
            lastModifiedBy: userId
        });

        // 记录初始版本
        await db.query(`
      INSERT INTO file_versions (
        file_id, version, storage_name, file_size, modified_by
      ) VALUES (
        :fileId, 1, :storageName, :fileSize, :modifiedBy
      )
    `, {
            fileId,
            storageName,
            fileSize: size,
            modifiedBy: userId
        });

        console.log(`文件已保存: ${originalName} -> ${storageName} (ID: ${fileId})`);

        return {
            id: fileId,
            originalName,
            storageName,
            size,
            extension,
            mimeType: getMimeType(extension)
        };
    } catch (error) {
        console.error('保存文件失败:', error);
        throw error;
    }
}

/**
 * 根据文件ID获取文件信息
 * @param {string} fileId 文件ID
 * @returns {Promise<Object|null>} 文件信息
 */
async function getFileById(fileId) {
    try {
        return await db.queryOne(`
      SELECT id, original_name, storage_name, file_size, 
             mime_type, extension, version, created_at, updated_at
      FROM files
      WHERE id = :fileId AND is_deleted = FALSE
    `, { fileId });
    } catch (error) {
        console.error('获取文件信息失败:', error);
        throw error;
    }
}

/**
 * 获取所有文件列表
 * @param {Object} options 查询选项
 * @param {number} options.limit 限制数量
 * @param {number} options.offset 偏移量
 * @returns {Promise<Array>} 文件列表
 */
async function getAllFiles(options = {}) {
    const { limit = 100, offset = 0 } = options;

    try {
        return await db.query(`
      SELECT id, original_name, storage_name, file_size, 
             mime_type, extension, version, created_at, updated_at
      FROM files
      WHERE is_deleted = FALSE
      ORDER BY created_at DESC
      LIMIT :limit OFFSET :offset
    `, { limit, offset });
    } catch (error) {
        console.error('获取文件列表失败:', error);
        throw error;
    }
}

/**
 * 更新文件内容
 * @param {Object} fileData 文件数据对象
 * @param {string} fileData.fileId 文件ID
 * @param {string} fileData.tempPath 临时文件路径
 * @param {number} fileData.size 文件大小
 * @param {string} fileData.userId 用户ID（可选）
 * @returns {Promise<Object>} 更新后的文件信息
 */
async function updateFile(fileData) {
    const { fileId, tempPath, size, userId = 'system' } = fileData;

    return await db.transaction(async (connection) => {
        // 获取当前文件信息 - 使用 queryOne 语义确保获取单个对象或 null
        // 我们假设 connection.query 在这里也会返回 [rows, fields] 结构，或者直接是 rows
        const fileInfoRows = await connection.query(
            'SELECT id, original_name, storage_name, version, extension FROM files WHERE id = ? AND is_deleted = FALSE LIMIT 1',
            [fileId]
        );

        // mysql2 query 返回 [rows, fields], 所以我们取 rows[0]
        const currentFile = (fileInfoRows && fileInfoRows[0] && fileInfoRows[0][0]) ? fileInfoRows[0][0] : null;

        if (!currentFile) {
            throw new Error(`文件不存在或已被删除: ${fileId}`);
        }

        // 确保 version 是一个数字，如果为 null/undefined，则视为0
        const currentVersion = parseInt(currentFile.version, 10);
        const newVersion = (isNaN(currentVersion) ? 0 : currentVersion) + 1;

        console.log(`  [updateFile] Current version for ${fileId}: ${currentFile.version}, Parsed: ${currentVersion}, New version: ${newVersion}`);

        const extension = currentFile.extension;
        const newStorageName = generateStorageName(extension);
        const storagePath = path.join(config.storage.uploadDir, newStorageName);

        await fs.move(tempPath, storagePath, { overwrite: true });

        await connection.query(`
            UPDATE files
            SET storage_name = ?,
                file_size = ?,
                version = ?,
                updated_at = CURRENT_TIMESTAMP,
                last_modified_by = ?
            WHERE id = ?
        `, [
            newStorageName,
            size,
            newVersion, // 使用确保是数字的 newVersion
            userId,
            fileId
        ]);

        await connection.query(`
            INSERT INTO file_versions (
                file_id, version, storage_name, file_size, modified_by
            ) VALUES (?, ?, ?, ?, ?)
        `, [
            fileId,
            newVersion,
            newStorageName,
            size,
            userId
        ]);

        console.log(`文件已更新: ${currentFile.original_name} (ID: ${fileId}, 版本: ${newVersion})`);

        return {
            id: fileId,
            originalName: currentFile.original_name,
            storageName: newStorageName,
            size,
            version: newVersion
        };
    });
}

/**
 * 删除文件
 * @param {string} fileId 文件ID
 * @returns {Promise<boolean>} 是否成功删除
 */
async function deleteFile(fileId) {
    try {
        // 标记文件为已删除（软删除）
        await db.query(`
      UPDATE files
      SET is_deleted = TRUE
      WHERE id = :fileId
    `, { fileId });

        console.log(`文件已标记为删除: ${fileId}`);
        return true;
    } catch (error) {
        console.error('删除文件失败:', error);
        throw error;
    }
}

/**
 * 物理删除文件
 * @param {string} fileId 文件ID
 * @returns {Promise<boolean>} 是否成功删除
 */
async function physicalDeleteFile(fileId) {
    try {
        // 获取文件信息
        const fileInfo = await getFileById(fileId);
        if (!fileInfo) {
            throw new Error(`文件不存在: ${fileId}`);
        }

        // 获取所有版本的存储文件名
        const versions = await db.query(`
      SELECT storage_name
      FROM file_versions
      WHERE file_id = :fileId
    `, { fileId });

        // 删除所有版本的物理文件
        for (const version of versions) {
            const filePath = path.join(config.storage.uploadDir, version.storage_name);
            if (await fs.pathExists(filePath)) {
                await fs.remove(filePath);
                console.log(`删除物理文件: ${filePath}`);
            }
        }

        // 删除数据库记录
        await db.query(`DELETE FROM file_versions WHERE file_id = :fileId`, { fileId });
        await db.query(`DELETE FROM files WHERE id = :fileId`, { fileId });

        console.log(`文件已完全删除: ${fileId}`);
        return true;
    } catch (error) {
        console.error('物理删除文件失败:', error);
        throw error;
    }
}

module.exports = {
    saveFile,
    getFileById,
    getAllFiles,
    updateFile,
    deleteFile,
    physicalDeleteFile,
    generateStorageName,
    getFileExtension,
    getMimeType
}; 