import { computed, ref, readonly, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import ApiService from '@/services/api'

// 类型定义
interface UserInfo {
  id?: string
  username: string
  email?: string
  fullName?: string
  role?: string
}

interface MenuItem {
  key: string
  label: string
  path?: string
  permission?: string | string[]
  children?: MenuItem[]
}

interface ApiResponse<T = unknown> {
  success: boolean
  data: T
  message?: string
}

interface PermissionApiData {
  permissions: string[]
  roles?: string[]
}

/**
 * 权限管理组合函数
 * @description 统一管理前端权限检查和控制
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */

// 全局权限状态
const userPermissions = ref<string[]>([])
const userRoles = ref<string[]>([])
const userInfo = ref<UserInfo | null>(null)

// 权限状态管理
export const permissionState = reactive({
  permissions: [] as string[],
  roles: [] as string[],
  isLoading: false,
  isInitialized: false,
  isSuperAdmin: false,
  user: null as UserInfo | null,
})

export function usePermissions() {
  const router = useRouter()

  /**
   * 初始化用户权限信息
   * @param permissions 用户权限列表
   * @param roles 用户角色列表
   * @param user 用户信息
   */
  const initPermissions = (permissions: string[], roles: string[] = []) => {
    const currentUser = getCurrentUser()

    console.log('🔐 [权限控制] 初始化权限信息:', {
      permissions,
      roles,
      user: currentUser?.username,
    })

    permissionState.permissions = permissions
    permissionState.roles = roles
    permissionState.user = currentUser
    permissionState.isSuperAdmin = permissions.includes('*')
    permissionState.isInitialized = true
  }

  /**
   * 检查是否拥有指定权限
   * @param permission 权限代码或权限数组
   * @returns 是否拥有权限
   */
  const hasPermission = (permission: string | string[]): boolean => {
    const currentUser = getCurrentUser()
    const effectivePermissions = getEffectivePermissions()

    console.log('🔍 [权限检查] hasPermission:', {
      requestedPermission: permission,
      currentUser: currentUser?.username,
      effectivePermissions,
      originalPermissions: permissionState.permissions,
      timestamp: new Date().toISOString(),
    })

    // 检查超级管理员权限
    if (effectivePermissions.includes('*')) {
      console.log('✅ [权限检查] 超级管理员权限通过')
      return true
    }

    // 如果是超级管理员，自动拥有所有权限
    if (permission === PERMISSIONS.SYSTEM.ALL && permissionState.isSuperAdmin) {
      console.log('🔐 [权限检查] 超级管理员访问全系统权限')
      return true
    }

    // 单个权限检查（字符串）
    if (typeof permission === 'string') {
      return effectivePermissions.includes(permission)
    }

    // 多个权限检查（数组）- 需要拥有任意一个权限
    if (Array.isArray(permission)) {
      return permission.some(p => effectivePermissions.includes(p))
    }

    console.warn('🔐 [权限检查] 权限格式无效:', permission)
    return false
  }

  /**
   * 检查是否拥有所有指定权限
   * @param permissions 权限数组
   * @returns 是否拥有所有权限
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!permissions || permissions.length === 0) return true

    // 超级管理员拥有所有权限
    if (userRoles.value.includes('super_admin') || userPermissions.value.includes('*')) {
      return true
    }

    return permissions.every(p => userPermissions.value.includes(p))
  }

  /**
   * 检查是否拥有指定角色
   * @param role 角色名称或角色数组
   * @returns 是否拥有角色
   */
  const hasRole = (role: string | string[]): boolean => {
    if (!role) return true

    if (Array.isArray(role)) {
      return role.some(r => userRoles.value.includes(r))
    } else {
      return userRoles.value.includes(role)
    }
  }

  /**
   * 检查模块权限
   * @param module 模块名称 (users, documents, permissions, etc.)
   * @param action 操作类型 (read, create, update, delete, *)
   * @returns 是否拥有模块权限
   */
  const hasModulePermission = (module: string): boolean => {
    const effectivePermissions = getEffectivePermissions()

    // 检查超级管理员权限
    if (effectivePermissions.includes('*')) {
      return true
    }

    // 检查模块权限
    const modulePattern = `${module}.*`
    return effectivePermissions.some(
      permission => permission === modulePattern || permission.startsWith(`${module}.`)
    )
  }

  /**
   * 获取菜单项权限过滤后的列表
   * @param menuItems 原始菜单项
   * @returns 过滤后的菜单项
   */
  const getFilteredMenuItems = (menuItems: MenuItem[]): MenuItem[] => {
    return menuItems
      .filter(item => {
        if (!item.permission) return true
        return hasPermission(item.permission)
      })
      .map(item => ({
        ...item,
        children: item.children ? getFilteredMenuItems(item.children) : undefined,
      }))
  }

  /**
   * 获取当前用户信息
   */
  const getCurrentUser = () => {
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr) {
      try {
        return JSON.parse(userInfoStr) as UserInfo
      } catch (error) {
        console.error('❌ [权限控制] 解析用户信息失败:', error)
        return null
      }
    }
    return null
  }

  /**
   * 获取有效权限列表
   */
  const getEffectivePermissions = () => {
    return permissionState.permissions
  }

  /**
   * 加载用户权限
   */
  const loadUserPermissions = async () => {
    try {
      permissionState.isLoading = true
      console.log('🔐 [Auth] 开始获取用户权限...')

      const response = (await ApiService.get(
        '/permissions/user/my'
      )) as ApiResponse<PermissionApiData>
      console.log('🔐 [Auth] 权限API响应:', response)

      const { permissions = [], roles = [] } = response.data || {}
      initPermissions(permissions, roles)

      console.log('✅ [Auth] 权限初始化成功:', {
        permissions: permissions.length,
        permissionList: permissions,
      })

      return { permissions, roles }
    } catch (error: unknown) {
      console.error('❌ [Auth] 权限获取失败:', error)
      throw error
    } finally {
      permissionState.isLoading = false
    }
  }

  /**
   * 清除权限信息（用于登出）
   */
  const clearPermissions = () => {
    console.log('🔐 [权限控制] 清除权限信息')
    permissionState.permissions = []
    permissionState.roles = []
    permissionState.user = null
    permissionState.isSuperAdmin = false
    permissionState.isInitialized = false
  }

  /**
   * 从localStorage加载权限信息
   */
  const loadPermissionsFromStorage = () => {
    try {
      const userInfoStr = localStorage.getItem('userInfo')
      const permissionsStr = localStorage.getItem('userPermissions')
      const rolesStr = localStorage.getItem('userRoles')

      if (userInfoStr) {
        const permissions = permissionsStr ? JSON.parse(permissionsStr) : []
        const roles = rolesStr ? JSON.parse(rolesStr) : []

        initPermissions(permissions, roles)
        return true
      }
    } catch (error) {
      console.error('❌ [权限控制] 加载权限信息失败:', error)
    }
    return false
  }

  /**
   * 保存权限信息到localStorage
   */
  const savePermissionsToStorage = () => {
    try {
      localStorage.setItem('userPermissions', JSON.stringify(userPermissions.value))
      localStorage.setItem('userRoles', JSON.stringify(userRoles.value))
      if (userInfo.value) {
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      }
    } catch (error) {
      console.error('❌ [权限控制] 保存权限信息失败:', error)
    }
  }

  /**
   * 权限不足时的处理
   */
  const handleInsufficientPermissions = (requiredPermission?: string) => {
    console.error('❌ [权限控制] 权限不足:', {
      requiredPermission,
      currentPermissions: permissionState.permissions,
      timestamp: new Date().toISOString(),
    })

    message.error('您没有权限执行此操作')

    // 可以选择重定向到首页或显示无权限页面
    router.push('/dashboard')
  }

  return {
    // 状态
    userPermissions: readonly(userPermissions),
    userRoles: readonly(userRoles),
    userInfo: readonly(userInfo),
    permissionState: computed(() => permissionState),

    // 方法
    initPermissions,
    hasPermission,
    hasAllPermissions,
    hasRole,
    hasModulePermission,
    getFilteredMenuItems,
    clearPermissions,
    loadPermissionsFromStorage,
    savePermissionsToStorage,
    loadUserPermissions,
    handleInsufficientPermissions,

    // 工具函数
    getCurrentUser,

    // 权限常量
    PERMISSIONS,
  }
}

// 权限指令 - 用于v-permission指令
export const permissionDirective = {
  mounted(el: HTMLElement, binding: { value: string | string[] }) {
    const { hasPermission } = usePermissions()
    const permission = binding.value

    if (!hasPermission(permission)) {
      el.style.display = 'none'
      // 或者移除元素：el.parentNode?.removeChild(el)
    }
  },
  updated(el: HTMLElement, binding: { value: string | string[] }) {
    const { hasPermission } = usePermissions()
    const permission = binding.value

    if (!hasPermission(permission)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  },
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 用户管理
  USERS: {
    READ: 'users.read',
    CREATE: 'users.create',
    UPDATE: 'users.update',
    DELETE: 'users.delete',
    ALL: 'users.*',
  },

  // 文档管理
  DOCUMENTS: {
    READ: 'documents.read',
    CREATE: 'documents.create',
    UPDATE: 'documents.update',
    DELETE: 'documents.delete',
    ALL: 'documents.*',
  },

  // 模板管理
  TEMPLATES: {
    READ: 'templates.read',
    CREATE: 'templates.create',
    UPDATE: 'templates.update',
    DELETE: 'templates.delete',
    ALL: 'templates.*',
  },

  // 权限管理
  PERMISSIONS: {
    READ: 'permissions.read',
    CREATE: 'permissions.create',
    UPDATE: 'permissions.update',
    DELETE: 'permissions.delete',
    ALL: 'permissions.*',
  },

  // 角色管理
  ROLES: {
    READ: 'roles.read',
    CREATE: 'roles.create',
    UPDATE: 'roles.update',
    DELETE: 'roles.delete',
    ALL: 'roles.*',
  },

  // 系统管理
  SYSTEM: {
    READ: 'system.read',
    MANAGE: 'system.manage',
    ALL: 'system.*',
  },

  // 配置管理
  CONFIG: {
    READ: 'config.read',
    UPDATE: 'config.update',
    ALL: 'config.*',
  },

  // 超级权限
  SUPER: '*',
} as const
