const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: '*************',
    port: 3306,
    user: 'onlyfile_user', 
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
};

async function debugUser() {
    let connection;
    
    try {
        console.log('🔄 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 查看所有用户
        console.log('🔄 查询所有用户...');
        const [users] = await connection.execute(`
            SELECT u.*, ur.name as role_name, ur.display_name as role_display_name
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            WHERE u.deleted_at IS NULL
            ORDER BY u.created_at
        `);
        
        console.log(`📋 找到 ${users.length} 个用户:`);
        users.forEach((user, index) => {
            console.log(`  ${index + 1}. 用户名: ${user.username}`);
            console.log(`     ID: ${user.id}`);
            console.log(`     邮箱: ${user.email || '无'}`);
            console.log(`     姓名: ${user.full_name || '无'}`);
            console.log(`     状态: ${user.status}`);
            console.log(`     角色: ${user.role_name} (${user.role_display_name})`);
            console.log(`     密码哈希: ${user.password_hash.substring(0, 30)}...`);
            console.log(`     创建时间: ${user.created_at}`);
            console.log('     ----------------------');
        });
        
        // 专门查看admin用户
        console.log('🔄 查询admin用户详情...');
        const [adminUsers] = await connection.execute(`
            SELECT u.*, ur.name as role_name, ur.display_name as role_display_name
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            WHERE u.username = 'admin' AND u.deleted_at IS NULL
        `);
        
        if (adminUsers.length > 0) {
            const admin = adminUsers[0];
            console.log('👑 Admin用户详情:');
            console.log(`  - ID: ${admin.id}`);
            console.log(`  - 用户名: ${admin.username}`);
            console.log(`  - 邮箱: ${admin.email || '无'}`);
            console.log(`  - 姓名: ${admin.full_name || '无'}`);
            console.log(`  - 状态: ${admin.status}`);
            console.log(`  - 角色ID: ${admin.role_id}`);
            console.log(`  - 角色名: ${admin.role_name}`);
            console.log(`  - 角色显示名: ${admin.role_display_name}`);
            console.log(`  - 密码哈希: ${admin.password_hash}`);
            console.log(`  - 失败登录次数: ${admin.failed_login_attempts || 0}`);
            console.log(`  - 锁定到期时间: ${admin.locked_until || '无'}`);
            console.log(`  - 创建时间: ${admin.created_at}`);
            console.log(`  - 更新时间: ${admin.updated_at}`);
        } else {
            console.log('❌ 未找到admin用户');
        }
        
        // 查看角色表
        console.log('🔄 查询所有角色...');
        const [roles] = await connection.execute('SELECT * FROM user_roles ORDER BY created_at');
        
        console.log(`📋 找到 ${roles.length} 个角色:`);
        roles.forEach((role, index) => {
            console.log(`  ${index + 1}. ID: ${role.id}`);
            console.log(`     名称: ${role.name}`);
            console.log(`     显示名: ${role.display_name}`);
            console.log(`     状态: ${role.status}`);
            console.log('     ----------------------');
        });
        
    } catch (error) {
        console.error('❌ 调试失败:', error.message);
        console.error('🔧 完整错误:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 执行调试
debugUser(); 