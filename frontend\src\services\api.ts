import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type AxiosError,
} from 'axios'
import NProgress from 'nprogress'
import type { ApiResponse } from '@/types/api.types'

// 创建axios实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: '/api',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  instance.interceptors.request.use(
    async config => {
      NProgress.start()

      // 添加认证头
      const token = localStorage.getItem('token')
      console.log('🔍 API请求拦截器调试:', {
        url: config.url,
        method: config.method,
        baseURL: config.baseURL,
        fullURL: (config.baseURL || '') + (config.url || ''),
        hasToken: !!token,
        tokenLength: token ? token.length : 0,
        tokenPreview: token ? token.substring(0, 20) + '...' : null,
        currentHeaders: config.headers,
      })

      if (token) {
        try {
          // 验证token格式
          const parts = token.split('.')
          if (parts.length !== 3) {
            console.log('❌ Token格式无效，长度不正确:', parts.length)
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            const authError = new Error('Token格式无效')
            authError.name = 'AuthenticationError'
            throw authError
          }

          // 解析token payload (不验证签名，仅用于调试)
          try {
            const payload = JSON.parse(atob(parts[1]))
            console.log('🔍 Token详细信息:', {
              header: JSON.parse(atob(parts[0])),
              payload: {
                sub: payload.sub,
                username: payload.username,
                iat: payload.iat,
                exp: payload.exp,
                issuer: payload.iss,
                isExpired: payload.exp ? Date.now() / 1000 > payload.exp : false,
                timeToExpire: payload.exp ? Math.max(0, payload.exp - Date.now() / 1000) : null,
              },
              signatureLength: parts[2].length,
              fullTokenLength: token.length,
            })
          } catch (parseError: unknown) {
            const error = parseError as Error
            console.log('⚠️ Token payload解析失败（可能是加密的）:', error.message)
          }

          config.headers.Authorization = `Bearer ${token}`
          console.log('✅ 已添加Authorization头:', `Bearer ${token.substring(0, 20)}...`)
        } catch (error) {
          console.error('❌ Token处理失败:', error)
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          const authError = new Error('Token无效')
          authError.name = 'AuthenticationError'
          throw authError
        }
      } else {
        console.log('⚠️ 未找到token，可能是无需认证的请求')
      }

      console.log('📤 最终请求配置:', {
        url: config.url,
        method: config.method,
        headers: config.headers,
        hasAuthHeader: !!config.headers.Authorization,
      })

      return config
    },
    error => {
      console.log('❌ 请求拦截器错误:', error)
      NProgress.done()
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      NProgress.done()

      console.log('📥 API响应成功:', {
        url: response.config.url,
        method: response.config.method,
        status: response.status,
        statusText: response.statusText,
        responseDataType: typeof response.data,
        responseData: response.data,
        timestamp: new Date().toISOString(),
      })

      // 如果是下载文件等特殊响应，直接返回
      if (response.headers['content-type']?.includes('application/octet-stream')) {
        return response
      }

      // 检查业务错误
      if (response.data && response.data.success === false) {
        console.error('❌ API业务错误:', {
          url: response.config.url,
          message: response.data.message,
          error: (response.data as ApiResponse & { error?: unknown }).error || 'unknown',
          data: response.data,
          timestamp: new Date().toISOString(),
        })

        // 抛出业务错误
        throw new Error(response.data.message || '请求失败')
      }

      return response
    },
    (error: AxiosError<ApiResponse>) => {
      NProgress.done()

      console.error('❌ API请求失败:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      })

      // 处理HTTP状态错误
      if (error.response) {
        const { status, data } = error.response
        let errorMessage = '请求失败'

        if (data?.message) {
          errorMessage = data.message
        } else {
          switch (status) {
            case 400:
              errorMessage = '请求参数错误'
              break
            case 401:
              errorMessage = '未授权，请重新登录'
              // 清除认证信息并跳转登录页
              localStorage.removeItem('token')
              localStorage.removeItem('userInfo')
              window.location.href = '/login'
              break
            case 403:
              errorMessage = '没有权限访问'
              break
            case 404:
              errorMessage = '请求的资源不存在'
              break
            case 500:
              errorMessage = '服务器内部错误'
              break
            default:
              errorMessage = `请求失败 (${status})`
          }
        }

        throw new Error(errorMessage)
      }

      // 网络错误等
      throw new Error(error.message || '网络连接失败')
    }
  )

  return instance
}

// 创建API实例
export const api = createApiInstance()

// 通用请求方法
export class ApiService {
  /**
   * GET请求
   */
  static async get<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.get<ApiResponse<T>>(url, config)
    return response.data.data as T
  }

  /**
   * POST请求
   */
  static async post<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.post<ApiResponse<T>>(url, data, config)
    return response.data.data as T
  }

  /**
   * PUT请求
   */
  static async put<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.put<ApiResponse<T>>(url, data, config)
    return response.data.data as T
  }

  /**
   * DELETE请求
   */
  static async delete<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.delete<ApiResponse<T>>(url, config)
    return response.data.data as T
  }

  /**
   * 文件上传
   */
  static async upload<T = unknown>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await api.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(percent)
        }
      },
    })

    return response.data.data as T
  }

  /**
   * 文件下载
   */
  static async download(
    url: string,
    filename?: string,
    config?: AxiosRequestConfig
  ): Promise<void> {
    const response = await api.get(url, {
      ...config,
      responseType: 'blob',
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

export default ApiService
