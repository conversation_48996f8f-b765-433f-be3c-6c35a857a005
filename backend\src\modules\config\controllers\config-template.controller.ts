import { Controller, Get, Post, Put, Delete, Param, Body, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { ConfigTemplateService } from '../services/config-template.service';

/**
 * 配置模板控制器
 * 提供OnlyOffice配置模板的管理API
 */
@ApiTags('配置管理')
@Controller('config-templates')
export class ConfigTemplateController {
  constructor(private configTemplateService: ConfigTemplateService) {}

  /**
   * 获取所有配置模板
   */
  @Get()
  @ApiOperation({ 
    summary: '获取所有配置模板',
    description: '获取系统中所有可用的OnlyOffice配置模板'
  })
  @ApiResponse({ 
    status: 200, 
    description: '成功获取配置模板列表',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'default-edit' },
              name: { type: 'string', example: '默认编辑版' },
              description: { type: 'string', example: '标准的编辑器配置' },
              is_default: { type: 'boolean', example: true },
              is_active: { type: 'boolean', example: true },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' }
            }
          }
        },
        message: { type: 'string', example: '获取配置模板列表成功' }
      }
    }
  })
  async getAllTemplates() {
    try {
      const templates = await this.configTemplateService.getAllTemplates();
      return {
        success: true,
        data: templates,
        message: '获取配置模板列表成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: '获取配置模板列表失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 根据ID获取配置模板详情
   */
  @Get(':id')
  @ApiOperation({ 
    summary: '获取配置模板详情',
    description: '根据模板ID获取配置模板的详细信息和配置项'
  })
  @ApiParam({ name: 'id', description: '配置模板ID', example: 'default-edit' })
  @ApiResponse({ 
    status: 200, 
    description: '成功获取配置模板详情',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            template: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'default-edit' },
                name: { type: 'string', example: '默认编辑版' },
                description: { type: 'string', example: '标准的编辑器配置' }
              }
            },
            config: {
              type: 'object',
              properties: {
                permissions: {
                  type: 'object',
                  properties: {
                    edit: { type: 'boolean', example: true },
                    download: { type: 'boolean', example: true },
                    print: { type: 'boolean', example: true }
                  }
                },
                customization: {
                  type: 'object',
                  properties: {
                    forcesave: { type: 'boolean', example: true },
                    autosave: { type: 'boolean', example: true }
                  }
                }
              }
            }
          }
        },
        message: { type: 'string', example: '获取配置模板详情成功' }
      }
    }
  })
  @ApiResponse({ status: 404, description: '配置模板不存在' })
  async getTemplateById(@Param('id') templateId: string) {
    try {
      const templateConfig = await this.configTemplateService.getTemplateConfig(templateId);
      return {
        success: true,
        data: templateConfig,
        message: '获取配置模板详情成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error.message === '配置模板不存在') {
        throw new HttpException(
          {
            success: false,
            message: '配置模板不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          success: false,
          message: '获取配置模板详情失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取默认配置模板
   */
  @Get('default/template')
  @ApiOperation({ 
    summary: '获取默认配置模板',
    description: '获取系统设置的默认配置模板'
  })
  @ApiResponse({ 
    status: 200, 
    description: '成功获取默认配置模板'
  })
  async getDefaultTemplate() {
    try {
      const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
      if (!defaultTemplate) {
        throw new HttpException(
          {
            success: false,
            message: '未找到默认配置模板',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }

      const templateConfig = await this.configTemplateService.getTemplateConfig((defaultTemplate as { id: string }).id);
      return {
        success: true,
        data: templateConfig,
        message: '获取默认配置模板成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '获取默认配置模板失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取模板的有效配置（仅包含启用的配置项，用于OnlyOffice）
   */
  @Get(':id/effective-config')
  @ApiOperation({ 
    summary: '获取模板的有效配置',
    description: '获取指定模板的有效配置，仅包含启用的配置项，用于OnlyOffice编辑器'
  })
  @ApiParam({ name: 'id', description: '配置模板ID' })
  @ApiResponse({ 
    status: 200, 
    description: '成功获取有效配置'
  })
  async getEffectiveConfig(@Param('id') templateId: string) {
    try {
      const effectiveConfig = await this.configTemplateService.getEffectiveConfig(templateId);
      return {
        success: true,
        data: effectiveConfig,
        message: '获取有效配置成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error.message === '配置模板不存在') {
        throw new HttpException(
          {
            success: false,
            message: '配置模板不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          success: false,
          message: '获取有效配置失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建新的配置模板
   */
  @Post()
  @ApiOperation({ 
    summary: '创建配置模板',
    description: '创建新的OnlyOffice配置模板'
  })
  @ApiBody({
    description: '配置模板数据',
    schema: {
      type: 'object',
      required: ['name', 'description'],
      properties: {
        name: { type: 'string', example: '自定义编辑版', description: '模板名称' },
        description: { type: 'string', example: '自定义的编辑器配置', description: '模板描述' },
        configItems: {
          type: 'array',
          description: '配置项列表',
          items: {
            type: 'object',
            properties: {
              config_group: { type: 'string', example: 'permissions' },
              config_key: { type: 'string', example: 'edit' },
              config_value: { type: 'string', example: 'true' },
              value_type: { type: 'string', example: 'boolean' },
              is_enabled: { type: 'boolean', example: true },
              is_required: { type: 'boolean', example: false },
              description: { type: 'string', example: '允许编辑文档' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ 
    status: 201, 
    description: '配置模板创建成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: { type: 'string', example: 'uuid-template-id', description: '创建的模板ID' },
        message: { type: 'string', example: '配置模板创建成功' }
      }
    }
  })
  async createTemplate(@Body() templateData: Record<string, unknown>) {
    try {
      const templateId = await this.configTemplateService.createTemplate(templateData);
      return {
        success: true,
        data: templateId,
        message: '配置模板创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: '配置模板创建失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新配置模板
   */
  @Put(':id')
  @ApiOperation({ 
    summary: '更新配置模板',
    description: '更新指定的配置模板信息和配置项'
  })
  @ApiParam({ name: 'id', description: '配置模板ID' })
  @ApiBody({
    description: '更新的配置模板数据',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: '更新的编辑版' },
        description: { type: 'string', example: '更新的编辑器配置' },
        configItems: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              config_group: { type: 'string' },
              config_key: { type: 'string' },
              config_value: { type: 'string' },
              value_type: { type: 'string' },
              is_enabled: { type: 'boolean' },
              is_required: { type: 'boolean' },
              description: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ 
    status: 200, 
    description: '配置模板更新成功'
  })
  @ApiResponse({ status: 404, description: '配置模板不存在' })
  async updateTemplate(@Param('id') templateId: string, @Body() templateData: Record<string, unknown>) {
    try {
      // 先检查模板是否存在
      const existingTemplate = await this.configTemplateService.getTemplateById(templateId);
      if (!existingTemplate) {
        throw new HttpException(
          {
            success: false,
            message: '配置模板不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }

      await this.configTemplateService.updateTemplate(templateId, templateData);
      return {
        success: true,
        data: true,
        message: '配置模板更新成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '配置模板更新失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除配置模板
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: '删除配置模板',
    description: '删除指定的配置模板（软删除）'
  })
  @ApiParam({ name: 'id', description: '配置模板ID' })
  @ApiResponse({ 
    status: 200, 
    description: '配置模板删除成功'
  })
  @ApiResponse({ status: 404, description: '配置模板不存在' })
  async deleteTemplate(@Param('id') templateId: string) {
    try {
      // 先检查模板是否存在
      const existingTemplate = await this.configTemplateService.getTemplateById(templateId);
      if (!existingTemplate) {
        throw new HttpException(
          {
            success: false,
            message: '配置模板不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }

      await this.configTemplateService.deleteTemplate(templateId);
      return {
        success: true,
        data: true,
        message: '配置模板删除成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '配置模板删除失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 设置默认配置模板
   */
  @Put(':id/set-default')
  @ApiOperation({ 
    summary: '设置默认配置模板',
    description: '将指定的配置模板设置为系统默认模板'
  })
  @ApiParam({ name: 'id', description: '配置模板ID' })
  @ApiResponse({ 
    status: 200, 
    description: '默认配置模板设置成功'
  })
  @ApiResponse({ status: 404, description: '配置模板不存在' })
  async setDefaultTemplate(@Param('id') templateId: string) {
    try {
      // 先检查模板是否存在
      const existingTemplate = await this.configTemplateService.getTemplateById(templateId);
      if (!existingTemplate) {
        throw new HttpException(
          {
            success: false,
            message: '配置模板不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }

      await this.configTemplateService.setDefaultTemplate(templateId);
      return {
        success: true,
        data: true,
        message: '默认配置模板设置成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '设置默认配置模板失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 