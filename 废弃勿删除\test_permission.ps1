$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NTEzNDMwNjEsImV4cCI6MTc1MTQyOTQ2MSwiYXVkIjoib25seW9mZmljZS1jbGllbnQiLCJpc3MiOiJvbmx5b2ZmaWNlLW5lc3RqcyJ9.fypHQeRRPybb0DX21oq9br4ktziXn_9whw2349zakPI"

$headers = @{
  "Authorization" = "Bearer $token"
  "Accept"        = "application/json"
  "Content-Type"  = "application/json"
}

try {
  Write-Host "正在调用权限API..."
  $response = Invoke-RestMethod -Uri "http://*************:3000/api/permissions/user/my" -Method GET -Headers $headers
  Write-Host "API响应:" 
  $response | ConvertTo-Json -Depth 10
}
catch {
  Write-Host "错误: $_"
  Write-Host "详细错误: $($_.Exception.Message)"
} 