const mysql = require('mysql2/promise');

async function checkAuditLogs() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库检查审计日志...');
    
    connection = await mysql.createConnection({
      host: '*************',
      port: 3306,
      user: 'onlyfile_user',
      password: '0nlyF!le$ecure#123',
      database: 'onlyfile'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查审计日志表结构
    console.log('\n📋 1. 检查 audit_logs 表结构...');
    const [columns] = await connection.query(`DESCRIBE audit_logs`);
    console.log('audit_logs 表字段:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // 2. 统计审计日志总数
    console.log('\n📈 2. 统计审计日志记录...');
    const [totalCount] = await connection.query(`SELECT COUNT(*) as total FROM audit_logs`);
    console.log(`审计日志总记录数: ${totalCount[0].total}`);
    
    // 3. 查看所有审计日志
    console.log('\n📋 3. 查看所有审计日志记录...');
    const [allLogs] = await connection.query(`
      SELECT 
        id, action, resource_type, resource_id, 
        created_at, status, details
      FROM audit_logs 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    if (allLogs.length > 0) {
      console.log('最近的审计日志:');
      allLogs.forEach((log, index) => {
        console.log(`${index + 1}. ID: ${log.id}`);
        console.log(`   操作: ${log.action}`);
        console.log(`   资源类型: ${log.resource_type}`);
        console.log(`   资源ID: ${log.resource_id}`);
        console.log(`   时间: ${log.created_at}`);
        console.log(`   状态: ${log.status}`);
        console.log(`   详情: ${log.details}`);
        console.log('');
      });
    } else {
      console.log('❌ 审计日志表为空');
    }
    
    // 4. 专门查看配置相关的日志
    console.log('\n🔍 4. 查看配置相关的审计日志...');
    const [configLogs] = await connection.query(`
      SELECT 
        id, action, resource_type, resource_id, 
        created_at, status, details
      FROM audit_logs 
      WHERE resource_type = 'system_config' 
         OR action LIKE '%config%'
         OR action LIKE '%setting%'
      ORDER BY created_at DESC
    `);
    
    console.log(`配置相关日志数量: ${configLogs.length}`);
    if (configLogs.length > 0) {
      console.log('配置相关日志:');
      configLogs.forEach((log, index) => {
        console.log(`${index + 1}. ID: ${log.id}`);
        console.log(`   操作: ${log.action}`);
        console.log(`   资源类型: ${log.resource_type}`);
        console.log(`   资源ID: ${log.resource_id}`);
        console.log(`   时间: ${log.created_at}`);
        console.log(`   详情: ${log.details}`);
        console.log('');
      });
    } else {
      console.log('❌ 没有找到配置相关的审计日志');
    }
    
    // 5. 检查最近更新的 cache.ttl 配置
    console.log('\n⏰ 5. 检查 cache.ttl 配置的最新状态...');
    const [cacheConfig] = await connection.query(`
      SELECT setting_key, setting_value, description, updated_at
      FROM system_settings 
      WHERE setting_key = 'cache.ttl'
    `);
    
    if (cacheConfig.length > 0) {
      const config = cacheConfig[0];
      console.log('cache.ttl 配置:');
      console.log(`  键: ${config.setting_key}`);
      console.log(`  值: ${config.setting_value}`);
      console.log(`  描述: ${config.description}`);
      console.log(`  更新时间: ${config.updated_at}`);
    } else {
      console.log('❌ 未找到 cache.ttl 配置');
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error('详细信息:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

console.log('🚀 开始检查审计日志...\n');
checkAuditLogs(); 