# 前端权限控制系统

> **版本**: v1.0  
> **更新时间**: 2024年12月19日  
> **适用范围**: OnlyOffice集成系统前端权限管理  

## 🎯 系统概述

前端权限控制系统是一个完整的RBAC（基于角色的访问控制）实现，提供了多层次的权限控制机制，确保用户只能访问其被授权的功能模块。

### 权限控制层次

```
┌─────────────────────────────────────────┐
│              前端权限控制               │
├─────────────────────────────────────────┤
│  1. 路由级权限 (页面访问控制)           │
│  2. 菜单级权限 (导航显示控制)           │
│  3. 组件级权限 (功能按钮控制)           │
│  4. 指令级权限 (元素显示控制)           │
└─────────────────────────────────────────┘
```

## 🏗️ 架构设计

### 1. 核心组成部分

#### **权限管理Composable** (`usePermissions.ts`)
- 统一的权限检查函数
- 权限状态管理
- 权限常量定义

#### **路由权限守卫** (`router/permission.ts`)
- 页面访问权限控制
- 自动重定向机制
- 权限状态加载

#### **权限菜单组件** (`PermissionMenu.vue`)
- 动态菜单显示
- 权限过滤
- 菜单状态管理

#### **权限指令** (`v-permission`)
- 元素级权限控制
- 声明式权限检查

### 2. 权限数据流

```mermaid
graph TB
    A[用户登录] --> B[获取用户权限]
    B --> C[初始化权限状态]
    C --> D[路由守卫检查]
    D --> E[菜单权限过滤]
    E --> F[组件权限控制]
    F --> G[指令权限检查]
```

## 📖 使用指南

### 1. 基础权限检查

```typescript
import { usePermissions, PERMISSIONS } from '@/composables/usePermissions'

const { hasPermission, hasRole, hasModulePermission } = usePermissions()

// 检查单个权限
const canEditUser = hasPermission(PERMISSIONS.USERS.UPDATE)

// 检查多个权限（任意一个）
const canAccessUserModule = hasPermission([
  PERMISSIONS.USERS.READ,
  PERMISSIONS.USERS.UPDATE
])

// 检查角色
const isAdmin = hasRole(['admin', 'super_admin'])

// 检查模块权限
const canManageDocuments = hasModulePermission('documents', 'read')
```

### 2. 模板中的权限控制

#### **v-if 条件渲染**
```vue
<template>
  <!-- 按钮权限控制 -->
  <a-button 
    v-if="hasPermission(PERMISSIONS.USERS.CREATE)" 
    type="primary"
    @click="createUser"
  >
    新建用户
  </a-button>

  <!-- 多权限检查 -->
  <a-button 
    v-if="hasPermission([PERMISSIONS.USERS.UPDATE, PERMISSIONS.USERS.DELETE])"
    @click="editUser"
  >
    编辑用户
  </a-button>

  <!-- 角色检查 -->
  <a-button 
    v-if="permissionState.isAdmin"
    type="danger"
    @click="deleteUser"
  >
    删除用户
  </a-button>
</template>
```

#### **v-permission 指令**
```vue
<template>
  <!-- 使用权限指令 -->
  <a-button v-permission="PERMISSIONS.USERS.CREATE">
    新建用户
  </a-button>

  <!-- 多权限指令 -->
  <div v-permission="[PERMISSIONS.USERS.READ, PERMISSIONS.USERS.UPDATE]">
    用户管理内容
  </div>
</template>
```

### 3. 路由权限配置

```typescript
// router/permission.ts
const ROUTE_PERMISSIONS = {
  '/users': ['users.read'],
  '/users/create': ['users.create'],
  '/users/edit': ['users.update'],
  '/permissions': ['permissions.read', 'roles.read'],
}

// 动态添加路由权限
addRoutePermission('/custom-page', ['custom.read'])
```

### 4. 菜单权限配置

```typescript
// components/PermissionMenu.vue
const menuItems = [
  {
    key: 'users',
    title: '用户管理',
    icon: UserOutlined,
    path: '/users',
    permission: PERMISSIONS.USERS.READ, // 权限要求
  },
  {
    key: 'permissions',
    title: '权限管理',
    path: '/permissions',
    permission: [PERMISSIONS.PERMISSIONS.READ, PERMISSIONS.ROLES.READ], // 多权限
  },
]
```

## 🛡️ 权限常量定义

### 系统内置权限

```typescript
export const PERMISSIONS = {
  // 用户管理
  USERS: {
    READ: 'users.read',      // 查看用户
    CREATE: 'users.create',  // 创建用户
    UPDATE: 'users.update',  // 更新用户
    DELETE: 'users.delete',  // 删除用户
    ALL: 'users.*'          // 用户管理全权限
  },
  
  // 文档管理
  DOCUMENTS: {
    READ: 'documents.read',
    CREATE: 'documents.create',
    UPDATE: 'documents.update',
    DELETE: 'documents.delete',
    ALL: 'documents.*'
  },
  
  // 权限管理
  PERMISSIONS: {
    READ: 'permissions.read',
    CREATE: 'permissions.create',
    UPDATE: 'permissions.update',
    DELETE: 'permissions.delete',
    ALL: 'permissions.*'
  },
  
  // 超级权限
  SUPER: '*'  // 所有权限
}
```

## 🔧 高级用法

### 1. 权限状态计算属性

```typescript
const { permissionState } = usePermissions()

// 使用计算属性
const canManageSystem = computed(() => {
  return permissionState.value.isSuperAdmin || 
         permissionState.value.canManageSystem
})
```

### 2. 动态权限检查

```typescript
// 组件中动态检查权限
const checkUserAction = (action: string, userId: string) => {
  // 检查基础权限
  if (!hasPermission(`users.${action}`)) {
    message.error('您没有权限执行此操作')
    return false
  }
  
  // 检查业务逻辑权限（如：只能编辑自己的信息）
  const currentUser = userInfo.value
  if (action === 'update' && userId !== currentUser.id && !hasRole('admin')) {
    message.error('您只能编辑自己的信息')
    return false
  }
  
  return true
}
```

### 3. 权限缓存和性能优化

```typescript
// 权限检查结果缓存
const permissionCache = new Map<string, boolean>()

const cachedHasPermission = (permission: string) => {
  if (permissionCache.has(permission)) {
    return permissionCache.get(permission)!
  }
  
  const result = hasPermission(permission)
  permissionCache.set(permission, result)
  return result
}
```

## 🔒 安全考虑

### 1. 前端权限控制的局限性

> ⚠️ **重要提醒**: 前端权限控制仅用于改善用户体验，真正的安全控制必须在后端实现！

- **前端权限**: 隐藏UI元素，改善用户体验
- **后端权限**: 真正的安全边界，防止恶意访问

### 2. 安全最佳实践

#### **后端API权限验证**
```typescript
// 每个API调用都应该有权限验证
const createUser = async (userData: CreateUserDto) => {
  try {
    // 前端检查（用户体验）
    if (!hasPermission(PERMISSIONS.USERS.CREATE)) {
      throw new Error('您没有创建用户的权限')
    }
    
    // 后端API调用（真正的安全检查）
    const response = await apiService.post('/users', userData)
    return response.data
  } catch (error) {
    // 处理权限错误
    if (error.status === 403) {
      message.error('权限不足，无法执行此操作')
      return
    }
    throw error
  }
}
```

#### **敏感信息保护**
```typescript
// 不要在前端暴露敏感权限逻辑
const isSuperAdmin = hasRole('super_admin')

// ❌ 错误：前端判断敏感操作
if (isSuperAdmin) {
  // 删除系统数据等敏感操作
}

// ✅ 正确：仅用于UI显示
if (isSuperAdmin) {
  // 显示管理员专用菜单
}
```

## 📋 实现清单

### ✅ 已实现功能

- [x] 权限管理Composable
- [x] 路由权限守卫
- [x] 权限菜单组件
- [x] 权限指令
- [x] 权限常量定义
- [x] 权限状态管理
- [x] 本地存储集成

### 🚧 待优化功能

- [ ] 权限数据实时同步
- [ ] 权限变更通知
- [ ] 细粒度权限控制
- [ ] 权限审计日志
- [ ] 权限性能监控

## 🔧 集成步骤

### 1. 初始化权限系统

```typescript
// main.ts
import { permissionDirective } from '@/composables/usePermissions'
import { setupPermissionGuard } from '@/router/permission'

// 注册权限指令
app.directive('permission', permissionDirective)

// 设置路由守卫
setupPermissionGuard(router)
```

### 2. 登录时初始化权限

```typescript
// 登录成功后
const handleLoginSuccess = (loginResponse) => {
  const { user, permissions, roles } = loginResponse
  
  // 保存认证信息
  localStorage.setItem('token', user.token)
  localStorage.setItem('userInfo', JSON.stringify(user))
  
  // 初始化权限
  const { initPermissions } = usePermissions()
  initPermissions(permissions, roles, user)
  
  // 跳转到有权限的页面
  router.push('/dashboard')
}
```

### 3. 组件中使用权限

```vue
<script setup>
import { usePermissions, PERMISSIONS } from '@/composables/usePermissions'

const { hasPermission, permissionState } = usePermissions()
</script>

<template>
  <!-- 权限控制按钮 -->
  <a-button v-if="hasPermission(PERMISSIONS.USERS.CREATE)">
    新建用户
  </a-button>
  
  <!-- 管理员功能 -->
  <div v-if="permissionState.isAdmin">
    管理员专用功能
  </div>
</template>
```

## 🐛 常见问题

### Q: 权限检查失效？
**A**: 确保已正确初始化权限信息，检查localStorage中是否有权限数据。

### Q: 路由守卫不生效？
**A**: 确认已在main.ts中调用`setupPermissionGuard(router)`。

### Q: 权限指令不工作？
**A**: 检查是否已注册权限指令`app.directive('permission', permissionDirective)`。

### Q: 权限更新不及时？
**A**: 权限变更后需要重新调用`initPermissions()`或刷新页面。

---

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。

**文档维护**: AI Assistant  
**最后更新**: 2024年12月19日 