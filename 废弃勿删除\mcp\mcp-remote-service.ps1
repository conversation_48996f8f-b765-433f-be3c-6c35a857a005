# MCP MySQL 远程服务启动脚本
# 将MCP服务器作为HTTP服务运行

param(
    [int]$Port = 3000,
    [string]$SecretKey = "mcp-mysql-secret-$(Get-Random)",
    [switch]$Install,
    [switch]$Start,
    [switch]$Stop,
    [switch]$Status,
    [switch]$Help
)

if ($Help) {
    Write-Host "MCP MySQL 远程服务管理脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\mcp-remote-service.ps1 -Install    # 安装服务配置"
    Write-Host "  .\mcp-remote-service.ps1 -Start      # 启动服务"
    Write-Host "  .\mcp-remote-service.ps1 -Stop       # 停止服务"
    Write-Host "  .\mcp-remote-service.ps1 -Status     # 查看状态"
    Write-Host ""
    Write-Host "参数:" -ForegroundColor Yellow
    Write-Host "  -Port        服务端口 (默认: 3000)"
    Write-Host "  -SecretKey   认证密钥 (默认: 随机生成)"
    Write-Host ""
    exit 0
}

$ServiceName = "MCP-MySQL-Remote"
$ServiceDisplayName = "MCP MySQL Remote Service"
$ServiceDescription = "MCP MySQL服务器远程HTTP服务"

# 配置信息
$Config = @{
    MYSQL_HOST = "*************"
    MYSQL_PORT = "3306"
    MYSQL_USER = "onlyfile_user"
    MYSQL_PASS = "0nlyF!le`$ecure#123"
    MYSQL_DB = "onlyfile"
    ALLOW_INSERT_OPERATION = "true"
    ALLOW_UPDATE_OPERATION = "true"
    ALLOW_DELETE_OPERATION = "false"
    MYSQL_ENABLE_LOGGING = "true"
    IS_REMOTE_MCP = "true"
    PORT = $Port.ToString()
    REMOTE_SECRET_KEY = $SecretKey
}

Write-Host "MCP MySQL 远程服务管理" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

if ($Install) {
    Write-Host "安装远程服务配置..." -ForegroundColor Yellow
    
    # 创建服务配置文件
    $configContent = ""
    foreach ($key in $Config.Keys) {
        $configContent += "$key=$($Config[$key])`n"
    }
    
    $configContent | Out-File -FilePath "mcp-remote.env" -Encoding UTF8
    Write-Host "✓ 配置文件已创建: mcp-remote.env" -ForegroundColor Green
    
    # 创建启动脚本
    $startScript = @"
# MCP MySQL 远程服务启动脚本
`$env:MYSQL_HOST = "$($Config.MYSQL_HOST)"
`$env:MYSQL_PORT = "$($Config.MYSQL_PORT)"
`$env:MYSQL_USER = "$($Config.MYSQL_USER)"
`$env:MYSQL_PASS = "$($Config.MYSQL_PASS)"
`$env:MYSQL_DB = "$($Config.MYSQL_DB)"
`$env:ALLOW_INSERT_OPERATION = "$($Config.ALLOW_INSERT_OPERATION)"
`$env:ALLOW_UPDATE_OPERATION = "$($Config.ALLOW_UPDATE_OPERATION)"
`$env:ALLOW_DELETE_OPERATION = "$($Config.ALLOW_DELETE_OPERATION)"
`$env:MYSQL_ENABLE_LOGGING = "$($Config.MYSQL_ENABLE_LOGGING)"
`$env:IS_REMOTE_MCP = "$($Config.IS_REMOTE_MCP)"
`$env:PORT = "$($Config.PORT)"
`$env:REMOTE_SECRET_KEY = "$($Config.REMOTE_SECRET_KEY)"

Write-Host "启动MCP MySQL远程服务..." -ForegroundColor Green
Write-Host "端口: $($Config.PORT)" -ForegroundColor Cyan
Write-Host "密钥: $($Config.REMOTE_SECRET_KEY)" -ForegroundColor Cyan
Write-Host ""

npx @benborla29/mcp-server-mysql
"@
    
    $startScript | Out-File -FilePath "start-mcp-remote.ps1" -Encoding UTF8
    Write-Host "✓ 启动脚本已创建: start-mcp-remote.ps1" -ForegroundColor Green
    
    # 创建Cursor远程配置
    $cursorRemoteConfig = @{
        mcpServers = @{
            mysql_remote = @{
                url = "http://localhost:$Port/mcp"
                type = "streamableHttp"
                headers = @{
                    Authorization = "Bearer $SecretKey"
                }
            }
        }
    }
    
    $cursorRemoteConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath ".cursor\mcp-remote.json" -Encoding UTF8
    Write-Host "✓ Cursor远程配置已创建: .cursor\mcp-remote.json" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "安装完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "配置信息:" -ForegroundColor Yellow
    Write-Host "  服务端口: $Port" -ForegroundColor White
    Write-Host "  认证密钥: $SecretKey" -ForegroundColor White
    Write-Host "  服务URL: http://localhost:$Port/mcp" -ForegroundColor White
    Write-Host ""
}

if ($Start) {
    Write-Host "启动远程服务..." -ForegroundColor Yellow
    
    if (-not (Test-Path "start-mcp-remote.ps1")) {
        Write-Host "✗ 启动脚本不存在，请先运行 -Install" -ForegroundColor Red
        exit 1
    }
    
    # 检查端口是否被占用
    $portCheck = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portCheck) {
        Write-Host "✗ 端口 $Port 已被占用" -ForegroundColor Red
        Write-Host "占用进程: $($portCheck.OwningProcess)" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "正在启动服务..." -ForegroundColor Cyan
    Start-Process PowerShell -ArgumentList "-File", "start-mcp-remote.ps1" -WindowStyle Normal
    
    # 等待服务启动
    Start-Sleep -Seconds 3
    
    # 检查服务是否启动成功
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✓ 服务启动成功！" -ForegroundColor Green
            Write-Host "  服务地址: http://localhost:$Port/mcp" -ForegroundColor Cyan
            
            # 测试HTTP连接
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$Port/mcp" -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
                Write-Host "✓ HTTP服务响应正常" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠ HTTP服务可能需要POST请求" -ForegroundColor Yellow
            }
        } else {
            Write-Host "✗ 服务启动失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ 无法检查服务状态: $($_.Exception.Message)" -ForegroundColor Red
    }
}

if ($Stop) {
    Write-Host "停止远程服务..." -ForegroundColor Yellow
    
    # 查找并停止相关进程
    $processes = Get-Process | Where-Object { $_.ProcessName -eq "node" -and $_.CommandLine -like "*mcp-server-mysql*" }
    
    if ($processes) {
        foreach ($process in $processes) {
            Write-Host "停止进程: $($process.Id)" -ForegroundColor Cyan
            Stop-Process -Id $process.Id -Force
        }
        Write-Host "✓ 服务已停止" -ForegroundColor Green
    } else {
        Write-Host "⚠ 未找到运行中的服务" -ForegroundColor Yellow
    }
}

if ($Status) {
    Write-Host "检查服务状态..." -ForegroundColor Yellow
    
    # 检查端口
    $portCheck = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portCheck) {
        Write-Host "✓ 端口 $Port 正在监听" -ForegroundColor Green
        Write-Host "  进程ID: $($portCheck.OwningProcess)" -ForegroundColor Cyan
        
        # 尝试HTTP请求
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$Port/mcp" -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
            Write-Host "✓ HTTP服务响应正常" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠ HTTP服务无响应或需要特定请求格式" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ 端口 $Port 未在监听" -ForegroundColor Red
    }
    
    # 检查配置文件
    if (Test-Path "mcp-remote.env") {
        Write-Host "✓ 配置文件存在" -ForegroundColor Green
    } else {
        Write-Host "✗ 配置文件不存在" -ForegroundColor Red
    }
    
    if (Test-Path ".cursor\mcp-remote.json") {
        Write-Host "✓ Cursor远程配置存在" -ForegroundColor Green
    } else {
        Write-Host "✗ Cursor远程配置不存在" -ForegroundColor Red
    }
}

if (-not ($Install -or $Start -or $Stop -or $Status)) {
    Write-Host "请指定操作参数，使用 -Help 查看帮助" -ForegroundColor Yellow
}
