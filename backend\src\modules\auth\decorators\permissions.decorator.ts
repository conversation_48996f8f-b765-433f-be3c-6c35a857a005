import { SetMetadata } from '@nestjs/common';
import { PERMISSIONS_KEY } from '../guards/permission.guard';

/**
 * 权限装饰器
 * 
 * @description 用于在控制器方法或类上设置权限要求
 * @decorator RequirePermissions
 * <AUTHOR> Team
 * @since 2024-12-19
 * 
 * @example
 * ```typescript
 * @RequirePermissions('documents.read', 'documents.create')
 * @Get('documents')
 * async getDocuments() {
 *   // 需要 documents.read 或 documents.create 权限才能访问
 * }
 * 
 * @RequirePermissions('users.manage')
 * @Controller('admin')
 * export class AdminController {
 *   // 该控制器下的所有方法都需要 users.manage 权限
 * }
 * ```
 */
export const RequirePermissions = (...permissions: string[]) => SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * 需要超级管理员权限的装饰器
 * 
 * @description 用于标记需要超级管理员权限的方法或类
 * @decorator RequireSuperAdmin
 * <AUTHOR> Team
 * @since 2024-12-19
 * 
 * @example
 * ```typescript
 * @RequireSuperAdmin()
 * @Delete('system/reset')
 * async resetSystem() {
 *   // 需要超级管理员权限才能访问
 * }
 * ```
 */
export const RequireSuperAdmin = () => SetMetadata(PERMISSIONS_KEY, ['*']);

/**
 * 权限检查模式枚举
 */
export enum PermissionCheckMode {
  /** 需要任意一个权限（默认） */
  ANY = 'any',
  /** 需要所有权限 */
  ALL = 'all',
}

/**
 * 权限检查模式装饰器元数据键
 */
export const PERMISSION_CHECK_MODE_KEY = 'permission_check_mode';

/**
 * 设置权限检查模式装饰器
 * 
 * @description 用于设置权限检查是需要所有权限还是任意权限
 * @decorator PermissionCheckMode
 * <AUTHOR> Team
 * @since 2024-12-19
 * 
 * @example
 * ```typescript
 * @RequirePermissions('documents.read', 'documents.write')
 * @PermissionCheckMode(PermissionCheckMode.ALL)
 * @Put('documents/:id')
 * async updateDocument() {
 *   // 需要同时拥有 documents.read 和 documents.write 权限
 * }
 * ```
 */
export const SetPermissionCheckMode = (mode: PermissionCheckMode) => 
  SetMetadata(PERMISSION_CHECK_MODE_KEY, mode);

/**
 * 权限组合装饰器 - 文档管理
 */
export const DocumentPermissions = {
  /** 文档读取权限 */
  Read: () => RequirePermissions('documents.read'),
  /** 文档创建权限 */
  Create: () => RequirePermissions('documents.create'),
  /** 文档更新权限 */
  Update: () => RequirePermissions('documents.update'),
  /** 文档删除权限 */
  Delete: () => RequirePermissions('documents.delete'),
  /** 文档管理权限（所有操作） */
  Manage: () => RequirePermissions('documents.manage'),
  /** 文档读写权限 */
  ReadWrite: () => RequirePermissions('documents.read', 'documents.create', 'documents.update'),
};

/**
 * 权限组合装饰器 - 用户管理
 */
export const UserPermissions = {
  /** 用户读取权限 */
  Read: () => RequirePermissions('users.read'),
  /** 用户创建权限 */
  Create: () => RequirePermissions('users.create'),
  /** 用户更新权限 */
  Update: () => RequirePermissions('users.update'),
  /** 用户删除权限 */
  Delete: () => RequirePermissions('users.delete'),
  /** 用户管理权限（所有操作） */
  Manage: () => RequirePermissions('users.manage'),
  /** 用户读写权限 */
  ReadWrite: () => RequirePermissions('users.read', 'users.create', 'users.update'),
};

/**
 * 权限组合装饰器 - 角色管理
 */
export const RolePermissions = {
  /** 角色读取权限 */
  Read: () => RequirePermissions('roles.read'),
  /** 角色创建权限 */
  Create: () => RequirePermissions('roles.create'),
  /** 角色更新权限 */
  Update: () => RequirePermissions('roles.update'),
  /** 角色删除权限 */
  Delete: () => RequirePermissions('roles.delete'),
  /** 角色管理权限（所有操作） */
  Manage: () => RequirePermissions('roles.manage'),
  /** 角色权限分配 */
  AssignPermissions: () => RequirePermissions('roles.assign_permissions'),
};

/**
 * 权限组合装饰器 - 权限管理
 */
export const PermissionPermissions = {
  /** 权限读取权限 */
  Read: () => RequirePermissions('permissions.read'),
  /** 权限创建权限 */
  Create: () => RequirePermissions('permissions.create'),
  /** 权限更新权限 */
  Update: () => RequirePermissions('permissions.update'),
  /** 权限删除权限 */
  Delete: () => RequirePermissions('permissions.delete'),
  /** 权限管理权限（所有操作） */
  Manage: () => RequirePermissions('permissions.manage'),
  /** 权限检查权限 */
  Check: () => RequirePermissions('permissions.check'),
};

/**
 * 权限组合装饰器 - 模板管理
 */
export const TemplatePermissions = {
  /** 模板读取权限 */
  Read: () => RequirePermissions('templates.read'),
  /** 模板创建权限 */
  Create: () => RequirePermissions('templates.create'),
  /** 模板更新权限 */
  Update: () => RequirePermissions('templates.update'),
  /** 模板删除权限 */
  Delete: () => RequirePermissions('templates.delete'),
  /** 模板管理权限（所有操作） */
  Manage: () => RequirePermissions('templates.manage'),
};

/**
 * 权限组合装饰器 - 系统配置
 */
export const ConfigPermissions = {
  /** 配置读取权限 */
  Read: () => RequirePermissions('config.read'),
  /** 配置更新权限 */
  Update: () => RequirePermissions('config.update'),
  /** 配置管理权限（所有操作） */
  Manage: () => RequirePermissions('config.manage'),
}; 