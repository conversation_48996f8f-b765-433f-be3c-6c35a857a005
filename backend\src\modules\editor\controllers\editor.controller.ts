import { 
  Controller, 
  Get, 
  Post, 
  Param, 
  Query, 
  Body, 
  HttpCode, 
  HttpStatus,
  ParseUUIDPipe,
  Logger,
  HttpException
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiParam, 
  ApiQuery, 
  ApiResponse, 
  ApiBody 
} from '@nestjs/swagger';
import { EditorService } from '../services/editor.service';
import { 
  CallbackDto, 
  CallbackResponseDto,
  SaveStatusResponseDto,
  ForceSaveDto,
  ForceSaveResponseDto,
  EditorConfigQueryDto
} from '../dto/index';

/**
 * OnlyOffice编辑器控制器
 * 
 * @description 处理OnlyOffice文档编辑相关的HTTP请求，包括：
 * - 编辑器配置获取
 * - OnlyOffice回调处理
 * - 文档保存状态检查
 * - 强制保存功能
 * 
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */
@ApiTags('OnlyOffice编辑器')
@Controller('editor')
export class EditorController {
  private readonly logger = new Logger(EditorController.name);

  constructor(private readonly editorService: EditorService) {}

  /**
   * 获取编辑器配置
   * @param documentId 文档ID
   * @param query 查询参数
   * @returns Promise<EditorConfig>
   */
  @Get(':documentId/config')
  @ApiOperation({ 
    summary: '获取编辑器配置', 
    description: '获取指定文档的OnlyOffice编辑器配置，支持配置模板' 
  })
  @ApiParam({ 
    name: 'documentId', 
    description: '文档ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiQuery({ 
    name: 'template', 
    required: false, 
    description: '配置模板ID',
    example: 'default-edit'
  })
  @ApiQuery({ 
    name: 'mode', 
    required: false, 
    description: '编辑模式 (edit/view)',
    example: 'edit'
  })
  @ApiQuery({ 
    name: 'lang', 
    required: false, 
    description: '语言设置',
    example: 'zh'
  })
  @ApiResponse({ 
    status: 200, 
    description: '编辑器配置获取成功',
    type: 'object'
  })
  @ApiResponse({ 
    status: 404, 
    description: '文档不存在'
  })
  async getEditorConfig(
    @Param('documentId') documentId: string,
    @Query() query: EditorConfigQueryDto,
  ) {
    try {
      console.log('🔄 [EditorController] getEditorConfig 开始获取编辑器配置')
      console.log('📋 [EditorController] 文档ID:', documentId)
      console.log('🔧 [EditorController] 查询参数:', query)
      console.log('🔧 [EditorController] 配置模板ID:', query.template)
      
      const config = await this.editorService.getEditorConfig(documentId, query as Record<string, unknown>)
      
      console.log('📥 [EditorController] 生成的编辑器配置:')
      console.log('  - documentType:', config.documentType)
      console.log('  - mode:', config.editorConfig?.mode)
      console.log('  - lang:', config.editorConfig?.lang)
      console.log('  - customization:', JSON.stringify(config.editorConfig?.customization, null, 2))
      console.log('  - 完整配置:', JSON.stringify(config, null, 2))
      
      return {
        success: true,
        data: config,
        message: '编辑器配置获取成功',
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      console.error('❌ [EditorController] 获取编辑器配置失败:', error)
      throw new HttpException(
        `获取编辑器配置失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * 处理OnlyOffice回调
   */
  @Post('callback')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'OnlyOffice编辑器回调处理',
    description: '处理OnlyOffice编辑器的回调请求，包括文档保存、状态变更等操作'
  })
  @ApiParam({ 
    name: 'fileId', 
    required: false, 
    description: '文件ID（可选，可从回调数据中提取）' 
  })
  @ApiBody({ 
    description: 'OnlyOffice回调数据', 
    type: CallbackDto 
  })
  @ApiResponse({ 
    status: 200, 
    description: '回调处理成功', 
    type: CallbackResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: '回调数据格式错误' 
  })
  @ApiResponse({ 
    status: 500, 
    description: '回调处理失败' 
  })
  async handleCallback(
    @Body() callbackData: CallbackDto,
    @Param('fileId') fileId?: string,
  ): Promise<CallbackResponseDto> {
    this.logger.log(`处理OnlyOffice回调: status=${callbackData.status}, key=${callbackData.key}`);
    
    const result = await this.editorService.handleCallback(callbackData, fileId);
    return {
      error: result.error,
      message: result.message,
    };
  }

  /**
   * 检查文档保存状态
   */
  @Get(':id/status')
  @ApiOperation({ 
    summary: '检查文档保存状态',
    description: '检查指定文档的当前保存状态，包括最后修改时间、版本号等信息'
  })
  @ApiParam({ 
    name: 'id', 
    description: '文档ID（UUID格式）', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @ApiResponse({ 
    status: 200, 
    description: '成功获取保存状态', 
    type: SaveStatusResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: '无效的文件ID格式' 
  })
  @ApiResponse({ 
    status: 404, 
    description: '找不到对应的文档记录' 
  })
  async checkSaveStatus(
    @Param('id', ParseUUIDPipe) fileId: string,
  ): Promise<SaveStatusResponseDto> {
    this.logger.log(`检查文档保存状态: ${fileId}`);
    return await this.editorService.checkSaveStatus(fileId);
  }

  /**
   * 强制保存文档
   */
  @Post(':id/force-save')
  @ApiOperation({ 
    summary: '强制保存文档',
    description: '向OnlyOffice服务器发送强制保存命令，立即保存当前编辑的文档'
  })
  @ApiParam({ 
    name: 'id', 
    description: '文档ID（UUID格式）', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @ApiBody({ 
    description: '强制保存参数', 
    type: ForceSaveDto 
  })
  @ApiResponse({ 
    status: 200, 
    description: '强制保存成功', 
    type: ForceSaveResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: '无效的请求参数' 
  })
  @ApiResponse({ 
    status: 404, 
    description: '找不到对应的文档' 
  })
  @ApiResponse({ 
    status: 500, 
    description: '强制保存失败' 
  })
  async forceSaveDocument(
    @Param('id', ParseUUIDPipe) fileId: string,
    @Body() forceSaveData: ForceSaveDto,
  ): Promise<ForceSaveResponseDto> {
    this.logger.log(`强制保存文档: ${fileId}`);
    console.log('📋 [EditorController] 收到强制保存请求:');
    console.log('  - 文档ID:', fileId);
    console.log('  - 请求数据:', JSON.stringify(forceSaveData, null, 2));
    return await this.editorService.forceSave(fileId, forceSaveData);
  }
} 