<template>
  <div class="documents-page">
    <a-page-header title="文档管理" sub-title="管理和编辑您的文档">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleUpload">
            <template #icon>
              <upload-outlined />
            </template>
            上传文档
          </a-button>
          <a-button @click="handleCreateFromTemplate">
            <template #icon>
              <plus-outlined />
            </template>
            从模板创建
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-wrapper">
      <!-- 搜索和筛选区域 -->
      <div class="search-section">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchQuery"
              placeholder="搜索文档名称、内容..."
              size="large"
              @search="handleSearch"
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="selectedType"
              placeholder="文档类型"
              size="large"
              allow-clear
              @change="handleTypeChange"
            >
              <a-select-option value="word"> Word文档 </a-select-option>
              <a-select-option value="excel"> Excel表格 </a-select-option>
              <a-select-option value="powerpoint"> PowerPoint </a-select-option>
              <a-select-option value="pdf"> PDF文件 </a-select-option>
              <a-select-option value="image"> 图片文件 </a-select-option>
              <a-select-option value="other"> 其他文件 </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="selectedStatus"
              placeholder="状态"
              size="large"
              allow-clear
              @change="handleStatusChange"
            >
              <a-select-option value="draft"> 草稿 </a-select-option>
              <a-select-option value="published"> 已发布 </a-select-option>
              <a-select-option value="archived"> 已归档 </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="8">
            <a-space>
              <a-button @click="handleRefresh">
                <template #icon>
                  <reload-outlined />
                </template>
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleBatchDownload">
                      <download-outlined />
                      批量下载
                    </a-menu-item>
                    <a-menu-item class="danger-item" @click="handleBatchDelete">
                      <delete-outlined />
                      批量删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  批量操作
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 文档列表 -->
      <a-table
        :key="tableKey"
        :columns="columns"
        :data-source="documents"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="document-name" @click="handleView(record)">
              <a-avatar shape="square" :style="{ backgroundColor: getTypeColor(record.type) }">
                <template #icon>
                  <component :is="getTypeIcon(record.type)" />
                </template>
              </a-avatar>
              <div class="name-info">
                <div class="name">
                  {{ record.title }}
                </div>
                <div class="path">
                  {{ record.path }}
                </div>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'version'">
            <a-space>
              <a-tag color="blue">v{{ record.version || 1 }}</a-tag>
              <a-button
                type="text"
                size="small"
                @click="handleViewVersions(record)"
                title="查看版本历史"
              >
                <template #icon>
                  <clock-circle-outlined />
                </template>
              </a-button>
            </a-space>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              <component :is="getStatusIcon(record.status)" />
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'size'">
            {{ formatFileSize(record.size) }}
          </template>

          <template v-else-if="column.key === 'updateTime'">
            <a-tooltip :title="formatFullDate(record.updatedAt)">
              {{ formatRelativeTime(record.updatedAt) }}
            </a-tooltip>
          </template>

          <template v-else-if="column.key === 'actions'">
            <DocumentActions
              :document-id="record.id"
              :document-name="record.title"
              @refresh-list="handleRefresh"
            />
          </template>
        </template>
      </a-table>
    </div>

    <!-- 从模板创建文档对话框 -->
    <a-modal
      v-model:open="templateModalVisible"
      title="从模板创建文档"
      width="800px"
      :confirm-loading="templateLoading"
      @ok="handleConfirmCreateDocument"
      @cancel="handleCancelCreateDocument"
    >
      <div class="template-selection-container">
        <!-- 模板列表 -->
        <div class="template-section">
          <h4>选择模板</h4>
          <a-spin :spinning="templateLoading">
            <div v-if="documentTemplates.length === 0" class="empty-templates">
              <a-empty description="暂无可用模板" />
            </div>
            <div v-else class="template-grid">
              <div
                v-for="template in documentTemplates"
                :key="template.id"
                class="template-card"
                :class="{ selected: selectedTemplate?.id === template.id }"
                @click="handleTemplateSelect(template)"
              >
                <div class="template-icon">
                  <file-text-outlined v-if="template.type === 'document'" />
                  <table-outlined v-else-if="template.type === 'spreadsheet'" />
                  <bar-chart-outlined v-else-if="template.type === 'presentation'" />
                  <file-outlined v-else />
                </div>
                <div class="template-info">
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-description">{{ template.description || '无描述' }}</div>
                  <div class="template-meta">
                    <span class="template-type">{{ getTemplateTypeText(template.type) }}</span>
                    <span class="template-size">{{ formatFileSize(template.size || 0) }}</span>
                  </div>
                </div>
                <div v-if="selectedTemplate?.id === template.id" class="template-selected-icon">
                  <check-circle-filled />
                </div>
              </div>
            </div>
          </a-spin>
        </div>

        <!-- 文档信息 -->
        <div v-if="selectedTemplate" class="document-info-section">
          <h4>新文档信息</h4>
          <a-form layout="vertical">
            <a-form-item label="文档名称" required>
              <a-input
                v-model:value="newDocumentName"
                placeholder="请输入文档名称"
                :maxlength="100"
                show-count
              />
            </a-form-item>
            <a-form-item label="基于模板">
              <a-input :value="selectedTemplate.name" readonly />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>

    <!-- 上传文档弹窗 -->
    <a-modal
      v-model:open="uploadModalVisible"
      title="上传文档"
      width="600px"
      @ok="handleUploadSubmit"
      @cancel="handleUploadCancel"
    >
      <a-form :model="uploadForm" layout="vertical">
        <a-form-item label="选择文件" required>
          <a-upload-dragger
            v-model:file-list="uploadForm.fileList"
            :multiple="true"
            :before-upload="() => false"
            accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
            @change="handleFileChange"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持单个或批量上传。支持 Word、Excel、PowerPoint、PDF 格式
            </p>
          </a-upload-dragger>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 重命名弹窗 -->
    <a-modal
      v-model:open="renameModalVisible"
      title="重命名文档"
      @ok="handleRenameSubmit"
      @cancel="handleRenameCancel"
    >
      <a-form :model="renameForm" layout="vertical">
        <a-form-item label="文档名称" required>
          <a-input v-model:value="renameForm.name" placeholder="请输入新的文档名称" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 版本历史弹窗 -->
    <a-modal
      v-model:open="versionModalVisible"
      :title="`版本历史 - ${selectedDocument?.title || ''}`"
      width="800px"
      :footer="null"
      @cancel="versionModalVisible = false"
    >
      <a-spin :spinning="loadingVersions">
        <div v-if="documentVersions.length === 0 && !loadingVersions" class="empty-versions">
          <a-empty description="暂无版本历史" />
        </div>
        <div v-else class="versions-list">
          <div v-for="version in documentVersions" :key="version.id" class="version-item">
            <div class="version-header">
              <div class="version-info">
                <a-tag color="blue" class="version-tag">v{{ version.version }}</a-tag>
                <span class="version-title">
                  {{ selectedDocument?.title }} (版本 {{ version.version }})
                </span>
              </div>
              <div class="version-actions">
                <a-button
                  type="text"
                  size="small"
                  @click="handleDownloadVersion(version)"
                  title="下载此版本"
                >
                  <template #icon>
                    <download-outlined />
                  </template>
                  下载
                </a-button>
              </div>
            </div>
            <div class="version-details">
              <div class="version-meta">
                <span class="meta-item">
                  <strong>修改时间:</strong> {{ formatFullDate(version.modifiedAt) }}
                </span>
                <span class="meta-item">
                  <strong>修改者:</strong> {{ version.modifiedBy || '系统' }}
                </span>
                <span class="meta-item">
                  <strong>文件大小:</strong> {{ formatFileSize(version.fileSize || 0) }}
                </span>
              </div>
              <div class="version-comment" v-if="version.comment">
                <strong>说明:</strong> {{ version.comment }}
              </div>
              <div class="version-hash" v-if="version.fileHash">
                <strong>文件哈希:</strong>
                <code class="hash-code">{{ version.fileHash.substring(0, 16) }}...</code>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import type { UploadFile } from 'ant-design-vue/es/upload/interface'
import {
  UploadOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  InboxOutlined,
  FileTextOutlined,
  TableOutlined,
  BarChartOutlined,
  CheckCircleFilled,
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { DocumentsApiService } from '@/services'
import { TemplatesApiService } from '@/services/templates.api'
import type { DocumentInfo, DocumentVersion, TemplateInfo } from '@/types/api.types'
import type { Component } from 'vue'
import DocumentActions from '@/components/DocumentActions.vue'

dayjs.extend(relativeTime)

const router = useRouter()
const loading = ref(false)
const searchQuery = ref('')
const selectedType = ref<string>()
const selectedStatus = ref<string>()
const uploadModalVisible = ref(false)
const renameModalVisible = ref(false)
const versionModalVisible = ref(false)
const templateModalVisible = ref(false)
const selectedRowKeys = ref<string[]>([])
const selectedDocument = ref<DocumentInfo | null>(null)
const documentVersions = ref<DocumentVersion[]>([])
const loadingVersions = ref(false)
const templateLoading = ref(false)

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
})

// 表单数据
const uploadForm = ref<{
  fileList: UploadFile[]
}>({
  fileList: [],
})

const renameForm = ref<{
  name: string
  id: string
}>({
  name: '',
  id: '',
})

// 文档数据
const documents = ref<DocumentInfo[]>([])
const tableKey = ref(0) // 用于强制表格重新渲染

// 模板数据
const documentTemplates = ref<TemplateInfo[]>([])
const selectedTemplate = ref<TemplateInfo | null>(null)
const newDocumentName = ref('')

// 获取文档列表
const fetchDocuments = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.current,
      limit: pagination.value.pageSize,
      search: searchQuery.value || undefined,
      extension: selectedType.value,
      // 注意：后端API暂不支持status过滤
    }

    console.log('📄 获取文档列表，参数:', params)
    const response = await DocumentsApiService.getDocuments(params)

    console.log('📄 文档API响应:', response)

    // 处理新的响应格式：{ data: [], total: number, pagination?: {} }
    console.log('🔍 [Debug] 响应数据结构分析:', {
      hasResponse: !!response,
      hasData: !!response.data,
      isDataArray: Array.isArray(response.data),
      total: response.total,
      pagination: response.pagination,
    })

    if (response && response.data && Array.isArray(response.data)) {
      console.log('✅ [分支判断] 进入新格式分支')
      // 新的响应格式：{ data: [], total: number, pagination?: {} }
      documents.value = (response.data as unknown as Record<string, unknown>[]).map(
        (doc: Record<string, unknown>) => {
          const fileName = String(doc.name || doc.title || '')
          const documentType = getDocumentTypeFromFileName(fileName)

          return {
            id: String(doc.id || ''),
            title: fileName, // 后端是name字段
            type: documentType, // 根据文件名判断文档类型
            size: Number(doc.size) || 0,
            status: 'published' as const, // 后端暂无status字段，默认为published
            createdAt: String(doc.createdAt || ''),
            updatedAt: String(doc.lastModified || doc.updatedAt || ''),
            createdBy: String(doc.createdBy || ''),
            lastEditor: String(doc.modifiedBy || doc.createdBy || ''),
            url: String(doc.url || ''),
            isPublic: true, // 默认公开
            category: '', // 后端暂无category字段
            tags: [], // 后端暂无tags字段
          } as DocumentInfo
        }
      )

      // 使用后端返回的正确总数
      const correctTotal = response.total || response.data.length

      console.log('🔍 [Debug] 设置总数为:', correctTotal, '类型:', typeof correctTotal)

      // 更新分页信息，确保total不被覆盖
      if (response.pagination) {
        console.log('🔍 [Debug] 更新分页信息:', response.pagination)
        pagination.value = {
          current: response.pagination.current,
          pageSize: response.pagination.pageSize,
          total: correctTotal, // 确保使用正确的总数
        }
      } else {
        // 如果没有pagination字段，只更新total
        pagination.value.total = correctTotal
      }

      // 强制表格重新渲染
      tableKey.value++

      console.log('🔍 [Debug] 最终分页状态:', pagination.value)
    } else {
      console.log('❌ [分支判断] 进入未知格式分支')
      console.warn('⚠️ 意外的文档API响应格式:', response)
      documents.value = []
      pagination.value.total = 0
    }

    console.log(`✅ 成功获取 ${documents.value.length} 个文档，总计: ${pagination.value.total}`)
    console.log(
      `📊 分页信息: 当前页=${pagination.value.current}, 每页=${pagination.value.pageSize}, 总计=${pagination.value.total}`
    )
  } catch (error) {
    console.error('❌ 获取文档列表失败:', error)
    message.error('获取文档列表失败')
    documents.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '文档名称',
    key: 'name',
    width: '32%',
    sorter: true,
  },
  {
    title: '类型',
    key: 'type',
    width: '8%',
    filters: [
      { text: 'Word文档', value: 'word' },
      { text: 'Excel表格', value: 'excel' },
      { text: 'PowerPoint', value: 'powerpoint' },
      { text: 'PDF文件', value: 'pdf' },
      { text: '图片文件', value: 'image' },
      { text: '其他文件', value: 'other' },
    ],
  },
  {
    title: '版本',
    key: 'version',
    width: '8%',
    sorter: true,
  },
  {
    title: '状态',
    key: 'status',
    width: '10%',
    filters: [
      { text: '草稿', value: 'draft' },
      { text: '已发布', value: 'published' },
      { text: '已归档', value: 'archived' },
    ],
  },
  {
    title: '大小',
    key: 'size',
    width: '8%',
    sorter: true,
  },
  {
    title: '创建者',
    dataIndex: 'createdBy',
    key: 'creator',
    width: '8%',
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: '14%',
    sorter: true,
  },
  {
    title: '操作',
    key: 'actions',
    width: '12%',
  },
]

// 表格行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (newSelectedRowKeys: string[]) => {
    selectedRowKeys.value = newSelectedRowKeys
  },
}

// 注意：现在使用后端分页，不需要前端过滤
// 搜索和筛选通过API参数传递给后端处理

// 计算属性：分页配置
const paginationConfig = computed(() => {
  const config = {
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    total: pagination.value.total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => {
      // 修复范围计算：基于实际的分页设置而不是数据源长度
      const currentPage = pagination.value.current
      const pageSize = pagination.value.pageSize
      const start = (currentPage - 1) * pageSize + 1
      const end = Math.min(currentPage * pageSize, total)

      console.log('📈 分页显示计算:', {
        total,
        originalRange: range,
        correctedRange: [start, end],
        currentPage,
        pageSize,
        paginationValueTotal: pagination.value.total,
      })

      return `第 ${start}-${end} 条，共 ${total} 个文档`
    },
    pageSizeOptions: ['10', '20', '50', '100'],
    showLessItems: false,
  }

  console.log('🔧 分页配置生成:', config)
  return config
})

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatRelativeTime = (dateTime: string): string => {
  const now = new Date()
  const time = new Date(dateTime)
  const diff = now.getTime() - time.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  const days = Math.floor(hours / 24)
  if (days < 30) return `${days}天前`
  return time.toLocaleDateString()
}

const formatFullDate = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString()
}

/**
 * 根据文件名获取文档类型
 * @param fileName 文件名（包含扩展名）
 * @returns 文档类型: 'word' | 'excel' | 'powerpoint' | 'pdf' | 'image' | 'other'
 */
const getDocumentTypeFromFileName = (
  fileName: string
): 'word' | 'excel' | 'powerpoint' | 'pdf' | 'image' | 'other' => {
  if (!fileName) return 'word'

  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  // Word文档
  if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(extension)) {
    return 'word'
  }

  // Excel表格
  if (['xls', 'xlsx', 'ods', 'csv'].includes(extension)) {
    return 'excel'
  }

  // PowerPoint演示文稿
  if (['ppt', 'pptx', 'odp'].includes(extension)) {
    return 'powerpoint'
  }

  // PDF文件
  if (['pdf'].includes(extension)) {
    return 'pdf'
  }

  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
    return 'image'
  }

  // 默认为其他类型
  return 'other'
}

const getTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    word: 'Word文档',
    excel: 'Excel表格',
    powerpoint: 'PowerPoint',
    pdf: 'PDF文件',
    image: '图片文件',
    other: '其他文件',
  }
  return typeMap[type] || type
}

const getTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    word: '#1890ff',
    excel: '#52c41a',
    powerpoint: '#fa8c16',
    pdf: '#f5222d',
    image: '#722ed1',
    other: '#d9d9d9',
  }
  return colorMap[type] || '#d9d9d9'
}

const getTypeIcon = (type: string): Component => {
  const iconMap: Record<string, Component> = {
    word: FileWordOutlined,
    excel: FileExcelOutlined,
    powerpoint: FilePptOutlined,
    pdf: FilePdfOutlined,
    image: FileImageOutlined,
    other: FileOutlined,
  }
  return iconMap[type] || FileOutlined
}

const getStatusName = (status: string): string => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档',
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'warning',
  }
  return colorMap[status] || 'default'
}

const getStatusIcon = (status: string): Component => {
  const iconMap: Record<string, Component> = {
    draft: EditOutlined,
    published: CheckCircleOutlined,
    archived: ClockCircleOutlined,
  }
  return iconMap[status] || ClockCircleOutlined
}

// 事件处理
const handleSearch = () => {
  console.log('🔍 搜索文档:', searchQuery.value)
  pagination.value.current = 1
  fetchDocuments()
}

const handleTypeChange = () => {
  console.log('📂 类型筛选:', selectedType.value)
  pagination.value.current = 1
  fetchDocuments()
}

const handleStatusChange = () => {
  console.log('📊 状态筛选:', selectedStatus.value)
  pagination.value.current = 1
  fetchDocuments()
}

const handleRefresh = () => {
  pagination.value.current = 1
  fetchDocuments()
  message.success('刷新成功')
}

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters: Record<string, unknown>,
  sorter: Record<string, unknown>
) => {
  console.log('🔄 分页变更:', pag)
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  console.log('📊 更新后的分页状态:', pagination.value)
  fetchDocuments() // 重新获取数据
  console.log('Table filters:', filters)
  console.log('Table sorter:', sorter)
}

const handleUpload = () => {
  uploadModalVisible.value = true
}

const handleCreateFromTemplate = async () => {
  templateModalVisible.value = true
  await fetchDocumentTemplates()
}

// 获取文档模板列表
const fetchDocumentTemplates = async () => {
  try {
    templateLoading.value = true
    const response = await TemplatesApiService.getDocumentTemplates({
      current: 1,
      pageSize: 50,
      category: '',
    })

    if (response && response.list) {
      documentTemplates.value = response.list
      console.log('📋 获取到模板列表:', documentTemplates.value.length, '个')
    }
  } catch (error) {
    console.error('❌ 获取模板列表失败:', error)
    message.error('获取模板列表失败')
  } finally {
    templateLoading.value = false
  }
}

// 处理模板选择
const handleTemplateSelect = (template: TemplateInfo) => {
  selectedTemplate.value = template
  newDocumentName.value = `基于${template.name}的新文档`
}

// 确认创建文档
const handleConfirmCreateDocument = async () => {
  if (!selectedTemplate.value || !newDocumentName.value.trim()) {
    message.warning('请选择模板并输入文档名称')
    return
  }

  try {
    templateLoading.value = true
    console.log('📄 基于模板创建文档:', {
      templateId: selectedTemplate.value.id,
      title: newDocumentName.value,
    })

    const response = await TemplatesApiService.createDocumentFromTemplate(
      selectedTemplate.value.id,
      {
        title: newDocumentName.value.trim(),
        description: `从模板"${selectedTemplate.value.name}"创建的文档`,
      }
    )

    console.log('✅ 编辑会话创建成功:', response)

    if (response.sessionId) {
      message.success('基于模板创建编辑会话成功，正在打开编辑器...')
      // 关闭对话框
      templateModalVisible.value = false
      selectedTemplate.value = null
      newDocumentName.value = ''

      // 不刷新文档列表，因为还没有正式创建文档

      // 暂时跳转到普通编辑器，传递模板会话信息
      // TODO: 后续需要创建专门的模板编辑器页面
      router.push(
        `/documents/editor/template-session/${response.sessionId}?templateId=${response.templateId}&documentName=${encodeURIComponent(response.documentName)}`
      )
    } else {
      message.error('创建编辑会话成功但无法获取会话ID')
    }
  } catch (error) {
    console.error('❌ 创建文档失败:', error)
    message.error('创建文档失败')
  } finally {
    templateLoading.value = false
  }
}

// 取消创建文档
const handleCancelCreateDocument = () => {
  templateModalVisible.value = false
  selectedTemplate.value = null
  newDocumentName.value = ''
}

const handleView = (record: DocumentInfo) => {
  router.push(`/documents/editor/${record.id}`)
}

const handleViewVersions = async (record: DocumentInfo) => {
  try {
    selectedDocument.value = record
    loadingVersions.value = true
    versionModalVisible.value = true

    console.log(`📋 获取文档版本历史: ${record.id}`)

    // 调用版本历史API
    const response = await fetch(`http://192.168.107.7:3000/api/documents/${record.id}/versions`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('access_token') || ''}`,
      },
    })

    if (!response.ok) {
      throw new Error(`获取版本历史失败: ${response.status}`)
    }

    const result = await response.json()

    if (result.success && Array.isArray(result.data)) {
      documentVersions.value = result.data
      console.log(`✅ 成功获取 ${result.data.length} 个版本`)
    } else {
      throw new Error(result.message || '版本数据格式错误')
    }
  } catch (error) {
    console.error('❌ 获取版本历史失败:', error)
    message.error('获取版本历史失败')
    documentVersions.value = []
  } finally {
    loadingVersions.value = false
  }
}

const handleDownloadVersion = async (version: DocumentVersion) => {
  try {
    if (!selectedDocument.value) return

    console.log(`📥 下载版本: ${selectedDocument.value.id} v${version.version}`)

    const url = `http://192.168.107.7:3000/api/documents/${selectedDocument.value.id}/versions/${version.version}`

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `${selectedDocument.value.title}_v${version.version}`
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success(`开始下载版本 v${version.version}`)
  } catch (error) {
    console.error('❌ 下载版本失败:', error)
    message.error('下载版本失败')
  }
}

const handleBatchDownload = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要下载的文档')
    return
  }
  message.info(`批量下载 ${selectedRowKeys.value.length} 个文档`)
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的文档')
    return
  }
  message.info(`批量删除 ${selectedRowKeys.value.length} 个文档`)
}

const handleFileChange = (info: { fileList: UploadFile[] }) => {
  uploadForm.value.fileList = info.fileList
}

const handleUploadSubmit = async () => {
  if (uploadForm.value.fileList.length === 0) {
    message.warning('请选择要上传的文件')
    return
  }

  const loadingMessage = message.loading('正在上传文件...', 0)

  try {
    // 使用FormData准备上传数据
    const formData = new FormData()

    // 遍历文件列表，添加到FormData
    uploadForm.value.fileList.forEach(file => {
      if (file.originFileObj) {
        formData.append('file', file.originFileObj)
      }
    })

    // 调用上传API
    const response = await fetch('/api/uploads', {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('access_token')}`,
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '上传失败')
    }

    const result = await response.json()
    console.log('上传成功:', result)

    loadingMessage()
    message.success('文档上传成功！')

    // 关闭对话框并重置表单
    uploadModalVisible.value = false
    uploadForm.value = {
      fileList: [],
    }

    // 刷新文档列表
    fetchDocuments()
  } catch (error) {
    loadingMessage()
    console.error('上传失败:', error)
    message.error(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

const handleUploadCancel = () => {
  uploadModalVisible.value = false
  uploadForm.value = {
    fileList: [],
  }
}

const handleRenameSubmit = () => {
  if (!renameForm.value.name.trim()) {
    message.warning('请输入文档名称')
    return
  }

  message.success('文档重命名成功！')
  renameModalVisible.value = false
}

const handleRenameCancel = () => {
  renameModalVisible.value = false
  renameForm.value = {
    name: '',
    id: '',
  }
}

// 工具函数
const getTemplateTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    document: 'Word文档',
    spreadsheet: 'Excel表格',
    presentation: 'PowerPoint演示文稿',
  }
  return typeMap[type] || '其他'
}

// 生命周期
onMounted(() => {
  fetchDocuments()
})
</script>

<style scoped>
.documents-page {
  padding: 0;
}

.content-wrapper {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}

.search-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.document-name {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.document-name:hover {
  color: #1890ff;
}

.name-info .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.name-info .path {
  font-size: 12px;
  color: #8c8c8c;
}

:deep(.danger-item) {
  color: #ff4d4f;
}

:deep(.danger-item:hover) {
  background-color: #fff2f0;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px 8px;
}

:deep(.ant-upload-drag) {
  padding: 40px 20px;
}

:deep(.ant-upload-drag-icon) {
  font-size: 48px;
  color: #1890ff;
}

:deep(.ant-upload-text) {
  font-size: 16px;
  color: #262626;
  margin: 16px 0 8px;
}

:deep(.ant-upload-hint) {
  color: #8c8c8c;
}

/* 版本历史弹窗样式 */
.empty-versions {
  padding: 40px 0;
  text-align: center;
}

.versions-list {
  max-height: 500px;
  overflow-y: auto;
}

.version-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.version-item:hover {
  background: #f0f8ff;
  border-color: #1890ff;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-tag {
  font-weight: 600;
}

.version-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.version-actions {
  display: flex;
  gap: 8px;
}

.version-details {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e8e8e8;
}

.version-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.meta-item {
  font-size: 13px;
  color: #595959;
}

.version-comment {
  margin-top: 8px;
  padding: 8px;
  background: #f6f8fa;
  border-radius: 4px;
  font-size: 13px;
  color: #262626;
}

.version-hash {
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.hash-code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #d63384;
}

/* 分页样式优化 */
:deep(.ant-pagination) {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  padding: 16px 0;
}

:deep(.ant-pagination-item) {
  min-width: 36px;
  height: 36px;
  line-height: 34px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.ant-pagination-item:hover) {
  border-color: #1890ff;
  color: #1890ff;
}

:deep(.ant-pagination-item-active) {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

:deep(.ant-pagination-item-active a) {
  color: #ffffff !important;
  font-weight: 600;
}

:deep(.ant-pagination-item a) {
  color: #262626;
  font-weight: 500;
}

:deep(.ant-pagination-prev),
:deep(.ant-pagination-next) {
  min-width: 36px;
  height: 36px;
  line-height: 34px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
}

:deep(.ant-pagination-prev:hover),
:deep(.ant-pagination-next:hover) {
  border-color: #1890ff;
  color: #1890ff;
}

:deep(.ant-pagination-options-size-changer) {
  margin-left: 16px;
}

:deep(.ant-pagination-total-text) {
  margin-right: 16px;
  color: #595959;
  font-weight: 500;
}

/* 模板选择对话框样式 */
.template-selection-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 600px;
}

.template-section h4,
.document-info-section h4 {
  margin: 0 0 16px 0;
  font-weight: 600;
  color: #262626;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}

.template-card {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
}

.template-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.template-card.selected {
  border-color: #1890ff;
  background: #f6ffed;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.template-icon {
  text-align: center;
  margin-bottom: 12px;
  font-size: 32px;
  color: #1890ff;
}

.template-info {
  text-align: center;
}

.template-name {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
  line-height: 1.3;
  height: 32px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #595959;
}

.template-selected-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #52c41a;
  font-size: 16px;
}

.empty-templates {
  text-align: center;
  padding: 40px 20px;
}

.document-info-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}
</style>
