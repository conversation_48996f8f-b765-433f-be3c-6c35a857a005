<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">📊 系统首页</h1>
      <p class="page-subtitle">OnlyOffice集成系统管理概览</p>
    </div>

    <!-- 统计卡片概览 -->
    <div class="status-overview">
      <div class="status-card">
        <div class="status-header">
          <div class="status-icon documents">📄</div>
          <div class="status-trend trend-up">↗ +15.3%</div>
        </div>
        <div class="status-value">{{ stats.documents || 1247 }}</div>
        <div class="status-label">文档总数</div>
        <div class="status-details">
          <span>本月新增: 156</span>
          <span>活跃: 892</span>
        </div>
      </div>

      <div class="status-card">
        <div class="status-header">
          <div class="status-icon users">👥</div>
          <div class="status-trend trend-up">↗ +8.7%</div>
        </div>
        <div class="status-value">{{ stats.users || 89 }}</div>
        <div class="status-label">活跃用户</div>
        <div class="status-details">
          <span>在线: 24</span>
          <span>本周: 67</span>
        </div>
      </div>

      <div class="status-card">
        <div class="status-header">
          <div class="status-icon storage">💾</div>
          <div class="status-trend trend-up">↗ +12.1%</div>
        </div>
        <div class="status-value">847GB</div>
        <div class="status-label">存储使用</div>
        <div class="status-details">
          <span>可用: 153GB</span>
          <span>使用率: 84.7%</span>
        </div>
      </div>

      <div class="status-card">
        <div class="status-header">
          <div class="status-icon system">❤️</div>
          <div class="status-trend trend-up">↗ +2.4%</div>
        </div>
        <div class="status-value">{{ stats.healthPercentage || 99.2 }}%</div>
        <div class="status-label">系统可用性</div>
        <div class="status-details">
          <span>响应: 180ms</span>
          <span>故障: 0.1%</span>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-grid">
      <!-- 左侧：快捷操作和最近文档 -->
      <div class="left-content">
        <!-- 快捷操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">🚀 快捷操作</h3>
            <span class="card-action">自定义</span>
          </div>
          <div class="quick-actions">
            <div class="actions-grid">
              <div class="action-item" @click="createDocument">
                <span class="action-icon">📝</span>
                <div class="action-label">创建文档</div>
                <div class="action-desc">新建Word、Excel、PPT</div>
              </div>
              <div class="action-item" @click="uploadFile">
                <span class="action-icon">📤</span>
                <div class="action-label">上传文件</div>
                <div class="action-desc">批量上传本地文档</div>
              </div>
              <div class="action-item" @click="manageTemplates">
                <span class="action-icon">📋</span>
                <div class="action-label">模板管理</div>
                <div class="action-desc">配置文档模板</div>
              </div>
              <div class="action-item" @click="manageUsers">
                <span class="action-icon">👥</span>
                <div class="action-label">用户管理</div>
                <div class="action-desc">管理系统用户</div>
              </div>
              <div class="action-item" @click="viewReports">
                <span class="action-icon">📊</span>
                <div class="action-label">数据报表</div>
                <div class="action-desc">查看统计报告</div>
              </div>
              <div class="action-item" @click="systemSettings">
                <span class="action-icon">⚙️</span>
                <div class="action-label">系统设置</div>
                <div class="action-desc">配置系统参数</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近文档 -->
        <div class="content-card recent-documents-card">
          <div class="card-header">
            <h3 class="card-title">📄 最近文档</h3>
            <span class="card-action" @click="viewAllDocuments">查看全部</span>
          </div>
          <div class="document-list">
            <div
              v-for="doc in recentDocuments"
              :key="doc.id"
              class="document-item"
              @click="openDocument(doc.id)"
            >
              <div class="doc-icon" :class="getDocTypeClass(doc.name)">
                {{ getDocTypeIcon(doc.name) }}
              </div>
              <div class="doc-info">
                <div class="doc-name">{{ doc.name }}</div>
                <div class="doc-meta">系统管理员 · {{ doc.updateTime }} · 2.4MB</div>
              </div>
              <div class="doc-actions">
                <button class="doc-btn btn-primary" @click.stop="editDocument(doc.id)">编辑</button>
                <button class="doc-btn btn-secondary" @click.stop="downloadDocument(doc.id)">
                  下载
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧侧边栏 -->
      <div class="sidebar-content">
        <!-- 用户信息 -->
        <div class="content-card">
          <div class="user-profile">
            <div class="profile-avatar">管</div>
            <div class="profile-name">系统管理员</div>
            <div class="profile-role">Administrator</div>
            <div class="profile-stats">
              <div class="stat-item">
                <div class="stat-number">{{ stats.documents || 156 }}</div>
                <div class="stat-label">我的文档</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">24</div>
                <div class="stat-label">协作项目</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ stats.users || 89 }}</div>
                <div class="stat-label">团队成员</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">🔧 系统状态</h3>
            <span class="card-action" @click="viewSystemDetails">详情</span>
          </div>
          <div class="system-info">
            <div v-for="healthCheck in healthChecks" :key="healthCheck.key" class="info-item">
              <span class="info-label">{{ healthCheck.title }}</span>
              <span class="info-value">
                <span class="status-dot" :class="getStatusDotClass(healthCheck.status)"></span>
                {{ getHealthStatusText(healthCheck.status) }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">系统负载</span>
              <span class="info-value">23%</span>
            </div>
            <div class="info-item">
              <span class="info-label">内存使用</span>
              <span class="info-value">67%</span>
            </div>
            <div class="info-item">
              <span class="info-label">磁盘空间</span>
              <span class="info-value">84%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { DocumentsApiService, UsersApiService, ConfigTemplateApiService } from '@/services'
import type { ConfigTemplate } from '@/services'
import { ApiService } from '@/services/api'

/**
 * API响应类型定义
 */
interface ApiResponse {
  total?: number
  length?: number
  data?: unknown[]
  [key: string]: unknown
}

const router = useRouter()
const loading = ref(true)
const isUnmounted = ref(false)

// 健康检查状态
enum HealthStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  WARNING = 'warning',
  LOADING = 'loading',
}

// 健康检查项接口
interface HealthCheck {
  key: string
  title: string
  url: string
  status: HealthStatus
  loading: boolean
  responseTime?: number
  message?: string
}

// 统计数据
const stats = ref({
  documents: 0,
  templates: 0,
  users: 0,
  healthPercentage: 0,
})

// 健康检查数据
const healthChecks = ref<HealthCheck[]>([
  {
    key: 'basic',
    title: '基础健康检查',
    url: '/health',
    status: HealthStatus.LOADING,
    loading: true,
  },
  {
    key: 'database',
    title: '数据库连接',
    url: '/health/db',
    status: HealthStatus.LOADING,
    loading: true,
  },
  {
    key: 'external',
    title: '外部服务',
    url: '/health/external',
    status: HealthStatus.LOADING,
    loading: true,
  },
  {
    key: 'info',
    title: '系统信息',
    url: '/health/info',
    status: HealthStatus.LOADING,
    loading: true,
  },
  {
    key: 'live',
    title: '存活检查',
    url: '/health/live',
    status: HealthStatus.LOADING,
    loading: true,
  },
  {
    key: 'ready',
    title: '就绪检查',
    url: '/health/ready',
    status: HealthStatus.LOADING,
    loading: true,
  },
])

// 最近文档
const recentDocuments = ref([
  {
    id: '1',
    name: '项目需求分析文档.docx',
    updateTime: '2小时前',
    color: '#1890ff',
  },
  {
    id: '2',
    name: 'Q4财务报表.xlsx',
    updateTime: '1天前',
    color: '#52c41a',
  },
  {
    id: '3',
    name: '产品发布会演示.pptx',
    updateTime: '3天前',
    color: '#fa8c16',
  },
  {
    id: '4',
    name: '用户操作手册.pdf',
    updateTime: '1周前',
    color: '#722ed1',
  },
])

// 执行单个健康检查
const checkSingleHealth = async (check: HealthCheck): Promise<void> => {
  if (isUnmounted.value) return

  const startTime = Date.now()
  try {
    const response: unknown = await ApiService.get(check.url)
    if (isUnmounted.value) return

    const endTime = Date.now()
    check.responseTime = endTime - startTime
    const typedResponse = response as { success?: boolean; message?: string }
    check.status = typedResponse.success !== false ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY
    check.message = typedResponse.message || '正常'
  } catch (error: unknown) {
    if (isUnmounted.value) return

    const endTime = Date.now()
    check.responseTime = endTime - startTime
    check.status = HealthStatus.UNHEALTHY
    check.message = error instanceof Error ? error.message : '连接失败'
  } finally {
    if (!isUnmounted.value) {
      check.loading = false
    }
  }
}

// 加载统计数据
const loadStats = async () => {
  if (isUnmounted.value) return

  loading.value = true
  try {
    // 并行获取各种统计数据
    const results = await Promise.allSettled([
      DocumentsApiService.getDocuments({ page: 1, limit: 1 }),
      UsersApiService.getUsers({ page: 1, pageSize: 1 }),
      ConfigTemplateApiService.getAllTemplates(),
    ])

    if (isUnmounted.value) return

    const [documentsResult, usersResult, configResult] = results

    // 处理各种API响应格式
    let documentsTotal = 1247 // 默认值
    let usersTotal = 89 // 默认值
    let templatesTotal = 0

    if (documentsResult.status === 'fulfilled') {
      const docResponse = documentsResult.value as unknown as ApiResponse
      documentsTotal = docResponse.total || docResponse.length || documentsTotal
    }

    if (usersResult.status === 'fulfilled') {
      const userResponse = usersResult.value as unknown as ApiResponse
      usersTotal = userResponse.total || userResponse.length || usersTotal
    }

    if (configResult.status === 'fulfilled') {
      const configResponse = configResult.value as ConfigTemplate[]
      templatesTotal = configResponse.length || 0
    }

    if (isUnmounted.value) return

    stats.value = {
      documents: documentsTotal,
      users: usersTotal,
      templates: templatesTotal,
      healthPercentage: 0,
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('加载统计数据失败:', error)
      stats.value = {
        documents: 1247,
        users: 89,
        templates: 0,
        healthPercentage: 0,
      }
    }
  } finally {
    if (!isUnmounted.value) {
      loading.value = false
    }
  }
}

// 加载健康检查状态
const loadHealthChecks = async () => {
  if (isUnmounted.value) return

  const promises = healthChecks.value.map(check => checkSingleHealth(check))
  await Promise.allSettled(promises)

  if (isUnmounted.value) return

  const healthyCount = healthChecks.value.filter(
    check => check.status === HealthStatus.HEALTHY
  ).length
  const totalCount = healthChecks.value.length
  stats.value.healthPercentage = Math.round((healthyCount / totalCount) * 100)
}

// 获取健康状态文本
const getHealthStatusText = (status: HealthStatus): string => {
  switch (status) {
    case HealthStatus.HEALTHY:
      return '正常'
    case HealthStatus.UNHEALTHY:
      return '异常'
    case HealthStatus.WARNING:
      return '警告'
    case HealthStatus.LOADING:
      return '检查中'
    default:
      return '未知'
  }
}

// 获取状态点样式类
const getStatusDotClass = (status: HealthStatus): string => {
  switch (status) {
    case HealthStatus.HEALTHY:
      return 'status-online'
    case HealthStatus.WARNING:
      return 'status-warning'
    case HealthStatus.UNHEALTHY:
      return 'status-offline'
    default:
      return 'status-offline'
  }
}

// 获取文档类型图标
const getDocTypeIcon = (name: string): string => {
  const extension = name.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'docx':
    case 'doc':
      return 'W'
    case 'xlsx':
    case 'xls':
      return 'E'
    case 'pptx':
    case 'ppt':
      return 'P'
    case 'pdf':
      return 'P'
    default:
      return 'D'
  }
}

// 获取文档类型样式类
const getDocTypeClass = (name: string): string => {
  const extension = name.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'docx':
    case 'doc':
      return 'doc-word'
    case 'xlsx':
    case 'xls':
      return 'doc-excel'
    case 'pptx':
    case 'ppt':
      return 'doc-ppt'
    case 'pdf':
      return 'doc-pdf'
    default:
      return 'doc-word'
  }
}

// 快捷操作方法
const createDocument = () => {
  message.info('创建文档功能开发中...')
}

const uploadFile = () => {
  message.info('上传文件功能开发中...')
}

const manageTemplates = () => {
  router.push('/templates')
}

const manageUsers = () => {
  router.push('/users')
}

const viewReports = () => {
  message.info('数据报表功能开发中...')
}

const systemSettings = () => {
  message.info('系统设置功能开发中...')
}

const viewAllDocuments = () => {
  router.push('/documents')
}

const viewSystemDetails = () => {
  message.info('系统详情功能开发中...')
}

// 文档操作方法
const openDocument = (id: string) => {
  message.info(`打开文档: ${id}`)
}

const editDocument = (id: string) => {
  message.info(`编辑文档: ${id}`)
}

const downloadDocument = (id: string) => {
  message.info(`下载文档: ${id}`)
}

// 生命周期
onMounted(async () => {
  await Promise.all([loadStats(), loadHealthChecks()])
})

onBeforeUnmount(() => {
  isUnmounted.value = true
})
</script>

<style scoped>
.dashboard {
  min-height: calc(100vh - 112px);
  background: var(--background-light);
}

/* 页面标题 */
.page-header {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  margin-bottom: 24px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 统计卡片概览 */
.status-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.status-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.status-icon.documents {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
}
.status-icon.users {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: #388e3c;
}
.status-icon.storage {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #f57c00;
}
.status-icon.system {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  color: #c2185b;
}

.status-trend {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.trend-up {
  background: #e8f5e8;
  color: #388e3c;
}

.trend-down {
  background: #ffebee;
  color: #d32f2f;
}

.status-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.status-label {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 12px;
}

.status-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-light);
}

/* 主要功能区域 */
.main-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.left-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 卡片组件 */
.content-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.content-card:hover {
  box-shadow: var(--card-shadow-hover);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f1f2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-action {
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.3s ease;
}

.card-action:hover {
  color: var(--primary-hover);
}

/* 快捷操作 */
.quick-actions {
  padding: 24px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.action-item {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.action-label {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.action-desc {
  color: var(--text-secondary);
  font-size: 12px;
}

/* 文档列表 */
.document-list {
  padding: 0 24px 24px;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f1f2;
  transition: all 0.3s ease;
  cursor: pointer;
}

.document-item:hover {
  background: #f8f9fa;
  margin: 0 -24px;
  padding: 16px 24px;
  border-radius: 8px;
}

.document-item:last-child {
  border-bottom: none;
}

.doc-icon {
  width: 44px;
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  font-weight: 600;
}

.doc-word {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}
.doc-excel {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}
.doc-ppt {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}
.doc-pdf {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

.doc-info {
  flex: 1;
}

.doc-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.doc-meta {
  font-size: 13px;
  color: var(--text-secondary);
}

.doc-actions {
  display: flex;
  gap: 8px;
}

.doc-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
  background: transparent;
}

.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  border-color: #e9ecef;
  color: var(--text-secondary);
}

.btn-secondary:hover {
  background: #f8f9fa;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 侧边栏内容 */
.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 用户信息 */
.user-profile {
  padding: 24px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.profile-avatar {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.profile-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.profile-role {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 系统信息 */
.system-info {
  padding: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f1f2;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-online {
  background: #28a745;
}
.status-warning {
  background: #ffc107;
}
.status-offline {
  background: #dc3545;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
  }

  .status-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .status-overview {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .profile-stats {
    grid-template-columns: 1fr;
  }

  .status-card,
  .content-card {
    padding: 16px;
  }

  .doc-actions {
    flex-direction: column;
  }
}
</style>
