# 权限管理控制器

> **版本**: v1.0  
> **更新时间**: 2024年12月19日  
> **作者**: OnlyOffice Team  

## 概述

权限管理控制器(`PermissionController`)是OnlyOffice集成系统中负责权限管理的核心组件，提供完整的权限CRUD操作、权限检查、统计分析等功能。

## 功能特性

### 🔧 基础权限管理
- ✅ 权限的创建、查询、更新、删除(CRUD)
- ✅ 基于代码的权限查找
- ✅ 权限状态切换(启用/禁用)
- ✅ 权限复制功能

### 📊 权限查询与统计
- ✅ 分页权限列表查询
- ✅ 多条件筛选（模块、操作类型、状态等）
- ✅ 权限统计信息（总数、按模块、按操作类型）
- ✅ 模块权限树结构

### 🔒 权限检查与验证
- ✅ 用户权限检查
- ✅ 当前用户权限获取
- ✅ 指定用户权限查询
- ✅ 权限上下文验证

### ⚡ 批量操作
- ✅ 批量启用/禁用权限
- ✅ 批量删除权限
- ✅ 操作结果统计

## API接口列表

### 权限基础操作

| 方法 | 路径 | 描述 | 权限要求 |
|------|------|------|----------|
| `GET` | `/permissions` | 获取权限列表 | `permissions.read` |
| `GET` | `/permissions/:id` | 获取权限详情 | `permissions.read` |
| `GET` | `/permissions/by-code/:code` | 根据代码获取权限 | `permissions.read` |
| `POST` | `/permissions` | 创建权限 | `permissions.create` |
| `PUT` | `/permissions/:id` | 更新权限 | `permissions.update` |
| `DELETE` | `/permissions/:id` | 删除权限 | `permissions.delete` |

### 权限统计与分析

| 方法 | 路径 | 描述 | 权限要求 |
|------|------|------|----------|
| `GET` | `/permissions/stats` | 获取权限统计 | `permissions.read` |
| `GET` | `/permissions/tree` | 获取模块权限树 | `permissions.read` |
| `GET` | `/permissions/modules/list` | 获取模块列表 | `permissions.read` |
| `GET` | `/permissions/actions/list` | 获取操作类型列表 | `permissions.read` |

### 权限检查

| 方法 | 路径 | 描述 | 权限要求 |
|------|------|------|----------|
| `POST` | `/permissions/check` | 检查用户权限 | `permissions.check` |
| `POST` | `/permissions/check/my` | 检查当前用户权限 | 无 |
| `GET` | `/permissions/user/my` | 获取当前用户权限 | 无 |
| `GET` | `/permissions/user/:userId` | 获取指定用户权限 | `permissions.check` |

### 批量操作

| 方法 | 路径 | 描述 | 权限要求 |
|------|------|------|----------|
| `POST` | `/permissions/batch` | 批量操作权限 | `permissions.update` |
| `POST` | `/permissions/:id/copy` | 复制权限 | `permissions.create` |
| `PUT` | `/permissions/:id/toggle-status` | 切换权限状态 | `permissions.update` |

## 使用示例

### 1. 获取权限列表

```bash
curl -X GET "http://localhost:3000/api/v1/permissions?page=1&pageSize=10&module=documents" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取权限列表成功",
  "data": {
    "data": [
      {
        "id": "perm-uuid-123",
        "code": "documents.read",
        "name": "查看文档",
        "description": "允许用户查看和浏览文档内容",
        "module": "documents",
        "resource": "document",
        "action": "read",
        "is_active": true,
        "sort_order": 10,
        "role_count": 3,
        "user_count": 15,
        "created_at": "2024-12-19T10:30:00Z",
        "updated_at": "2024-12-19T10:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10,
    "totalPages": 5
  }
}
```

### 2. 创建权限

```bash
curl -X POST "http://localhost:3000/api/v1/permissions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "documents.export",
    "name": "导出文档",
    "description": "允许用户导出文档到本地",
    "module": "documents",
    "resource": "document",
    "action": "export",
    "is_active": true,
    "sort_order": 50
  }'
```

### 3. 检查用户权限

```bash
curl -X POST "http://localhost:3000/api/v1/permissions/check" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-uuid-123",
    "permissions": ["documents.read", "documents.create"],
    "context": {
      "documentId": "doc-456"
    }
  }'
```

**响应示例:**
```json
{
  "success": true,
  "message": "权限检查完成",
  "data": {
    "results": {
      "documents.read": true,
      "documents.create": false
    },
    "has_all": false,
    "has_any": true,
    "missing_permissions": ["documents.create"]
  }
}
```

### 4. 批量操作权限

```bash
curl -X POST "http://localhost:3000/api/v1/permissions/batch" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "permissionIds": ["perm-uuid-1", "perm-uuid-2", "perm-uuid-3"],
    "operation": "activate"
  }'
```

## 权限代码规范

### 命名规则
- 格式: `模块.操作`
- 示例: `documents.read`, `users.create`, `config.manage`
- 超级管理员权限: `*`

### 标准操作类型
- `read` - 查看/读取
- `create` - 创建
- `update` - 更新
- `delete` - 删除
- `manage` - 管理（包含所有操作）
- `execute` - 执行特定功能

### 标准模块分类
- `users` - 用户管理
- `documents` - 文档管理
- `templates` - 模板管理
- `config` - 系统配置
- `upload` - 文件上传
- `editor` - 编辑器
- `auth` - 认证管理

## 权限装饰器使用

### 基础权限装饰器

```typescript
import { RequirePermissions, PermissionPermissions } from '../../auth/decorators/permissions.decorator';

// 方式1: 直接使用
@RequirePermissions('documents.read', 'documents.create')
@Get('documents')
async getDocuments() {
  // 需要 documents.read 或 documents.create 权限
}

// 方式2: 使用预定义装饰器
@PermissionPermissions.Read()
@Get('permissions')
async getPermissions() {
  // 需要 permissions.read 权限
}
```

### 组合权限装饰器

```typescript
import { DocumentPermissions, UserPermissions } from '../../auth/decorators/permissions.decorator';

@DocumentPermissions.ReadWrite()
@Put('documents/:id')
async updateDocument() {
  // 需要文档读写权限
}

@UserPermissions.Manage()
@Controller('admin/users')
export class UserAdminController {
  // 整个控制器需要用户管理权限
}
```

## 数据库表结构

### user_permissions 表
```sql
CREATE TABLE user_permissions (
  id VARCHAR(36) PRIMARY KEY,
  code VARCHAR(100) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  module VARCHAR(50) NOT NULL,
  resource VARCHAR(50),
  action VARCHAR(50),
  conditions JSON,
  is_active TINYINT(1) DEFAULT 1,
  sort_order INT DEFAULT 0,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_by VARCHAR(36),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_code (code),
  INDEX idx_module (module),
  INDEX idx_action (action),
  INDEX idx_active (is_active)
);
```

### role_permissions 关联表
```sql
CREATE TABLE role_permissions (
  role_id VARCHAR(36) NOT NULL,
  permission_code VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  PRIMARY KEY (role_id, permission_code),
  FOREIGN KEY (role_id) REFERENCES user_roles_detail(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_code) REFERENCES user_permissions(code) ON DELETE CASCADE
);
```

## 初始化权限

### 运行权限初始化脚本

```bash
# 方式1: 通过npm脚本
npm run init-permissions

# 方式2: 直接运行TypeScript
npx ts-node src/scripts/init-permissions.ts
```

### 初始化内容
- ✅ 创建基础权限（用户、角色、权限、文档、模板等模块）
- ✅ 创建超级管理员角色
- ✅ 权限与角色关联
- ✅ 自动检查重复，避免重复初始化

## 错误处理

### 常见错误码
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 权限不存在
- `409` - 权限代码冲突

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 最佳实践

### 1. 权限设计原则
- 🎯 **最小权限原则**: 用户只拥有完成工作所需的最小权限
- 🔒 **职责分离**: 敏感操作需要多个权限组合
- 📝 **权限可审计**: 所有权限操作都有日志记录

### 2. 权限命名建议
- 使用清晰、一致的命名约定
- 权限代码应该是自解释的
- 避免过于细粒度的权限划分

### 3. 性能优化
- 权限检查结果可以缓存
- 批量操作时使用事务
- 合理使用数据库索引

### 4. 安全考虑
- 权限检查应该在业务逻辑之前
- 敏感权限操作需要额外验证
- 定期审核用户权限分配

## 相关文档

- [用户管理控制器文档](./user.controller.md)
- [角色管理控制器文档](./role.controller.md)
- [权限装饰器文档](../../auth/decorators/README.md)
- [权限守卫文档](../../auth/guards/README.md)

## 更新日志

### v1.0 (2024-12-19)
- ✅ 初始版本发布
- ✅ 完整的权限CRUD功能
- ✅ 权限检查和验证功能
- ✅ 批量操作支持
- ✅ 权限统计和分析功能 