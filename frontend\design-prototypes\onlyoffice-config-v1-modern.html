<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 现代化版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 16px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .brand-info h1 {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .brand-info p {
            font-size: 14px;
            color: #718096;
        }

        .navbar-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.5);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: #4a5568;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            padding: 32px;
            gap: 32px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        /* 左侧模板列表 */
        .templates-panel {
            width: 380px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 32px;
            height: fit-content;
            max-height: calc(100vh - 200px);
            overflow: hidden;
        }

        .panel-header {
            margin-bottom: 24px;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .panel-subtitle {
            font-size: 14px;
            color: #718096;
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            margin-bottom: 24px;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px 16px 48px;
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 16px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 16px;
        }

        .templates-list {
            max-height: 500px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 transparent;
        }

        .templates-list::-webkit-scrollbar {
            width: 4px;
        }

        .templates-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .templates-list::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 2px;
        }

        .template-item {
            padding: 20px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.6);
            border: 2px solid transparent;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .template-item:hover {
            background: rgba(255, 255, 255, 0.9);
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .template-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        .template-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .template-desc {
            font-size: 13px;
            opacity: 0.8;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .template-badges {
            display: flex;
            gap: 6px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
        }

        .badge-default {
            background: #ffd700;
            color: #8b7000;
        }

        .badge-active {
            background: #48bb78;
            color: white;
        }

        .badge-inactive {
            background: #e2e8f0;
            color: #718096;
        }

        /* 右侧配置面板 */
        .config-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .config-header {
            padding: 32px 32px 0;
            border-bottom: 1px solid rgba(226, 232, 240, 0.6);
        }

        .config-title-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .config-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .config-subtitle {
            font-size: 14px;
            color: #718096;
        }

        .config-actions {
            display: flex;
            gap: 12px;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 13px;
        }

        /* 配置标签页 */
        .config-tabs {
            background: rgba(248, 250, 252, 0.8);
            padding: 0 32px;
            display: flex;
            gap: 4px;
        }

        .tab-item {
            padding: 16px 20px;
            background: transparent;
            border: none;
            border-radius: 12px 12px 0 0;
            font-size: 14px;
            font-weight: 500;
            color: #718096;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            background: white;
            color: #667eea;
            box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
        }

        .tab-item:hover {
            color: #667eea;
        }

        /* 配置内容区域 */
        .config-content {
            flex: 1;
            padding: 32px;
            background: white;
            overflow-y: auto;
        }

        .config-group {
            margin-bottom: 32px;
        }

        .group-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .group-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .config-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .config-item {
            background: rgba(248, 250, 252, 0.8);
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
        }

        .config-item:hover {
            border-color: #667eea;
            background: white;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .item-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .item-desc {
            font-size: 13px;
            color: #718096;
            line-height: 1.4;
        }

        .switch-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .switch-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .switch {
            width: 44px;
            height: 24px;
            background: #e2e8f0;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .switch.active {
            background: #667eea;
        }

        .switch-thumb {
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .switch.active .switch-thumb {
            transform: translateX(20px);
        }

        .switch-label {
            font-size: 13px;
            color: #4a5568;
            font-weight: 500;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            margin-top: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-enabled {
            background: #48bb78;
        }

        .status-disabled {
            background: #cbd5e0;
        }

        .required-badge {
            background: #f56565;
            color: white;
            padding: 2px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: #718096;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #4a5568;
        }

        .empty-desc {
            font-size: 14px;
            max-width: 300px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
                gap: 24px;
            }

            .templates-panel {
                width: 100%;
                max-height: 400px;
            }

            .config-items {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 16px;
            }

            .templates-panel,
            .config-panel {
                padding: 20px;
            }

            .config-title-section {
                flex-direction: column;
                gap: 16px;
            }

            .config-actions {
                width: 100%;
                justify-content: stretch;
            }

            .btn {
                flex: 1;
                justify-content: center;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .template-item,
        .config-item {
            animation: fadeInUp 0.3s ease;
        }

        /* 浮动操作按钮 */
        .floating-action {
            position: fixed;
            bottom: 32px;
            right: 32px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .floating-action:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.5);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <nav class="top-navbar">
            <div class="logo-section">
                <div class="logo">OO</div>
                <div class="brand-info">
                    <h1>OnlyOffice 配置管理</h1>
                    <p>企业级文档编辑器配置平台</p>
                </div>
            </div>
            <div class="navbar-actions">
                <a href="#" class="btn btn-secondary">
                    <i class="fas fa-chart-line"></i>
                    统计报告
                </a>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新建模板
                </a>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧模板列表 -->
            <aside class="templates-panel">
                <div class="panel-header">
                    <h2 class="panel-title">配置模板</h2>
                    <p class="panel-subtitle">管理你的OnlyOffice配置模板</p>
                </div>

                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索模板...">
                </div>

                <div class="templates-list">
                    <div class="template-item active">
                        <div class="template-name">默认编辑模板</div>
                        <div class="template-desc">适用于一般文档编辑场景，包含常用的编辑功能和权限设置</div>
                        <div class="template-meta">
                            <div class="template-badges">
                                <span class="badge badge-default">默认</span>
                                <span class="badge badge-active">启用</span>
                            </div>
                            <span>12-19</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-name">只读查看模板</div>
                        <div class="template-desc">只允许查看和评论，适用于文档审阅场景</div>
                        <div class="template-meta">
                            <div class="template-badges">
                                <span class="badge badge-active">启用</span>
                            </div>
                            <span>12-18</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-name">协作编辑模板</div>
                        <div class="template-desc">支持多人实时协作，启用所有协作功能</div>
                        <div class="template-meta">
                            <div class="template-badges">
                                <span class="badge badge-active">启用</span>
                            </div>
                            <span>12-17</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-name">功能限制模板</div>
                        <div class="template-desc">限制高级功能使用，适用于外部用户</div>
                        <div class="template-meta">
                            <div class="template-badges">
                                <span class="badge badge-inactive">禁用</span>
                            </div>
                            <span>12-16</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧配置面板 -->
            <section class="config-panel">
                <div class="config-header">
                    <div class="config-title-section">
                        <div>
                            <h2 class="config-title">默认编辑模板</h2>
                            <p class="config-subtitle">配置文档编辑器的功能和权限设置</p>
                        </div>
                        <div class="config-actions">
                            <button class="btn btn-secondary btn-sm">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-save"></i>
                                保存配置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="config-tabs">
                    <button class="tab-item active">权限控制</button>
                    <button class="tab-item">界面定制</button>
                    <button class="tab-item">功能设置</button>
                    <button class="tab-item">协作配置</button>
                    <button class="tab-item">高级选项</button>
                </div>

                <div class="config-content">
                    <div class="config-group">
                        <h3 class="group-title">
                            <div class="group-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            文档权限配置
                        </h3>
                        <div class="config-items">
                            <div class="config-item">
                                <div class="item-header">
                                    <div>
                                        <div class="item-title">编辑权限</div>
                                        <div class="item-desc">允许用户编辑文档内容</div>
                                    </div>
                                    <span class="required-badge">必需</span>
                                </div>
                                <div class="switch-group">
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">启用编辑</span>
                                    </div>
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示此项</span>
                                    </div>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot status-enabled"></div>
                                    <span>已启用并可见</span>
                                </div>
                            </div>

                            <div class="config-item">
                                <div class="item-header">
                                    <div>
                                        <div class="item-title">下载权限</div>
                                        <div class="item-desc">允许用户下载文档副本</div>
                                    </div>
                                </div>
                                <div class="switch-group">
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">允许下载</span>
                                    </div>
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示此项</span>
                                    </div>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot status-enabled"></div>
                                    <span>已启用并可见</span>
                                </div>
                            </div>

                            <div class="config-item">
                                <div class="item-header">
                                    <div>
                                        <div class="item-title">打印权限</div>
                                        <div class="item-desc">允许用户打印文档</div>
                                    </div>
                                </div>
                                <div class="switch-group">
                                    <div class="switch-wrapper">
                                        <div class="switch">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">允许打印</span>
                                    </div>
                                    <div class="switch-wrapper">
                                        <div class="switch">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示此项</span>
                                    </div>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot status-disabled"></div>
                                    <span>已禁用且隐藏</span>
                                </div>
                            </div>

                            <div class="config-item">
                                <div class="item-header">
                                    <div>
                                        <div class="item-title">评论权限</div>
                                        <div class="item-desc">允许用户添加和查看评论</div>
                                    </div>
                                </div>
                                <div class="switch-group">
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">允许评论</span>
                                    </div>
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示此项</span>
                                    </div>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot status-enabled"></div>
                                    <span>已启用并可见</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-group">
                        <h3 class="group-title">
                            <div class="group-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            界面定制
                        </h3>
                        <div class="config-items">
                            <div class="config-item">
                                <div class="item-header">
                                    <div>
                                        <div class="item-title">显示工具栏</div>
                                        <div class="item-desc">显示编辑器顶部工具栏</div>
                                    </div>
                                </div>
                                <div class="switch-group">
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示工具栏</span>
                                    </div>
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示此项</span>
                                    </div>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot status-enabled"></div>
                                    <span>已启用并可见</span>
                                </div>
                            </div>

                            <div class="config-item">
                                <div class="item-header">
                                    <div>
                                        <div class="item-title">显示状态栏</div>
                                        <div class="item-desc">显示编辑器底部状态栏</div>
                                    </div>
                                </div>
                                <div class="switch-group">
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示状态栏</span>
                                    </div>
                                    <div class="switch-wrapper">
                                        <div class="switch active">
                                            <div class="switch-thumb"></div>
                                        </div>
                                        <span class="switch-label">显示此项</span>
                                    </div>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot status-enabled"></div>
                                    <span>已启用并可见</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 浮动操作按钮 -->
        <button class="floating-action">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <script>
        // 简单的交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 模板选择
            const templateItems = document.querySelectorAll('.template-item');
            templateItems.forEach(item => {
                item.addEventListener('click', function() {
                    templateItems.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 标签页切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabItems.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 开关切换
            const switches = document.querySelectorAll('.switch');
            switches.forEach(switchEl => {
                switchEl.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            });
        });
    </script>
</body>
</html> 