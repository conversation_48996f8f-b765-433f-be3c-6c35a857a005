import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  OnlyOfficeConfig, 
  DocumentPermissions,
  EditorUser,
  EditorCustomization,
  CoEditingConfig 
} from '../interfaces/editor-config.interface';
import { 
  CallbackDto, 
  ForceSaveDto,
  SaveStatusResponseDto,
  ForceSaveResponseDto 
} from '../dto/index';
import { OnlyOfficeJwtService } from '../../config/services/onlyoffice-jwt.service';
import { DocumentService } from '../../documents/services/document.service';
import { ConfigTemplateService } from '../../config/services/config-template.service';

/**
 * 配置模板接口
 */
interface ConfigTemplate {
  template?: {
    id: string;
    name: string;
    description?: string;
    isDefault: boolean;
    isActive: boolean;
  };
  config?: {
    permissions?: {
      edit?: boolean;
      download?: boolean;
      review?: boolean;
      comment?: boolean;
      fillForms?: boolean;
      modifyFilter?: boolean;
      modifyContentControl?: boolean;
      chat?: boolean;
      copy?: boolean;
      print?: boolean;
      protect?: boolean;
    };
    customization?: {
      autosave?: boolean;
      forcesave?: boolean;
      chat?: boolean;
      comments?: boolean;
      help?: boolean;
      about?: boolean;
      feedback?: boolean;
      review?: boolean;
      toolbarNoTabs?: boolean;
      toolbarHideFileName?: boolean;
    };
    coEditing?: {
      mode?: string;
      change?: boolean;
    };
    user?: {
      id?: string;
      name?: string;
    };
  };
  configStates?: {
    permissions?: {
      [key: string]: {
        enabled: boolean;
        required: boolean;
        value: unknown;
      };
    };
    customization?: {
      [key: string]: {
        enabled: boolean;
        required: boolean;
        value: unknown;
      };
    };
    [key: string]: unknown;
  };
}

/**
 * 文档信息接口
 */
interface DocumentInfo {
  id: string;
  name?: string;
  originalName?: string;
  extension?: string;
  type?: string;
  version?: number;
  lastModified?: string;
}

/**
 * 查询参数接口
 */
interface EditorQueryParams {
  template?: string;
  readonly?: boolean;
  hideComments?: boolean;
  hideChat?: boolean;
  userId?: string;
  userName?: string;
  [key: string]: unknown;
}

/**
 * OnlyOffice编辑器服务
 * 
 * @description 提供OnlyOffice文档编辑功能的核心服务，包括：
 * - 编辑器配置生成
 * - 文档保存回调处理  
 * - 保存状态管理
 * - 强制保存功能
 * 
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */
@Injectable()
export class EditorService {
  private readonly logger = new Logger(EditorService.name);
  
  constructor(
    private configService: ConfigService,
    private onlyOfficeJwtService: OnlyOfficeJwtService,
    private documentService: DocumentService,
    private configTemplateService: ConfigTemplateService,
  ) {}

  /**
   * 简化版文档信息接口
   */
  private async getDocumentById(fileId: string): Promise<DocumentInfo> {
    // 使用新的DocumentService
    return await this.documentService.getDocumentById(fileId);
  }

  /**
   * 验证UUID格式
   * @param uuid - 待验证的UUID字符串
   * @returns 是否为有效UUID
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * 根据文件扩展名确定文档类型
   * @param fileExtension - 文件扩展名
   * @returns OnlyOffice文档类型
   */
  private getDocumentType(fileExtension: string): 'word' | 'cell' | 'slide' | 'pdf' {
    const ext = fileExtension.toLowerCase().replace('.', '');
    
    if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) {
      return 'word';
    } else if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) {
      return 'cell';
    } else if (['ppt', 'pptx', 'odp'].includes(ext)) {
      return 'slide';
    } else if (['pdf'].includes(ext)) {
      return 'pdf';
    }
    
    return 'word'; // 默认类型
  }

  /**
   * 从数据库获取配置模板
   * @param templateId - 可选的模板ID，如果不提供则使用默认模板
   * @returns 配置模板对象
   */
  private async getConfigFromTemplate(templateId?: string): Promise<ConfigTemplate> {
    try {
      console.log('🔄 [EditorService] getConfigFromTemplate 开始获取配置模板')
      console.log('🔧 [EditorService] 请求的模板ID:', templateId)
      
      if (templateId) {
        console.log('✅ [EditorService] 使用指定的配置模板:', templateId)
        try {
          // 使用新的ConfigTemplateService获取指定模板配置
          const template = await this.configTemplateService.getTemplateConfig(templateId);
          console.log('📥 [EditorService] 成功获取到指定配置模板:')
          console.log('  - 模板ID:', templateId)
          console.log('  - 模板内容:', JSON.stringify(template, null, 2))
          return template;
        } catch (error) {
          console.error('❌ [EditorService] 获取指定配置模板失败:', templateId, error)
          // 如果指定模板获取失败，回退到默认模板
          console.log('⚠️ [EditorService] 指定模板获取失败，尝试获取默认模板')
        }
      } else {
        console.log('🔍 [EditorService] 未指定模板ID，查找默认模板')
      }
      
      // 获取默认模板
      const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
      if (defaultTemplate) {
        console.log('✅ [EditorService] 找到默认模板:', (defaultTemplate as { id: string, name: string }).id, (defaultTemplate as { id: string, name: string }).name)
        const template = await this.configTemplateService.getTemplateConfig((defaultTemplate as { id: string }).id);
        console.log('📥 [EditorService] 成功获取到默认配置模板:')
        console.log('  - 默认模板ID:', (defaultTemplate as { id: string }).id)
        console.log('  - 默认模板内容:', JSON.stringify(template, null, 2))
        return template;
      } else {
        console.log('⚠️ [EditorService] 未找到默认模板，使用系统默认配置')
      }
      
      // 如果没有模板，回退到默认配置
      const defaultConfig = await this.getDefaultConfig();
      console.log('📥 [EditorService] 使用系统默认配置:')
      console.log('  - 系统默认配置:', JSON.stringify(defaultConfig, null, 2))
      return defaultConfig;
    } catch (error) {
      console.error('❌ [EditorService] 从配置模板获取配置失败:', error);
      this.logger.error('从配置模板获取配置失败:', error);
      // 回退到默认配置
      const defaultConfig = await this.getDefaultConfig();
      console.log('📥 [EditorService] 错误回退，使用系统默认配置:')
      console.log('  - 系统默认配置:', JSON.stringify(defaultConfig, null, 2))
      return defaultConfig;
    }
  }

  /**
   * 获取默认配置
   * @returns 默认配置对象
   */
  private async getDefaultConfig(): Promise<ConfigTemplate> {
    try {
      console.log('🔄 [EditorService] getDefaultConfig 开始获取默认配置')
      const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
      if (defaultTemplate) {
        console.log('✅ [EditorService] 找到默认模板:', (defaultTemplate as { id: string, name: string }).id, (defaultTemplate as { id: string, name: string }).name)
        const template = await this.configTemplateService.getTemplateConfig((defaultTemplate as { id: string }).id);
        console.log('📥 [EditorService] 成功获取到默认配置模板:')
        console.log('  - 默认模板ID:', (defaultTemplate as { id: string }).id)
        console.log('  - 默认模板内容:', JSON.stringify(template, null, 2))
        return template;
      } else {
        console.error('❌ [EditorService] 数据库中未找到默认配置模板')
        throw new Error('数据库中未找到默认配置模板，请先在配置管理中创建默认模板');
      }
    } catch (error) {
      console.error('❌ [EditorService] 从数据库获取默认配置失败:', error);
      this.logger.error('从数据库获取默认配置失败:', error);
      throw new InternalServerErrorException('无法获取编辑器配置，请检查配置模板设置');
    }
  }

  /**
   * 获取文档的OnlyOffice编辑器配置
   * @param fileId - 文档ID
   * @param queryParams - 查询参数（可选配置覆盖）
   * @returns OnlyOffice编辑器配置
   */
  async getEditorConfig(fileId: string, queryParams: EditorQueryParams = {}): Promise<OnlyOfficeConfig> {
    console.log('🔄 [EditorService] getEditorConfig 开始生成编辑器配置')
    console.log('📋 [EditorService] 文档ID/会话ID:', fileId)
    console.log('🔧 [EditorService] 原始查询参数:', queryParams)
    
    // 从数据库获取配置模板
    const templateConfig = await this.getConfigFromTemplate(queryParams.template);
    console.log('📋 [EditorService] 使用的配置模板:', JSON.stringify(templateConfig, null, 2))
    
    try {
      // 验证文件ID格式
      if (!this.isValidUUID(fileId)) {
        throw new Error(`无效的文档ID/会话ID格式: ${fileId}`);
      }

      // 检查是否是模板会话（通过URL路径判断）
      const isTemplateSession = queryParams.templateId;
      let document: any;
      
      if (isTemplateSession) {
        console.log('🎯 [EditorService] 检测到模板会话模式, sessionId:', fileId)
        console.log('🎯 [EditorService] 模板ID:', queryParams.templateId)
        console.log('🎯 [EditorService] 文档名称:', queryParams.documentName)
        
        // 模板会话模式：创建虚拟文档信息
        document = {
          id: fileId, // 使用会话ID作为临时文档ID
          name: queryParams.documentName || '基于模板的新文档.docx',
          extension: 'docx',
          type: 'Word文档',
          size: 0,
          templateId: queryParams.templateId
        };
      } else {
        // 普通文档模式：从数据库获取文档信息
        console.log('🔍 [EditorService] 普通文档模式，开始获取文档信息...')
        document = await this.getDocumentById(fileId);
      }
      console.log('📄 [EditorService] 文档信息:', {
        id: document.id,
        name: document.name,
        extension: document.extension,
        type: document.type
      })

      // 生成文件键值
      const fileKey = `${fileId}-${Date.now()}`;
      console.log('🔑 [EditorService] 生成文件键值:', fileKey)

      // 构建文件URL
      let fileUrl: string;
      if (isTemplateSession) {
        // 模板会话模式：使用模板文档的URL
        fileUrl = `${this.getServerBaseUrl()}/api/document-templates/${queryParams.templateId}/download`;
        console.log('🔗 [EditorService] 构建模板文件URL:', fileUrl)
      } else {
        // 普通文档模式：使用文档URL
        fileUrl = `${this.getServerBaseUrl()}/api/documents/${fileId}`;
        console.log('🔗 [EditorService] 构建文档文件URL:', fileUrl)
      }

      // 确定文档类型
      const documentType = this.getDocumentType(document.extension || document.type);
      console.log('📑 [EditorService] 确定文档类型:', documentType)
      
      // 从模板配置构建权限配置
      const templatePermissions = templateConfig.config?.permissions || {};
      const templateConfigStates = templateConfig.configStates?.permissions || {};
      
      console.log('🔒 [EditorService] 模板中的权限配置:', JSON.stringify(templatePermissions, null, 2))
      console.log('🔒 [EditorService] 模板中的权限状态:', JSON.stringify(templateConfigStates, null, 2))
      
      const permissions: DocumentPermissions = {
        edit: (templateConfigStates.edit?.enabled !== false ? templatePermissions.edit !== false : false) && !queryParams.readonly && document.extension !== 'pdf',
        download: templateConfigStates.download?.enabled !== false ? templatePermissions.download !== false : false,
        review: templateConfigStates.review?.enabled !== false ? templatePermissions.review !== false : false,
        comment: (templateConfigStates.comment?.enabled !== false ? templatePermissions.comment !== false : false) && !queryParams.hideComments,
        fillForms: templateConfigStates.fillForms?.enabled !== false ? templatePermissions.fillForms !== false : false,
        modifyFilter: templateConfigStates.modifyFilter?.enabled !== false ? templatePermissions.modifyFilter !== false : false,
        modifyContentControl: templateConfigStates.modifyContentControl?.enabled !== false ? templatePermissions.modifyContentControl !== false : false,
      };
      console.log('🔒 [EditorService] 构建的权限配置:', JSON.stringify(permissions, null, 2))

      // 从模板配置构建自定义配置
      const templateCustomization = templateConfig.config?.customization || {};
      const templateCustomizationStates = templateConfig.configStates?.customization || {};
      
      console.log('🎨 [EditorService] 模板中的自定义配置:', JSON.stringify(templateCustomization, null, 2))
      console.log('🎨 [EditorService] 模板中的自定义配置状态:', JSON.stringify(templateCustomizationStates, null, 2))
      
      const customization: EditorCustomization = {
        chat: (templateConfigStates.chat?.enabled !== false ? templatePermissions.chat === true : false) && !queryParams.hideChat,
        comments: (templateConfigStates.comment?.enabled !== false ? templatePermissions.comment !== false : false) && !queryParams.hideComments,
        help: templateCustomizationStates.help?.enabled !== false ? templateCustomization.help !== false : false,
        about: templateCustomizationStates.about?.enabled !== false ? templateCustomization.about !== false : false,
        feedback: templateCustomizationStates.feedback?.enabled !== false ? templateCustomization.feedback === true : false,
        forcesave: templateCustomizationStates.forcesave?.enabled !== false ? templateCustomization.forcesave !== false : false,
        review: templateConfigStates.review?.enabled !== false ? templatePermissions.review !== false : false,
        toolbarNoTabs: templateCustomizationStates.toolbarNoTabs?.enabled !== false ? templateCustomization.toolbarNoTabs === true : false,
        toolbarHideFileName: templateCustomizationStates.toolbarHideFileName?.enabled !== false ? templateCustomization.toolbarHideFileName === true : false,
      };
      console.log('🎨 [EditorService] 构建的自定义配置:', JSON.stringify(customization, null, 2))

      // 从模板配置构建用户信息
      const templateUser = templateConfig.config?.user || {};
      const user: EditorUser = {
        id: queryParams.userId || templateUser.id || 'user-1',
        name: queryParams.userName || templateUser.name || '默认用户',
      };
      console.log('👤 [EditorService] 构建的用户信息:', JSON.stringify(user, null, 2))

      // 从模板配置构建协同编辑配置
      const templateCoEditing = templateConfig.config?.coEditing || {};
      const coEditing: CoEditingConfig = {
        mode: (templateCoEditing.mode === 'strict' ? 'strict' : 'fast') as 'fast' | 'strict',
        change: templateCoEditing.change !== false,
      };
      console.log('👥 [EditorService] 构建的协同编辑配置:', JSON.stringify(coEditing, null, 2))

      // 构建完整配置
      const editorConfig: OnlyOfficeConfig = {
        document: {
          fileType: (document.extension || document.type).replace('.', ''),
          key: fileKey,
          title: document.name || document.originalName,
          url: fileUrl,
          permissions,
          dbId: fileId,
        },
        documentType,
        editorConfig: {
          callbackUrl: `${this.getServerBaseUrl()}/api/editor/callback`,
          lang: 'zh',
          mode: permissions.edit ? 'edit' : 'view',
          customization,
          user,
          coEditing,
        },
        apiUrl: `${this.getOnlyOfficeBaseUrl().replace(/\/$/, '')}/web-apps/apps/api/documents/api.js`,
      };
      
      console.log('📋 [EditorService] 构建完成的完整编辑器配置:')
      console.log('  📄 文档信息:', JSON.stringify(editorConfig.document, null, 2))
      console.log('  🔧 编辑器配置:', JSON.stringify(editorConfig.editorConfig, null, 2))
      console.log('  🌐 API URL:', editorConfig.apiUrl)
      console.log('  📑 文档类型:', editorConfig.documentType)

      // 使用OnlyOfficeJwtService签名
      const signedConfig = await this.onlyOfficeJwtService.signConfig(editorConfig);
      
      console.log('🔐 [EditorService] JWT签名完成:')
      console.log('  ✅ 签名成功')
      console.log('  🎯 最终发送给OnlyOffice的配置:', JSON.stringify(signedConfig, null, 2))
      
      this.logger.log('生成编辑器配置成功并签名JWT: ' + (document.name || document.originalName));
      return signedConfig;

    } catch (error) {
      this.logger.error(`获取编辑器配置失败: ${error.message}`, error.stack);
      
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      
      throw new InternalServerErrorException('获取编辑器配置时发生内部错误');
    }
  }

  /**
   * 处理OnlyOffice回调
   * @param callbackData - 回调数据
   * @param fileId - 文件ID（可选）
   * @returns 回调处理结果
   */
  async handleCallback(callbackData: CallbackDto, _fileId?: string): Promise<{ error: number; message?: string }> {
    this.logger.log(`处理OnlyOffice回调: status=${callbackData.status}, key=${callbackData.key}`);

    // 立即返回成功响应，避免OnlyOffice超时（和老项目保持一致）
    const response = { error: 0, message: '回调处理成功' };

    // 在后台异步处理实际的保存逻辑，不影响响应速度
    setTimeout(async () => {
      try {
        const result = await this.documentService.handleCallback(callbackData);
        this.logger.log(`后台文档保存处理结果: ${result ? '成功' : '失败'}`);
      } catch (error) {
        this.logger.error(`后台处理回调失败: ${error.message}`, error.stack);
      }
    }, 10);

    return response;
  }

  /**
   * 检查文档保存状态
   * @param fileId - 文档ID
   * @returns 保存状态信息
   */
  async checkSaveStatus(fileId: string): Promise<SaveStatusResponseDto> {
    this.logger.log(`检查保存状态: ${fileId}`);

    try {
      const document = await this.getDocumentById(fileId);
      if (!document) {
        throw new NotFoundException('找不到对应的文档');
      }

      return {
        success: true,
        status: 'saved',
        message: '文档状态正常',
        fileName: document.name || document.originalName,
        version: document.version || 1,
        lastModifiedDb: document.lastModified || new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`检查保存状态失败: ${error.message}`, error.stack);
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new InternalServerErrorException('检查保存状态时发生内部错误');
    }
  }

  /**
   * 强制保存文档
   * @param fileId - 文档ID
   * @param forceSaveData - 强制保存参数
   * @returns 强制保存结果
   */
  async forceSave(fileId: string, forceSaveData: ForceSaveDto): Promise<ForceSaveResponseDto> {
    this.logger.log(`强制保存文档: ${fileId}`);
    console.log('🔄 [EditorService] 开始强制保存流程')
    console.log('📋 [EditorService] 文档ID:', fileId)
    console.log('🔧 [EditorService] 强制保存参数:', JSON.stringify(forceSaveData, null, 2))

    try {
      // 验证文档是否存在
      const document = await this.getDocumentById(fileId);
      if (!document) {
        throw new NotFoundException('找不到对应的文档');
      }

      console.log('📄 [EditorService] 找到文档:', document.name || document.originalName)

      // 验证documentKey是否提供
      if (!forceSaveData.documentKey) {
        throw new BadRequestException('缺少必需的文档密钥(documentKey)参数');
      }

      const documentKey = forceSaveData.documentKey;
      console.log('🔑 [EditorService] 使用的文档key:', documentKey)

      // 构建OnlyOffice Command Service API调用参数
      const commandData = {
        c: 'forcesave', // 命令类型
        key: documentKey, // 文档key
        userdata: JSON.stringify({
          fileId: fileId,
          userId: 'user-1', // 可以从请求中获取实际用户ID
          timestamp: new Date().toISOString(),
          force: forceSaveData.force || true,
          requestType: 'manual' // 标记为手动强制保存
        })
      };

      console.log('📤 [EditorService] 准备发送OnlyOffice强制保存命令:', JSON.stringify(commandData, null, 2))

      // 调用OnlyOffice Command Service API
      const onlyOfficeBaseUrl = this.getOnlyOfficeBaseUrl().replace(/\/$/, ''); // 确保没有尾部斜杠
      const commandUrl = `${onlyOfficeBaseUrl}/coauthoring/CommandService.ashx`;
      
      console.log('🌐 [EditorService] OnlyOffice Command Service URL:', commandUrl)

      // 如果有JWT配置，需要签名command请求
      let requestData: Record<string, unknown> = commandData;
      if (this.onlyOfficeJwtService) {
        try {
          // 对于Command Service API，也需要JWT签名
          const jwtToken = await this.onlyOfficeJwtService.generateToken(commandData);
          requestData = {
            ...commandData,
            token: jwtToken
          };
          console.log('🔐 [EditorService] Command请求已签名')
        } catch (error) {
          console.error('❌ [EditorService] Command请求签名失败:', error)
          // 如果签名失败，仍然尝试发送未签名的请求
        }
      }

      // 发送HTTP请求到OnlyOffice
      const response = await fetch(commandUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('📥 [EditorService] OnlyOffice响应状态:', response.status)

      if (!response.ok) {
        const responseText = await response.text();
        console.error('❌ [EditorService] OnlyOffice Command Service错误响应:', responseText)
        throw new Error(`OnlyOffice Command Service 返回错误: ${response.status} ${response.statusText}, 响应: ${responseText}`);
      }

      const responseData = await response.json();
      console.log('📥 [EditorService] OnlyOffice响应数据:', JSON.stringify(responseData, null, 2))

      // 检查OnlyOffice响应结果
      if (responseData.error === 0) {
        console.log('✅ [EditorService] 强制保存命令发送成功')
        return {
          success: true,
          message: '强制保存命令已成功发送至OnlyOffice服务器，文档将在处理完成后自动保存到FileNet',
        };
      } else {
        console.error('❌ [EditorService] OnlyOffice强制保存失败:', responseData)
        
        // 根据错误码提供更具体的错误信息和处理策略
        let errorMessage = '强制保存失败';
        let shouldReturnSuccess = false;
        
        switch (responseData.error) {
          case 1:
            errorMessage = '文档密钥错误或文档不存在';
            break;
          case 2:
            errorMessage = '回调URL错误';
            break;
          case 3:
            errorMessage = '内部服务器错误';
            break;
          case 4:
            // 官方定义：在forcesave命令之前，文档没有应用任何更改
            // 这种情况应该返回成功，因为文档已经是最新状态
            errorMessage = 'OnlyOffice认为文档自上次保存以来没有变更，将等待自动保存';
            shouldReturnSuccess = true;
            console.log('⚠️ [EditorService] 错误码4：文档可能已经是最新状态，或OnlyOffice缓存问题');
            break;
          case 5:
            errorMessage = '强制保存命令格式错误';
            break;
          case 6:
            errorMessage = 'JWT令牌无效';
            break;
          default:
            errorMessage = `OnlyOffice服务器返回错误码: ${responseData.error}`;
        }

        // 对于错误码4，我们返回成功但提供说明信息
        if (shouldReturnSuccess) {
          return {
            success: true,
            message: `${errorMessage}。如果您确实进行了修改，请稍等片刻让OnlyOffice检测到变更，或关闭编辑器触发自动保存。`,
          };
        } else {
          throw new Error(errorMessage);
        }
      }

    } catch (error) {
      console.error('❌ [EditorService] 强制保存失败:', error)
      this.logger.error(`强制保存失败: ${error.message}`, error.stack);
      
      // 如果是已知的HTTP异常，直接重新抛出
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      
      // 检查是否是我们特意抛出的错误（如OnlyOffice错误码处理）
      if (error instanceof Error && error.message.includes('OnlyOffice服务器返回错误码')) {
        throw new BadRequestException(error.message);
      }
      
      // 其他未知错误
      throw new InternalServerErrorException(`强制保存时发生内部错误: ${error.message}`);
    }
  }

  /**
   * 获取服务器基础URL
   * @returns 服务器基础URL
   */
  private getServerBaseUrl(): string {
    const host = process.env.SERVER_HOST || '*************';
    const port = parseInt(process.env.PORT || '3000', 10);
    return `http://${host}:${port}`;
  }

  /**
   * 获取OnlyOffice文档服务器基础URL
   * @returns OnlyOffice文档服务器基础URL
   */
  private getOnlyOfficeBaseUrl(): string {
    return process.env.ONLYOFFICE_DOCUMENT_SERVER_URL || 'http://*************';
  }
}