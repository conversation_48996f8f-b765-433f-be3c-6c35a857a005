import { ref, computed } from 'vue'
import type { Ref } from 'vue'
import { useRoute } from 'vue-router'
import type {
  OnlyOfficeConfig,
  DocEditor,
  DocumentStateChangeEvent,
  ErrorEvent,
  OnlyOfficeEvents,
} from '@/types/onlyoffice.types'

/**
 * OnlyOffice编辑器管理 Composable
 *
 * @description 提供编辑器初始化、配置管理和操作功能
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

/**
 * 编辑器配置查询参数
 */
export interface EditorConfigQuery {
  /** 配置模板名称 */
  template?: string
  /** 是否隐藏聊天功能 */
  hideChat?: boolean
  /** 是否隐藏评论功能 */
  hideComments?: boolean
  /** 是否设置为只读模式 */
  readonly?: boolean
  /** 用户ID */
  userId?: string
  /** 用户名称 */
  userName?: string
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = unknown> {
  /** 请求是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 响应消息 */
  message?: string
  /** 错误信息 */
  error?: string
}

/**
 * 创建编辑器错误
 */
function createEditorError(message: string, originalError?: Error): Error {
  const error = new Error(message)
  error.name = 'EditorError'
  if (originalError) {
    console.error('原始错误:', originalError)
  }
  return error
}

/**
 * 编辑器管理 Hook
 * @param fileId 文件ID引用
 */
export function useEditor(fileId: Ref<string>) {
  const route = useRoute()

  // 编辑器实例
  const docEditor = ref<DocEditor | null>(null)

  // 编辑器配置
  const editorConfig = ref<OnlyOfficeConfig | null>(null)

  // 文档标题
  const docTitle = ref<string>('加载中...')

  // 编辑器是否就绪
  const isEditorReady = ref<boolean>(false)

  // 是否正在初始化
  const isInitializing = ref<boolean>(false)

  /**
   * 从路由查询参数中提取编辑器配置参数
   * @returns 编辑器配置查询参数
   * @deprecated 此函数已不再使用，保留用于向后兼容
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getConfigQueryFromRoute = (): EditorConfigQuery => {
    const query = route.query
    const configQuery: EditorConfigQuery = {}

    // 提取配置参数
    if (query.template && typeof query.template === 'string') {
      configQuery.template = query.template
    }
    if (query.hideChat !== undefined) {
      configQuery.hideChat = query.hideChat === 'true'
    }
    if (query.hideComments !== undefined) {
      configQuery.hideComments = query.hideComments === 'true'
    }
    if (query.readonly !== undefined) {
      configQuery.readonly = query.readonly === 'true'
    }
    if (query.userId && typeof query.userId === 'string') {
      configQuery.userId = query.userId
    }
    if (query.userName && typeof query.userName === 'string') {
      configQuery.userName = query.userName
    }

    console.log('🔧 [Editor] 从路由提取配置参数:', configQuery)
    return configQuery
  }

  /**
   * 获取编辑器配置
   * @param documentId 文档ID
   * @param query 查询参数，包括template等
   * @returns Promise<EditorConfig>
   */
  const fetchEditorConfig = async (
    documentId: string,
    query: EditorConfigQuery = {}
  ): Promise<OnlyOfficeConfig> => {
    try {
      console.log('🔄 [useEditor] fetchEditorConfig 开始获取编辑器配置')
      console.log('📋 [useEditor] 文档ID:', documentId)
      console.log('🔧 [useEditor] 查询参数:', query)

      // 构建查询字符串
      const searchParams = new URLSearchParams()
      if (query.template) {
        searchParams.append('template', query.template)
        console.log('✅ [useEditor] 使用配置模板:', query.template)
      }
      if (query.hideChat !== undefined) {
        searchParams.append('hideChat', String(query.hideChat))
      }
      if (query.hideComments !== undefined) {
        searchParams.append('hideComments', String(query.hideComments))
      }
      if (query.readonly !== undefined) {
        searchParams.append('readonly', String(query.readonly))
      }
      if (query.userId && typeof query.userId === 'string') {
        searchParams.append('userId', query.userId)
      }
      if (query.userName && typeof query.userName === 'string') {
        searchParams.append('userName', query.userName)
      }

      const queryString = searchParams.toString()
      const url = `/api/editor/${documentId}/config${queryString ? `?${queryString}` : ''}`

      console.log('📤 [useEditor] 请求URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw createEditorError(`获取编辑器配置失败: ${response.status} ${response.statusText}`)
      }

      const result: ApiResponse<OnlyOfficeConfig> = await response.json()

      if (!result.success || !result.data) {
        throw createEditorError(result.message || '获取编辑器配置失败')
      }

      console.log('📥 [useEditor] 获取到编辑器配置:')
      console.log('  - documentType:', result.data.documentType)
      console.log('  - mode:', result.data.editorConfig?.mode)
      console.log('  - lang:', result.data.editorConfig?.lang)
      console.log('  - customization:', result.data.editorConfig?.customization)
      console.log('  - 完整配置对象:', result.data)

      return result.data
    } catch (error) {
      console.error('❌ [useEditor] 获取编辑器配置失败:', error)
      throw error
    }
  }

  /**
   * 动态加载OnlyOffice API脚本
   * @param apiUrl OnlyOffice API脚本URL
   */
  const loadOnlyOfficeScript = async (apiUrl: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (window.DocsAPI) {
        resolve()
        return
      }

      // 检查是否已经有脚本标签
      const existingScript = document.querySelector(`script[src="${apiUrl}"]`)
      if (existingScript) {
        // 如果脚本存在但API未加载，等待加载完成
        existingScript.addEventListener('load', () => resolve())
        existingScript.addEventListener('error', () => reject(new Error('OnlyOffice脚本加载失败')))
        return
      }

      // 创建新的脚本标签
      const script = document.createElement('script')
      script.src = apiUrl
      script.type = 'text/javascript'
      script.async = true

      script.onload = () => {
        console.log('OnlyOffice API脚本加载成功')
        resolve()
      }

      script.onerror = () => {
        console.error('OnlyOffice API脚本加载失败')
        reject(new Error('OnlyOffice脚本加载失败'))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 初始化编辑器
   * @param query 查询参数
   */
  const initializeEditor = async (query?: EditorConfigQuery): Promise<void> => {
    if (isInitializing.value) {
      console.warn('编辑器正在初始化中...')
      return
    }

    if (!fileId.value) {
      throw createEditorError('文件ID不能为空')
    }

    isInitializing.value = true

    try {
      console.log('🚀 [Editor] 开始初始化编辑器，文档ID:', fileId.value)

      // 获取编辑器配置
      const config = await fetchEditorConfig(fileId.value, query)

      // 动态加载OnlyOffice API脚本
      if (config.apiUrl) {
        await loadOnlyOfficeScript(config.apiUrl)
      }

      // 检查OnlyOffice API是否可用
      if (!window.DocsAPI) {
        throw createEditorError('OnlyOffice API未加载，请确保OnlyOffice服务器正常运行')
      }

      // 设置文档标题
      docTitle.value = config.document.title || '未知文档'

      // 创建事件处理器
      const events: OnlyOfficeEvents = {
        onDocumentReady: () => {
          console.log('✅ [Editor] 文档已准备就绪')
          isEditorReady.value = true
        },
        onDocumentStateChange: (event: DocumentStateChangeEvent) => {
          console.log('📝 [Editor] 文档状态变更:', event)
          // 这里可以添加状态变更的处理逻辑
        },
        onError: (event: ErrorEvent) => {
          console.error('❌ [Editor] 编辑器错误:', event)
          isEditorReady.value = false
          const errorMessage = event.data?.errorDescription || '编辑器发生未知错误'
          console.error('OnlyOffice编辑器错误:', errorMessage)
        },
      }

      // 创建完整的编辑器配置
      const documentConfig: OnlyOfficeConfig = {
        ...config,
        events,
        height: '100%',
        width: '100%',
        // 添加防止滚动条的配置
        editorConfig: {
          ...config.editorConfig,
          customization: {
            ...config.editorConfig?.customization,
            // 编辑器基础设置
            autosave: true,
            forcesave: false,
            compactToolbar: false,
            toolbarNoTabs: false,
            // 确保编辑器完全嵌入，不出现滚动条
            integrationMode: 'embed',
            zoom: 100,
            // 界面优化设置
            uiTheme: 'theme-light',
            // 禁用可能导致布局问题的元素
            hideRightMenu: false,
            hideRulers: false,
          },
        },
      }

      // 保存配置
      editorConfig.value = documentConfig

      console.log('🔧 [Editor] 最终编辑器配置:', documentConfig)

      // 初始化OnlyOffice编辑器
      docEditor.value = new window.DocsAPI.DocEditor('editor-container', documentConfig)

      console.log('✅ [Editor] 编辑器初始化完成')
    } catch (error) {
      console.error('❌ [Editor] 编辑器初始化失败:', error)

      if (error instanceof Error) {
        throw error
      } else {
        throw createEditorError('编辑器初始化失败：未知错误')
      }
    } finally {
      isInitializing.value = false
    }
  }

  /**
   * 销毁编辑器
   */
  const destroyEditor = (): void => {
    if (docEditor.value && typeof docEditor.value.destroyEditor === 'function') {
      try {
        docEditor.value.destroyEditor()
        console.log('编辑器已销毁')
      } catch (error) {
        console.error('销毁编辑器失败:', error)
      }
    }

    docEditor.value = null
    editorConfig.value = null
    isEditorReady.value = false
    docTitle.value = ''
  }

  /**
   * 强制保存文档
   */
  const forceSave = async (): Promise<void> => {
    if (!isEditorReady.value) {
      throw createEditorError('编辑器未就绪，无法执行保存操作')
    }

    if (!editorConfig.value?.document?.key) {
      throw createEditorError('无法获取文档密钥，无法执行保存操作')
    }

    try {
      console.log('🔄 [useEditor] 开始强制保存')
      console.log('📋 [useEditor] 文档ID:', fileId.value)
      console.log('🔑 [useEditor] 文档Key:', editorConfig.value.document.key)

      const response = await fetch(`/api/editor/${fileId.value}/force-save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentKey: editorConfig.value.document.key, // 添加documentKey
          force: true,
          timestamp: new Date().toISOString(),
        }),
      })

      console.log('📥 [useEditor] 强制保存响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ [useEditor] 强制保存失败响应:', errorText)
        throw createEditorError(`强制保存失败: ${response.status} ${response.statusText}`)
      }

      const result: ApiResponse = await response.json()
      console.log('📥 [useEditor] 强制保存响应数据:', result)

      if (!result.success) {
        throw createEditorError(result.message || '强制保存失败')
      }

      console.log('✅ [useEditor] 强制保存成功:', result.message)

      // 根据消息内容判断是否需要特殊提示
      if (result.message && result.message.includes('可能已经是最新状态')) {
        console.log('ℹ️ [useEditor] 文档可能已经是最新状态，无需强制保存')
      }
    } catch (error) {
      console.error('❌ [useEditor] 强制保存异常:', error)
      if (error instanceof Error) {
        throw error
      }
      throw createEditorError('强制保存时发生未知错误')
    }
  }

  /**
   * 重新加载编辑器
   */
  const reloadEditor = async (query?: EditorConfigQuery): Promise<void> => {
    destroyEditor()
    await initializeEditor(query)
  }

  return {
    // 状态
    docEditor: computed(() => docEditor.value),
    editorConfig: computed(() => editorConfig.value),
    docTitle: computed(() => docTitle.value),
    isEditorReady: computed(() => isEditorReady.value),
    isInitializing: computed(() => isInitializing.value),

    // 方法
    initializeEditor,
    destroyEditor,
    forceSave,
    reloadEditor,
    fetchEditorConfig,
  }
}
