# OnlyOffice 配置模板系统 - 使用指南

## 📖 快速开始

### 1. 系统初始化

```bash
# 启动 OnlyOffice 服务器
npm start

# 初始化配置模板数据库（如果还未运行）
npm run init-config-templates
```

### 2. 验证系统运行

访问测试页面验证系统正常：
```
http://localhost:3000/test-config-templates.html
```

### 3. 访问管理界面

```
http://localhost:3000/config-templates
```

## 🎯 核心功能使用

### 1. 模板管理

#### 查看所有模板
```javascript
// API 调用
GET /api/config-templates

// 响应示例
{
  "success": true,
  "data": [
    {
      "id": "default-edit",
      "name": "默认编辑版",
      "description": "标准的编辑器配置，包含基本编辑功能",
      "is_default": true,
      "is_active": true
    }
  ]
}
```

#### 获取特定模板
```javascript
// 通过 ID 获取
GET /api/config-templates/default-edit

// 通过名称获取（自动识别）
GET /api/config-templates/默认编辑版
```

#### 创建新模板
```javascript
POST /api/config-templates
Content-Type: application/json

{
  "name": "我的自定义模板",
  "description": "为特定场景定制的编辑器配置",
  "is_default": false,
  "items": [
    {
      "config_group": "permissions",
      "config_key": "edit",
      "config_value": "true",
      "value_type": "boolean",
      "description": "允许编辑文档"
    }
    // ... 更多配置项
  ]
}
```

### 2. 编辑器集成

#### 基本使用
```javascript
// 使用默认模板
/editor/文件ID

// 使用指定模板
/editor/文件ID?template=法务编辑版
```

#### 动态参数覆盖
```javascript
// 隐藏聊天功能
/editor/文件ID?template=默认编辑版&hideChat=true

// 设置只读模式
/editor/文件ID?template=默认编辑版&readonly=true

// 多个参数组合
/editor/文件ID?template=功能简化版&hideComments=true&hideChat=true
```

### 3. 模板操作

#### 复制模板
```javascript
POST /api/config-templates/源模板ID/copy
Content-Type: application/json

{
  "newName": "复制的模板名称",
  "newDescription": "基于原模板创建的副本"
}
```

#### 导出模板
```javascript
// 下载模板配置文件
GET /api/config-templates/模板ID/export
```

#### 导入模板
```javascript
POST /api/config-templates/import
Content-Type: application/json

{
  "name": "导入的模板",
  "description": "从外部导入的配置模板",
  "items": [...]
}
```

## 🔧 配置项详解

### 权限配置 (permissions)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| edit | boolean | true | 允许编辑文档 |
| download | boolean | true | 允许下载文档 |
| print | boolean | true | 允许打印文档 |
| comment | boolean | true | 允许添加评论 |
| chat | boolean | false | 允许聊天功能 |
| review | boolean | true | 允许审阅模式 |
| copy | boolean | true | 允许复制内容 |

### 界面自定义 (customization)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| compactHeader | boolean | false | 使用紧凑头部 |
| hideRightMenu | boolean | false | 隐藏右侧菜单 |
| forcesave | boolean | true | 启用强制保存 |
| autosave | boolean | true | 启用自动保存 |
| help | boolean | true | 显示帮助按钮 |
| about | boolean | true | 显示关于按钮 |
| zoom | number | 100 | 默认缩放比例 |
| uiTheme | string | theme-light | UI主题 |

### 用户设置 (user)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| id | string | user-1 | 用户ID |
| name | string | OnlyOffice用户 | 用户姓名 |
| group | string | editors | 用户组 |

### 服务器设置 (server)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| lang | string | zh | 界面语言 |
| region | string | zh-CN | 地区设置 |

## 🎨 预设模板说明

### 1. 默认编辑版 (default-edit)
- **适用场景**: 日常文档编辑
- **特点**: 包含所有基本编辑功能
- **权限**: 允许编辑、下载、打印、评论
- **界面**: 标准界面，显示所有菜单

### 2. 法务编辑版 (legal-edit)
- **适用场景**: 法务文档处理
- **特点**: 完整功能，包含审阅工具
- **权限**: 全部权限开启
- **界面**: 完整界面，包含所有专业工具

### 3. 员工只读版 (employee-readonly)
- **适用场景**: 普通员工查看文档
- **特点**: 仅允许查看，禁止编辑和下载
- **权限**: 仅查看权限
- **界面**: 简化界面，隐藏编辑工具

### 4. 功能简化版 (simple-edit)
- **适用场景**: 专注编辑，减少干扰
- **特点**: 基础编辑功能，隐藏聊天和评论
- **权限**: 编辑、下载、打印权限
- **界面**: 简洁界面，隐藏干扰功能

## 🔄 实际应用场景

### 场景1: 部门文档编辑
```javascript
// 法务部门使用完整功能模板
/editor/合同文档ID?template=法务编辑版

// 普通员工使用只读模板
/editor/政策文档ID?template=员工只读版
```

### 场景2: 文档类型差异化
```javascript
// 正式合同 - 启用审阅功能
/editor/合同ID?template=法务编辑版&review=true

// 内部文档 - 简化界面
/editor/内部文档ID?template=功能简化版
```

### 场景3: 临时权限调整
```javascript
// 临时给某用户下载权限
/editor/文档ID?template=员工只读版&download=true

// 演示模式 - 隐藏所有菜单
/editor/文档ID?template=功能简化版&hideRightMenu=true&compactHeader=true
```

## 🛠️ 开发者指南

### 1. 扩展配置项

在 `configTemplateService.js` 的 `getConfigGroups()` 方法中添加新的配置项：

```javascript
customization: {
  items: {
    // 添加新的配置项
    newFeature: { 
      type: 'boolean', 
      description: '新功能开关', 
      default: false 
    }
  }
}
```

### 2. 自定义模板逻辑

在 `buildEditorConfig()` 方法中添加自定义合并逻辑：

```javascript
// 应用特殊业务规则
if (template.name === '特殊模板') {
  // 自定义配置处理
  mergedConfig.customLogic = true;
}
```

### 3. 新增API端点

在 `routes/config-templates.js` 中添加新的路由：

```javascript
router.get('/templates/:id/statistics', async (req, res) => {
  // 获取模板使用统计
});
```

## 🚨 故障排除

### 问题1: 模板配置不生效
**症状**: 编辑器中看不到模板配置的效果
**解决方案**:
1. 检查模板ID是否正确
2. 验证配置项格式是否符合规范
3. 查看浏览器控制台错误信息

### 问题2: API返回404错误
**症状**: 访问配置模板API时返回404
**解决方案**:
1. 确认服务器已启动
2. 检查路由是否正确注册
3. 验证数据库连接是否正常

### 问题3: 配置模板列表为空
**症状**: 获取模板列表返回空数组
**解决方案**:
1. 运行数据库初始化脚本
2. 检查数据库表是否创建成功
3. 验证模板数据是否正确插入

## 📞 技术支持

### 日志查看
```bash
# 启动服务器并查看详细日志
npm start

# 测试配置模板API
node test-final.js
```

### 调试信息
- 检查浏览器开发者工具的Network标签
- 查看服务器控制台输出
- 验证数据库中的数据完整性

### 常用测试命令
```bash
# 测试API响应
curl http://localhost:3000/api/config-templates

# 测试特定模板
curl http://localhost:3000/api/config-templates/default-edit

# 测试配置分组
curl http://localhost:3000/api/config-templates/meta/groups
```

---

**最后更新**: 2025-01-28
**版本**: 1.0.0
**维护团队**: OnlyOffice 集成开发团队 