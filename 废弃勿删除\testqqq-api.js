const http = require('http');

// 测试配置历史API
function testHistoryAPI() {
  const options = {
    hostname: '*************',
    port: 3000,
    path: '/api/system-config/history?limit=5',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  console.log('🔍 测试 system-config/history API...');
  console.log(`URL: http://${options.hostname}:${options.port}${options.path}`);

  const req = http.request(options, (res) => {
    let data = '';
    
    console.log(`📊 响应状态码: ${res.statusCode}`);
    console.log(`📋 响应头:`, res.headers);

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        console.log('\n✅ API调用成功!');
        console.log('📄 响应数据:');
        console.log(JSON.stringify(jsonData, null, 2));
        
        if (jsonData.success && jsonData.data) {
          console.log(`\n📈 返回了 ${jsonData.data.length} 条历史记录`);
          if (jsonData.data.length > 0) {
            console.log('🎯 第一条记录结构:');
            const firstRecord = jsonData.data[0];
            console.log('- ID:', firstRecord.id ? '✅ 存在' : '❌ 缺失');
            console.log('- setting_key:', firstRecord.setting_key ? '✅ 存在' : '❌ 缺失');
            console.log('- new_value:', firstRecord.new_value !== undefined ? '✅ 存在' : '❌ 缺失');
            console.log('- changed_at:', firstRecord.changed_at ? '✅ 存在' : '❌ 缺失');
          }
        }
      } catch (error) {
        console.log('\n❌ JSON解析失败:');
        console.log('原始响应:', data);
        console.log('解析错误:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ 请求失败:', error.message);
  });

  req.end();
}

// 测试所有配置API
function testAllConfigsAPI() {
  const options = {
    hostname: '*************',
    port: 3000,
    path: '/api/system-config/all',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  console.log('\n🔍 测试 system-config/all API...');

  const req = http.request(options, (res) => {
    let data = '';
    
    console.log(`📊 响应状态码: ${res.statusCode}`);

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        console.log('\n✅ 获取所有配置成功!');
        console.log(`📈 共有 ${jsonData.data ? jsonData.data.length : 0} 个配置项`);
        
        if (jsonData.data && jsonData.data.length > 0) {
          console.log('🎯 配置项示例:');
          console.log('- 键名:', jsonData.data[0].setting_key);
          console.log('- 值:', jsonData.data[0].setting_value);
          console.log('- 更新时间:', jsonData.data[0].updated_at);
        }
      } catch (error) {
        console.log('\n❌ JSON解析失败:', error.message);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ 请求失败:', error.message);
  });

  req.end();
}

// 依次执行测试
console.log('🚀 开始API测试...\n');
testAllConfigsAPI();

setTimeout(() => {
  testHistoryAPI();
}, 1000); 