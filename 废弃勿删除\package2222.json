{"name": "onlyoffice-integration-system", "version": "2.0.0", "description": "OnlyOffice集成系统 - 架构过渡版本 (Vue 3 + Ant Design Pro + TypeScript)", "main": "server.js", "scripts": {"setup": "npm run setup:root && npm run setup:backend && npm run setup:frontend", "setup:root": "npm install", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:legacy": "npm run start", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "node server.js", "start:new": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "clean": "rimraf backend/dist frontend/dist backend/node_modules frontend/node_modules", "reset": "npm run clean && npm run setup"}, "keywords": ["onlyoffice", "vue3", "ant-design-pro", "typescript", "express", "document-management"], "author": "OnlyOffice Integration Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^7.6.0", "rimraf": "^5.0.10"}, "dependencies": {"@playwright/test": "^1.52.0", "axios": "^1.6.0", "bcrypt": "^6.0.0", "body-parser": "^1.20.0", "cors": "^2.8.5", "ejs": "^3.1.8", "express": "^4.18.2", "fs-extra": "^11.1.0", "jsonwebtoken": "^9.0.0", "multer": "^2.0.0", "mysql2": "^3.6.0", "onlyoffice-integration-system": "file:"}}