# OnlyOffice集成系统 - 配置管理架构说明

> **版本**: v2.0.0  
> **更新时间**: 2024-12-19  
> **架构**: 分层配置管理 + JWT双重分离  

## 🎯 架构概览

我们采用了分层配置管理架构，将配置分为三个层次：

```
┌─────────────────────────────────────────────┐
│            配置管理架构                        │
├─────────────────────────────────────────────┤
│  🔧 环境变量 (.env)                          │
│  └── 基础设施配置 (数据库、服务器、文件存储等)      │
├─────────────────────────────────────────────┤
│  🎛️ 配置模板系统 (config_templates)           │
│  └── OnlyOffice业务配置 (权限、界面、功能)      │
├─────────────────────────────────────────────┤
│  🔐 系统设置 (system_settings)               │
│  └── JWT配置 + 系统开关                      │
└─────────────────────────────────────────────┘
```

## 🏗️ 分层详解

### 1. 环境变量层 (.env)
**用途**: 基础设施配置，主要是连接信息和系统级设置

```bash
# 数据库连接
DB_HOST=*************
DB_PORT=3306
DB_NAME=onlyfile
DB_USER=onlyfile_user
DB_PASSWORD="0nlyF!le$ecure#123"

# API认证JWT (前后端认证)
JWT_SECRET="API-Auth-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV"
JWT_EXPIRES_IN=24h

# 应用基础配置
PORT=3000
FRONTEND_PORT=8080
NODE_ENV=development

# 文件存储
UPLOAD_PATH=./uploads
TMP_PATH=./tmp
MAX_FILE_SIZE=50MB
```

**特点**:
- ✅ 容器化友好
- ✅ 部署环境隔离
- ✅ 敏感信息保护
- ❌ 修改需要重启服务

### 2. 配置模板层 (config_templates + config_template_items)
**用途**: OnlyOffice编辑器的业务配置，支持多模板切换

**数据表结构**:
```sql
-- 配置模板主表
config_templates (
  id, name, description, is_default, is_active
)

-- 配置项详表  
config_template_items (
  id, template_id, config_group, config_key, 
  config_value, value_type, is_enabled
)
```

**示例配置**:
```json
{
  "permissions": {
    "download": true,
    "edit": true,
    "print": false
  },
  "customization": {
    "about": false,
    "comments": true,
    "help": true
  },
  "coEditing": {
    "mode": "fast",
    "change": true
  }
}
```

**特点**:
- ✅ 动态切换模板
- ✅ 用户级别配置
- ✅ 热更新无需重启
- ✅ 版本化管理

### 3. 系统设置层 (system_settings)
**用途**: JWT分离管理 + 系统开关配置

**主要配置项**:
```sql
-- OnlyOffice JWT配置 (与API JWT分离)
('jwt.onlyoffice.secret', 'OnlyOffice-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV')
('jwt.onlyoffice.header', 'Authorization')  
('jwt.onlyoffice.in_body', 'true')

-- 系统功能开关
('system.features.hot_reload', 'true')
('system.features.debug_mode', 'true')
('system.performance.enable_monitoring', 'true')
```

**特点**:
- ✅ JWT双重分离
- ✅ 实时配置更新
- ✅ 安全密钥管理
- ✅ 功能开关控制

## 🔐 JWT双重分离架构

### 问题背景
原来的JWT配置混用导致以下问题：
- 前后端认证和OnlyOffice文档服务器认证使用同一个JWT
- 配置来源不统一，难以管理
- 安全风险：一个密钥泄露影响整个系统

### 解决方案
实现JWT双重分离：

```
┌─────────────────────────┐    ┌─────────────────────────┐
│      API JWT            │    │   OnlyOffice JWT        │
├─────────────────────────┤    ├─────────────────────────┤
│ 用途: 前后端API认证      │    │ 用途: 文档服务器认证      │
│ 来源: 环境变量(.env)    │    │ 来源: 数据库配置         │
│ 密钥: JWT_SECRET       │    │ 密钥: jwt.onlyoffice.*  │
│ 管理: 运维手动配置      │    │ 管理: API动态更新       │
│ 更新: 需要重启服务      │    │ 更新: 实时生效          │
└─────────────────────────┘    └─────────────────────────┘
```

### JWT配置对比

| 项目 | API JWT | OnlyOffice JWT |
|------|---------|----------------|
| **用途** | 前后端API认证 | 文档服务器通信认证 |
| **配置来源** | 环境变量 | 数据库配置 |
| **密钥示例** | `API-Auth-R7CpjZbb...` | `OnlyOffice-R7CpjZbb...` |
| **过期时间** | 24小时 | 无过期 |
| **更新方式** | 修改.env重启 | API实时更新 |
| **安全级别** | 高（环境变量） | 中（数据库加密） |

## 🛠️ API接口说明

### JWT配置管理 API

```bash
# 获取JWT配置状态
GET /api/jwt-config/status

# 获取API JWT配置信息（不含密钥）
GET /api/jwt-config/api-jwt  

# 获取OnlyOffice JWT配置信息（不含密钥）
GET /api/jwt-config/onlyoffice-jwt

# 验证JWT配置完整性
GET /api/jwt-config/validate

# 生成推荐的JWT密钥
GET /api/jwt-config/generate-secret

# 更新OnlyOffice JWT配置
PUT /api/jwt-config/onlyoffice-config
{
  "secret": "new-secret-key",
  "header": "Authorization",
  "inBody": true
}

# 获取JWT配置概览
GET /api/jwt-config/overview
```

### 配置模板管理 API (已有)

```bash
# 获取配置模板列表
GET /api/config-templates

# 获取默认配置
GET /api/config-templates/default

# 切换配置模板
PUT /api/config-templates/{templateId}/activate
```

## ⚙️ 使用指南

### 开发环境配置

1. **初始化环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 配置数据库连接
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASSWORD=your-database-password

# 配置API JWT密钥
JWT_SECRET="API-Auth-YourUniqueSecretKey"
```

2. **初始化JWT配置**
```bash
# 系统启动时会自动创建默认OnlyOffice JWT配置
# 可通过API查看和更新
curl http://localhost:3000/api/jwt-config/status
```

3. **配置OnlyOffice模板**
```bash
# 使用默认配置模板
curl http://localhost:3000/api/config-templates/default

# 或创建自定义模板
curl -X POST http://localhost:3000/api/config-templates \
  -H "Content-Type: application/json" \
  -d '{"name": "Custom Template", "description": "自定义配置"}'
```

### 生产环境配置

1. **安全加固**
```bash
# 生成强JWT密钥
curl http://localhost:3000/api/jwt-config/generate-secret

# 更新生产环境密钥
JWT_SECRET="API-Auth-ProductionSecureKey64Characters"

# 更新OnlyOffice JWT
curl -X PUT http://localhost:3000/api/jwt-config/onlyoffice-config \
  -H "Content-Type: application/json" \
  -d '{"secret": "OnlyOffice-ProductionSecureKey64Characters"}'
```

2. **环境变量管理**
```bash
# 使用Docker Secrets或Kubernetes Secrets
echo "API-Auth-ProductionKey" | docker secret create jwt_secret -

# 或使用环境变量注入
docker run -e JWT_SECRET="API-Auth-ProductionKey" onlyoffice-app
```

### 运维监控

1. **配置健康检查**
```bash
# 验证JWT配置
curl http://localhost:3000/api/jwt-config/validate

# 检查配置概览
curl http://localhost:3000/api/jwt-config/overview
```

2. **日志监控**
```bash
# 查看JWT配置相关日志
docker logs onlyoffice-app | grep "JWT配置"

# 监控配置更新
tail -f logs/app.log | grep "配置已更新"
```

## 🔄 迁移指南

### 从旧版本迁移

1. **备份现有配置**
```sql
-- 备份system_settings表
CREATE TABLE system_settings_backup AS SELECT * FROM system_settings;

-- 备份config_templates相关表
CREATE TABLE config_templates_backup AS SELECT * FROM config_templates;
```

2. **执行数据库迁移**
```bash
# 运行迁移脚本
mysql -u user -p database < backend/src/database/migrations/001-enhance-system-settings.sql
```

3. **更新环境变量**
```bash
# 简化.env文件，移除业务配置
# 只保留基础设施配置
# 更新JWT密钥命名
```

4. **验证迁移结果**
```bash
# 检查服务启动
curl http://localhost:3000/api/health

# 验证JWT配置
curl http://localhost:3000/api/jwt-config/validate

# 验证配置模板
curl http://localhost:3000/api/config-templates
```

## 🚨 注意事项

### 安全建议
1. **JWT密钥管理**
   - API JWT和OnlyOffice JWT必须使用不同的密钥
   - 生产环境密钥长度建议至少64字符
   - 定期轮换JWT密钥（建议3-6个月）

2. **配置访问控制**
   - JWT配置API需要管理员权限
   - 敏感配置不在日志中暴露
   - 数据库连接信息严格保护

### 性能优化
1. **配置缓存**
   - JWT配置缓存5分钟自动刷新
   - 配置模板缓存可手动刷新
   - 数据库连接池复用

2. **热更新**
   - OnlyOffice配置支持热更新
   - 环境变量更改需要重启
   - 配置模板切换实时生效

### 故障排除
1. **常见问题**
   - JWT验证失败：检查密钥配置和时间同步
   - 配置不生效：检查缓存刷新和数据库连接
   - 模板加载失败：检查数据表完整性

2. **调试工具**
   - JWT配置验证API
   - 配置概览和状态API
   - 详细的错误日志记录

---

**🎉 恭喜！您已成功重构为分层配置管理架构！**

现在您可以：
- ✅ 在环境变量中管理基础设施配置
- ✅ 使用配置模板系统管理OnlyOffice业务配置
- ✅ 通过API动态管理JWT配置
- ✅ 享受JWT双重分离带来的安全提升 