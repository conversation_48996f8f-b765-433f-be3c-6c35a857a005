<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice文档系统 - 功能导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 5px;
        }

        header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .nav-card-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .nav-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-card h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .nav-card-content {
            color: #666;
            margin-bottom: 20px;
        }

        .nav-card-footer {
            display: flex;
            justify-content: space-between;
        }

        .nav-button {
            display: inline-block;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-button:hover {
            background-color: #2980b9;
        }

        .nav-button.secondary {
            background-color: #95a5a6;
        }

        .nav-button.secondary:hover {
            background-color: #7f8c8d;
        }

        .status-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-ready {
            background-color: #2ecc71;
            color: white;
        }

        .status-development {
            background-color: #f39c12;
            color: white;
        }

        .status-planned {
            background-color: #95a5a6;
            color: white;
        }

        footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #777;
            font-size: 14px;
            border-top: 1px solid #ddd;
        }

        @media (max-width: 768px) {
            .nav-card-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>OnlyOffice文档管理系统</h1>
            <p>功能导航中心</p>
        </header>

        <div class="nav-card-container">
            <!-- 文档管理 -->
            <div class="nav-card">
                <h2>文档管理 <span class="status-tag status-ready">已上线</span></h2>
                <div class="nav-card-content">
                    <p>上传、编辑和管理您的文档。支持多种文档格式，包括Word、Excel和PowerPoint。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="/" class="nav-button">进入文档管理</a>
                </div>
            </div>

            <!-- 模板管理 -->
            <div class="nav-card">
                <h2>模板管理 <span class="status-tag status-ready">已上线</span></h2>
                <div class="nav-card-content">
                    <p>创建和管理文档模板，快速生成标准化文档。支持模板启用/禁用和源文档管理。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="/template-manager.html" class="nav-button">进入模板管理</a>
                </div>
            </div>

            <!-- 模板创建 -->
            <div class="nav-card">
                <h2>从模板创建文档 <span class="status-tag status-ready">已上线</span></h2>
                <div class="nav-card-content">
                    <p>从已有模板快速创建新文档，提高工作效率。系统会自动复制模板内容并允许编辑。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="/#create-from-template" class="nav-button">创建新文档</a>
                </div>
            </div>

            <!-- OnlyOffice配置管理 -->
            <div class="nav-card">
                <h2>OnlyOffice配置管理 <span class="status-tag status-ready">已上线</span></h2>
                <div class="nav-card-content">
                    <p>统一管理OnlyOffice编辑器的各项配置设置。包括文档权限、界面设置、功能控制、布局设置等，提供可视化的配置管理界面。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="/weboffice-config.html" class="nav-button">进入配置管理</a>
                </div>
            </div>

            <!-- 配置模板管理 -->
            <div class="nav-card">
                <h2>配置模板管理 <span class="status-tag status-ready">已上线</span></h2>
                <div class="nav-card-content">
                    <p>管理OnlyOffice编辑器的配置模板。支持创建多种预设模板（默认编辑版、法务编辑版、员工只读版等），实现不同场景下的编辑器定制化配置。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="/config-templates" class="nav-button">进入模板管理</a>
                </div>
            </div>

            <!-- 配置模板测试 -->
            <div class="nav-card">
                <h2>配置模板测试 <span class="status-tag status-ready">已上线</span></h2>
                <div class="nav-card-content">
                    <p>测试配置模板的功能效果。验证各种配置模板的API接口、参数设置和实际效果，确保模板配置正确应用到编辑器中。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="/template-config-test.html" class="nav-button">进入测试页面</a>
                </div>
            </div>

            <!-- 用户管理 -->
            <div class="nav-card">
                <h2>用户管理 <span class="status-tag status-development">开发中</span></h2>
                <div class="nav-card-content">
                    <p>管理用户账户和权限设置。控制不同用户对系统功能和文档的访问权限。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="#" class="nav-button secondary">即将上线</a>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="nav-card">
                <h2>系统设置 <span class="status-tag status-planned">计划中</span></h2>
                <div class="nav-card-content">
                    <p>配置系统参数和集成选项。包括OnlyOffice编辑器设置、数据库配置和文件存储设置。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="#" class="nav-button secondary">开发规划中</a>
                </div>
            </div>

            <!-- 数据统计 -->
            <div class="nav-card">
                <h2>数据统计 <span class="status-tag status-planned">计划中</span></h2>
                <div class="nav-card-content">
                    <p>查看系统使用数据统计和文档访问分析。了解用户活动和文档使用情况。</p>
                </div>
                <div class="nav-card-footer">
                    <a href="#" class="nav-button secondary">开发规划中</a>
                </div>
            </div>
        </div>

        <footer>
            <p>© 2023-2025 OnlyOffice文档管理系统 | <a
                    href="https://github.com/yourusername/onlyoffice-doc-management">项目源码</a> | <a
                    href="/README.md">使用说明</a></p>
        </footer>
    </div>
</body>

</html>