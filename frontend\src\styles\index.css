/* 全局样式 - 明亮经典风格 */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  background: #f5f7fa;
  color: #2c3e50;
  line-height: 1.6;
}

* {
  box-sizing: border-box;
}

#app {
  min-height: 100vh;
}

/* 主色调配色方案 */
:root {
  --primary-color: #1976d2;
  --primary-hover: #1565c0;
  --primary-light: #e3f2fd;
  --secondary-color: #f8f9fa;
  --success-color: #388e3c;
  --warning-color: #f57c00;
  --error-color: #d32f2f;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #95a5a6;
  --border-color: #e8eaec;
  --background-light: #f5f7fa;
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 通用样式 */
.page-container {
  padding: 24px;
  margin: 0;
  background: var(--background-light);
  min-height: calc(100vh - 112px);
}

.page-header {
  margin-bottom: 24px;
  background: #ffffff;
  padding: 20px 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
}

.page-header .ant-page-header-heading-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 20px;
}

.page-header .ant-page-header-heading-sub-title {
  color: var(--text-secondary);
  font-size: 14px;
}

.content-wrapper {
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: box-shadow 0.3s ease;
}

.content-wrapper:hover {
  box-shadow: var(--card-shadow-hover);
}

/* Ant Design 组件样式覆盖 */
.ant-layout {
  background: var(--background-light);
}

.ant-layout-sider {
  background: #ffffff !important;
  border-right: 1px solid var(--border-color);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.ant-layout-header {
  background: #ffffff !important;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.ant-layout-content {
  background: var(--background-light);
}

.ant-layout-footer {
  background: #ffffff !important;
  border-top: 1px solid var(--border-color);
  color: var(--text-light);
}

/* 菜单样式 */
.ant-menu {
  border: none !important;
  background: transparent !important;
}

.ant-menu-item {
  border-radius: 0 !important;
  margin: 0 !important;
  padding-left: 20px !important;
  color: var(--text-primary) !important;
  border-left: 3px solid transparent !important;
  transition: all 0.3s ease !important;
}

.ant-menu-item:hover,
.ant-menu-item-selected {
  background: var(--primary-light) !important;
  color: var(--primary-color) !important;
  border-left-color: var(--primary-color) !important;
}

.ant-menu-item-icon {
  font-size: 16px !important;
}

/* 卡片样式 */
.ant-card {
  border-radius: 12px !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--card-shadow) !important;
  transition: all 0.3s ease !important;
}

.ant-card:hover {
  box-shadow: var(--card-shadow-hover) !important;
  transform: translateY(-2px);
}

.ant-card-head {
  border-bottom: 1px solid #f0f1f2 !important;
  padding: 20px 24px !important;
}

.ant-card-head-title {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.ant-card-body {
  padding: 24px !important;
}

/* 表格样式优化 */
.ant-table-wrapper {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.ant-table {
  border-radius: 12px !important;
}

.ant-table-thead>tr>th {
  background-color: #fafafa !important;
  font-weight: 600 !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.ant-table-tbody>tr:hover>td {
  background: var(--primary-light) !important;
}

.ant-table-tbody>tr>td {
  border-bottom: 1px solid #f0f1f2 !important;
  padding: 16px !important;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.ant-btn-primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3) !important;
}

.ant-btn-primary:hover {
  background: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4) !important;
}

.ant-btn-default {
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.ant-btn-default:hover {
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

/* 表单样式 */
.ant-form {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
}

.ant-input,
.ant-select-selector {
  border-radius: 6px !important;
  border-color: var(--border-color) !important;
  transition: all 0.3s ease !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}

/* 统计卡片样式 */
.status-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: linear-gradient(135deg, var(--primary-light) 0%, #bbdefb 100%);
  color: var(--primary-color);
}

.status-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.status-label {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 12px;
}

.status-trend {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  background: #e8f5e8;
  color: var(--success-color);
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.button-group .ant-btn {
  margin-right: 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 20px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
}

.search-form .ant-form-item {
  margin-bottom: 16px;
}

.search-form .ant-form-item:last-child {
  margin-bottom: 0;
}

/* 操作栏样式 */
.table-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
}

/* 状态标签样式 */
.status-tag {
  border-radius: 12px !important;
  font-size: 12px !important;
  padding: 2px 8px !important;
  font-weight: 500 !important;
  border: none !important;
}

.status-enabled {
  background: #e8f5e8 !important;
  color: var(--success-color) !important;
}

.status-disabled {
  background: #ffebee !important;
  color: var(--error-color) !important;
}

/* 面包屑样式 */
.ant-breadcrumb {
  color: var(--text-secondary);
}

.ant-breadcrumb a {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.ant-breadcrumb a:hover {
  color: var(--primary-color);
}

/* 分页样式 */
.ant-pagination {
  text-align: center;
  margin-top: 24px;
}

.ant-pagination-item {
  border-radius: 6px !important;
}

.ant-pagination-item-active {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .button-group {
    flex-direction: column;
  }

  .table-actions {
    flex-direction: column;
    gap: 16px;
  }

  .status-card {
    padding: 16px;
  }

  .search-form {
    padding: 16px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 加载动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* OnlyOffice编辑器样式优化 - 防止双滚动条 */
#editor-container {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
  position: relative !important;
}

#editor-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  overflow: hidden !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* 确保OnlyOffice编辑器内部不产生滚动条 */
.asc-window-content,
.asc-window-body {
  overflow: hidden !important;
}

/* 编辑器页面特殊样式 */
.editor-page .main-content {
  padding: 0 !important;
  overflow: hidden !important;
  height: 100% !important;
}

/* 确保编辑器容器占满整个空间 */
.editor-container,
.editor-iframe-container {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* OnlyOffice内部元素优化 */
#editor-container .asc-documenteditor,
#editor-container .asc-spreadsheeteditor,
#editor-container .asc-presentationeditor {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* 隐藏OnlyOffice内部可能的滚动条 */
#editor-container .ws-canvas-outer,
#editor-container .ws-canvas-area,
#editor-container .ws-scrollable-area {
  overflow: hidden !important;
  scrollbar-width: none !important;
  /* Firefox */
  -ms-overflow-style: none !important;
  /* IE 10+ */
}

#editor-container .ws-canvas-outer::-webkit-scrollbar,
#editor-container .ws-canvas-area::-webkit-scrollbar,
#editor-container .ws-scrollable-area::-webkit-scrollbar {
  display: none !important;
  /* Chrome, Safari, Opera */
}

/* 确保编辑器工具栏不会造成额外高度 */
#editor-container .asc-window-toolbar,
#editor-container .toolbar {
  flex-shrink: 0 !important;
}

/* 调整编辑区域 */
#editor-container .asc-editor-area {
  flex: 1 !important;
  overflow: hidden !important;
}