// 检查后端实际运行时的JWT配置
const jwt = require('jsonwebtoken');

// 模拟NestJS环境变量配置
const envVars = {
  JWT_SECRET: "R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV",
  JWT_EXPIRES_IN: "24h",
  JWT_ISSUER: "onlyoffice-nestjs",
  JWT_AUDIENCE: "onlyoffice-client"
};

console.log('=== 后端JWT配置检查 ===');
console.log('环境变量配置:');
console.log(JSON.stringify(envVars, null, 2));

// 模拟生成token的过程
const tokenPayload = {
  sub: "user-admin",
  username: "admin", 
  role: "super_admin",
  type: "access"
};

console.log('\n🔧 生成Token测试:');
console.log('Payload:', JSON.stringify(tokenPayload, null, 2));

try {
  // 使用与NestJS相同的配置生成token
  const testToken = jwt.sign(tokenPayload, envVars.JWT_SECRET, {
    expiresIn: envVars.JWT_EXPIRES_IN,
    issuer: envVars.JWT_ISSUER,
    audience: envVars.JWT_AUDIENCE
  });
  
  console.log('\n✅ 测试Token生成成功:');
  console.log('Token:', testToken);
  
  // 验证生成的token
  const verified = jwt.verify(testToken, envVars.JWT_SECRET, {
    issuer: envVars.JWT_ISSUER,
    audience: envVars.JWT_AUDIENCE
  });
  
  console.log('\n✅ 测试Token验证成功:');
  console.log('验证结果:', JSON.stringify(verified, null, 2));
  
} catch (error) {
  console.error('\n❌ 测试失败:', error.message);
}

// 测试现有的token
const existingToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NDkxODAzMTAsImV4cCI6MTc0OTI2NjcxMCwiaXNzIjoib25seW9mZmljZS1uZXN0anMiLCJhdWQiOiJvbmx5b2ZmaWNlLWNsaWVudCJ9.Rk3vd99oqmLQo94dqMDxpYZqVL5HZlZiFlVRzBdI0fw';

console.log('\n🔍 现有Token验证测试:');

// 尝试不同的密钥
const possibleSecrets = [
  "R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV",  // 当前配置的密钥
  "your-secret-key",                    // 默认密钥
  "default-secret-key",                 // 另一个默认密钥
];

for (const secret of possibleSecrets) {
  try {
    const verified = jwt.verify(existingToken, secret, {
      issuer: envVars.JWT_ISSUER,
      audience: envVars.JWT_AUDIENCE
    });
    console.log(`✅ 使用密钥 "${secret}" 验证成功!`);
    break;
  } catch (error) {
    console.log(`❌ 使用密钥 "${secret}" 验证失败: ${error.message}`);
  }
} 