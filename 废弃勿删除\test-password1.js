const bcrypt = require('bcrypt');
const mysql = require('mysql2/promise');

async function testPassword() {
  const connection = await mysql.createConnection({
    host: '*************',
    user: 'onlyfile_user',
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
  });

  // 获取admin用户的密码哈希
  const [users] = await connection.execute('SELECT username, password_hash FROM users WHERE username = ?', ['admin']);
  
  if (users.length === 0) {
    console.log('❌ 未找到admin用户');
    return;
  }
  
  const user = users[0];
  console.log('👤 用户名:', user.username);
  console.log('🔒 密码哈希:', user.password_hash);
  
  // 测试密码
  const testPassword = 'admin123';
  console.log('🧪 测试密码:', testPassword);
  
  try {
    const isValid = await bcrypt.compare(testPassword, user.password_hash);
    console.log('✅ 密码验证结果:', isValid ? '正确' : '错误');
    
    if (!isValid) {
      // 生成新的密码哈希
      console.log('\n🔧 生成新的密码哈希...');
      const newHash = await bcrypt.hash(testPassword, 10);
      console.log('🆕 新密码哈希:', newHash);
      
      // 更新密码
      await connection.execute('UPDATE users SET password_hash = ? WHERE username = ?', [newHash, 'admin']);
      console.log('✅ 密码已更新');
    }
  } catch (error) {
    console.error('❌ 密码验证失败:', error.message);
  }

  await connection.end();
}

testPassword().catch(console.error); 