# OnlyOffice系统架构类图 - 第四部分：数据模型和业务流程

## 数据模型关系图

```mermaid
erDiagram
    %% 系统配置
    SYSTEM_SETTINGS {
        varchar setting_key PK "配置键"
        text setting_value "配置值"
        text description "描述"
        timestamp updated_at "更新时间"
    }

    %% 文件存储表
    FILES {
        varchar id PK "文件ID (64位哈希)"
        varchar original_name "原始文件名"
        varchar storage_name "存储文件名"
        bigint file_size "文件大小"
        varchar mime_type "MIME类型"
        varchar extension "文件扩展名"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
        varchar last_modified_by "最后修改者"
        int version "版本号"
        boolean is_deleted "删除标记"
    }

    %% FileNet文档表
    FILENET_DOCUMENTS {
        varchar id PK "内部文档ID (UUID)"
        varchar fn_doc_id UK "FileNet文档ID"
        varchar original_name "原始文件名"
        bigint file_size "文件大小"
        varchar mime_type "MIME类型"
        varchar extension "文件扩展名"
        int version "当前版本号"
        varchar file_hash "文件哈希值"
        varchar created_by "创建者"
        varchar last_modified_by "最后修改者"
        varchar template_id FK "关联模板ID"
        timestamp uploaded_at "上传时间"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
        boolean is_deleted "软删除标记"
    }

    %% FileNet文档版本表
    FILENET_DOCUMENT_VERSIONS {
        varchar id PK "版本ID (UUID)"
        varchar doc_id FK "文档ID"
        varchar fn_doc_id "FileNet文档ID (此版本)"
        int version "版本号"
        varchar file_hash "文件哈希值"
        varchar modified_by "修改者"
        timestamp modified_at "修改时间"
        bigint file_size "文件大小"
        text comment "版本注释"
    }

    %% 模板分类表
    TEMPLATE_CATEGORIES {
        varchar id PK "分类ID (UUID)"
        varchar name "分类名称"
        varchar parent_id FK "父分类ID"
        text description "分类描述"
        int sort_order "排序序号"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    %% 模板表
    TEMPLATES {
        varchar id PK "模板ID (UUID)"
        varchar name "模板名称"
        varchar category_id FK "分类ID"
        varchar doc_id FK "关联文档ID"
        text description "模板描述"
        int current_version "当前版本"
        varchar created_by "创建者"
        timestamp created_at "创建时间"
        varchar updated_by "更新者"
        timestamp updated_at "更新时间"
        enum status "状态 (enabled/disabled)"
        boolean is_deleted "软删除标记"
    }

    %% 模板版本表
    TEMPLATE_VERSIONS {
        varchar id PK "版本ID (UUID)"
        varchar template_id FK "模板ID"
        int version "版本号"
        varchar doc_id FK "文档ID (此版本)"
        text comment "版本注释"
        varchar modified_by "修改者"
        timestamp modified_at "修改时间"
        varchar file_hash "文件哈希值"
    }

    %% 配置模板表
    CONFIG_TEMPLATES {
        varchar id PK "配置模板ID (UUID)"
        varchar name "配置模板名称"
        text description "配置描述"
        boolean is_default "是否默认配置"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    %% 配置模板项表
    CONFIG_TEMPLATE_ITEMS {
        varchar id PK "配置项ID (UUID)"
        varchar template_id FK "配置模板ID"
        varchar config_group "配置组"
        varchar config_key "配置键"
        text config_value "配置值"
        varchar value_type "值类型"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    %% 关系定义
    FILENET_DOCUMENTS ||--o{ FILENET_DOCUMENT_VERSIONS : "has versions"
    FILENET_DOCUMENTS ||--o| TEMPLATES : "used as template"
    TEMPLATE_CATEGORIES ||--o{ TEMPLATE_CATEGORIES : "parent-child"
    TEMPLATE_CATEGORIES ||--o{ TEMPLATES : "categorizes"
    TEMPLATES ||--|| FILENET_DOCUMENTS : "based on"
    TEMPLATES ||--o{ TEMPLATE_VERSIONS : "has versions"
    TEMPLATE_VERSIONS ||--|| FILENET_DOCUMENTS : "references"
    CONFIG_TEMPLATES ||--o{ CONFIG_TEMPLATE_ITEMS : "contains"
```

## 核心业务流程类图

```mermaid
classDiagram
    %% 文档编辑业务流程
    class DocumentEditingProcess {
        <<Business Process>>
        +startEditing(fileId: string): EditorConfig
        +saveDocument(callbackData: Object): SaveResult
        +closeDocument(fileId: string): boolean
        +handleCallback(body: Object): boolean
        +checkSaveStatus(fileId: string): SaveStatus
        -validateUser(token: string): boolean
        -generateFileKey(fileId: string): string
        -createBackup(fileId: string): boolean
    }

    %% 文档生命周期管理
    class DocumentLifecycle {
        <<Business Process>>
        +uploadDocument(file: File): DocumentInfo
        +createDocumentRecord(fileInfo: Object): string
        +updateDocumentVersion(fileId: string, version: int): boolean
        +deleteDocument(fileId: string): boolean
        +restoreDocument(fileId: string): boolean
        +archiveDocument(fileId: string): boolean
        -validateFileType(extension: string): boolean
        -calculateFileHash(content: Buffer): string
        -notifyVersionChange(fileId: string): void
    }

    %% 模板管理业务流程
    class TemplateManagement {
        <<Business Process>>
        +createTemplate(templateData: Object): Template
        +updateTemplate(templateId: string, data: Object): Template
        +deleteTemplate(templateId: string): boolean
        +createTemplateVersion(templateId: string): TemplateVersion
        +applyTemplate(templateId: string, targetDoc: string): boolean
        +validateTemplate(templateId: string): ValidationResult
        -copyDocumentForTemplate(docId: string): string
        -updateTemplateReferences(templateId: string): void
    }

    %% 配置管理业务流程
    class ConfigurationManagement {
        <<Business Process>>
        +createConfigTemplate(configData: Object): ConfigTemplate
        +updateConfigTemplate(templateId: string, items: Array): boolean
        +applyConfigToEditor(templateId: string, fileId: string): EditorConfig
        +mergeConfigurations(base: Object, overrides: Object): Object
        +validateConfiguration(config: Object): ValidationResult
        -resolveConfigInheritance(templateId: string): Object
        -sanitizeConfigValues(config: Object): Object
    }

    %% FileNet集成业务流程
    class FileNetIntegration {
        <<Business Process>>
        +uploadToFileNet(file: File): FileNetResponse
        +downloadFromFileNet(fnDocId: string): File
        +updateFileNetDocument(fnDocId: string, content: Buffer): boolean
        +getFileNetMetadata(fnDocId: string): Metadata
        +syncVersions(localDocId: string): SyncResult
        -authenticateFileNet(): boolean
        -handleFileNetError(error: Error): void
        -retryOperation(operation: Function, maxRetries: int): any
    }

    %% 版本控制业务流程
    class VersionControl {
        <<Business Process>>
        +createVersion(docId: string, comment: string): Version
        +getVersionHistory(docId: string): Array~Version~
        +revertToVersion(docId: string, version: int): boolean
        +compareVersions(docId: string, v1: int, v2: int): Comparison
        +mergeVersions(docId: string, versions: Array): MergeResult
        -calculateVersionDiff(v1: Version, v2: Version): Diff
        -validateVersionIntegrity(version: Version): boolean
    }

    %% 用户权限管理
    class PermissionManagement {
        <<Business Process>>
        +checkEditPermission(userId: string, docId: string): boolean
        +checkViewPermission(userId: string, docId: string): boolean
        +grantPermission(userId: string, docId: string, permission: string): boolean
        +revokePermission(userId: string, docId: string, permission: string): boolean
        +getDocumentPermissions(docId: string): Array~Permission~
        -validateUserToken(token: string): User
        -logPermissionChange(userId: string, docId: string, action: string): void
    }

    %% 业务流程关系
    DocumentEditingProcess --> DocumentLifecycle : uses
    DocumentEditingProcess --> ConfigurationManagement : applies config
    DocumentEditingProcess --> PermissionManagement : checks permissions
    DocumentEditingProcess --> VersionControl : manages versions
    
    DocumentLifecycle --> FileNetIntegration : syncs with FileNet
    DocumentLifecycle --> VersionControl : creates versions
    
    TemplateManagement --> DocumentLifecycle : creates template docs
    TemplateManagement --> ConfigurationManagement : uses config templates
    
    ConfigurationManagement --> PermissionManagement : configures permissions
    
    FileNetIntegration --> VersionControl : syncs versions
    
    note for DocumentEditingProcess "核心文档编辑流程\n处理OnlyOffice编辑器集成"
    note for DocumentLifecycle "文档完整生命周期管理\n从创建到删除的全过程"
    note for TemplateManagement "模板创建和管理\n支持文档模板化"
    note for ConfigurationManagement "编辑器配置管理\n动态配置OnlyOffice编辑器"
    note for FileNetIntegration "FileNet系统集成\n企业文档管理集成"
    note for VersionControl "版本控制系统\n文档版本历史管理"
    note for PermissionManagement "权限控制系统\n用户访问权限管理"
```

## 数据流程图

```mermaid
flowchart TD
    %% 文档编辑流程
    A[用户请求编辑文档] --> B{检查权限}
    B -->|有权限| C[生成编辑器配置]
    B -->|无权限| D[返回权限错误]
    
    C --> E[加载OnlyOffice编辑器]
    E --> F[用户编辑文档]
    F --> G[OnlyOffice发送保存回调]
    
    G --> H[验证回调令牌]
    H --> I[下载文档内容]
    I --> J[上传到FileNet]
    J --> K[更新数据库记录]
    K --> L[创建新版本记录]
    L --> M[返回保存成功]
    
    %% 模板应用流程
    N[选择模板] --> O[获取模板配置]
    O --> P[合并用户配置]
    P --> Q[生成编辑器配置]
    Q --> E
    
    %% 版本管理流程
    R[版本变更事件] --> S[计算文件哈希]
    S --> T[检查重复版本]
    T -->|不重复| U[创建版本记录]
    T -->|重复| V[跳过版本创建]
    U --> W[更新文档版本号]
    
    %% 配置管理流程
    X[配置模板创建] --> Y[验证配置项]
    Y --> Z[保存配置模板]
    Z --> AA[应用到编辑器]
    
    style A fill:#e1f5fe
    style G fill:#fff3e0
    style K fill:#f3e5f5
    style N fill:#e8f5e8
```

## 核心实体状态图

```mermaid
stateDiagram-v2
    %% 文档状态流转
    [*] --> 已上传 : uploadDocument()
    已上传 --> 编辑中 : startEditing()
    编辑中 --> 保存中 : saveCallback()
    保存中 --> 已保存 : saveComplete()
    已保存 --> 编辑中 : continueEditing()
    已保存 --> 已删除 : deleteDocument()
    已删除 --> [*]
    
    %% 模板状态流转
    state 模板管理 {
        [*] --> 模板草稿 : createTemplate()
        模板草稿 --> 模板启用 : enableTemplate()
        模板启用 --> 模板禁用 : disableTemplate()
        模板禁用 --> 模板启用 : enableTemplate()
        模板启用 --> 模板已删除 : deleteTemplate()
        模板禁用 --> 模板已删除 : deleteTemplate()
        模板已删除 --> [*]
    }
    
    %% 版本状态流转
    state 版本控制 {
        [*] --> 版本1 : initialVersion()
        版本1 --> 版本2 : createNewVersion()
        版本2 --> 版本3 : createNewVersion()
        版本3 --> 版本N : createNewVersion()
        版本N --> 版本1 : revertToVersion()
    }
```

## 系统数据模型特点

### 1. 数据模型设计原则
- **分层存储**: 区分本地文件存储和FileNet企业存储
- **版本管理**: 完整的文档版本历史记录
- **软删除**: 支持数据恢复的软删除机制
- **关系完整性**: 完整的外键约束保证数据一致性

### 2. 业务流程特点
- **异步处理**: 文档保存采用异步回调机制
- **事务控制**: 关键操作使用数据库事务保证一致性
- **错误恢复**: 支持操作重试和错误恢复机制
- **权限控制**: 细粒度的用户权限管理

### 3. 数据一致性保证
- **原子操作**: 文档上传和记录创建的原子性
- **级联更新**: 相关记录的级联更新机制
- **数据验证**: 多层数据验证确保数据质量
- **备份机制**: 重要操作的备份和恢复

### 4. 性能优化设计
- **索引优化**: 关键查询字段的索引设计
- **分页查询**: 大数据量的分页处理
- **缓存策略**: 配置和元数据的缓存机制
- **异步队列**: 耗时操作的异步队列处理

### 5. 扩展性设计
- **插件架构**: 支持功能模块的插件式扩展
- **配置驱动**: 通过配置模板驱动编辑器行为
- **API开放**: 完整的REST API支持第三方集成
- **微服务友好**: 服务层设计支持微服务拆分 