import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Param, 
  Query, 
  Body, 
  HttpException, 
  HttpStatus,
  Res,
  Put
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { DocumentService } from '../services/document.service';

/**
 * 文档管理控制器
 * 提供文档管理的RESTful API接口
 * 迁移自原有的文档路由系统
 */
@ApiTags('文档管理')
@Controller('documents')
export class DocumentController {
  constructor(private documentService: DocumentService) {}

  /**
   * 获取文档列表
   * GET /api/documents
   */
  @Get()
  @ApiOperation({ 
    summary: '获取文档列表',
    description: '获取系统中的文档列表，支持分页和搜索'
  })
  @ApiQuery({ name: 'page', required: false, description: '页码（从1开始）', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 20 })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词', example: '合同' })
  @ApiQuery({ name: 'extension', required: false, description: '文件扩展名', example: 'docx' })
  @ApiResponse({ 
    status: 200, 
    description: '获取文档列表成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'doc-uuid-123' },
              name: { type: 'string', example: '合同模板.docx' },
              type: { type: 'string', example: 'docx' },
              size: { type: 'number', example: 1024000 },
              lastModified: { type: 'string', format: 'date-time' },
              url: { type: 'string', example: '/api/documents/doc-uuid-123' },
              editUrl: { type: 'string', example: '/editor/doc-uuid-123' },
              version: { type: 'number', example: 1 },
              createdAt: { type: 'string', format: 'date-time' },
              createdBy: { type: 'string', example: 'user123' }
            }
          }
        },
        message: { type: 'string', example: '获取文档列表成功' }
      }
    }
  })
  async getDocuments(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('extension') extension?: string,
  ) {
    try {
      const options: Record<string, unknown> = {};
      
      // 处理分页参数
      if (page && limit) {
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 20;
        options.limit = limitNum;
        options.offset = (pageNum - 1) * limitNum;
      }
      
      // 处理搜索参数
      if (search) {
        options.search = search;
      }
      
      if (extension) {
        options.extension = extension;
      }
      
      const result = await this.documentService.getDocumentList(options);
      
      return {
        success: true,
        data: result.data,
        pagination: {
          current: result.page,
          pageSize: result.limit,
          total: result.total
        },
        total: result.total, // 兼容旧格式
        message: '获取文档列表成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: '获取文档列表失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 直接获取文档内容（用于OnlyOffice访问）
   * GET /api/documents/:id
   * 注意：这个路由必须在其他更具体的路由之前定义
   */
  @Get(':id')
  @ApiOperation({ 
    summary: '获取文档内容',
    description: '直接获取文档的二进制内容，供OnlyOffice等客户端访问使用'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiResponse({ 
    status: 200, 
    description: '文档内容获取成功',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async getDocumentContent(
    @Param('id') documentId: string,
    @Res() res: Response
  ) {
    try {
      console.log(`[DocumentController] 直接访问文档内容: ${documentId}`);
      
      // 设置CORS头，允许OnlyOffice服务器访问
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      
      // 检查是否是UUID格式的文档ID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(documentId)) {
        throw new HttpException('无效的文档ID格式', HttpStatus.BAD_REQUEST);
      }
      
      // 1. 获取文档信息
      const document = await this.documentService.getDocumentById(documentId);
      
      if (!document) {
        throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
      }
      
      console.log(`[DocumentController] 找到文档: ${document.name}, FileNet ID: ${document.fnDocId}`);
      
      // 2. 从FileNet获取文档内容
      if (!document.fnDocId) {
        throw new HttpException('文档未关联到FileNet，无法访问', HttpStatus.BAD_REQUEST);
      }
      
      const downloadResult = await this.documentService.downloadFromFileNet(document.fnDocId);
      
      if (!downloadResult || !downloadResult.stream) {
        throw new HttpException('从FileNet获取文档内容失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      
      // 处理文件名编码问题
      let fileName = document.name;
      try {
        if (/%[0-9A-F]{2}/i.test(fileName)) {
          const decodedName = decodeURIComponent(fileName);
          if (decodedName !== fileName) {
            console.log(`[DocumentController] 文件名URL解码: ${fileName} -> ${decodedName}`);
            fileName = decodedName;
          }
        }
      } catch (e) {
        console.warn(`[DocumentController] 文件名解码失败: ${e.message}`);
      }
      
      // 3. 设置响应头
      res.set({
        'Content-Type': document.mimeType || 'application/octet-stream',
        'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`,
        'Cache-Control': 'no-cache',
        'Last-Modified': new Date(document.updatedAt).toUTCString(),
      });
      
      // 如果有文件大小信息，设置Content-Length
      if (document.size) {
        res.set('Content-Length', document.size.toString());
      }
      
      console.log(`[DocumentController] 提供文档内容服务: ${fileName}, 内容类型: ${document.mimeType}`);
      
      // 4. 直接pipe流到响应
      downloadResult.stream.pipe(res);
      
    } catch (error) {
      console.error(`[DocumentController] 获取文档内容失败: ${error.message}`, error.stack);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        {
          success: false,
          message: '获取文档内容失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文档版本历史
   * GET /api/documents/:id/versions
   */
  @Get(':id/versions')
  @ApiOperation({ 
    summary: '获取文档版本历史',
    description: '获取指定文档的所有版本信息'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiResponse({ 
    status: 200, 
    description: '获取版本历史成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: '版本ID' },
              doc_id: { type: 'string', description: '文档ID' },
              fn_doc_id: { type: 'string', description: 'FileNet文档ID' },
              version: { type: 'number', description: '版本号' },
              file_hash: { type: 'string', description: '文件哈希' },
              modified_by: { type: 'string', description: '修改者' },
              modified_at: { type: 'string', description: '修改时间' },
              file_size: { type: 'number', description: '文件大小' },
              comment: { type: 'string', description: '版本说明' }
            }
          }
        },
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async getDocumentVersions(@Param('id') documentId: string) {
    try {
      console.log(`[DocumentController] 获取文档版本历史: ${documentId}`);
      
      // 检查文档是否存在
      const document = await this.documentService.getDocumentById(documentId);
      if (!document) {
        throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
      }
      
      const versions = await this.documentService.getDocumentVersions(documentId);
      
      return {
        success: true,
        data: versions,
        message: '获取版本历史成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`[DocumentController] 获取版本历史失败: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        {
          success: false,
          message: '获取版本历史失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文档特定版本的内容
   * GET /api/documents/:id/versions/:version
   */
  @Get(':id/versions/:version')
  @ApiOperation({ 
    summary: '获取文档特定版本内容',
    description: '下载指定文档的特定版本内容'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiParam({ name: 'version', description: '版本号', example: '2' })
  @ApiResponse({ 
    status: 200, 
    description: '获取版本内容成功',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档或版本不存在' })
  async getDocumentVersionContent(
    @Param('id') documentId: string,
    @Param('version') versionParam: string,
    @Res() res: Response
  ) {
    try {
      const version = parseInt(versionParam, 10);
      if (isNaN(version) || version < 1) {
        throw new HttpException('无效的版本号', HttpStatus.BAD_REQUEST);
      }
      
      console.log(`[DocumentController] 获取文档版本内容: ${documentId} v${version}`);
      
      // 设置CORS头
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      
      const versionData = await this.documentService.getDocumentVersionContent(documentId, version);
      
      if (!versionData || !versionData.content || !versionData.content.stream) {
        throw new HttpException('版本内容获取失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      
             // 处理文件名 - 从原始文件名中提取扩展名
       const originalName = versionData.documentInfo.name || 'document';
       const extensionMatch = originalName.match(/\.([^.]+)$/);
       const extension = extensionMatch ? extensionMatch[1] : 'docx';
       const baseName = originalName.replace(/\.[^/.]+$/, '');
       const fileName = `${baseName}_v${version}.${extension}`;
       
       // 设置响应头
       res.set({
         'Content-Type': versionData.documentInfo.mimeType || 'application/octet-stream',
         'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`,
         'Cache-Control': 'no-cache',
         'Last-Modified': new Date(versionData.versionInfo.modified_at as string).toUTCString(),
       });
      
      // 如果有文件大小信息，设置Content-Length
      if (versionData.versionInfo.file_size) {
        res.set('Content-Length', versionData.versionInfo.file_size.toString());
      }
      
      console.log(`[DocumentController] 提供版本内容服务: ${fileName}`);
      
      // 直接pipe流到响应
      versionData.content.stream.pipe(res);
      
    } catch (error) {
      console.error(`[DocumentController] 获取版本内容失败: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        {
          success: false,
          message: '获取版本内容失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文档OnlyOffice配置
   * GET /api/documents/:id/config
   */
  @Get(':id/config')
  @ApiOperation({ 
    summary: '获取文档OnlyOffice配置',
    description: '获取用于OnlyOffice编辑器的文档配置信息'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiResponse({ 
    status: 200, 
    description: '获取文档配置成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            document: {
              type: 'object',
              properties: {
                fileType: { type: 'string', example: 'docx' },
                key: { type: 'string', example: 'doc-uuid-123-1234567890' },
                title: { type: 'string', example: '合同模板.docx' },
                url: { type: 'string', example: 'http://localhost:3000/api/documents/doc-uuid-123/download' },
                permissions: { type: 'object' }
              }
            },
            documentType: { type: 'string', example: 'word' },
            editorConfig: { type: 'object' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async getDocumentConfig(@Param('id') documentId: string) {
    try {
      const config = await this.documentService.getDocumentConfig(documentId);
      
      return {
        success: true,
        data: config,
        message: '获取文档配置成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error.message === '文档不存在') {
        throw new HttpException(
          {
            success: false,
            message: '文档不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          success: false,
          message: '获取文档配置失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 文档下载接口
   * GET /api/documents/:id/download
   */
  @Get(':id/download')
  @ApiOperation({ 
    summary: '下载文档',
    description: '下载指定的文档文件，供OnlyOffice或用户下载使用'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiResponse({ 
    status: 200, 
    description: '文档下载成功',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async downloadDocument(
    @Param('id') documentId: string,
    @Res() res: Response
  ) {
    try {
      console.log(`[DocumentController] 下载文档请求: ${documentId}`);
      
      // 设置CORS头，允许OnlyOffice服务器访问
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      
      // 1. 获取文档信息
      const document = await this.documentService.getDocumentById(documentId);
      
      if (!document) {
        throw new HttpException('文档不存在', HttpStatus.NOT_FOUND);
      }
      
      console.log(`[DocumentController] 找到文档: ${document.name}, FileNet ID: ${document.fnDocId}`);
      
      // 2. 从FileNet获取文档内容
      if (!document.fnDocId) {
        throw new HttpException('文档未关联到FileNet，无法下载', HttpStatus.BAD_REQUEST);
      }
      
      const downloadResult = await this.documentService.downloadFromFileNet(document.fnDocId);
      
      if (!downloadResult || !downloadResult.stream) {
        throw new HttpException('从FileNet下载文档失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      
      // 处理文件名编码问题
      let fileName = document.name;
      try {
        if (/%[0-9A-F]{2}/i.test(fileName)) {
          const decodedName = decodeURIComponent(fileName);
          if (decodedName !== fileName) {
            console.log(`[DocumentController] 文件名URL解码: ${fileName} -> ${decodedName}`);
            fileName = decodedName;
          }
        }
      } catch (e) {
        console.warn(`[DocumentController] 文件名解码失败: ${e.message}`);
      }
      
      // 3. 设置响应头并返回文档流
      res.set({
        'Content-Type': document.mimeType || 'application/octet-stream',
        'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`,
        'Cache-Control': 'no-cache',
        'Last-Modified': new Date(document.updatedAt).toUTCString(),
      });
      
      // 如果有文件大小信息，设置Content-Length
      if (document.size) {
        res.set('Content-Length', document.size.toString());
      }
      
      console.log(`[DocumentController] 提供文档下载服务: ${fileName}, 内容类型: ${document.mimeType}`);
      
      // 直接将FileNet的流pipe到响应
      downloadResult.stream.pipe(res);
      
      // 不返回任何值，让流直接处理响应
      return;
      
    } catch (error) {
      console.error(`[DocumentController] 下载文档失败: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      if (error.message === '文档不存在') {
        throw new HttpException(
          {
            success: false,
            message: '文档不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      throw new HttpException(
        {
          success: false,
          message: '下载文档失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新文档信息
   * PUT /api/documents/:id/update-info
   */
  @Put(':id/update-info')
  @ApiOperation({ 
    summary: '更新文档信息',
    description: '更新文档的基本信息，如名称等'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiBody({
    description: '文档更新数据',
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          example: '新的文档名称.docx', 
          description: '文档名称（可选）' 
        }
      }
    }
  })
  @ApiResponse({ 
    status: 200, 
    description: '文档更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'doc-uuid-123' },
            name: { type: 'string', example: '新的文档名称.docx' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        message: { type: 'string', example: '文档更新成功' }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  @ApiResponse({ status: 400, description: '参数错误' })
  async updateDocument(
    @Param('id') documentId: string,
    @Body() updateData: { name?: string }
  ) {
    try {
      // 检查文档是否存在
      const document = await this.documentService.getDocumentById(documentId);
      if (!document) {
        throw new HttpException(
          {
            success: false,
            message: '文档不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }

      // 验证参数
      if (!updateData || Object.keys(updateData).length === 0) {
        throw new HttpException(
          {
            success: false,
            message: '请提供要更新的字段',
            timestamp: new Date().toISOString()
          },
          HttpStatus.BAD_REQUEST
        );
      }

      // 验证文档名称
      if (updateData.name !== undefined) {
        if (!updateData.name || updateData.name.trim().length === 0) {
          throw new HttpException(
            {
              success: false,
              message: '文档名称不能为空',
              timestamp: new Date().toISOString()
            },
            HttpStatus.BAD_REQUEST
          );
        }

        // 检查文件名长度限制
        if (updateData.name.length > 255) {
          throw new HttpException(
            {
              success: false,
              message: '文档名称不能超过255个字符',
              timestamp: new Date().toISOString()
            },
            HttpStatus.BAD_REQUEST
          );
        }
      }

      // 更新文档信息
      const updatedDocument = await this.documentService.updateDocumentInfo(documentId, updateData);
      
      return {
        success: true,
        data: updatedDocument,
        message: '文档更新成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '更新文档失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除文档
   * DELETE /api/documents/:id
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: '删除文档',
    description: '删除指定的文档（软删除）'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiResponse({ 
    status: 200, 
    description: '文档删除成功'
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async deleteDocument(@Param('id') documentId: string) {
    try {
      const success = await this.documentService.deleteDocument(documentId);
      
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: '文档不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      return {
        success: true,
        data: true,
        message: '文档删除成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '删除文档失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 设置用户关闭意图
   * PUT /api/documents/:id/close-intent
   */
  @Put(':id/close-intent')
  @ApiOperation({ 
    summary: '设置用户关闭意图',
    description: '设置用户的关闭意图：save(保存并关闭) 或 no-save(直接关闭不保存)'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiBody({
    description: '关闭意图',
    schema: {
      type: 'object',
      properties: {
        intent: {
          type: 'string',
          enum: ['save', 'no-save'],
          description: '用户意图：save表示保存并关闭，no-save表示直接关闭不保存'
        }
      },
      required: ['intent']
    }
  })
  @ApiResponse({ 
    status: 200, 
    description: '设置关闭意图成功'
  })
  @ApiResponse({ status: 400, description: '参数错误' })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async setCloseIntent(
    @Param('id') documentId: string, 
    @Body() body: { intent: 'save' | 'no-save' }
  ) {
    try {
      if (!body.intent || !['save', 'no-save'].includes(body.intent)) {
        throw new HttpException(
          {
            success: false,
            message: 'intent参数必须为save或no-save',
            timestamp: new Date().toISOString()
          },
          HttpStatus.BAD_REQUEST
        );
      }

      // 检查文档是否存在
      const document = await this.documentService.getDocumentById(documentId);
      if (!document) {
        throw new HttpException(
          {
            success: false,
            message: '文档不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }

      // 设置用户关闭意图
      this.documentService.setUserCloseIntent(documentId, body.intent);
      
      return {
        success: true,
        data: { documentId, intent: body.intent },
        message: `已设置文档关闭意图为: ${body.intent}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '设置关闭意图失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文档保存状态
   * GET /api/documents/:id/save-status
   */
  @Get(':id/save-status')
  @ApiOperation({ 
    summary: '获取文档保存状态',
    description: '获取文档最近的保存时间和版本信息，用于监控保存进度'
  })
  @ApiParam({ name: 'id', description: '文档ID', example: 'doc-uuid-123' })
  @ApiResponse({ 
    status: 200, 
    description: '获取保存状态成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            documentId: { type: 'string' },
            version: { type: 'number' },
            lastModified: { type: 'string' },
            fileSize: { type: 'number' },
            saveTimestamp: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '文档不存在' })
  async getSaveStatus(@Param('id') documentId: string) {
    try {
      const document = await this.documentService.getDocumentById(documentId);
      
      if (!document) {
        throw new HttpException(
          {
            success: false,
            message: '文档不存在',
            timestamp: new Date().toISOString()
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      return {
        success: true,
        data: {
          documentId: document.id,
          version: document.version || 1,
          lastModified: document.updatedAt,
          fileSize: document.size,
          saveTimestamp: new Date(document.updatedAt).getTime()
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: '获取保存状态失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  /**
   * 创建文档记录
   * POST /api/documents
   */
  @Post()
  @ApiOperation({ 
    summary: '创建文档记录',
    description: '在数据库中创建新的文档记录'
  })
  @ApiBody({
    description: '文档数据',
    schema: {
      type: 'object',
      required: ['originalName'],
      properties: {
        originalName: { type: 'string', example: '新建合同.docx', description: '原始文件名' },
        fileSize: { type: 'number', example: 1024000, description: '文件大小（字节）' },
        mimeType: { type: 'string', example: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
        extension: { type: 'string', example: 'docx', description: '文件扩展名' },
        fnDocId: { type: 'string', example: 'filenet-doc-id-123', description: 'FileNet文档ID' },
        templateId: { type: 'string', example: 'template-uuid-123', description: '模板ID' },
        createdBy: { type: 'string', example: 'user123', description: '创建者' }
      }
    }
  })
  @ApiResponse({ 
    status: 201, 
    description: '文档记录创建成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: { type: 'string', example: 'doc-uuid-123', description: '文档ID' },
        message: { type: 'string', example: '文档记录创建成功' }
      }
    }
  })
  async createDocument(@Body() documentData: Record<string, unknown>) {
    try {
      const documentId = await this.documentService.createDocumentRecord(documentData);
      
      return {
        success: true,
        data: documentId,
        message: '文档记录创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: '创建文档记录失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 