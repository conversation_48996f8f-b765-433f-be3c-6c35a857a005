import { Module } from '@nestjs/common';
import { EditorController } from './controllers/editor.controller';
import { EditorService } from './services/editor.service';
import { DocumentsModule } from '../documents/documents.module';
import { ConfigModule } from '../config/config.module';

/**
 * OnlyOffice编辑器模块
 * 
 * @description 提供OnlyOffice文档编辑功能，包括：
 * - 编辑器配置生成
 * - 文档保存回调处理
 * - 保存状态检查
 * - 强制保存功能
 * 
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */
@Module({
  imports: [
    DocumentsModule,
    ConfigModule,
  ],
  controllers: [EditorController],
  providers: [EditorService],
  exports: [EditorService],
})
export class EditorModule {} 