import { 
  Controller, 
  Get, 
  Put, 
  Post, 
  Param, 
  Body, 
  Query,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { SystemConfigService } from '../services/system-config.service';

/**
 * 系统配置管理控制器
 * @description 提供系统配置的查看和管理功能
 */
@ApiTags('系统配置管理')
@Controller('system-config')
export class SystemConfigController {
  constructor(
    private readonly systemConfigService: SystemConfigService,
  ) {}

  /**
   * 获取所有配置项
   */
  @Get('all')
  @ApiOperation({ summary: '获取所有配置项' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取所有配置项成功',
  })
  async getAllConfigs() {
    const configs = await this.systemConfigService.getAllConfigs();
    return {
      success: true,
      data: configs,
      message: '获取配置成功',
    };
  }

  /**
   * 根据分类获取配置项
   */
  @Get('category/:category')
  @ApiOperation({ summary: '根据分类获取配置项' })
  @ApiParam({ name: 'category', description: '配置分类（如：jwt, onlyoffice, filenet等）' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取分类配置成功',
  })
  async getConfigsByCategory(@Param('category') category: string) {
    const configs = await this.systemConfigService.getConfigsByCategory(category);
    return {
      success: true,
      data: configs,
      message: `获取${category}分类配置成功`,
    };
  }

  /**
   * 批量更新配置项
   */
  @Put('batch')
  @ApiOperation({ summary: '批量更新配置项' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量更新配置成功',
  })
  async batchUpdateConfigs(
    @Body() data: {
      configs: Array<{
        setting_key: string;
        setting_value: string;
        description?: string;
      }>;
    }
  ) {
    await this.systemConfigService.batchUpdateConfigs(data.configs);
    return {
      success: true,
      message: '批量更新配置成功',
    };
  }

  /**
   * 重置为默认配置
   */
  @Post('reset')
  @ApiOperation({ summary: '重置为默认配置' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '重置配置成功',
  })
  async resetToDefaults() {
    await this.systemConfigService.resetToDefaults();
    return {
      success: true,
      message: '配置已重置为默认值',
    };
  }

  /**
   * 获取配置变更历史
   */
  @Get('history')
  @ApiOperation({ summary: '获取配置变更历史' })
  @ApiQuery({ name: 'limit', required: false, description: '返回记录数量限制' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取配置历史成功',
  })
  async getConfigHistory(@Query('limit') limit = 50) {
    const history = await this.systemConfigService.getConfigHistory(limit);
    return {
      success: true,
      data: history,
      message: '获取配置历史成功',
    };
  }

  /**
   * 导出配置
   */
  @Get('export')
  @ApiOperation({ summary: '导出配置' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '导出配置成功',
  })
  async exportConfigs() {
    const configs = await this.systemConfigService.exportConfigs();
    return {
      success: true,
      data: configs,
      message: '导出配置成功',
    };
  }

  /**
   * 获取单个配置项
   */
  @Get(':key')
  @ApiOperation({ summary: '获取单个配置项' })
  @ApiParam({ name: 'key', description: '配置键名' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取配置项成功',
  })
  async getConfig(@Param('key') key: string) {
    const config = await this.systemConfigService.getConfig(key);
    return {
      success: true,
      data: config,
      message: '获取配置项成功',
    };
  }

  /**
   * 更新单个配置项
   */
  @Put(':key')
  @ApiOperation({ summary: '更新配置项' })
  @ApiParam({ name: 'key', description: '配置键名' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新配置项成功',
  })
  async updateConfig(
    @Param('key') key: string,
    @Body() updateData: { setting_value: string; description?: string }
  ) {
    await this.systemConfigService.updateConfig(key, updateData);
    return {
      success: true,
      message: '配置更新成功',
    };
  }

  /**
   * 测试配置连接
   */
  @Post(':key/test')
  @ApiOperation({ summary: '测试配置连接' })
  @ApiParam({ name: 'key', description: '配置键名' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '配置测试完成',
  })
  async testConfig(@Param('key') key: string) {
    const result = await this.systemConfigService.testConfig(key);
    return {
      success: true,
      data: result,
      message: '配置测试完成',
    };
  }

  /**
   * 验证配置格式
   */
  @Post('validate')
  @ApiOperation({ summary: '验证配置格式' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '配置验证完成',
  })
  async validateConfig(@Body() data: { key: string; value: string }) {
    const result = await this.systemConfigService.validateConfig(data.key, data.value);
    return {
      success: true,
      data: result,
      message: '配置验证完成',
    };
  }
} 