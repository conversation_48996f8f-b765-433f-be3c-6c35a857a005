const http = require('http');

// 调用权限API并监控后端日志
const testPermissionsAPI = async () => {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NTEyNjQ2NTcsImV4cCI6MTc1MTM1MTA1NywiYXVkIjoib25seW9mZmljZS1jbGllbnQiLCJpc3MiOiJvbmx5b2ZmaWNlLW5lc3RqcyJ9.Qwr0ARTldJAb_6EvCVgUar1A2hzfLfP23J5S8x6gau4';
  
  console.log('🔍 开始测试权限API...');
  console.log('🔑 使用Token:', token.substring(0, 50) + '...');
  
  const options = {
    hostname: '*************',
    port: 3000,
    path: '/api/permissions/user/my',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`\n✅ API响应状态: ${res.statusCode}`);
    console.log(`📋 响应头:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('\n📥 权限API完整响应:');
        console.log(JSON.stringify(response, null, 2));
        
        if (response.data) {
          console.log('\n🔍 权限数据详情:');
          console.log('👤 用户ID:', response.data.user_id);
          console.log('🔐 权限列表:', response.data.permissions);
          console.log('👥 角色列表:', response.data.roles);
          console.log('📊 权限数量:', response.data.permission_count);
          console.log('⭐ 超级管理员:', response.data.has_super_admin);
        }
        
        // 验证数据完整性
        if (response.data && response.data.permissions.length === 0) {
          console.log('\n⚠️  权限列表为空，这是问题所在！');
          console.log('🔍 需要检查:');
          console.log('   1. 数据库权限数据是否存在');
          console.log('   2. 后端权限查询SQL是否正确执行');
          console.log('   3. JWT Token解析是否正确');
          console.log('   4. 后端日志是否有错误信息');
        }
        
      } catch (error) {
        console.error('❌ 解析响应JSON失败:', error.message);
        console.log('原始响应数据:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ API请求失败:', error.message);
  });

  req.end();
  
  console.log('📤 权限API请求已发送，等待响应...');
};

// 延迟执行，确保后端日志能被看到
setTimeout(() => {
  testPermissionsAPI();
}, 1000);

console.log('⏳ 等待1秒后开始测试...'); 