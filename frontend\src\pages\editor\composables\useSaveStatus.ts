import { ref, computed, Ref } from 'vue'

/**
 * 保存状态管理 Composable
 *
 * @description 提供文档保存状态的跟踪和管理功能
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

/**
 * 保存状态类型
 */
export type SaveStatusType = 'saved' | 'saving' | 'editing' | 'error'

/**
 * 保存状态信息
 */
export interface SaveStatus {
  /** 状态类型 */
  status: SaveStatusType
  /** 状态描述 */
  message: string
  /** 最后保存时间 */
  lastSaveTime?: Date
}

/**
 * 保存状态管理 Hook
 * @param fileId 文件ID引用
 */
export function useSaveStatus(fileId: Ref<string>) {
  // 保存状态
  const saveStatus = ref<SaveStatus>({
    status: 'saved',
    message: '已保存',
    lastSaveTime: new Date(),
  })

  // 自动检查定时器
  let autoCheckInterval: number | null = null

  /**
   * 更新保存状态
   * @param status 状态类型
   * @param message 状态消息
   */
  const updateSaveStatus = (status: SaveStatusType, message: string): void => {
    saveStatus.value = {
      status,
      message,
      lastSaveTime: status === 'saved' ? new Date() : saveStatus.value.lastSaveTime,
    }
  }

  /**
   * 检查保存状态
   */
  const checkSaveStatus = async (): Promise<void> => {
    try {
      const response = await fetch(`/api/editor/save-status/${fileId.value}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          const { lastSaved } = result.data

          if (lastSaved) {
            const savedTime = new Date(lastSaved)
            const timeDiff = Math.abs(new Date().getTime() - savedTime.getTime())

            if (timeDiff < 60000) {
              // 1分钟内
              updateSaveStatus('saved', '已保存')
            } else {
              const minutes = Math.floor(timeDiff / 60000)
              updateSaveStatus('saved', `已保存 (${minutes}分钟前)`)
            }
          }
        }
      }
    } catch (error) {
      console.error('检查保存状态失败:', error)
    }
  }

  /**
   * 开始自动检查保存状态
   */
  const startAutoCheck = (): void => {
    if (autoCheckInterval) {
      clearInterval(autoCheckInterval)
    }

    // 每30秒检查一次保存状态
    autoCheckInterval = window.setInterval(checkSaveStatus, 30000)
  }

  /**
   * 停止自动检查保存状态
   */
  const stopAutoCheck = (): void => {
    if (autoCheckInterval) {
      clearInterval(autoCheckInterval)
      autoCheckInterval = null
    }
  }

  /**
   * 获取状态指示器类名
   */
  const getStatusClass = computed(() => {
    switch (saveStatus.value.status) {
      case 'saved':
        return 'status-saved'
      case 'saving':
        return 'status-saving'
      case 'editing':
        return 'status-editing'
      case 'error':
        return 'status-error'
      default:
        return 'status-saved'
    }
  })

  /**
   * 获取状态颜色
   */
  const getStatusColor = computed(() => {
    switch (saveStatus.value.status) {
      case 'saved':
        return '#2ecc71' // 绿色
      case 'saving':
        return '#f39c12' // 橙色
      case 'editing':
        return '#3498db' // 蓝色
      case 'error':
        return '#e74c3c' // 红色
      default:
        return '#2ecc71'
    }
  })

  return {
    // 状态
    saveStatus: computed(() => saveStatus.value),
    getStatusClass,
    getStatusColor,

    // 方法
    updateSaveStatus,
    checkSaveStatus,
    startAutoCheck,
    stopAutoCheck,
  }
}
