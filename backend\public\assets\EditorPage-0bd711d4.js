import{d as P,L as x,z as R,o as U,M as $,c as B,b as i,j as d,v as r,h as F,_ as H}from"./index-5218909a.js";import{u as V,a as z,b as A,E as I,c as O,N as j}from"./NotificationPanel-847d52aa.js";import"./editor-utils-5bc6f677.js";const T={class:"editor-page"},q=P({__name:"EditorPage",setup(G){const f=x(),c=R(()=>f.params.id),{editorConfig:g,docTitle:v,isEditorReady:a,initializeEditor:p,destroyEditor:E,forceSave:m}=V(c),{saveStatus:l,updateSaveStatus:s,checkSaveStatus:_,startAutoCheck:y,stopAutoCheck:h}=z(c),{notification:S,showNotification:t,hideNotification:w}=A(),k=()=>{console.log("编辑器已准备就绪"),t("编辑器已准备就绪","success",3e3),y(),_()},C=e=>{console.log("文档状态变更:",e),e.data===!0&&s("editing","有未保存的更改")},D=e=>{var n;console.log("文档保存事件:",e),(n=e.data)!=null&&n.url&&(s("saved","已保存"),t("文档已自动保存","success",2e3))},M=e=>{console.error("编辑器错误:",e);const o=e;let n="编辑器发生错误";o.data&&(o.data.errorDescription?n=o.data.errorDescription:typeof o.data=="string"&&(n=o.data)),t(n,"error")},N=async()=>{if(!a.value){t("编辑器未就绪","warning");return}try{s("saving","正在保存..."),t("正在保存文档...","info"),await m(),s("saved","保存成功"),t("文档保存成功","success")}catch(e){console.error("强制保存失败:",e),s("error","保存失败");const o=e instanceof Error?e.message:"未知错误";t(`保存失败: ${o}`,"error")}},b=async()=>{if(!a.value){t("编辑器未就绪","warning");return}try{t("正在加密文档...","info"),t("文档已加密，只能填写表单","success")}catch(e){console.error("加密失败:",e);const o=e instanceof Error?e.message:"未知错误";t(`加密失败: ${o}`,"error")}},L=async()=>{if(!a.value){t("编辑器未就绪","warning");return}try{t("正在解锁文档...","info"),t("文档已解锁，可以正常编辑","success")}catch(e){console.error("解锁失败:",e);const o=e instanceof Error?e.message:"未知错误";t(`解锁失败: ${o}`,"error")}},u=e=>{if(l.value.status==="editing"){const o="您有未保存的更改，确定要离开吗？";return e.returnValue=o,o}};return U(async()=>{console.log("初始化编辑器页面:",c.value);try{await p()}catch(e){console.error("初始化编辑器失败:",e);const o=e instanceof Error?e.message:"未知错误";t(`初始化失败: ${o}`,"error")}window.addEventListener("beforeunload",u)}),$(()=>{console.log("销毁编辑器页面"),h(),E(),window.removeEventListener("beforeunload",u)}),(e,o)=>(F(),B("div",T,[i(" 编辑器头部 "),d(I,{"doc-title":r(v),"save-status":r(l),"is-ready":r(a),onForceSave:N,onLockDocument:b,onUnlockDocument:L},null,8,["doc-title","save-status","is-ready"]),i(" 编辑器容器 "),d(O,{"is-ready":r(a),config:r(g),onEditorReady:k,onDocumentStateChange:C,onSave:D,onError:M},null,8,["is-ready","config"]),i(" 通知组件 "),d(j,{notification:r(S),onHide:r(w)},null,8,["notification","onHide"])]))}});const W=H(q,[["__scopeId","data-v-9f98b542"],["__file","D:/Code/OnlyOffice/frontend/src/pages/editor/EditorPage.vue"]]);export{W as default};
