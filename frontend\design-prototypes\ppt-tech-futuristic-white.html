<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        :root {
            --future-white: #ffffff;
            --future-gray-50: #f9fafb;
            --future-gray-100: #f3f4f6;
            --future-gray-200: #e5e7eb;
            --future-gray-400: #9ca3af;
            --future-gray-700: #374151;
            --future-gray-900: #111827;
            --future-blue: #2563eb;
            --future-cyan: #06b6d4;
            --future-violet: #7c3aed;
            --future-pink: #ec4899;
            --future-shadow: rgba(17, 24, 39, 0.1);
            --future-glow-blue: rgba(37, 99, 235, 0.4);
            --future-glow-cyan: rgba(6, 182, 212, 0.3);
        }

        html {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: radial-gradient(ellipse at center, var(--future-gray-50) 0%, var(--future-white) 70%);
        }

        body {
            width: 1280px;
            height: 720px;
            margin: 0;
            padding: 0;
            position: relative;
            overflow: hidden;
            background: var(--future-white);
            font-family: 'SF Pro Display', 'Inter', sans-serif;
            flex-shrink: 0;
        }
        
        .slide-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: var(--future-gray-900);
            position: relative;
            background: var(--future-white);
            border-radius: 16px;
            overflow: hidden;
        }
        
        /* 未来主义背景效果 */
        .futuristic-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .orbital-ring {
            position: absolute;
            border: 1px solid var(--future-blue);
            border-radius: 50%;
            opacity: 0.1;
            animation: rotate 20s linear infinite;
        }
        
        .orbital-ring.ring-1 {
            top: 50px;
            right: 50px;
            width: 150px;
            height: 150px;
        }
        
        .orbital-ring.ring-2 {
            bottom: 80px;
            left: 100px;
            width: 100px;
            height: 100px;
            animation-duration: 15s;
            animation-direction: reverse;
        }
        
        .light-beam {
            position: absolute;
            background: linear-gradient(45deg, transparent 0%, var(--future-glow-blue) 50%, transparent 100%);
            opacity: 0.03;
            animation: beam-sweep 8s ease-in-out infinite;
        }
        
        .light-beam.beam-1 {
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            transform-origin: bottom;
        }
        
        .light-beam.beam-2 {
            top: 0;
            right: 0;
            width: 100%;
            height: 2px;
            transform-origin: left;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes beam-sweep {
            0%, 100% { transform: rotate(0deg); opacity: 0.03; }
            50% { transform: rotate(15deg); opacity: 0.08; }
        }
        
        /* 粒子效果 */
        .particle-system {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--future-cyan);
            border-radius: 50%;
            opacity: 0.6;
            animation: float 6s ease-in-out infinite;
        }
        
        .particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { top: 60%; left: 80%; animation-delay: 2s; }
        .particle:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; }
        .particle:nth-child(4) { top: 30%; left: 90%; animation-delay: 1s; }
        .particle:nth-child(5) { top: 70%; left: 60%; animation-delay: 3s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
            50% { transform: translateY(-20px) scale(1.5); opacity: 1; }
        }
        
        .slide-header {
            padding: 60px 80px 25px 80px;
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }
        
        .slide-title {
            font-size: clamp(2.8rem, 4vw, 4.6rem);
            font-weight: 800;
            color: var(--future-gray-900);
            margin: 0;
            line-height: 1.1;
            letter-spacing: -0.03em;
            background: linear-gradient(135deg, var(--future-gray-900) 0%, var(--future-blue) 50%, var(--future-violet) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }
        
        .slide-title::after {
            content: "";
            position: absolute;
            bottom: -15px;
            left: 0;
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--future-blue), var(--future-cyan), var(--future-violet));
            border-radius: 2px;
            box-shadow: 0 0 10px var(--future-glow-blue);
        }
        
        .slide-content {
            flex: 1;
            padding: 35px 80px 60px 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .content-main {
            font-size: clamp(1.2rem, 2.5vw, 1.7rem);
            line-height: 1.7;
            color: var(--future-gray-700);
            font-weight: 400;
        }
        
        /* 未来主义列表 */
        .content-points {
            list-style: none;
            padding: 0;
            margin: 35px 0 0 0;
        }
        
        .content-points li {
            margin-bottom: 28px;
            padding-left: 55px;
            position: relative;
            font-size: 1.4rem;
            color: var(--future-gray-900);
            font-weight: 500;
        }
        
        .content-points li:before {
            content: "";
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            background: radial-gradient(circle, var(--future-blue) 30%, transparent 70%);
            border: 2px solid var(--future-blue);
            border-radius: 50%;
            box-shadow: 
                0 0 8px var(--future-glow-blue),
                inset 0 0 8px var(--future-glow-blue);
            animation: pulse-glow 3s ease-in-out infinite;
        }
        
        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 8px var(--future-glow-blue), inset 0 0 8px var(--future-glow-blue); }
            50% { box-shadow: 0 0 15px var(--future-glow-blue), inset 0 0 15px var(--future-glow-blue); }
        }
        
        /* 全息投影风格卡片 */
        .hologram-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 35px 0;
        }
        
        .hologram-card {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--future-gray-200);
            border-radius: 16px;
            padding: 30px 25px;
            text-align: center;
            position: relative;
            transition: all 0.4s ease;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .hologram-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg, 
                transparent 0%, 
                rgba(37, 99, 235, 0.1) 50%, 
                transparent 100%
            );
            transition: left 0.6s ease;
        }
        
        .hologram-card:hover::before {
            left: 100%;
        }
        
        .hologram-card::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid transparent;
            border-radius: 16px;
            background: linear-gradient(135deg, var(--future-blue), var(--future-cyan)) border-box;
            mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            mask-composite: subtract;
            opacity: 0;
            transition: opacity 0.4s ease;
        }
        
        .hologram-card:hover::after {
            opacity: 1;
        }
        
        .hologram-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 
                0 20px 60px var(--future-shadow),
                0 0 0 1px rgba(37, 99, 235, 0.2);
        }
        
        .hologram-value {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--future-blue), var(--future-violet));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }
        
        .hologram-label {
            font-size: 1rem;
            color: var(--future-gray-700);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .slide-footer {
            position: absolute;
            bottom: 30px;
            right: 80px;
            font-size: 14px;
            color: var(--future-gray-400);
            font-weight: 500;
            z-index: 1;
        }

        /* 全息界面元素 */
        .holo-interface {
            position: absolute;
            top: 30px;
            left: 80px;
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 1;
        }
        
        .holo-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            position: relative;
        }
        
        .holo-indicator.active {
            background: var(--future-blue);
            box-shadow: 0 0 12px var(--future-glow-blue);
            animation: pulse-active 2s ease-in-out infinite;
        }
        
        .holo-indicator.standby {
            background: var(--future-cyan);
            opacity: 0.6;
        }
        
        .holo-indicator.offline {
            background: var(--future-gray-400);
            opacity: 0.4;
        }
        
        @keyframes pulse-active {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        .holo-text {
            font-size: 11px;
            color: var(--future-gray-700);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }
        
        @media (max-width: 1280px) {
            body {
                width: 100vw;
                height: 56.25vw;
                max-height: 100vh;
            }
            .slide-container {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="futuristic-bg">
            <div class="orbital-ring ring-1"></div>
            <div class="orbital-ring ring-2"></div>
            <div class="light-beam beam-1"></div>
            <div class="light-beam beam-2"></div>
        </div>
        
        <div class="particle-system">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <div class="holo-interface">
            <div class="holo-indicator active"></div>
            <div class="holo-indicator standby"></div>
            <div class="holo-indicator offline"></div>
            <span class="holo-text">系统状态</span>
        </div>
        
        <header class="slide-header">
            <h1 class="slide-title">{{ main_heading }}</h1>
        </header>
        
        <main class="slide-content">
            <div class="content-main">
                {{ page_content }}

                <!--
                <ul class="content-points">
                    <li>量子计算与人工智能深度融合</li>
                    <li>全息投影界面交互技术</li>
                    <li>神经网络自适应学习系统</li>
                    <li>时空数据处理架构</li>
                </ul>
                -->

                <!--
                <div class="hologram-grid">
                    <div class="hologram-card">
                        <span class="hologram-value">∞</span>
                        <span class="hologram-label">计算能力</span>
                    </div>
                    <div class="hologram-card">
                        <span class="hologram-value">0.1ns</span>
                        <span class="hologram-label">响应时间</span>
                    </div>
                    <div class="hologram-card">
                        <span class="hologram-value">100%</span>
                        <span class="hologram-label">智能化</span>
                    </div>
                </div>
                -->
            </div>
        </main>
        
        <footer class="slide-footer">
            {{ current_page_number }} / {{ total_page_count }}
        </footer>
    </div>
</body>
</html> 