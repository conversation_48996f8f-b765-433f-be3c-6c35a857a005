import{A as e}from"./index-5218909a.js";class c{static async getUsers(s){return e.get("/users",{params:s})}static async getUserById(s){return e.get(`/users/${s}`)}static async createUser(s){return e.post("/users",s)}static async updateUser(s,t){return e.put(`/users/${s}`,t)}static async deleteUser(s){return e.delete(`/users/${s}`)}static async changePassword(s,t){return e.post(`/users/${s}/change-password`,t)}static async resetPassword(s){return e.post("/users/reset-password",s)}static async getCurrentUser(){return e.get("/users/profile/me")}static async updateCurrentUser(s){return e.put("/users/profile/me",s)}static async getUserStats(){return e.get("/users/stats")}}export{c as U};
