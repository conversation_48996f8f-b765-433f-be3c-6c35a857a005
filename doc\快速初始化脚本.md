# OnlyOffice项目 - 快速初始化完成脚本

## 🎯 当前状态
- ✅ 环境检查完成 (Node.js v22.15.0)
- ✅ 根目录配置完成
- ✅ Backend基础环境搭建完成
- ⏳ Frontend需要手动初始化

## 🚀 立即执行的步骤

### 1. 前端初始化 (在frontend目录下执行)

```bash
# 当前应该在 D:\Code\OnlyOffice\frontend 目录
# 如果不在，请先执行: cd frontend

# 安装Vue 3相关依赖
npm install vue@latest @vitejs/plugin-vue vite
npm install vue-router@4 pinia axios dayjs
npm install ant-design-vue @ant-design/icons-vue
npm install -D typescript vue-tsc @vue/tsconfig
```

### 2. 配置前端 package.json

```json
{
  "name": "onlyoffice-frontend",
  "version": "1.0.0",
  "description": "OnlyOffice集成系统前端 - Vue 3 + Ant Design Pro",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc --noEmit && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"
  },
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "axios": "^1.6.0",
    "ant-design-vue": "^4.0.0",
    "@ant-design/icons-vue": "^7.0.0",
    "dayjs": "^1.11.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.4.0",
    "vite": "^4.4.0",
    "typescript": "^5.2.0",
    "vue-tsc": "^1.8.0"
  }
}
```

### 3. 创建基础配置文件

#### `frontend/vite.config.ts`
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: '../backend/public',
    emptyOutDir: true,
  },
})
```

#### `frontend/tsconfig.json`
```json
{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 4. 创建前端目录结构

```bash
# 在 frontend 目录下执行
mkdir components pages services types utils styles assets -Force
mkdir public -Force

# 创建基础文件
echo 'export {}' > src\main.ts
echo '<template><div>Hello Vue 3</div></template>' > App.vue
echo 'declare module "*.vue" { import type { DefineComponent } from "vue"; const component: DefineComponent<{}, {}, any>; export default component; }' > vue-env.d.ts
```

### 5. 创建环境变量

```bash
# 在根目录 D:\Code\OnlyOffice 执行
copy .env.example .env
```

然后编辑 `.env` 文件，填入您的实际配置：
- 数据库连接信息
- JWT密钥 (至少32个字符)
- OnlyOffice服务器地址
- FileNet配置等

### 6. 测试环境

```bash
# 在根目录执行
npm run dev
```

这将同时启动：
- 后端: http://localhost:3000
- 前端: http://localhost:8080

## 🎯 **您的优势策略确认**

### ✅ 前后端整合开发 (当前阶段)
- **一个命令启动**: `npm run dev` 同时运行前后端
- **共享配置**: 环境变量统一管理
- **开发便利**: 代理设置，无跨域问题
- **快速部署**: 前端构建到后端static目录

### 🚀 未来分离升级 (需要时)
- **独立构建**: `npm run build:frontend` 构建到CDN
- **独立部署**: 后端API独立服务
- **容器化**: Docker分别打包
- **微服务**: 后端按业务拆分

## 📋 下一步规划

### 第1周目标：
1. ✅ 完成环境初始化 (今天)
2. 🔄 迁移现有services到backend/src/services
3. 🔄 创建第一个TypeScript API接口
4. 🔄 创建第一个Vue页面

### 第2周目标：
1. API统一化改造
2. Swagger文档集成
3. 基础权限系统

## ❓ 需要确认的问题

1. **数据库信息**: 您需要在`.env`文件中配置MySQL连接信息
2. **OnlyOffice服务**: 当前OnlyOffice服务器地址是什么？
3. **FileNet配置**: 如果使用FileNet，需要相关连接信息
4. **开发端口**: 3000(后端) + 8080(前端) 是否合适？

## 🚀 快速验证

完成上述步骤后，您应该能够：
- 访问 http://localhost:8080 看到Vue页面
- 访问 http://localhost:3000 看到Express服务
- 从前端通过 `/api` 路径调用后端接口

准备好继续下一步了吗？我可以帮您：
1. 创建第一个API健康检查接口
2. 创建第一个Vue页面
3. 迁移现有的核心服务
4. 配置具体的环境变量 