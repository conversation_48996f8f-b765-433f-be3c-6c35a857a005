import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './controllers/health.controller';
import { DatabaseModule } from '../database/database.module';

/**
 * 健康检查模块
 * 
 * 提供系统健康状态监控功能
 * 包括数据库连接、外部服务、磁盘空间等检查
 * 
 * @class HealthModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Module({
  imports: [
    // NestJS健康检查模块
    TerminusModule,
    
    // HTTP客户端模块(用于外部服务检查)
    HttpModule,
    DatabaseModule,
  ],
  controllers: [HealthController],
  providers: [],
  exports: [],
})
export class HealthModule {} 