/**
 * API接口相关类型定义
 * @description 定义API请求和响应的TypeScript类型
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

// 通用API响应格式
export interface ApiResponse<T = unknown> {
  success: boolean
  message?: string
  data?: T
  code?: number
  timestamp?: string
  total?: number // 添加总数字段
  pagination?: {
    current: number
    pageSize: number
    total: number
  }
}

// 分页请求参数
export interface PaginationParams {
  current: number
  pageSize: number
  total?: number
}

// 分页响应数据
export interface PaginationResponse<T> {
  list: T[]
  total: number
  current: number
  pageSize: number
}

// 表格列定义
export interface TableColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  fixed?: 'left' | 'right'
  align?: 'left' | 'center' | 'right'
  sorter?: boolean | ((a: unknown, b: unknown) => number)
  filters?: TableFilter[]
  customRender?: (params: { text: unknown; record: unknown; index: number }) => unknown
}

// 表格过滤器
export interface TableFilter {
  text: string
  value: string | number
}

// 排序参数
export interface SortParams {
  field: string
  order: 'ascend' | 'descend'
}

// 筛选参数
export interface FilterParams {
  [key: string]: string[] | number[] | null
}

// 表格变化参数
export interface TableChangeParams {
  pagination: PaginationParams
  filters: FilterParams
  sorter: SortParams | SortParams[]
}

// 文档数据类型
export interface DocumentInfo {
  id: string
  title: string
  type: 'word' | 'excel' | 'powerpoint' | 'pdf' | 'image' | 'other'
  size: number
  status: 'draft' | 'published' | 'archived'
  createdAt: string
  updatedAt: string
  createdBy: string
  lastEditor: string
  url?: string
  thumbnail?: string
  tags?: string[]
  category?: string
  isPublic: boolean
}

// 用户数据类型
export interface UserInfo {
  id: string
  username: string
  email: string
  fullName: string
  phone?: string
  department?: string
  role: 'admin' | 'editor' | 'viewer'
  status: 'active' | 'inactive' | 'locked'
  avatar?: string
  createdAt: string
  lastLoginAt?: string
  permissions: string[]
}

// 模板数据类型
export interface TemplateInfo {
  id: string
  name: string
  description?: string
  type: 'document' | 'spreadsheet' | 'presentation'
  category: string
  status: 'active' | 'inactive' | 'draft'
  isDefault: boolean
  previewUrl?: string
  downloadUrl?: string
  size: number
  createdAt: string
  updatedAt: string
  createdBy: string
  usageCount: number
  tags?: string[]
}

// 配置模板数据类型
export interface ConfigTemplate {
  id: string
  name: string
  description?: string
  type: string
  config: Record<string, unknown>
  isDefault: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 权限数据类型
export interface Permission {
  id: string
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  module: string
  resource: string
  action: string
  level?: 'basic' | 'medium' | 'high'
  parentId?: string
  path?: string
  icon?: string
  description?: string
  sort: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 角色数据类型
export interface Role {
  id: string
  name: string
  code: string
  displayName?: string
  description?: string
  status: 'active' | 'inactive'
  permissions: string[]
  createdAt: string
  updatedAt: string
}

// 登录响应数据
export interface LoginResponse {
  token?: string // 兼容旧格式
  accessToken?: string // 新格式
  refreshToken?: string
  userInfo?: UserInfo // 兼容旧格式
  user?: UserInfo // 新格式
  expiresIn: number
  tokenType?: string
}

// 文件上传响应
export interface UploadResponse {
  fileId: string
  fileName: string
  fileSize: number
  fileType: string
  fileUrl: string
  thumbnailUrl?: string
}

// 系统统计数据
export interface SystemStats {
  totalUsers: number
  totalDocuments: number
  totalTemplates: number
  storageUsed: number
  storageTotal: number
  activeUsers: number
  recentActivities: Activity[]
}

// 活动记录
export interface Activity {
  id: string
  type: 'login' | 'upload' | 'edit' | 'download' | 'share'
  userId: string
  userName: string
  description: string
  timestamp: string
  metadata?: Record<string, unknown>
}

// 搜索参数
export interface SearchParams {
  keyword?: string
  type?: string
  category?: string
  status?: string
  startDate?: string
  endDate?: string
  tags?: string[]
}

// OnlyOffice配置响应
export interface OnlyOfficeConfigResponse {
  documentType: 'word' | 'cell' | 'slide'
  document: {
    fileType: string
    key: string
    title: string
    url: string
    permissions: {
      edit: boolean
      download: boolean
      print: boolean
      comment: boolean
    }
  }
  editorConfig: {
    callbackUrl: string
    lang: string
    mode: 'edit' | 'view'
    user: {
      id: string
      name: string
    }
  }
}

// 文档版本数据类型
export interface DocumentVersion {
  id: string
  version: number
  docId: string
  fnDocId: string
  fileHash?: string
  modifiedBy?: string
  modifiedAt: string
  fileSize?: number
  comment?: string
}

// OnlyOffice编辑器事件类型
export interface EditorEvent {
  type: 'documentStateChange' | 'save' | 'error'
  data?: unknown
}

// 编辑器状态变化事件
export interface DocumentStateChangeEvent extends EditorEvent {
  type: 'documentStateChange'
  data: {
    key: string
    url: string
    title: string
    saved: boolean
  }
}

// 编辑器保存事件
export interface SaveEvent extends EditorEvent {
  type: 'save'
  data: {
    key: string
    url: string
    title: string
    status: number
  }
}

// 编辑器错误事件
export interface ErrorEvent extends EditorEvent {
  type: 'error'
  data: {
    message: string
    code?: number
  }
}

// 错误响应
export interface ApiError {
  code: number
  message: string
  details?: unknown
  timestamp: string
}
