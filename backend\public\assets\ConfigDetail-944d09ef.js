import{d as P,z as H,q as $,h as n,c as p,e as a,i as F,t as T,b as d,x as I,s as O,j as u,v as M,X as K,Y as X,O as Y,n as z,F as y,g as f,_ as A,r as Z,U as G,Z as J,o as Q,$ as h,a0 as _}from"./index-5218909a.js";const W={class:"config-card-compact"},x={class:"config-item-header-compact"},ee={class:"config-item-info"},oe={class:"config-item-title-compact"},ae={key:0,class:"required-indicator-compact"},se={class:"config-item-desc-compact"},te={class:"config-status-compact"},ie={class:"switch-controls-compact"},le={class:"switch-row-compact"},ne={key:0,class:"switch-row-compact"},ce={class:"config-status-info-compact"},re={class:"status-text-compact"},pe=P({__name:"ConfigItem",props:{label:{type:String,required:!0},description:{type:String,required:!0},configKey:{type:String,required:!0},configValue:{type:null,required:!0},isEnabled:{type:Boolean,required:!0},isRequired:{type:Boolean,required:!0},valueType:{type:String,required:!0},options:{type:Array,required:!1}},emits:["update-value","update-enabled"],setup(B,{emit:v}){const C=B,l=v,q=H(()=>{if(!C.isEnabled)return;const t=C.configValue;switch(C.valueType){case"boolean":return t===!0||t==="true";case"number":return typeof t=="number"?t:Number(t)||0;case"string":case"select":return String(t||"");default:return t}}),U=t=>{if(!C.isEnabled)return;let c=t;switch(C.valueType){case"boolean":c=!!t;break;case"number":c=Number(t)||0;break;case"string":case"select":c=String(t||"");break}l("update-value",c)},S=t=>{l("update-enabled",t)},D=t=>t.replace("权限","").replace("设置","").replace("配置",""),R=()=>C.isEnabled?C.valueType==="boolean"?q.value?"功能已启用":"功能已关闭":"功能已启用":"功能已禁用";return(t,c)=>{const V=$("a-tooltip"),j=$("a-input"),L=$("a-input-number"),b=$("a-select-option"),g=$("a-select");return n(),p("div",W,[a("div",x,[a("div",ee,[a("div",oe,[F(T(t.label)+" ",1),t.isRequired?(n(),p("span",ae,"必需")):d("v-if",!0)]),a("div",se,T(t.description),1)]),a("div",te,[t.isEnabled?t.isRequired?(n(),I(V,{key:1,title:"此配置项为必需项"},{default:O(()=>[u(M(X),{class:"status-icon status-icon--required"})]),_:1})):(n(),I(V,{key:2,title:"此配置项已启用"},{default:O(()=>[u(M(Y),{class:"status-icon status-icon--enabled"})]),_:1})):(n(),I(V,{key:0,title:"此配置项已禁用，不会出现在最终配置中"},{default:O(()=>[u(M(K),{class:"status-icon status-icon--disabled"})]),_:1}))])]),a("div",ie,[a("div",le,[c[4]||(c[4]=a("span",{class:"switch-label-compact"},"启用选项",-1)),a("div",{class:z(["toggle-switch-compact",{active:t.isEnabled}]),onClick:c[0]||(c[0]=r=>S(!t.isEnabled))},c[3]||(c[3]=[a("div",{class:"toggle-thumb-compact"},null,-1)]),2)]),t.valueType==="boolean"?(n(),p("div",ne,[a("span",{class:z(["switch-label-compact",{"disabled-label":!t.isEnabled}])},"允许"+T(D(t.label)),3),a("div",{class:z(["toggle-switch-compact",{active:q.value&&t.isEnabled,disabled:!t.isEnabled}]),onClick:c[1]||(c[1]=r=>t.isEnabled&&U(!q.value))},c[5]||(c[5]=[a("div",{class:"toggle-thumb-compact"},null,-1)]),2)])):d("v-if",!0)]),d(" 非布尔类型的控制器 "),t.valueType!=="boolean"&&t.isEnabled?(n(),p("div",{key:0,class:z(["config-control-compact",{"control-disabled":!t.isEnabled}])},[d(" 字符串类型 "),t.valueType==="string"?(n(),I(j,{key:0,value:q.value,size:"small",onInput:c[2]||(c[2]=r=>U(r.target.value)),placeholder:"请输入"+t.label},null,8,["value","placeholder"])):t.valueType==="number"?(n(),p(y,{key:1},[d(" 数字类型 "),u(L,{value:q.value,size:"small",style:{width:"100%"},onChange:U,placeholder:"请输入"+t.label},null,8,["value","placeholder"])],2112)):t.valueType==="select"&&t.options?(n(),p(y,{key:2},[d(" 选择器类型 "),u(g,{value:q.value,size:"small",style:{width:"100%"},onChange:U,placeholder:"请选择"+t.label},{default:O(()=>[(n(!0),p(y,null,f(t.options,r=>(n(),I(b,{key:String(r.value),value:r.value},{default:O(()=>[F(T(r.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","placeholder"])],2112)):d("v-if",!0)],2)):d("v-if",!0),a("div",ce,[a("div",re,[a("div",{class:z(["status-icon-compact",t.isEnabled?"status-enabled":"status-disabled"])},null,2),F(" "+T(R()),1)])])])}}});const E=A(pe,[["__scopeId","data-v-12388f3a"],["__file","D:/Code/OnlyOffice/frontend/src/pages/OnlyOfficeConfig/ConfigItem.vue"]]),de={class:"config-detail-compact"},ue={class:"config-section-compact"},ye={class:"config-group-compact"},be={class:"config-grid-compact"},ge={class:"config-section-compact"},ve={class:"config-group-compact"},fe={class:"config-grid-compact"},ke={class:"config-section-compact"},me={class:"config-group-compact"},he={class:"config-grid-compact"},_e={class:"config-section-compact"},Ee={class:"config-group-compact"},Ce={class:"config-grid-compact"},qe={class:"config-section-compact"},Ue={class:"config-group-compact"},we={class:"config-grid-compact"},Ve={class:"config-section-compact"},$e={class:"config-group-compact"},Te={class:"config-grid-compact"},Ie={class:"config-section-compact"},Oe={class:"config-group-compact"},ze={class:"config-grid-compact"},Se={class:"config-section-compact"},De={class:"config-group-compact"},Re={class:"config-grid-compact"},je={class:"config-section-compact"},Le={class:"config-group-compact"},Be={class:"config-grid-compact"},Fe={class:"config-section-compact"},Me={class:"config-group-compact"},Ne={class:"config-grid-compact"},Pe=P({__name:"ConfigDetail",props:{templateData:{type:Object,required:!1},initialConfig:{type:Object,required:!1},initialConfigStates:{type:Object,required:!1},activeTab:{type:String,required:!0}},setup(B){const v=B,C=Z(),l=G({permissions:{},customization:{},features:{},layout:{},coEditing:{},review:{},user:{},customer:{},server:{},mobile:{}}),q=[{key:"edit",label:"编辑权限",description:"是否允许编辑文档",type:"boolean"},{key:"download",label:"下载权限",description:"是否允许下载文档",type:"boolean"},{key:"print",label:"打印权限",description:"是否允许打印文档",type:"boolean"},{key:"copy",label:"复制权限",description:"是否允许复制内容",type:"boolean"},{key:"modifyFilter",label:"筛选权限",description:"是否允许修改筛选器",type:"boolean"},{key:"modifyContentControl",label:"内容控制权限",description:"是否允许修改内容控制",type:"boolean"},{key:"fillForms",label:"表单填写权限",description:"是否允许填写表单",type:"boolean"},{key:"comment",label:"评论权限",description:"是否允许添加评论",type:"boolean"},{key:"chat",label:"聊天权限",description:"是否允许使用聊天功能",type:"boolean"},{key:"review",label:"审阅权限",description:"是否允许审阅文档",type:"boolean"}],U=[{key:"about",label:"关于信息",description:"是否显示关于信息",type:"boolean"},{key:"autosave",label:"自动保存",description:"是否启用自动保存",type:"boolean"},{key:"comments",label:"评论功能",description:"是否显示评论功能",type:"boolean"},{key:"compactHeader",label:"紧凑头部",description:"是否使用紧凑型头部",type:"boolean"},{key:"compactToolbar",label:"紧凑工具栏",description:"是否使用紧凑型工具栏",type:"boolean"},{key:"forcesave",label:"强制保存",description:"是否强制保存文档",type:"boolean"},{key:"help",label:"帮助功能",description:"是否显示帮助功能",type:"boolean"},{key:"hideRightMenu",label:"隐藏右侧菜单",description:"是否隐藏右侧菜单",type:"boolean"},{key:"hideRulers",label:"隐藏标尺",description:"是否隐藏标尺",type:"boolean"},{key:"macros",label:"宏功能",description:"是否启用宏功能",type:"boolean"},{key:"plugins",label:"插件功能",description:"是否启用插件功能",type:"boolean"},{key:"spellcheck",label:"拼写检查",description:"是否启用拼写检查",type:"boolean"},{key:"uiTheme",label:"UI主题",description:"用户界面主题",type:"select",options:[{label:"默认",value:"theme-light"},{label:"深色",value:"theme-dark"}]},{key:"unit",label:"测量单位",description:"文档中使用的测量单位",type:"select",options:[{label:"厘米",value:"cm"},{label:"磅",value:"pt"},{label:"英寸",value:"inch"}]},{key:"zoom",label:"缩放比例",description:"文档的默认缩放比例",type:"number"}],S=[{key:"mode",label:"协作模式",description:"协作编辑的模式",type:"select",options:[{label:"快速模式",value:"fast"},{label:"严格模式",value:"strict"}]},{key:"change",label:"实时更改",description:"是否启用实时更改跟踪",type:"boolean"}],D=[{key:"id",label:"用户ID",description:"用户的唯一标识符",type:"string"},{key:"name",label:"用户名",description:"用户显示名称",type:"string"},{key:"group",label:"用户组",description:"用户所属的组",type:"string"}],R=[{key:"address",label:"地址",description:"客户地址信息",type:"string"},{key:"info",label:"信息",description:"客户附加信息",type:"string"},{key:"logo",label:"Logo",description:"客户Logo URL",type:"string"},{key:"logoDark",label:"深色Logo",description:"深色主题下的Logo URL",type:"string"},{key:"mail",label:"邮箱",description:"客户邮箱地址",type:"string"},{key:"name",label:"名称",description:"客户名称",type:"string"},{key:"phone",label:"电话",description:"客户电话号码",type:"string"},{key:"www",label:"网站",description:"客户网站地址",type:"string"}],t=[{key:"trackChanges",label:"修订跟踪",description:"是否启用修订跟踪功能",type:"boolean"},{key:"requestclose",label:"请求关闭",description:"是否允许请求关闭文档",type:"boolean"},{key:"goback",label:"返回功能",description:"是否显示返回按钮",type:"boolean"},{key:"submitForm",label:"提交表单",description:"是否启用表单提交功能",type:"boolean"}],c=[{key:"leftMenu",label:"左侧菜单",description:"是否显示左侧菜单",type:"boolean"},{key:"rightMenu",label:"右侧菜单",description:"是否显示右侧菜单",type:"boolean"},{key:"toolbar",label:"工具栏",description:"是否显示工具栏",type:"boolean"},{key:"statusBar",label:"状态栏",description:"是否显示状态栏",type:"boolean"},{key:"header",label:"头部区域",description:"是否显示头部区域",type:"boolean"}],V=[{key:"reviewDisplay",label:"审阅显示",description:"审阅信息的显示方式",type:"select",options:[{label:"原稿",value:"original"},{label:"最终",value:"final"},{label:"标记",value:"markup"}]},{key:"trackChanges",label:"跟踪修订",description:"是否自动启用修订跟踪",type:"boolean"},{key:"hoverMode",label:"悬停模式",description:"是否启用悬停模式",type:"boolean"}],j=[{key:"url",label:"服务器URL",description:"OnlyOffice服务器地址",type:"string"},{key:"timeout",label:"超时时间",description:"请求超时时间（秒）",type:"number"},{key:"token",label:"访问令牌",description:"服务器访问令牌",type:"string"}],L=[{key:"forceDesktop",label:"强制桌面版",description:"移动设备是否强制使用桌面版",type:"boolean"},{key:"hideFileMenu",label:"隐藏文件菜单",description:"移动端是否隐藏文件菜单",type:"boolean"},{key:"hideRightPanel",label:"隐藏右侧面板",description:"移动端是否隐藏右侧面板",type:"boolean"}],b=(i,o)=>{var e,s;return(s=(e=l[i])==null?void 0:e[o])==null?void 0:s.value},g=(i,o)=>{var e,s;return((s=(e=l[i])==null?void 0:e[o])==null?void 0:s.enabled)??!0},r=(i,o)=>{var e,s;return((s=(e=l[i])==null?void 0:e[o])==null?void 0:s.required)??!1},k=(i,o,e)=>{l[i]||(l[i]={}),l[i][o]?l[i][o].value=e:l[i][o]={value:e,enabled:!0,required:!1}},m=(i,o,e)=>{l[i]||(l[i]={}),l[i][o]?l[i][o].enabled=e:l[i][o]={value:null,enabled:e,required:!1}},N=()=>{Object.keys(l).forEach(o=>{l[o]={}}),v.initialConfigStates?Object.keys(v.initialConfigStates).forEach(o=>{l[o]||(l[o]={}),Object.keys(v.initialConfigStates[o]).forEach(e=>{l[o][e]={...v.initialConfigStates[o][e]}})}):v.initialConfig&&Object.keys(v.initialConfig).forEach(o=>{const e=v.initialConfig[o];e&&typeof e=="object"&&(l[o]||(l[o]={}),Object.keys(e).forEach(s=>{const w=e[s];l[o][s]={value:w,enabled:!0,required:!1}}))}),[...q.map(o=>({group:"permissions",...o})),...U.map(o=>({group:"customization",...o})),...t.map(o=>({group:"features",...o})),...c.map(o=>({group:"layout",...o})),...S.map(o=>({group:"coEditing",...o})),...V.map(o=>({group:"review",...o})),...D.map(o=>({group:"user",...o})),...R.map(o=>({group:"customer",...o})),...j.map(o=>({group:"server",...o})),...L.map(o=>({group:"mobile",...o}))].forEach(o=>{var e,s;if(l[o.group]||(l[o.group]={}),!l[o.group][o.key]){let w=null;switch(o.type){case"boolean":w=!1;break;case"number":w=o.key==="zoom"?100:0;break;case"select":w=((s=(e=o.options)==null?void 0:e[0])==null?void 0:s.value)||"";break;default:w=""}l[o.group][o.key]={value:w,enabled:!1,required:!1}}})};return J(()=>[v.initialConfig,v.initialConfigStates],()=>{N()},{immediate:!0,deep:!0}),Q(()=>{N()}),(i,o)=>(n(),p("div",de,[a("div",{class:"config-content-compact",ref_key:"configContentRef",ref:C},[d(" 权限配置 "),h(a("div",ue,[a("div",ye,[o[0]||(o[0]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"🛡️"),a("h3",{class:"group-title-compact"},"文档权限配置")],-1)),a("div",be,[(n(),p(y,null,f(q,e=>u(E,{key:`permissions.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("permissions",e.key),"is-enabled":g("permissions",e.key),"is-required":r("permissions",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("permissions",e.key,s),onUpdateEnabled:s=>m("permissions",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="permissions"]]),d(" 界面自定义 "),h(a("div",ge,[a("div",ve,[o[1]||(o[1]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"🎨"),a("h3",{class:"group-title-compact"},"界面和功能自定义")],-1)),a("div",fe,[(n(),p(y,null,f(U,e=>u(E,{key:`customization.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("customization",e.key),"is-enabled":g("customization",e.key),"is-required":r("customization",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("customization",e.key,s),onUpdateEnabled:s=>m("customization",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="customization"]]),d(" 协作编辑 "),h(a("div",ke,[a("div",me,[o[2]||(o[2]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"👥"),a("h3",{class:"group-title-compact"},"协作编辑设置")],-1)),a("div",he,[(n(),p(y,null,f(S,e=>u(E,{key:`coEditing.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("coEditing",e.key),"is-enabled":g("coEditing",e.key),"is-required":r("coEditing",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("coEditing",e.key,s),onUpdateEnabled:s=>m("coEditing",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="coEditing"]]),d(" 用户设置 "),h(a("div",_e,[a("div",Ee,[o[3]||(o[3]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"👤"),a("h3",{class:"group-title-compact"},"用户信息配置")],-1)),a("div",Ce,[(n(),p(y,null,f(D,e=>u(E,{key:`user.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("user",e.key),"is-enabled":g("user",e.key),"is-required":r("user",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("user",e.key,s),onUpdateEnabled:s=>m("user",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="user"]]),d(" 功能特性 "),h(a("div",qe,[a("div",Ue,[o[4]||(o[4]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"⚙️"),a("h3",{class:"group-title-compact"},"功能特性设置")],-1)),a("div",we,[(n(),p(y,null,f(t,e=>u(E,{key:`features.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("features",e.key),"is-enabled":g("features",e.key),"is-required":r("features",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("features",e.key,s),onUpdateEnabled:s=>m("features",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="features"]]),d(" 布局设置 "),h(a("div",Ve,[a("div",$e,[o[5]||(o[5]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"📱"),a("h3",{class:"group-title-compact"},"布局设置")],-1)),a("div",Te,[(n(),p(y,null,f(c,e=>u(E,{key:`layout.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("layout",e.key),"is-enabled":g("layout",e.key),"is-required":r("layout",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("layout",e.key,s),onUpdateEnabled:s=>m("layout",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="layout"]]),d(" 审阅设置 "),h(a("div",Ie,[a("div",Oe,[o[6]||(o[6]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"✏️"),a("h3",{class:"group-title-compact"},"审阅设置")],-1)),a("div",ze,[(n(),p(y,null,f(V,e=>u(E,{key:`review.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("review",e.key),"is-enabled":g("review",e.key),"is-required":r("review",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("review",e.key,s),onUpdateEnabled:s=>m("review",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="review"]]),d(" 服务器设置 "),h(a("div",Se,[a("div",De,[o[7]||(o[7]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"🖥️"),a("h3",{class:"group-title-compact"},"服务器设置")],-1)),a("div",Re,[(n(),p(y,null,f(j,e=>u(E,{key:`server.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("server",e.key),"is-enabled":g("server",e.key),"is-required":r("server",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("server",e.key,s),onUpdateEnabled:s=>m("server",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="server"]]),d(" 移动端设置 "),h(a("div",je,[a("div",Le,[o[8]||(o[8]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"📱"),a("h3",{class:"group-title-compact"},"移动端设置")],-1)),a("div",Be,[(n(),p(y,null,f(L,e=>u(E,{key:`mobile.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("mobile",e.key),"is-enabled":g("mobile",e.key),"is-required":r("mobile",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("mobile",e.key,s),onUpdateEnabled:s=>m("mobile",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="mobile"]]),d(" 客户定制 "),h(a("div",Fe,[a("div",Me,[o[9]||(o[9]=a("div",{class:"group-header-compact"},[a("div",{class:"group-icon-compact"},"🏢"),a("h3",{class:"group-title-compact"},"客户定制信息")],-1)),a("div",Ne,[(n(),p(y,null,f(R,e=>u(E,{key:`customer.${e.key}`,label:e.label,description:e.description,"config-key":e.key,"config-value":b("customer",e.key),"is-enabled":g("customer",e.key),"is-required":r("customer",e.key),"value-type":e.type,options:e.options,onUpdateValue:s=>k("customer",e.key,s),onUpdateEnabled:s=>m("customer",e.key,s)},null,8,["label","description","config-key","config-value","is-enabled","is-required","value-type","options","onUpdateValue","onUpdateEnabled"])),64))])])],512),[[_,i.activeTab==="customer"]])],512)]))}});const He=A(Pe,[["__scopeId","data-v-c189265b"],["__file","D:/Code/OnlyOffice/frontend/src/pages/OnlyOfficeConfig/ConfigDetail.vue"]]);export{He as default};
