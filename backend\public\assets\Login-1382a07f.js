import{j as r,k as S,d as z,u as A,a9 as F,r as j,o as C,q as u,c as N,e as n,b as g,s as l,t as P,v as p,h as B,aa as _,a1 as D,i as v,_ as M}from"./index-5218909a.js";var U={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};const q=U;function b(s){for(var o=1;o<arguments.length;o++){var a=arguments[o]!=null?Object(arguments[o]):{},t=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(t=t.concat(Object.getOwnPropertySymbols(a).filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable}))),t.forEach(function(i){H(s,i,a[i])})}return s}function H(s,o,a){return o in s?Object.defineProperty(s,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):s[o]=a,s}var f=function(o,a){var t=b({},o,a.attrs);return r(S,b({},t,{icon:q}),null)};f.displayName="LockOutlined";f.inheritAttrs=!1;const E=f,I={class:"login-container"},Y={class:"login-bg"},$={class:"login-form-wrapper"},R={class:"login-form"},T={class:"login-options"},G={class:"login-footer"},J=z({__name:"Login",setup(s){const o=A(),a=F(),t=j({username:"admin",password:"password123",remember:!1}),i=new Date().getFullYear(),h={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度为3-20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20个字符",trigger:"blur"}]},O=async()=>{try{await a.login({username:t.value.username,password:t.value.password}),o.push("/")}catch(d){console.error("登录失败:",d)}},w=d=>{console.log("表单验证失败:",d)};return C(()=>{a.isAuthenticated&&o.push("/")}),(d,e)=>{const y=u("a-input"),m=u("a-form-item"),x=u("a-input-password"),k=u("a-checkbox"),L=u("a-button"),V=u("a-form");return B(),N("div",I,[n("div",Y,[g(" 背景动画元素 "),e[7]||(e[7]=n("div",{class:"bg-animation"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),g(" 登录表单区域 "),n("div",$,[n("div",R,[e[6]||(e[6]=n("div",{class:"login-header"},[n("div",{class:"logo"},"🏢"),n("h2",null,"用户登录"),n("p",null,"欢迎使用OnlyOffice集成系统")],-1)),r(V,{model:t.value,rules:h,layout:"vertical",size:"large",onFinish:O,onFinishFailed:w},{default:l(()=>[r(m,{name:"username",label:"用户名"},{default:l(()=>[r(y,{value:t.value.username,"onUpdate:value":e[0]||(e[0]=c=>t.value.username=c),placeholder:"请输入用户名",prefix:_(p(D))},null,8,["value","prefix"])]),_:1}),r(m,{name:"password",label:"密码"},{default:l(()=>[r(x,{value:t.value.password,"onUpdate:value":e[1]||(e[1]=c=>t.value.password=c),placeholder:"请输入密码",prefix:_(p(E))},null,8,["value","prefix"])]),_:1}),r(m,null,{default:l(()=>[n("div",T,[r(k,{checked:t.value.remember,"onUpdate:checked":e[2]||(e[2]=c=>t.value.remember=c)},{default:l(()=>e[3]||(e[3]=[v(" 记住我 ")])),_:1,__:[3]},8,["checked"]),e[4]||(e[4]=n("a",{href:"#",class:"forgot-password"},"忘记密码？",-1))])]),_:1}),r(m,null,{default:l(()=>[r(L,{type:"primary","html-type":"submit",loading:p(a).loading,block:"",size:"large"},{default:l(()=>e[5]||(e[5]=[v(" 登录 ")])),_:1,__:[5]},8,["loading"])]),_:1})]),_:1},8,["model"]),n("div",G,[n("p",null,"© "+P(p(i))+" OnlyOffice集成系统. All rights reserved.",1)])])])])])}}});const W=M(J,[["__scopeId","data-v-52a59ab5"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Auth/Login.vue"]]);export{W as default};
