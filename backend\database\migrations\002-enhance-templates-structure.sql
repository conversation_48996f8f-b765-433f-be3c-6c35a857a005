-- ===================================================================
-- 模板表结构升级迁移脚本
-- 版本: 002
-- 日期: 2024-12-19
-- 描述: 升级templates表结构，支持多种模板来源类型
-- ===================================================================

-- 1. 删除现有的外键约束（如果存在）
SET @constraint_exists = (SELECT COUNT(*) 
                         FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                         WHERE TABLE_SCHEMA = DATABASE() 
                         AND TABLE_NAME = 'templates' 
                         AND CONSTRAINT_NAME = 'fk_t_doc_id');

SET @sql = IF(@constraint_exists > 0, 
              'ALTER TABLE templates DROP FOREIGN KEY fk_t_doc_id', 
              'SELECT "外键约束不存在，跳过删除"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 重命名doc_id为source_doc_id
ALTER TABLE templates CHANGE COLUMN doc_id source_doc_id VARCHAR(36) COMMENT '引用的源文档ID（当source_type=reference时使用）';

-- 3. 确保新字段存在（如果不存在则添加）
SET @column_exists = (SELECT COUNT(*) 
                     FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'templates' 
                     AND COLUMN_NAME = 'fn_doc_id');

SET @sql = IF(@column_exists = 0, 
              'ALTER TABLE templates ADD COLUMN fn_doc_id VARCHAR(255) COMMENT "FileNet文档ID"', 
              'SELECT "fn_doc_id字段已存在，跳过添加"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @column_exists = (SELECT COUNT(*) 
                     FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'templates' 
                     AND COLUMN_NAME = 'source_type');

SET @sql = IF(@column_exists = 0, 
              'ALTER TABLE templates ADD COLUMN source_type ENUM("upload", "create", "reference") DEFAULT "reference" COMMENT "模板来源类型"', 
              'SELECT "source_type字段已存在，跳过添加"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为现有模板填充FileNet ID（从filenet_documents表获取）
UPDATE templates t
SET t.fn_doc_id = (
    SELECT fd.fn_doc_id 
    FROM filenet_documents fd 
    WHERE fd.id = t.source_doc_id
)
WHERE t.source_doc_id IS NOT NULL 
AND (t.fn_doc_id IS NULL OR t.fn_doc_id = '');

-- 5. 设置现有模板的类型为reference
UPDATE templates 
SET source_type = 'reference' 
WHERE source_doc_id IS NOT NULL 
AND (source_type IS NULL OR source_type = '');

-- 6. 重新创建外键约束（可选，保持数据完整性）
SET @constraint_exists = (SELECT COUNT(*) 
                         FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                         WHERE TABLE_SCHEMA = DATABASE() 
                         AND TABLE_NAME = 'templates' 
                         AND CONSTRAINT_NAME = 'fk_t_source_doc_id');

SET @sql = IF(@constraint_exists = 0, 
              'ALTER TABLE templates ADD CONSTRAINT fk_t_source_doc_id FOREIGN KEY (source_doc_id) REFERENCES filenet_documents(id) ON DELETE SET NULL ON UPDATE CASCADE', 
              'SELECT "新外键约束已存在，跳过添加"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 验证迁移结果
SELECT 
    id,
    name,
    source_type,
    CASE 
        WHEN fn_doc_id IS NOT NULL THEN '✅ 有FileNet ID'
        ELSE '❌ 缺少FileNet ID'
    END as fn_doc_status,
    CASE 
        WHEN source_doc_id IS NOT NULL THEN '✅ 有源文档引用'
        ELSE '⚠️ 无源文档引用'
    END as source_doc_status
FROM templates 
WHERE is_deleted = FALSE
ORDER BY created_at DESC; 