<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        :root {
            --grid-white: #ffffff;
            --grid-gray-50: #fafafa;
            --grid-gray-100: #f5f5f5;
            --grid-gray-300: #d1d5db;
            --grid-gray-500: #6b7280;
            --grid-gray-800: #1f2937;
            --grid-blue: #1d4ed8;
            --grid-cyan: #0891b2;
            --grid-green: #059669;
            --grid-shadow: rgba(31, 41, 55, 0.08);
            --grid-glow: rgba(29, 78, 216, 0.4);
        }

        html {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--grid-gray-50);
        }

        body {
            width: 1280px;
            height: 720px;
            margin: 0;
            padding: 0;
            position: relative;
            overflow: hidden;
            background: var(--grid-white);
            font-family: 'JetBrains Mono', 'SF Mono', 'Consolas', monospace;
            flex-shrink: 0;
        }
        
        .slide-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: var(--grid-gray-800);
            position: relative;
            background: var(--grid-white);
            border: 1px solid var(--grid-gray-300);
        }
        
        /* 电路板风格网格背景 */
        .circuit-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            background-image: 
                /* 主网格 */
                linear-gradient(var(--grid-gray-300) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid-gray-300) 1px, transparent 1px),
                /* 次级网格 */
                linear-gradient(rgba(29, 78, 216, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(29, 78, 216, 0.1) 1px, transparent 1px);
            background-size: 
                80px 80px,
                80px 80px,
                20px 20px,
                20px 20px;
            opacity: 0.6;
        }
        
        /* 连接点装饰 */
        .circuit-nodes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        
        .node {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--grid-blue);
            border-radius: 50%;
            box-shadow: 0 0 6px var(--grid-glow);
        }
        
        .node.node-1 { top: 160px; left: 160px; }
        .node.node-2 { top: 160px; right: 240px; }
        .node.node-3 { bottom: 160px; left: 240px; }
        .node.node-4 { bottom: 160px; right: 160px; }
        .node.node-5 { top: 50%; left: 50%; transform: translate(-50%, -50%); }
        
        /* 连接线 */
        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, var(--grid-blue), transparent);
            height: 1px;
            opacity: 0.3;
        }
        
        .line-1 {
            top: 162px;
            left: 164px;
            width: 200px;
        }
        
        .line-2 {
            bottom: 162px;
            left: 244px;
            width: 180px;
        }
        
        .slide-header {
            padding: 60px 80px 20px 80px;
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--grid-gray-100);
        }
        
        .slide-title {
            font-size: clamp(2.6rem, 4vw, 4.3rem);
            font-weight: 600;
            color: var(--grid-gray-800);
            margin: 0;
            line-height: 1.1;
            letter-spacing: -0.01em;
            font-family: 'Inter', 'SF Pro Display', sans-serif;
            position: relative;
        }
        
        .slide-title::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, var(--grid-blue), var(--grid-cyan));
            border-radius: 2px;
        }
        
        .slide-content {
            flex: 1;
            padding: 40px 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .content-main {
            font-size: clamp(1.2rem, 2.5vw, 1.7rem);
            line-height: 1.6;
            color: var(--grid-gray-500);
            font-weight: 400;
            font-family: 'Inter', 'SF Pro Display', sans-serif;
        }
        
        /* 电路风格列表 */
        .content-points {
            list-style: none;
            padding: 0;
            margin: 35px 0 0 0;
        }
        
        .content-points li {
            margin-bottom: 25px;
            padding-left: 50px;
            position: relative;
            font-size: 1.4rem;
            color: var(--grid-gray-800);
            font-weight: 500;
            font-family: 'Inter', 'SF Pro Display', sans-serif;
        }
        
        .content-points li:before {
            content: "";
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--grid-blue);
            border-radius: 50%;
            box-shadow: 0 0 4px var(--grid-glow);
        }
        
        .content-points li:after {
            content: "";
            position: absolute;
            left: 6px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            border: 1px solid var(--grid-blue);
            border-radius: 50%;
            opacity: 0.2;
        }
        
        /* 电路板数据卡片 */
        .circuit-grid-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 35px 0;
        }
        
        .circuit-card {
            background: var(--grid-white);
            border: 1px solid var(--grid-gray-300);
            padding: 30px 20px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            font-family: 'JetBrains Mono', monospace;
        }
        
        .circuit-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(90deg, transparent 0%, var(--grid-blue) 2%, transparent 4%),
                linear-gradient(0deg, transparent 0%, var(--grid-blue) 2%, transparent 4%);
            background-size: 25px 25px;
            background-position: 0 0, 0 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        
        .circuit-card:hover::before {
            opacity: 0.1;
        }
        
        .circuit-card:hover {
            border-color: var(--grid-blue);
            box-shadow: 
                0 8px 25px var(--grid-shadow),
                0 0 0 1px var(--grid-blue);
            transform: translateY(-3px);
        }
        
        .circuit-card::after {
            content: "";
            position: absolute;
            top: 10px;
            right: 10px;
            width: 8px;
            height: 8px;
            background: var(--grid-green);
            border-radius: 50%;
            box-shadow: 0 0 4px var(--grid-green);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .circuit-card:hover::after {
            opacity: 1;
        }
        
        .circuit-number {
            font-size: 2.8rem;
            font-weight: 700;
            color: var(--grid-blue);
            display: block;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }
        
        .circuit-label {
            font-size: 0.9rem;
            color: var(--grid-gray-500);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }
        
        .slide-footer {
            position: absolute;
            bottom: 30px;
            right: 80px;
            font-size: 14px;
            color: var(--grid-gray-500);
            font-weight: 500;
            z-index: 1;
            font-family: 'JetBrains Mono', monospace;
        }

        /* 状态指示器 */
        .status-indicator {
            position: absolute;
            top: 30px;
            right: 80px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--grid-gray-500);
            font-family: 'JetBrains Mono', monospace;
            z-index: 1;
        }
        
        .status-dot {
            width: 6px;
            height: 6px;
            background: var(--grid-green);
            border-radius: 50%;
            box-shadow: 0 0 4px var(--grid-green);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 1280px) {
            body {
                width: 100vw;
                height: 56.25vw;
                max-height: 100vh;
            }
            .slide-container {
                border: none;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="circuit-grid"></div>
        <div class="circuit-nodes">
            <div class="node node-1"></div>
            <div class="node node-2"></div>
            <div class="node node-3"></div>
            <div class="node node-4"></div>
            <div class="node node-5"></div>
            <div class="connection-line line-1"></div>
            <div class="connection-line line-2"></div>
        </div>
        
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>SYSTEM ONLINE</span>
        </div>
        
        <header class="slide-header">
            <h1 class="slide-title">{{ main_heading }}</h1>
        </header>
        
        <main class="slide-content">
            <div class="content-main">
                {{ page_content }}

                <!--
                <ul class="content-points">
                    <li>分布式微处理器集群架构</li>
                    <li>实时数据流处理引擎</li>
                    <li>自适应负载均衡算法</li>
                    <li>多层次安全防护机制</li>
                </ul>
                -->

                <!--
                <div class="circuit-grid-cards">
                    <div class="circuit-card">
                        <span class="circuit-number">256</span>
                        <span class="circuit-label">CPU CORES</span>
                    </div>
                    <div class="circuit-card">
                        <span class="circuit-number">1TB</span>
                        <span class="circuit-label">RAM CACHE</span>
                    </div>
                    <div class="circuit-card">
                        <span class="circuit-number">99.99%</span>
                        <span class="circuit-label">UPTIME</span>
                    </div>
                </div>
                -->
            </div>
        </main>
        
        <footer class="slide-footer">
            {{ current_page_number }} / {{ total_page_count }}
        </footer>
    </div>
</body>
</html> 