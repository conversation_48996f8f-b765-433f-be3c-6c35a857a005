-- 文件上传系统数据库表
-- 用于存储上传文件的信息和元数据

-- 上传文件主表
CREATE TABLE IF NOT EXISTS uploaded_files (
    id VARCHAR(36) PRIMARY KEY,
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    filename VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    extension VARCHAR(20) COMMENT '文件扩展名',
    file_hash VARCHAR(64) COMMENT '文件哈希值（MD5）',
    uploaded_by VARCHAR(100) COMMENT '上传者',
    template_id VARCHAR(36) COMMENT '关联的配置模板ID',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '软删除标记',
    
    INDEX idx_original_name (original_name),
    INDEX idx_filename (filename),
    INDEX idx_file_hash (file_hash),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_template_id (template_id),
    INDEX idx_uploaded_at (uploaded_at),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文件下载记录表
CREATE TABLE IF NOT EXISTS file_download_logs (
    id VARCHAR(36) PRIMARY KEY,
    file_id VARCHAR(36) NOT NULL COMMENT '关联uploaded_files.id',
    downloaded_by VARCHAR(100) COMMENT '下载者',
    download_ip VARCHAR(45) COMMENT '下载IP地址',
    user_agent TEXT COMMENT '用户代理',
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
    
    FOREIGN KEY (file_id) REFERENCES uploaded_files(id) ON DELETE CASCADE,
    INDEX idx_file_id (file_id),
    INDEX idx_downloaded_by (downloaded_by),
    INDEX idx_downloaded_at (downloaded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文件访问统计表
CREATE TABLE IF NOT EXISTS file_access_stats (
    id VARCHAR(36) PRIMARY KEY,
    file_id VARCHAR(36) NOT NULL COMMENT '关联uploaded_files.id',
    access_type ENUM('view', 'download', 'edit') NOT NULL COMMENT '访问类型',
    access_count INT DEFAULT 1 COMMENT '访问次数',
    last_access_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后访问时间',
    
    UNIQUE KEY unique_file_access (file_id, access_type),
    FOREIGN KEY (file_id) REFERENCES uploaded_files(id) ON DELETE CASCADE,
    INDEX idx_file_id_stats (file_id),
    INDEX idx_access_type (access_type),
    INDEX idx_last_access (last_access_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建视图：文件信息汇总
DROP VIEW IF EXISTS v_file_summary;
CREATE VIEW v_file_summary AS
SELECT 
    f.id,
    f.original_name,
    f.filename,
    f.file_size,
    f.mime_type,
    f.extension,
    f.uploaded_by,
    f.uploaded_at,
    f.is_deleted,
    COALESCE(s_view.access_count, 0) as view_count,
    COALESCE(s_download.access_count, 0) as download_count,
    COALESCE(s_edit.access_count, 0) as edit_count,
    s_download.last_access_at as last_download_at
FROM uploaded_files f
LEFT JOIN file_access_stats s_view ON f.id = s_view.file_id AND s_view.access_type = 'view'
LEFT JOIN file_access_stats s_download ON f.id = s_download.file_id AND s_download.access_type = 'download'
LEFT JOIN file_access_stats s_edit ON f.id = s_edit.file_id AND s_edit.access_type = 'edit'
WHERE f.is_deleted = FALSE
ORDER BY f.uploaded_at DESC; 