import { Logger } from '@nestjs/common';
import { DatabaseService } from '../modules/database/services/database.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 权限初始化脚本
 * 
 * @description 用于初始化系统基础权限数据
 * <AUTHOR> Team
 * @since 2024-12-19
 */

const logger = new Logger('PermissionInit');

/**
 * 基础权限配置
 */
const BASE_PERMISSIONS = [
  // 用户管理权限
  {
    code: 'users.read',
    name: '查看用户',
    description: '允许查看用户列表和用户详情',
    module: 'users',
    resource: 'user',
    action: 'read',
  },
  {
    code: 'users.create',
    name: '创建用户',
    description: '允许创建新用户',
    module: 'users',
    resource: 'user',
    action: 'create',
  },
  {
    code: 'users.update',
    name: '更新用户',
    description: '允许更新用户信息',
    module: 'users',
    resource: 'user',
    action: 'update',
  },
  {
    code: 'users.delete',
    name: '删除用户',
    description: '允许删除用户',
    module: 'users',
    resource: 'user',
    action: 'delete',
  },
  {
    code: 'users.manage',
    name: '管理用户',
    description: '用户管理的所有权限',
    module: 'users',
    resource: 'user',
    action: 'manage',
  },

  // 角色管理权限
  {
    code: 'roles.read',
    name: '查看角色',
    description: '允许查看角色列表和角色详情',
    module: 'users',
    resource: 'role',
    action: 'read',
  },
  {
    code: 'roles.create',
    name: '创建角色',
    description: '允许创建新角色',
    module: 'users',
    resource: 'role',
    action: 'create',
  },
  {
    code: 'roles.update',
    name: '更新角色',
    description: '允许更新角色信息',
    module: 'users',
    resource: 'role',
    action: 'update',
  },
  {
    code: 'roles.delete',
    name: '删除角色',
    description: '允许删除角色',
    module: 'users',
    resource: 'role',
    action: 'delete',
  },
  {
    code: 'roles.manage',
    name: '管理角色',
    description: '角色管理的所有权限',
    module: 'users',
    resource: 'role',
    action: 'manage',
  },
  {
    code: 'roles.assign_permissions',
    name: '分配权限',
    description: '允许为角色分配权限',
    module: 'users',
    resource: 'role',
    action: 'assign_permissions',
  },

  // 权限管理权限
  {
    code: 'permissions.read',
    name: '查看权限',
    description: '允许查看权限列表和权限详情',
    module: 'users',
    resource: 'permission',
    action: 'read',
  },
  {
    code: 'permissions.create',
    name: '创建权限',
    description: '允许创建新权限',
    module: 'users',
    resource: 'permission',
    action: 'create',
  },
  {
    code: 'permissions.update',
    name: '更新权限',
    description: '允许更新权限信息',
    module: 'users',
    resource: 'permission',
    action: 'update',
  },
  {
    code: 'permissions.delete',
    name: '删除权限',
    description: '允许删除权限',
    module: 'users',
    resource: 'permission',
    action: 'delete',
  },
  {
    code: 'permissions.manage',
    name: '管理权限',
    description: '权限管理的所有权限',
    module: 'users',
    resource: 'permission',
    action: 'manage',
  },
  {
    code: 'permissions.check',
    name: '检查权限',
    description: '允许检查用户权限',
    module: 'users',
    resource: 'permission',
    action: 'check',
  },

  // 文档管理权限
  {
    code: 'documents.read',
    name: '查看文档',
    description: '允许查看文档列表和文档详情',
    module: 'documents',
    resource: 'document',
    action: 'read',
  },
  {
    code: 'documents.create',
    name: '创建文档',
    description: '允许创建新文档',
    module: 'documents',
    resource: 'document',
    action: 'create',
  },
  {
    code: 'documents.update',
    name: '更新文档',
    description: '允许更新文档内容',
    module: 'documents',
    resource: 'document',
    action: 'update',
  },
  {
    code: 'documents.delete',
    name: '删除文档',
    description: '允许删除文档',
    module: 'documents',
    resource: 'document',
    action: 'delete',
  },
  {
    code: 'documents.manage',
    name: '管理文档',
    description: '文档管理的所有权限',
    module: 'documents',
    resource: 'document',
    action: 'manage',
  },

  // 模板管理权限
  {
    code: 'templates.read',
    name: '查看模板',
    description: '允许查看模板列表和模板详情',
    module: 'templates',
    resource: 'template',
    action: 'read',
  },
  {
    code: 'templates.create',
    name: '创建模板',
    description: '允许创建新模板',
    module: 'templates',
    resource: 'template',
    action: 'create',
  },
  {
    code: 'templates.update',
    name: '更新模板',
    description: '允许更新模板内容',
    module: 'templates',
    resource: 'template',
    action: 'update',
  },
  {
    code: 'templates.delete',
    name: '删除模板',
    description: '允许删除模板',
    module: 'templates',
    resource: 'template',
    action: 'delete',
  },
  {
    code: 'templates.manage',
    name: '管理模板',
    description: '模板管理的所有权限',
    module: 'templates',
    resource: 'template',
    action: 'manage',
  },

  // 系统配置权限
  {
    code: 'config.read',
    name: '查看配置',
    description: '允许查看系统配置',
    module: 'config',
    resource: 'config',
    action: 'read',
  },
  {
    code: 'config.update',
    name: '更新配置',
    description: '允许更新系统配置',
    module: 'config',
    resource: 'config',
    action: 'update',
  },
  {
    code: 'config.manage',
    name: '管理配置',
    description: '系统配置管理的所有权限',
    module: 'config',
    resource: 'config',
    action: 'manage',
  },

  // 文件上传权限
  {
    code: 'upload.execute',
    name: '文件上传',
    description: '允许上传文件',
    module: 'upload',
    resource: 'file',
    action: 'execute',
  },
  {
    code: 'upload.manage',
    name: '管理上传',
    description: '文件上传管理的所有权限',
    module: 'upload',
    resource: 'file',
    action: 'manage',
  },

  // 编辑器权限
  {
    code: 'editor.read',
    name: '查看编辑器',
    description: '允许查看编辑器界面',
    module: 'editor',
    resource: 'editor',
    action: 'read',
  },
  {
    code: 'editor.manage',
    name: '管理编辑器',
    description: '编辑器管理的所有权限',
    module: 'editor',
    resource: 'editor',
    action: 'manage',
  },

  // 超级管理员权限
  {
    code: '*',
    name: '超级管理员',
    description: '拥有系统所有权限',
    module: 'system',
    resource: '*',
    action: 'manage',
  },
];

/**
 * 初始化权限
 */
export async function initPermissions(databaseService: DatabaseService): Promise<void> {
  logger.log('开始初始化权限数据...');

  try {
    // 检查是否已经初始化过权限
    const existingPermissions = await databaseService.query(
      'SELECT COUNT(*) as count FROM user_permissions WHERE module IN (?, ?, ?, ?, ?, ?, ?)',
      ['users', 'documents', 'templates', 'config', 'upload', 'editor', 'system']
    ) as Array<{ count: number }>;

    if (existingPermissions[0].count > 0) {
      logger.log('权限已经初始化过，跳过权限创建');
      return;
    }

    // 创建权限
    const systemUserId = 'system-init';
    const now = new Date();
    let createdCount = 0;

    for (const permission of BASE_PERMISSIONS) {
      const permissionId = uuidv4();
      
      const sql = `
        INSERT INTO user_permissions (
          id, code, name, description, module, resource, action,
          is_active, sort_order, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?)
      `;

      const values = [
        permissionId,
        permission.code,
        permission.name,
        permission.description,
        permission.module,
        permission.resource || null,
        permission.action || null,
        createdCount * 10, // 排序序号
        systemUserId,
        now,
        now,
      ];

      try {
        await databaseService.query(sql, values);
        createdCount++;
        logger.log(`权限创建成功: ${permission.code} - ${permission.name}`);
      } catch (error) {
        logger.error(`权限创建失败: ${permission.code} - ${error.message}`);
      }
    }

    logger.log(`权限初始化完成，共创建 ${createdCount} 个权限`);

    // 创建默认的超级管理员角色
    await createSuperAdminRole(databaseService, systemUserId, now);

  } catch (error) {
    logger.error('权限初始化失败:', error);
    throw error;
  }
}

/**
 * 创建超级管理员角色
 */
async function createSuperAdminRole(databaseService: DatabaseService, systemUserId: string, now: Date): Promise<void> {
  try {
    // 检查是否已存在超级管理员角色
    const existingRole = await databaseService.query(
      'SELECT id FROM user_roles_detail WHERE name = ?',
      ['super_admin']
    );

    if (existingRole.length > 0) {
      logger.log('超级管理员角色已存在，跳过创建');
      return;
    }

    // 创建超级管理员角色
    const roleId = uuidv4();
    const createRoleSql = `
      INSERT INTO user_roles_detail (
        id, name, display_name, description, permissions, is_system, is_active,
        sort_order, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, 1, 1, 0, ?, ?, ?)
    `;

    const roleValues = [
      roleId,
      'super_admin',
      '超级管理员',
      '拥有系统所有权限的超级管理员角色',
      JSON.stringify(['*']), // 超级管理员拥有所有权限
      systemUserId,
      now,
      now,
    ];

    await databaseService.query(createRoleSql, roleValues);
    logger.log('超级管理员角色创建成功');

    // 为角色分配权限
    const assignPermissionSql = `
      INSERT INTO role_permissions (role_id, permission_code, created_at)
      VALUES (?, ?, ?)
    `;

    await databaseService.query(assignPermissionSql, [roleId, '*', now]);
    logger.log('超级管理员角色权限分配成功');

  } catch (error) {
    logger.error('创建超级管理员角色失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
export async function main(): Promise<void> {
  const databaseService = new DatabaseService();
  
  try {
    // 数据库服务会在构造时自动初始化
    await databaseService.onModuleInit();
    
    // 确保数据库连接
    await databaseService.query('SELECT 1');
    logger.log('数据库连接成功');
    
    // 初始化权限
    await initPermissions(databaseService);
    
    logger.log('权限初始化脚本执行完成');
    process.exit(0);
  } catch (error) {
    logger.error('权限初始化脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
} 