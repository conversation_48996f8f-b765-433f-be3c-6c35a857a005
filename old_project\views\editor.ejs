<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        <%= docTitle %> - 在线编辑
    </title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            height: 100%;
        }

        body {
            height: 100%;
            padding-top: 50px;
            /* 为固定头部预留空间 */
            box-sizing: border-box;
            /* 确保padding包含在总高度内 */
            overflow: hidden;
            /* 防止意外滚动 */
            position: relative;
            /* 可选，为内部绝对定位元素提供上下文 */
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 5px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10000;
            /* 确保高层级 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .header h1 {
            font-size: 18px;
            font-weight: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 60%;
        }

        .header a {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            transition: background-color 0.2s;
        }

        .header a:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* 保存状态指示器样式 */
        .save-status {
            display: flex;
            align-items: center;
            color: white;
            font-size: 14px;
            margin-right: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-saved {
            background-color: #2ecc71;
        }

        .status-saving {
            background-color: #f39c12;
            animation: pulse 1s infinite;
        }

        .status-error {
            background-color: #e74c3c;
        }

        @keyframes pulse {
            0% {
                opacity: 0.5;
            }

            50% {
                opacity: 1;
            }

            100% {
                opacity: 0.5;
            }
        }

        .header-actions {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        .save-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            margin-right: 15px;
            cursor: pointer;
            font-size: 14px;
        }

        .save-button:hover {
            background-color: #2980b9;
        }

        #loadingMessage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            color: #666;
            text-align: center;
        }

        #errorMessage {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            color: #e74c3c;
            text-align: center;
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 80%;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 70px;
            right: 20px;
            background-color: #2c3e50;
            color: white;
            padding: 15px 25px;
            border-radius: 4px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.3s, transform 0.3s;
            font-size: 15px;
            min-width: 250px;
            max-width: 400px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .notification.success {
            background-color: #27ae60;
            border-left: 5px solid #219653;
        }

        .notification.error {
            background-color: #e74c3c;
            border-left: 5px solid #c0392b;
        }

        .notification.warning {
            background-color: #f39c12;
            border-left: 5px solid #d35400;
        }

        .notification.info {
            background-color: #3498db;
            border-left: 5px solid #2980b9;
        }

        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }

        .notification-close {
            cursor: pointer;
            margin-left: 15px;
            font-size: 18px;
            opacity: 0.8;
        }

        .notification-close:hover {
            opacity: 1;
        }

        /* 进度条样式 */
        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(255, 255, 255, 0.5);
            width: 100%;
            transform-origin: left;
        }

        #placeholder {
            /* 移除 padding-top，因为body已经处理了 */
            overflow: hidden;
            /* 裁剪内部可能溢出的内容 */
            position: relative;
            /* 为iframe内部的绝对定位提供上下文 */
            width: 100%;
            height: 100%;
            /* 填充body在padding-top以下的剩余空间 */
            /* background-color: lightblue; */
            /* 调试时可取消注释查看区域 */
        }
    </style>
    <script type="text/javascript" src="<%= apiUrl %>"></script>
</head>

<body>
    <div class="header">
        <h1>
            <%= docTitle %>
        </h1>
        <div class="header-actions">
            <button class="save-button" id="lockDocumentBtn" style="background-color: #e74c3c;">一键加密(仅填表单)</button>
            <button class="save-button" id="unlockDocumentBtn" style="background-color: #27ae60;">一键解锁文档</button>
            <div class="save-status">
                <span class="status-indicator status-saved" id="saveIndicator"></span>
                <span id="saveStatusText">已保存</span>
            </div>
            <button class="save-button" id="manualSaveBtn">强制保存</button>
            <a href="/">返回首页</a>
        </div>
    </div>

    <div id="placeholder">
        <div id="loadingMessage">正在加载文档，请稍候...</div>
        <div id="errorMessage"></div>
    </div>

    <!-- 通知元素 -->
    <div class="notification" id="notification">
        <span id="notification-text"></span>
        <span class="notification-close" onclick="hideNotification()">×</span>
        <div class="notification-progress" id="notification-progress"></div>
    </div>

    <script>
        // 从URL中获取文件ID
        const pathParts = window.location.pathname.split('/');
        const fileId = pathParts[pathParts.length - 1];

        // 保存状态变量
        let saveStatus = 'saved';
        let docEditor = null;
        let lastSaveTime = new Date();
        let autoSaveInterval = null;
        let hasUnsavedChanges = false;

        // 所有的功能函数定义
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.getElementById('notification');
            const notificationText = document.getElementById('notification-text');
            const notificationProgress = document.getElementById('notification-progress');

            notification.className = `notification ${type}`;
            notificationText.textContent = message;
            notification.classList.add('show');

            // 重置进度条
            notificationProgress.style.transform = 'scaleX(1)';
            notificationProgress.style.transition = 'none';

            setTimeout(() => {
                notificationProgress.style.transition = `transform ${duration}ms linear`;
                notificationProgress.style.transform = 'scaleX(0)';
            }, 10);

            setTimeout(() => {
                hideNotification();
            }, duration);
        }

        function hideNotification() {
            const notification = document.getElementById('notification');
            notification.classList.remove('show');
        }

        function updateSaveStatus(status, text = '') {
            const indicator = document.getElementById('saveIndicator');
            const statusText = document.getElementById('saveStatusText');

            indicator.className = 'status-indicator';
            saveStatus = status;

            switch (status) {
                case 'saved':
                    indicator.classList.add('status-saved');
                    statusText.textContent = text || '已保存';
                    break;
                case 'saving':
                    indicator.classList.add('status-saving');
                    statusText.textContent = text || '保存中...';
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    statusText.textContent = text || '保存失败';
                    break;
            }
        }

        // 强制保存函数
        function forceSave() {
            if (!docEditor || saveStatus === 'saving') {
                console.log('编辑器未就绪或正在保存中');
                return;
            }

            try {
                // 重试机制变量
                let retryCount = 0;
                const maxRetries = 3;
                const retryDelay = 2000;

                function attemptSave() {
                    updateSaveStatus('saving', '正在保存...');
                    showNotification('正在保存文档...', 'info');

                    fetch(`/api/editor/save/${fileId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            force: true,
                            timestamp: new Date().toISOString()
                        })
                    })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
                            return response.json();
                        })
                        .then(result => {
                            if (result.success) {
                                updateSaveStatus('saved', '保存成功');
                                showNotification('文档保存成功', 'success');
                                lastSaveTime = new Date();
                                hasUnsavedChanges = false;
                            } else {
                                throw new Error(result.message || '保存失败');
                            }
                        })
                        .catch(error => {
                            console.error('保存失败:', error);

                            if (retryCount < maxRetries) {
                                retryCount++;
                                updateSaveStatus('saving', `正在重试... (${retryCount}/${maxRetries})`);
                                showNotification(`保存失败，正在重试... (${retryCount}/${maxRetries})`, 'warning');
                                setTimeout(attemptSave, retryDelay);
                            } else {
                                updateSaveStatus('error', '保存失败');
                                showNotification(`保存失败: ${error.message}`, 'error');
                            }
                        });
                }

                attemptSave();
            } catch (error) {
                console.error('保存操作异常:', error);
                updateSaveStatus('error', '保存异常');
                showNotification(`保存异常: ${error.message}`, 'error');
            }
        }

        // 检查保存状态
        function checkSaveStatus() {
            // 调用API检查保存状态
            fetch(`/api/editor/check-save-status/${fileId}`)
                .then(response => response.json())
                .then(result => {
                    if (result.lastSaved) {
                        const savedTime = new Date(result.lastSaved);
                        const timeDiff = Math.abs(new Date() - savedTime);

                        if (timeDiff < 60000) { // 1分钟内
                            updateSaveStatus('saved', '已保存');
                        } else {
                            updateSaveStatus('saved', `已保存 (${Math.floor(timeDiff / 60000)}分钟前)`);
                        }
                    }
                })
                .catch(error => {
                    console.error('检查保存状态失败:', error);
                });
        }

        // 文档加密/解锁功能
        function lockDocument() {
            if (!docEditor) {
                showNotification('编辑器未就绪', 'warning');
                return;
            }

            showNotification('正在加密文档...', 'info');

            fetch(`/api/editor/lock/${fileId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showNotification('文档已加密，只能填写表单', 'success');
                    } else {
                        showNotification(`加密失败: ${result.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('加密失败:', error);
                    showNotification(`加密失败: ${error.message}`, 'error');
                });
        }

        function unlockDocument() {
            if (!docEditor) {
                showNotification('编辑器未就绪', 'warning');
                return;
            }

            showNotification('正在解锁文档...', 'info');

            fetch(`/api/editor/unlock/${fileId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showNotification('文档已解锁，可以正常编辑', 'success');
                    } else {
                        showNotification(`解锁失败: ${result.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('解锁失败:', error);
                    showNotification(`解锁失败: ${error.message}`, 'error');
                });
        }

        // 获取文档配置并初始化编辑器
        async function initializeEditor() {
            try {
                document.getElementById('loadingMessage').textContent = '正在获取配置...';

                // 从当前页面URL中提取查询参数
                const urlParams = new URLSearchParams(window.location.search);
                const queryString = urlParams.toString();

                // 构建API URL，如果有查询参数则添加
                let apiUrl = `/api/editor/config/${fileId}`;
                if (queryString) {
                    apiUrl += `?${queryString}`;
                }

                console.log('调用API:', apiUrl);

                // 获取配置 - 包含查询参数
                const response = await fetch(apiUrl);
                if (!response.ok) {
                    throw new Error(`配置获取失败: ${response.status}`);
                }

                const config = await response.json();

                document.getElementById('loadingMessage').textContent = '正在初始化编辑器...';

                // 创建文档配置
                const documentConfig = {
                    document: {
                        fileType: config.document.fileType,
                        key: config.document.key,
                        title: config.document.title,
                        url: config.document.url,
                        permissions: config.document.permissions || {}
                    },
                    documentType: config.documentType,
                    editorConfig: {
                        callbackUrl: config.editorConfig.callbackUrl,
                        lang: config.editorConfig.lang || 'zh',
                        mode: config.editorConfig.mode,
                        customization: config.editorConfig.customization || {},
                        user: config.editorConfig.user || { id: 'user-1', name: '默认用户' },
                        coEditing: config.editorConfig.coEditing || { mode: 'fast', change: true }
                    },
                    token: config.token
                };

                // 添加调试信息
                console.log('编辑器配置:', {
                    documentType: documentConfig.documentType,
                    fileType: documentConfig.document.fileType,
                    url: documentConfig.document.url,
                    mode: documentConfig.editorConfig.mode
                });

                // 添加事件处理器
                documentConfig.events = {
                    onAppReady: function () {
                        console.log('编辑器已准备就绪');
                        document.getElementById('loadingMessage').style.display = 'none';
                        showNotification('编辑器已准备就绪', 'success', 3000);

                        // 检查保存状态
                        checkSaveStatus();

                        // 设置定期检查保存状态
                        if (autoSaveInterval) {
                            clearInterval(autoSaveInterval);
                        }
                        autoSaveInterval = setInterval(checkSaveStatus, 30000); // 每30秒检查一次
                    },
                    onDocumentStateChange: function (event) {
                        console.log('文档状态变更:', event);

                        if (event && event.data === true) {
                            hasUnsavedChanges = true;
                            updateSaveStatus('saving', '有未保存的更改');
                        }
                    },
                    onSave: function (event) {
                        console.log('文档保存事件:', event);

                        if (event && event.data && event.data.url) {
                            updateSaveStatus('saved', '已保存');
                            lastSaveTime = new Date();
                            hasUnsavedChanges = false;
                            showNotification('文档已自动保存', 'success', 2000);
                        }
                    },
                    onError: function (event) {
                        console.error('编辑器错误:', event);
                        let errorMessage = '编辑器发生错误';

                        if (event && event.data) {
                            if (event.data.message) {
                                errorMessage = event.data.message;
                            } else if (typeof event.data === 'string') {
                                errorMessage = event.data;
                            }
                        }

                        document.getElementById('loadingMessage').style.display = 'none';
                        document.getElementById('errorMessage').textContent = errorMessage;
                        document.getElementById('errorMessage').style.display = 'block';
                        showNotification(errorMessage, 'error');
                    }
                };

                // 初始化OnlyOffice编辑器
                docEditor = new DocsAPI.DocEditor("placeholder", documentConfig);

            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('loadingMessage').style.display = 'none';
                document.getElementById('errorMessage').textContent = `初始化失败: ${error.message}`;
                document.getElementById('errorMessage').style.display = 'block';
                showNotification(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 绑定按钮事件
            document.getElementById('manualSaveBtn').addEventListener('click', forceSave);
            document.getElementById('lockDocumentBtn').addEventListener('click', lockDocument);
            document.getElementById('unlockDocumentBtn').addEventListener('click', unlockDocument);

            // 初始化编辑器
            initializeEditor();
        });

        // 页面卸载前检查未保存的更改
        window.addEventListener('beforeunload', function (e) {
            if (hasUnsavedChanges && saveStatus !== 'saving') {
                const message = '您有未保存的更改，确定要离开吗？';
                e.returnValue = message;
                return message;
            }
        });

        // 清理定时器
        window.addEventListener('unload', function () {
            if (autoSaveInterval) {
                clearInterval(autoSaveInterval);
            }
        });
    </script>
</body>

</html>