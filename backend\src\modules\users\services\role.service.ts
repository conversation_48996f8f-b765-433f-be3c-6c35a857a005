import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';
import { CreateRoleDto, UpdateRoleDto, RoleQueryDto } from '../dto/role.dto';
import { UserRole } from '../entities/user.entity';
import { v4 as uuidv4 } from 'uuid';

// 角色查询结果类型定义
interface RoleQueryResult {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string;
  is_system: number | boolean;
  is_active: number | boolean;
  sort_order: number;
  created_by?: string;
  created_at: Date | string;
  updated_by?: string;
  updated_at: Date | string;
  user_count: number | string;
}

interface CountQueryResult {
  total: number;
  count: number;
}

interface PermissionQueryResult {
  id: string;
  code: string;
  name: string;
  description?: string;
  module: string;
  resource?: string;
  action?: string;
  conditions?: string;
  is_active: number | boolean;
  sort_order: number;
  created_at: Date | string;
  updated_at: Date | string;
}

/**
 * 角色管理服务
 * 
 * 提供角色的CRUD操作、权限分配和用户关联管理
 * 
 * @service RoleService
 * <AUTHOR> Team
 * @since 2024-12-19
 */
@Injectable()
export class RoleService {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 获取角色列表（支持分页和筛选）
   */
  async findMany(query: RoleQueryDto): Promise<{
    data: UserRole[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      pageSize = 10,
      search,
      status,
      isSystem,
      sortBy = 'sort_order',
      sortOrder = 'ASC',
    } = query;

    let sql = `
      SELECT 
        r.*,
        COUNT(u.id) as user_count
      FROM user_roles r
      LEFT JOIN users u ON r.id = u.role_id
      WHERE 1=1
    `;

    const params: (string | number)[] = [];

    // 搜索条件
    if (search) {
      sql += ` AND (r.name LIKE ? OR r.display_name LIKE ? OR r.description LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // 状态筛选
    if (status !== undefined) {
      sql += ` AND r.is_active = ?`;
      params.push(status === 'active' ? 1 : 0);
    }

    // 系统角色筛选
    if (isSystem !== undefined) {
      sql += ` AND r.is_system = ?`;
      params.push(isSystem ? 1 : 0);
    }

    // 分组
    sql += ` GROUP BY r.id`;

    // 排序
    const validSortFields = ['name', 'display_name', 'created_at', 'sort_order', 'user_count'];
    const validSortOrders = ['ASC', 'DESC'];
    
    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
      if (sortBy === 'user_count') {
        sql += ` ORDER BY user_count ${sortOrder}`;
      } else {
        sql += ` ORDER BY r.${sortBy} ${sortOrder}`;
      }
    }

    // 分页
    const offset = (page - 1) * pageSize;
    sql += ` LIMIT ? OFFSET ?`;
    params.push(pageSize, offset);

    const results = await this.databaseService.query(sql, params) as RoleQueryResult[];

    // 获取总数
    let countSql = `
      SELECT COUNT(DISTINCT r.id) as total
      FROM user_roles r
      WHERE 1=1
    `;
    
    const countParams: (string | number)[] = [];
    if (search) {
      countSql += ` AND (r.name LIKE ? OR r.display_name LIKE ? OR r.description LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (status !== undefined) {
      countSql += ` AND r.is_active = ?`;
      countParams.push(status === 'active' ? 1 : 0);
    }
    
    if (isSystem !== undefined) {
      countSql += ` AND r.is_system = ?`;
      countParams.push(isSystem ? 1 : 0);
    }

    const countResult = await this.databaseService.query(countSql, countParams) as CountQueryResult[];
    const total = countResult[0]?.total || 0;

    // 处理结果数据
    const data = results.map(role => ({
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      description: role.description,
      permissions: this.parsePermissions(role.permissions),
      is_active: Boolean(role.is_active),
      is_system: Boolean(role.is_system),
      sort_order: role.sort_order,
      created_by: role.created_by,
      updated_by: role.updated_by,
      user_count: parseInt(String(role.user_count)) || 0,
      created_at: new Date(role.created_at),
      updated_at: new Date(role.updated_at),
    } as UserRole));

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 根据ID获取角色详情
   */
  async findById(id: string): Promise<UserRole | null> {
    const sql = `
      SELECT 
        r.*,
        COUNT(u.id) as user_count
      FROM user_roles r
      LEFT JOIN users u ON r.id = u.role_id
      WHERE r.id = ?
      GROUP BY r.id
    `;
    
    const results = await this.databaseService.query(sql, [id]) as RoleQueryResult[];
    
    if (results.length === 0) {
      return null;
    }

    const role = results[0];
    return {
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      description: role.description,
      permissions: this.parsePermissions(role.permissions),
      is_active: Boolean(role.is_active),
      is_system: Boolean(role.is_system),
      sort_order: role.sort_order,
      created_by: role.created_by,
      updated_by: role.updated_by,
      user_count: parseInt(String(role.user_count)) || 0,
      created_at: new Date(role.created_at),
      updated_at: new Date(role.updated_at),
    } as UserRole;
  }

  /**
   * 创建角色
   */
  async createRole(createRoleDto: CreateRoleDto, createdBy: string): Promise<UserRole> {
    const {
      name,
      display_name,
      description,
      permissions = [],
      is_active = true,
      sort_order = 0,
    } = createRoleDto;

    // 检查角色名称是否已存在
    const existingRole = await this.databaseService.query(
      'SELECT id FROM user_roles WHERE name = ?',
      [name]
    );

    if (existingRole.length > 0) {
      throw new Error('角色名称已存在');
    }

    const roleId = uuidv4();
    const now = new Date();

    const sql = `
      INSERT INTO user_roles 
      (id, name, display_name, description, permissions, is_system, is_active, sort_order, created_by, created_at, updated_by, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await this.databaseService.query(sql, [
      roleId,
      name,
      display_name,
      description || null,
      JSON.stringify(permissions),
      false, // 用户创建的角色不是系统角色
      is_active,
      sort_order,
      createdBy,
      now,
      createdBy,
      now,
    ]);

    return this.findById(roleId);
  }

  /**
   * 更新角色
   */
  async updateRole(id: string, updateRoleDto: UpdateRoleDto, updatedBy: string): Promise<UserRole> {
    const existingRole = await this.findById(id);
    if (!existingRole) {
      throw new Error('角色不存在');
    }

    // 系统角色不允许修改核心属性
    if (existingRole.is_system && (updateRoleDto.name || updateRoleDto.permissions)) {
      throw new Error('系统角色不允许修改名称和核心权限');
    }

    const updateFields: string[] = [];
    const updateValues: (string | number | Date | boolean | null)[] = [];

    // 动态构建更新字段
    if (updateRoleDto.name !== undefined) {
      // 检查新名称是否已存在
      const existingName = await this.databaseService.query(
        'SELECT id FROM user_roles WHERE name = ? AND id != ?',
        [updateRoleDto.name, id]
      );
      
      if (existingName.length > 0) {
        throw new Error('角色名称已存在');
      }
      
      updateFields.push('name = ?');
      updateValues.push(updateRoleDto.name);
    }

    if (updateRoleDto.display_name !== undefined) {
      updateFields.push('display_name = ?');
      updateValues.push(updateRoleDto.display_name);
    }

    if (updateRoleDto.description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(updateRoleDto.description);
    }

    if (updateRoleDto.permissions !== undefined) {
      updateFields.push('permissions = ?');
      updateValues.push(JSON.stringify(updateRoleDto.permissions));
    }

    if (updateRoleDto.is_active !== undefined) {
      updateFields.push('is_active = ?');
      updateValues.push(updateRoleDto.is_active);
    }

    if (updateRoleDto.sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateValues.push(updateRoleDto.sort_order);
    }

    if (updateFields.length === 0) {
      return existingRole; // 没有需要更新的字段
    }

    // 添加更新时间和更新者
    updateFields.push('updated_by = ?', 'updated_at = ?');
    updateValues.push(updatedBy, new Date());

    const sql = `UPDATE user_roles SET ${updateFields.join(', ')} WHERE id = ?`;
    updateValues.push(id);

    await this.databaseService.query(sql, updateValues);

    return this.findById(id);
  }

  /**
   * 删除角色
   */
  async deleteRole(id: string, _deletedBy: string): Promise<void> {
    const role = await this.findById(id);
    if (!role) {
      throw new Error('角色不存在');
    }

    if (role.is_system) {
      throw new Error('系统角色不允许删除');
    }

    // 检查是否有用户使用此角色
    const userCount = await this.getRoleUserCount(id);
    if (userCount > 0) {
      throw new Error(`无法删除角色，还有 ${userCount} 个用户正在使用此角色`);
    }

    await this.databaseService.query('DELETE FROM user_roles WHERE id = ?', [id]);
  }

  /**
   * 为角色分配权限
   */
  async assignPermissions(id: string, permissions: string[], updatedBy: string): Promise<UserRole> {
    const role = await this.findById(id);
    if (!role) {
      throw new Error('角色不存在');
    }

    const sql = `
      UPDATE user_roles 
      SET permissions = ?, updated_by = ?, updated_at = ?
      WHERE id = ?
    `;

    await this.databaseService.query(sql, [
      JSON.stringify(permissions),
      updatedBy,
      new Date(),
      id,
    ]);

    return this.findById(id);
  }

  /**
   * 获取角色的用户数量
   */
  async getRoleUserCount(roleId: string): Promise<number> {
    const result = await this.databaseService.query(
      'SELECT COUNT(*) as count FROM users WHERE role_id = ?',
      [roleId]
    ) as CountQueryResult[];
    
    return result[0]?.count || 0;
  }

  /**
   * 复制角色
   */
  async copyRole(id: string, createdBy: string): Promise<UserRole> {
    const originalRole = await this.findById(id);
    if (!originalRole) {
      throw new Error('原角色不存在');
    }

    // 生成新的角色名称
    let newRoleName = `${originalRole.name}_copy`;
    let counter = 1;
    let nameFound = false;
    
    while (!nameFound) {
      const existing = await this.databaseService.query(
        'SELECT id FROM user_roles WHERE name = ?',
        [newRoleName]
      );
      
      if (existing.length === 0) {
        nameFound = true;
      } else {
      newRoleName = `${originalRole.name}_copy_${counter}`;
      counter++;
      }
    }

    const createRoleDto: CreateRoleDto = {
      name: newRoleName,
      display_name: `${originalRole.display_name} (副本)`,
      description: originalRole.description,
      permissions: originalRole.permissions,
      is_active: originalRole.is_active,
      sort_order: originalRole.sort_order,
    };

    return this.createRole(createRoleDto, createdBy);
  }

  /**
   * 切换角色状态
   */
  async toggleRoleStatus(id: string, updatedBy: string): Promise<UserRole> {
    const role = await this.findById(id);
    if (!role) {
      throw new Error('角色不存在');
    }

    const newStatus = !role.is_active;
    
    await this.databaseService.query(
      'UPDATE user_roles SET is_active = ?, updated_by = ?, updated_at = ? WHERE id = ?',
      [newStatus, updatedBy, new Date(), id]
    );

    return this.findById(id);
  }

  /**
   * 获取所有可用权限
   */
  async getAllPermissions(): Promise<Array<Record<string, unknown>>> {
    const sql = `
      SELECT id, code, name, description, module, resource, action, is_active, sort_order
      FROM user_permissions 
      WHERE is_active = 1
      ORDER BY module, sort_order, name
    `;
    
    const results = await this.databaseService.query(sql) as PermissionQueryResult[];
    
    return results.map(permission => ({
      id: permission.id,
      code: permission.code,
      name: permission.name,
      description: permission.description,
      module: permission.module,
      resource: permission.resource,
      action: permission.action,
      sort_order: permission.sort_order,
      created_at: permission.created_at,
      updated_at: permission.updated_at,
      is_active: Boolean(permission.is_active),
    }));
  }

  /**
   * 解析权限字符串为数组
   */
  private parsePermissions(permissions: string | string[] | null | undefined): string[] {
    if (!permissions) {
      return [];
    }
    
    let permissionStr: string;
    
    if (typeof permissions === 'string') {
      permissionStr = permissions;
    } else if (Buffer.isBuffer(permissions)) {
      permissionStr = permissions.toString('utf8');
    } else if (typeof permissions === 'object') {
      try {
        if (Array.isArray(permissions)) {
          return permissions.filter(p => typeof p === 'string' && p.length > 0);
        } else {
          permissionStr = JSON.stringify(permissions);
        }
      } catch (error) {
        console.warn('[RoleService] 无法解析权限对象:', permissions, error instanceof Error ? error.message : String(error));
        return [];
      }
    } else {
      permissionStr = String(permissions);
    }
    
    permissionStr = permissionStr.trim();
    if (!permissionStr) {
      return [];
    }
    
    if (permissionStr === '*') {
      return ['*'];
    }
    
    if (permissionStr.startsWith('[') && permissionStr.endsWith(']')) {
      try {
        const parsed = JSON.parse(permissionStr);
        if (Array.isArray(parsed)) {
          return parsed.filter(p => typeof p === 'string' && p.length > 0);
        }
              } catch (error) {
          console.warn('[RoleService] 无法解析JSON权限数组:', permissionStr, error instanceof Error ? error.message : String(error));
      }
    }
    
    return permissionStr.split(',').map(p => p.trim()).filter(p => p.length > 0);
  }
} 