import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { UploadController } from './controllers/upload.controller';
import { UploadService } from './services/upload.service';
import { DatabaseModule } from '../database/database.module';
import { FilenetModule } from '../filenet/filenet.module';
import { DocumentsModule } from '../documents/documents.module';

/**
 * 文件上传模块
 * 提供文件上传、存储和管理功能
 * @version 2.0.0
 * @since 2024-12-19
 */
@Module({
  imports: [
    MulterModule.register({
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB 文件大小限制
      },
    }),
    DatabaseModule,
    FilenetModule,
    DocumentsModule,
  ],
  controllers: [UploadController],
  providers: [UploadService],
  exports: [UploadService],
})
export class UploadModule {} 