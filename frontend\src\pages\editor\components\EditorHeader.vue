<template>
  <div class="editor-header">
    <h1 class="doc-title">{{ docTitle }}</h1>

    <div class="header-actions">
      <button
        class="action-button lock-button"
        @click="$emit('lock-document')"
        :disabled="!isReady"
      >
        🔒 一键加密
      </button>

      <button
        class="action-button unlock-button"
        @click="$emit('unlock-document')"
        :disabled="!isReady"
      >
        🔓 一键解锁
      </button>

      <div class="save-status">
        <span
          class="status-indicator"
          :class="saveStatus.status"
          :style="{ backgroundColor: getStatusColor() }"
        ></span>
        <span class="status-text">{{ saveStatus.message }}</span>
      </div>

      <button
        class="action-button save-button"
        @click="$emit('force-save')"
        :disabled="!isReady || saveStatus.status === 'saving'"
      >
        💾 强制保存
      </button>

      <!-- 关闭操作按钮 - 在内嵌和弹窗模式下都显示 -->
      <div class="close-actions">
        <button
          class="action-button save-close-button"
          @click="handleSaveAndClose"
          :disabled="!isReady || saveStatus.status === 'saving'"
        >
          💾❌ 保存并关闭
        </button>
        <button class="action-button close-button" @click="handleDirectClose">❌ 直接关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Note: computed and isPopupMode imports removed as they are no longer needed
import type { SaveStatus } from '../composables/useSaveStatus'

/**
 * 编辑器头部组件
 *
 * @description 显示文档标题、保存状态和操作按钮
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

interface Props {
  /** 文档标题 */
  docTitle: string
  /** 保存状态 */
  saveStatus: SaveStatus
  /** 编辑器是否就绪 */
  isReady?: boolean
}

interface Emits {
  /** 强制保存事件 */
  (e: 'force-save'): void
  /** 锁定文档事件 */
  (e: 'lock-document'): void
  /** 解锁文档事件 */
  (e: 'unlock-document'): void
  /** 保存并关闭事件 */
  (e: 'save-and-close'): void
  /** 直接关闭事件 */
  (e: 'direct-close'): void
}

const props = withDefaults(defineProps<Props>(), {
  isReady: false,
})

const emit = defineEmits<Emits>()

// Note: isPopupMode check removed as buttons now show in both modes

/**
 * 保存并关闭
 */
const handleSaveAndClose = (): void => {
  emit('save-and-close')
}

/**
 * 直接关闭（不保存）
 */
const handleDirectClose = (): void => {
  // 显示确认对话框
  if (confirm('确定要直接关闭而不保存当前修改吗？未保存的内容将会丢失。')) {
    emit('direct-close')
  }
}

/**
 * 获取保存状态颜色
 */
const getStatusColor = (): string => {
  switch (props.saveStatus.status) {
    case 'saved':
      return '#2ecc71'
    case 'saving':
      return '#f39c12'
    case 'editing':
      return '#3498db'
    case 'error':
      return '#e74c3c'
    default:
      return '#2ecc71'
  }
}
</script>

<style scoped>
.editor-header {
  background-color: #2c3e50;
  color: white;
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  min-height: 60px;
  max-height: 60px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  flex-shrink: 0; /* 防止被压缩 */
}

.doc-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 40%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
}

.action-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.action-button:hover:not(:disabled) {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.action-button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
  opacity: 0.6;
}

.lock-button {
  background-color: #e74c3c;
}

.lock-button:hover:not(:disabled) {
  background-color: #c0392b;
}

.unlock-button {
  background-color: #27ae60;
}

.unlock-button:hover:not(:disabled) {
  background-color: #219653;
}

.close-button {
  background-color: #6c757d;
}

.close-button:hover:not(:disabled) {
  background-color: #545b62;
}

.close-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.save-close-button {
  background-color: #28a745;
}

.save-close-button:hover:not(:disabled) {
  background-color: #218838;
}

.save-close-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.status-indicator.saving {
  animation: pulse 1.5s infinite;
}

.status-text {
  font-size: 14px;
  white-space: nowrap;
}

.back-link {
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s ease;
}

.back-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
