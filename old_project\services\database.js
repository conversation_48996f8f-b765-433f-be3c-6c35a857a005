/**
 * 数据库服务
 * 提供数据库连接池和查询方法
 */
const mysql = require('mysql2/promise');
const dbConfig = require('../config/database');

// 创建数据库连接池
const pool = mysql.createPool({
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.user,
  password: dbConfig.password,
  database: dbConfig.database,
  waitForConnections: dbConfig.waitForConnections,
  connectionLimit: dbConfig.connectionLimit,
  queueLimit: dbConfig.queueLimit,
  namedPlaceholders: dbConfig.namedPlaceholders,
  charset: dbConfig.charset
});

/**
 * 初始化数据库
 * 创建必要的表结构
 */
async function initDatabase() {
  try {
    console.log('正在检查数据库初始化状态...');

    // 创建系统配置表（如果不存在）
    await pool.query(`
      CREATE TABLE IF NOT EXISTS system_settings (
        setting_key VARCHAR(100) PRIMARY KEY,
        setting_value TEXT NOT NULL,
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 检查数据库是否已经初始化过
    const initStatus = await pool.query(`
      SELECT setting_value FROM system_settings WHERE setting_key = 'database_initialized'
    `);

    // 如果有初始化记录且值为true，则跳过初始化
    if (initStatus[0].length > 0 && initStatus[0][0].setting_value === 'true') {
      console.log('数据库已经完成初始化，跳过初始化步骤。');
      return true;
    }

    console.log('正在初始化数据库结构...');

    // 创建文件表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS files (
        id VARCHAR(64) PRIMARY KEY,
        original_name VARCHAR(255) NOT NULL,
        storage_name VARCHAR(255) NOT NULL,
        file_size BIGINT NOT NULL,
        mime_type VARCHAR(100),
        extension VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_modified_by VARCHAR(100),
        version INT DEFAULT 1,
        is_deleted BOOLEAN DEFAULT FALSE,
        INDEX idx_original_name (original_name),
        INDEX idx_storage_name (storage_name),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 创建文件版本表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS filenet_document_versions (
        id VARCHAR(36) PRIMARY KEY,
        doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id',
        fn_doc_id VARCHAR(255) NOT NULL COMMENT 'FileNet Document ID for this version',
        version INT NOT NULL COMMENT 'Version number',
        file_hash VARCHAR(64) COMMENT 'SHA-256 hash of this version',
        modified_by VARCHAR(100) COMMENT 'User who created this version',
        modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When this version was created',
        file_size BIGINT COMMENT 'File size of this version in bytes',
        comment TEXT COMMENT 'Version comment/description',
        UNIQUE KEY unique_doc_version (doc_id, version),
        INDEX idx_version_fn_doc_id (fn_doc_id),
        FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 检查filenet_documents表是否存在
    const tableExists = await pool.query(`
            SELECT COUNT(*) as count FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'filenet_documents'
        `);

    if (tableExists[0][0].count === 0) {
      // 如果表不存在，创建它
      await pool.query(`
                CREATE TABLE IF NOT EXISTS filenet_documents (
                    id VARCHAR(36) PRIMARY KEY,
                    fn_doc_id VARCHAR(255) NOT NULL COMMENT 'FileNet Document ID',
                    original_name VARCHAR(255) NOT NULL COMMENT 'Original file name',
                    file_size BIGINT COMMENT 'File size in bytes',
                    mime_type VARCHAR(100) COMMENT 'MIME type of the file',
                    extension VARCHAR(20) COMMENT 'File extension',
                    version INT DEFAULT 1 COMMENT 'Current document version',
                    file_hash VARCHAR(64) COMMENT 'SHA-256 hash of file content',
                    created_by VARCHAR(100) COMMENT 'User who created the document',
                    last_modified_by VARCHAR(100) COMMENT 'User who last modified the document',
                    template_id VARCHAR(36) COMMENT 'ID of template if document is based on template',
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of upload to FileNet',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
                    is_deleted BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag',
                    UNIQUE KEY unique_fn_doc_id (fn_doc_id),
                    INDEX idx_original_name_filenet (original_name),
                    INDEX idx_file_hash (file_hash),
                    INDEX idx_template_id (template_id),
                    INDEX idx_is_deleted (is_deleted)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            `);
    } else {
      // 表已存在，添加缺失的列
      console.log('filenet_documents表已存在，检查并添加缺失的列...');

      // 我们需要先检查列是否存在，然后再添加
      async function checkAndAddColumn(tableName, columnName, columnDefinition, afterColumn = '') {
        try {
          // 检查列是否存在
          const columnExists = await pool.query(`
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = ? 
            AND column_name = ?
          `, [tableName, columnName]);

          if (columnExists[0][0].count === 0) {
            // 列不存在，添加它
            const afterClause = afterColumn ? ` AFTER ${afterColumn}` : '';
            await pool.query(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDefinition}${afterClause}`);
            console.log(`成功添加列 ${columnName} 到 ${tableName}`);
          } else {
            console.log(`列 ${columnName} 已存在于 ${tableName} (跳过)`);
          }
        } catch (error) {
          console.log(`添加列 ${columnName} 出错: ${error.message}`);
        }
      }

      // 检查索引是否存在，如果不存在则添加
      async function checkAndAddIndex(tableName, indexName, columnList) {
        try {
          // 检查索引是否存在
          const indexExists = await pool.query(`
            SELECT COUNT(*) as count 
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = ? 
            AND index_name = ?
          `, [tableName, indexName]);

          if (indexExists[0][0].count === 0) {
            // 索引不存在，添加它
            await pool.query(`ALTER TABLE ${tableName} ADD INDEX ${indexName} (${columnList})`);
            console.log(`成功添加索引 ${indexName} 到 ${tableName}`);
          } else {
            console.log(`索引 ${indexName} 已存在于 ${tableName} (跳过)`);
          }
        } catch (error) {
          console.log(`添加索引 ${indexName} 出错: ${error.message}`);
        }
      }

      // 待添加的列
      const columnsToAdd = [
        { name: 'file_size', definition: "BIGINT COMMENT 'File size in bytes'", after: 'original_name' },
        { name: 'mime_type', definition: "VARCHAR(100) COMMENT 'MIME type of the file'", after: 'file_size' },
        { name: 'extension', definition: "VARCHAR(20) COMMENT 'File extension'", after: 'mime_type' },
        { name: 'version', definition: "INT DEFAULT 1 COMMENT 'Current document version'", after: 'extension' },
        { name: 'file_hash', definition: "VARCHAR(64) COMMENT 'SHA-256 hash of file content'", after: 'version' },
        { name: 'created_by', definition: "VARCHAR(100) COMMENT 'User who created the document'", after: 'file_hash' },
        { name: 'last_modified_by', definition: "VARCHAR(100) COMMENT 'User who last modified the document'", after: 'created_by' },
        { name: 'template_id', definition: "VARCHAR(36) COMMENT 'ID of template if document is based on template'", after: 'last_modified_by' },
        { name: 'is_deleted', definition: "BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag'", after: 'updated_at' }
      ];

      // 待添加的索引
      const indexesToAdd = [
        { name: 'idx_file_hash', columns: 'file_hash' },
        { name: 'idx_template_id', columns: 'template_id' },
        { name: 'idx_is_deleted', columns: 'is_deleted' }
      ];

      // 添加所有缺失的列
      for (const column of columnsToAdd) {
        await checkAndAddColumn('filenet_documents', column.name, column.definition, column.after);
      }

      // 添加所有缺失的索引
      for (const index of indexesToAdd) {
        await checkAndAddIndex('filenet_documents', index.name, index.columns);
      }
    }

    // 创建模板分类表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS template_categories (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT 'Category name',
        parent_id VARCHAR(36) COMMENT 'Parent category ID for hierarchical categories',
        description TEXT COMMENT 'Category description',
        sort_order INT DEFAULT 0 COMMENT 'Sort order for display',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_parent_id (parent_id),
        FOREIGN KEY (parent_id) REFERENCES template_categories(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 创建模板表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS templates (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT 'Template name',
        category_id VARCHAR(36) COMMENT 'Category ID',
        doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id',
        description TEXT COMMENT 'Template description',
        current_version INT DEFAULT 1 COMMENT 'Current active version',
        created_by VARCHAR(100) COMMENT 'Creator',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by VARCHAR(100) COMMENT 'Last updater',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        status ENUM('enabled', 'disabled') DEFAULT 'enabled' COMMENT 'Template status',
        is_deleted BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag',
        INDEX idx_category (category_id),
        INDEX idx_status (status),
        INDEX idx_is_deleted (is_deleted),
        FOREIGN KEY (category_id) REFERENCES template_categories(id) ON DELETE SET NULL,
        FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE RESTRICT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 创建模板版本表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS template_versions (
        id VARCHAR(36) PRIMARY KEY,
        template_id VARCHAR(36) NOT NULL COMMENT 'Reference to templates.id',
        version INT NOT NULL COMMENT 'Version number',
        doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id for this version',
        comment TEXT COMMENT 'Version comment',
        modified_by VARCHAR(100) COMMENT 'User who created this version',
        modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When this version was created',
        file_hash VARCHAR(64) COMMENT 'SHA-256 hash of this version',
        FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE CASCADE,
        FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE RESTRICT,
        UNIQUE KEY unique_template_version (template_id, version)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 处理file_versions表和视图
    await pool.query(`DROP TABLE IF EXISTS file_versions`);

    await pool.query(`
      CREATE OR REPLACE VIEW file_versions AS 
      SELECT * FROM filenet_document_versions
    `);

    // 在初始化完成后，设置初始化标记
    await pool.query(
      `INSERT INTO system_settings (setting_key, setting_value, description) 
       VALUES ('database_initialized', 'true', '标记数据库是否已完成初始化') 
       ON DUPLICATE KEY UPDATE setting_value = 'true', updated_at = CURRENT_TIMESTAMP`
    );

    // Helper function to check and add foreign keys
    async function checkAndAddForeignKey(tableName, constraintName, columnName, referencedTable, referencedColumn, onDeleteAction = 'SET NULL', onUpdateAction = 'NO ACTION') {
      try {
        const [columnExistsResults] = await pool.query(
          `SELECT 1 
           FROM information_schema.columns 
           WHERE table_schema = DATABASE() 
           AND table_name = ? AND column_name = ?`,
          [tableName, columnName]
        );

        const [refColumnExistsResults] = await pool.query(
          `SELECT 1 
           FROM information_schema.columns 
           WHERE table_schema = DATABASE() 
           AND table_name = ? AND column_name = ?`,
          [referencedTable, referencedColumn]
        );

        if (columnExistsResults.length === 0) {
          console.log(`外键 ${constraintName} 跳过: 列 ${tableName}.${columnName} 不存在。`);
          return;
        }
        if (refColumnExistsResults.length === 0) {
          console.log(`外键 ${constraintName} 跳过: 引用列 ${referencedTable}.${referencedColumn} 不存在。`);
          return;
        }

        const [fkExistsResults] = await pool.query(
          `SELECT CONSTRAINT_NAME 
           FROM information_schema.KEY_COLUMN_USAGE 
           WHERE TABLE_SCHEMA = DATABASE() 
           AND TABLE_NAME = ? 
           AND COLUMN_NAME = ? 
           AND REFERENCED_TABLE_NAME = ? 
           AND REFERENCED_COLUMN_NAME = ? 
           AND CONSTRAINT_NAME = ?`,
          [tableName, columnName, referencedTable, referencedColumn, constraintName]
        );

        if (fkExistsResults.length === 0) {
          console.log(`正在添加外键 ${constraintName} 到 ${tableName}.${columnName} 引用 ${referencedTable}.${referencedColumn}...`);
          // Simpler string concatenation for fkSql to avoid backtick escaping issues
          let fkSql = 'ALTER TABLE `' + tableName + '` ADD CONSTRAINT `' + constraintName + '` ' +
            'FOREIGN KEY (`' + columnName + '`) REFERENCES `' + referencedTable + '`(`' + referencedColumn + '`)';
          if (onDeleteAction) {
            fkSql += ' ON DELETE ' + onDeleteAction;
          }
          // MySQL default ON UPDATE is RESTRICT. Uncomment to be explicit or change.
          // if (onUpdateAction && onUpdateAction !== 'NO ACTION') { // Only add if not default or explicitly set
          //     fkSql += ' ON UPDATE ' + onUpdateAction;
          // }
          await pool.query(fkSql);
          console.log(`外键 ${constraintName} 添加成功。`);
        } else {
          console.log(`外键 ${constraintName} 已存在于 ${tableName} (跳过)`);
        }
      } catch (error) {
        console.error(`检查或添加外键 ${constraintName} 到 ${tableName} 时出错: ${error.message}`);
      }
    }

    console.log('检查并确保外键约束...');
    await checkAndAddForeignKey('filenet_documents', 'fk_fdocs_template_id', 'template_id', 'templates', 'id', 'SET NULL');
    await checkAndAddForeignKey('template_categories', 'fk_tc_parent_id', 'parent_id', 'template_categories', 'id', 'SET NULL');
    await checkAndAddForeignKey('templates', 'fk_t_category_id', 'category_id', 'template_categories', 'id', 'SET NULL');
    await checkAndAddForeignKey('templates', 'fk_t_doc_id', 'doc_id', 'filenet_documents', 'id', 'RESTRICT');
    await checkAndAddForeignKey('template_versions', 'fk_tv_template_id', 'template_id', 'templates', 'id', 'CASCADE');
    await checkAndAddForeignKey('template_versions', 'fk_tv_doc_id', 'doc_id', 'filenet_documents', 'id', 'RESTRICT');

    console.log('数据库初始化完成，并已记录初始化状态。');
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

/**
 * 测试数据库连接
 * @returns {Promise<boolean>} 连接是否成功
 */
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    connection.release();
    console.log('数据库连接成功');
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    return false;
  }
}

/**
 * 执行SQL查询
 * @param {string} sql SQL语句
 * @param {Array|Object} params 查询参数
 * @returns {Promise<Array>} 查询结果
 */
async function query(sql, params = []) {
  try {
    const [rows] = await pool.query(sql, params);
    return rows;
  } catch (error) {
    console.error('SQL查询失败:', error);
    throw error;
  }
}

/**
 * 执行单个SQL查询并返回第一行结果
 * @param {string} sql SQL语句
 * @param {Array|Object} params 查询参数
 * @returns {Promise<Object>} 查询结果的第一行
 */
async function queryOne(sql, params = []) {
  const rows = await query(sql, params);
  return rows.length > 0 ? rows[0] : null;
}

/**
 * 执行事务
 * @param {Function} callback 事务回调函数，接收connection参数
 * @returns {Promise<any>} 事务执行结果
 */
async function transaction(callback) {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// 导出数据库服务
module.exports = {
  pool,
  query,
  queryOne,
  transaction,
  initDatabase,
  testConnection
}; 