<template>
  <div class="config-item" :class="{ compact: compact }">
    <div class="config-info">
      <div class="config-key">
        <span class="key-text">{{ config.setting_key }}</span>
        <a-tag v-if="isAdvanced" size="small" color="orange">高级</a-tag>
        <a-tag v-if="isSensitive" size="small" color="red">敏感</a-tag>
      </div>
      <div v-if="config.description" class="config-description">{{ config.description }}</div>
    </div>

    <div class="config-control">
      <div class="config-input">
        <a-input
          v-if="!isSensitive || showSensitive"
          v-model:value="localValue"
          :placeholder="config.setting_value"
          @blur="handleUpdate"
          @pressEnter="handleUpdate"
        />
        <a-input-password
          v-else
          v-model:value="localValue"
          :placeholder="maskValue(config.setting_value)"
          @blur="handleUpdate"
          @pressEnter="handleUpdate"
        />
      </div>

      <div class="config-actions">
        <a-tooltip title="测试连接">
          <a-button
            type="text"
            size="small"
            @click="handleTest"
            :disabled="!canTest"
            :class="{ compact: compact }"
          >
            <template #icon>
              <ApiOutlined />
            </template>
          </a-button>
        </a-tooltip>

        <a-tooltip v-if="isSensitive" :title="showSensitive ? '隐藏' : '显示'">
          <a-button type="text" size="small" @click="toggleSensitive" :class="{ compact: compact }">
            <template #icon>
              <EyeOutlined v-if="!showSensitive" />
              <EyeInvisibleOutlined v-else />
            </template>
          </a-button>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ApiOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'
import type { SystemConfig } from '../../../types/system-config'

interface Props {
  config: SystemConfig
  compact?: boolean
}

interface Emits {
  (e: 'update', key: string, value: string): void
  (e: 'test', config: SystemConfig): void
}

const props = withDefaults(defineProps<Props>(), {
  compact: false,
})

const emit = defineEmits<Emits>()

const localValue = ref('')
const showSensitive = ref(false)

// 计算属性
const isAdvanced = computed(() => {
  return (
    props.config.setting_key.includes('advanced') ||
    props.config.setting_key.includes('debug') ||
    props.config.setting_key.includes('dev')
  )
})

const isSensitive = computed(() => {
  const key = props.config.setting_key.toLowerCase()
  return (
    key.includes('password') ||
    key.includes('secret') ||
    key.includes('key') ||
    key.includes('token')
  )
})

const canTest = computed(() => {
  const key = props.config.setting_key.toLowerCase()
  return (
    key.includes('url') ||
    key.includes('host') ||
    key.includes('endpoint') ||
    key.includes('server')
  )
})

// 方法
const maskValue = (value: string): string => {
  if (!value) return ''
  return '*'.repeat(Math.min(value.length, 8))
}

const handleUpdate = (): void => {
  if (localValue.value !== props.config.setting_value) {
    emit('update', props.config.setting_key, localValue.value)
  }
}

const handleTest = (): void => {
  emit('test', props.config)
}

const toggleSensitive = (): void => {
  showSensitive.value = !showSensitive.value
}

// 监听配置变化
watch(
  () => props.config.setting_value,
  newValue => {
    localValue.value = newValue
  },
  { immediate: true }
)
</script>

<style scoped>
.config-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.2s;
}

.config-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.config-item.compact {
  padding: 12px;
  gap: 12px;
  background: transparent;
  border: none;
  border-radius: 4px;
}

.config-item.compact:hover {
  background: #f5f5f5;
  box-shadow: none;
}

.config-info {
  flex: 1;
  min-width: 0;
}

.config-key {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.key-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  font-weight: 500;
  color: #1890ff;
  word-break: break-all;
}

.config-item.compact .key-text {
  font-size: 12px;
}

.config-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  margin-top: 2px;
}

.config-item.compact .config-description {
  font-size: 11px;
}

.config-control {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.config-item.compact .config-control {
  min-width: 160px;
}

.config-input {
  flex: 1;
}

.config-actions {
  display: flex;
  gap: 4px;
}

.config-actions .ant-btn.compact {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.config-actions .ant-btn.compact .anticon {
  font-size: 12px;
}

/* 特殊标记 */
.ant-tag {
  margin: 0;
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  border-radius: 2px;
}

.config-item.compact .ant-tag {
  font-size: 9px;
  height: 16px;
  line-height: 14px;
}
</style>
