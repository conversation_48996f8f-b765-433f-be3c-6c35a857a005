<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 创意版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            color: #2d3748;
            line-height: 1.6;
            min-height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* 头部区域 - 创意设计 */
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            margin-bottom: 32px;
            border-radius: 0 0 32px 32px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .app-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 26px;
            font-weight: bold;
            animation: bounce 2s infinite;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .header-text h1 {
            font-size: 30px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 4px;
        }

        .header-text p {
            color: #718096;
            font-size: 16px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b6b;
            border: 2px solid #ff6b6b;
        }

        .btn-outline:hover {
            background: #ff6b6b;
            color: white;
        }

        /* 主要内容布局 */
        .main-layout {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 32px;
            margin-bottom: 32px;
        }

        /* 左侧模板区域 - 创意卡片 */
        .templates-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 32px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            height: fit-content;
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .templates-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 107, 107, 0.05) 50%, transparent 70%);
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .section-header {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-subtitle {
            font-size: 14px;
            color: #718096;
            margin-top: 4px;
        }

        .add-btn {
            width: 44px;
            height: 44px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            position: relative;
            z-index: 1;
        }

        .add-btn:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .search-container {
            position: relative;
            margin-bottom: 24px;
            z-index: 1;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 3px solid transparent;
            border-radius: 25px;
            font-size: 14px;
            background: rgba(247, 250, 252, 0.8);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            border-color: #ff6b6b;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 0 4px rgba(255, 107, 107, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #ff6b6b;
            font-size: 16px;
        }

        .template-card {
            background: rgba(255, 255, 255, 0.8);
            border: 3px solid transparent;
            border-radius: 24px;
            padding: 24px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            z-index: 1;
        }

        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .template-card::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.1), transparent);
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
        }

        .template-card:hover {
            border-color: #ff6b6b;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
        }

        .template-card:hover::before {
            opacity: 1;
        }

        .template-card:hover::after {
            width: 200px;
            height: 200px;
        }

        .template-card.active {
            border-color: #4ecdc4;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 20px 40px rgba(78, 205, 196, 0.25);
            transform: translateY(-4px);
        }

        .template-card.active::before {
            opacity: 1;
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
            position: relative;
            z-index: 2;
        }

        .template-name {
            font-size: 18px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .template-status {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-default {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
        }

        .status-active {
            background: linear-gradient(45deg, #4ecdc4, #6eddd6);
            color: white;
        }

        .status-inactive {
            background: #e2e8f0;
            color: #718096;
        }

        .template-desc {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
            position: relative;
            z-index: 2;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #a0aec0;
            position: relative;
            z-index: 2;
        }

        .template-actions {
            display: flex;
            gap: 6px;
        }

        .action-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .action-btn:hover {
            background: #ff6b6b;
            color: white;
            transform: scale(1.2) rotate(15deg);
        }

        /* 右侧配置区域 - 创意设计 */
        .config-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            height: fit-content;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .config-header {
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1);
            color: white;
            padding: 36px;
            position: relative;
            overflow: hidden;
        }

        .config-header::before {
            content: '';
            position: absolute;
            top: -100%;
            left: -100%;
            width: 300%;
            height: 300%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.15)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="80" r="2.5" fill="rgba(255,255,255,0.12)"/></svg>') repeat;
            animation: float 25s linear infinite;
        }

        .config-title {
            font-size: 26px;
            font-weight: 800;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .config-subtitle {
            opacity: 0.9;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .config-nav {
            background: rgba(247, 250, 252, 0.8);
            padding: 0 24px;
            display: flex;
            gap: 4px;
            overflow-x: auto;
            backdrop-filter: blur(10px);
        }

        .nav-tab {
            padding: 16px 20px;
            background: transparent;
            border: none;
            color: #718096;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 20px 20px 0 0;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-tab.active {
            background: rgba(255, 255, 255, 0.95);
            color: #ff6b6b;
            backdrop-filter: blur(10px);
        }

        .nav-tab.active::before {
            width: 80%;
        }

        .nav-tab:hover {
            color: #ff6b6b;
            background: rgba(255, 255, 255, 0.7);
        }

        .config-content {
            padding: 32px;
            max-height: 600px;
            overflow-y: auto;
        }

        .config-group {
            margin-bottom: 40px;
        }

        .group-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .group-icon {
            width: 52px;
            height: 52px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .group-title {
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
        }

        .config-card {
            background: rgba(247, 250, 252, 0.8);
            border: 3px solid transparent;
            border-radius: 24px;
            padding: 24px;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .config-card::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(78, 205, 196, 0.1), transparent);
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
        }

        .config-card:hover {
            border-color: rgba(255, 107, 107, 0.3);
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 12px 30px rgba(255, 107, 107, 0.15);
            transform: translateY(-4px);
        }

        .config-card:hover::before {
            width: 200px;
            height: 200px;
        }

        .config-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
            position: relative;
            z-index: 2;
        }

        .config-item-title {
            font-size: 16px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 4px;
        }

        .config-item-desc {
            font-size: 13px;
            color: #718096;
            line-height: 1.4;
        }

        .required-indicator {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px rgba(255, 107, 107, 0.3); }
            to { box-shadow: 0 0 15px rgba(255, 107, 107, 0.6); }
        }

        .switch-controls {
            display: flex;
            flex-direction: column;
            gap: 16px;
            position: relative;
            z-index: 2;
        }

        .switch-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .switch-label {
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
        }

        .toggle-switch {
            position: relative;
            width: 54px;
            height: 30px;
            background: #e2e8f0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch.active {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }

        .toggle-thumb {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            transition: all 0.4s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-thumb {
            transform: translateX(24px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .config-status {
            margin-top: 16px;
            padding: 12px;
            background: rgba(255, 107, 107, 0.05);
            border-radius: 16px;
            border-left: 4px solid #ff6b6b;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
        }

        .status-text {
            font-size: 12px;
            color: #ff6b6b;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .status-enabled {
            background: linear-gradient(45deg, #4ecdc4, #6eddd6);
        }

        .status-disabled {
            background: #cbd5e0;
        }

        /* 底部操作栏 */
        .action-bar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            padding: 24px 32px;
            border-top: 1px solid rgba(226, 232, 240, 0.5);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .save-info {
            color: #718096;
            font-size: 14px;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
        }

        .btn-secondary {
            background: rgba(226, 232, 240, 0.8);
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .templates-section {
                order: 2;
            }

            .config-section {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .config-grid {
                grid-template-columns: 1fr;
            }

            .config-content,
            .templates-section {
                padding: 24px;
            }
        }

        /* 滚动条创意样式 */
        .config-content::-webkit-scrollbar {
            width: 8px;
        }

        .config-content::-webkit-scrollbar-track {
            background: rgba(247, 250, 252, 0.5);
            border-radius: 4px;
        }

        .config-content::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 4px;
        }

        .config-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #ff5252, #26c6da);
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="header-left">
                    <div class="app-logo">✨</div>
                    <div class="header-text">
                        <h1>创意配置工作室</h1>
                        <p>让配置管理变得生动有趣</p>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-download"></i>
                        导入配置
                    </a>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i>
                        创建模板
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <div class="container">
        <div class="main-layout">
            <!-- 左侧模板列表 -->
            <aside class="templates-section">
                <div class="section-header">
                    <div>
                        <h2 class="section-title">配置魔法盒</h2>
                        <p class="section-subtitle">选择你的配置魔法</p>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索配置魔法...">
                </div>

                <div class="templates-list">
                    <div class="template-card active">
                        <div class="template-header">
                            <div class="template-name">🎨 创意编辑模板</div>
                            <div class="template-status">
                                <span class="status-badge status-default">默认</span>
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            为创意工作者量身定制的编辑环境，支持丰富的视觉效果和交互体验，让文档编辑变得更加有趣。
                        </div>
                        <div class="template-meta">
                            <span>✨ 今天更新</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">🚀 团队协作模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            专为团队协作打造的超级模板，实时同步、智能提醒、协作冲突解决，让团队合作如丝般顺滑。
                        </div>
                        <div class="template-meta">
                            <span>🔥 昨天更新</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">🎭 演示模式模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            专为演示和展示设计的特殊模板，大字体、高对比度、精简界面，让你的演示更加专业。
                        </div>
                        <div class="template-meta">
                            <span>💫 一周前</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧配置区域 -->
            <main class="config-section">
                <div class="config-header">
                    <h2 class="config-title">🎨 创意编辑模板配置</h2>
                    <p class="config-subtitle">用魔法般的设置创造完美的编辑体验</p>
                </div>

                <nav class="config-nav">
                    <button class="nav-tab active">✨ 魔法权限</button>
                    <button class="nav-tab">🎨 视觉定制</button>
                    <button class="nav-tab">🚀 功能增强</button>
                    <button class="nav-tab">👥 协作魔法</button>
                    <button class="nav-tab">⚡ 高级魔法</button>
                </nav>

                <div class="config-content">
                    <div class="config-group">
                        <div class="group-header">
                            <div class="group-icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <h3 class="group-title">创意权限魔法</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">🎨 创意编辑权限</div>
                                        <div class="config-item-desc">释放无限创意，享受自由编辑的乐趣</div>
                                    </div>
                                    <span class="required-indicator">核心</span>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">开启创意模式</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示魔法按钮</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        ✨ 创意魔法已激活
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">🌈 色彩魔法权限</div>
                                        <div class="config-item-desc">使用丰富的颜色让文档生动起来</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">启用色彩魔法</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示调色板</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        🌈 色彩魔法已就绪
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">🔒 安全保护魔法</div>
                                        <div class="config-item-desc">保护你的创意作品不被意外修改</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">启用保护魔法</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示保护盾</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-disabled"></div>
                                        🛡️ 保护魔法待唤醒
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">💬 社交魔法权限</div>
                                        <div class="config-item-desc">与朋友一起创作和分享精彩内容</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">开启社交模式</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示分享按钮</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        💬 社交魔法运行中
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-bar">
                    <div class="save-info">
                        <i class="fas fa-star"></i>
                        魔法同步于 刚刚
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            重置魔法
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-magic"></i>
                            施展魔法
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模板卡片选择
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    templateCards.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 添加选择动画
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });

            // 标签页切换
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    navTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 添加切换动画
                    this.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 开关切换
            const toggleSwitches = document.querySelectorAll('.toggle-switch');
            toggleSwitches.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 更新状态显示
                    const card = this.closest('.config-card');
                    const statusText = card.querySelector('.status-text');
                    const switches = card.querySelectorAll('.toggle-switch');
                    
                    const enableSwitch = switches[0];
                    const visibilitySwitch = switches[1];
                    
                    if (enableSwitch.classList.contains('active') && visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>✨ 魔法已激活并显示';
                    } else if (enableSwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>🔮 魔法已激活但隐藏';
                    } else if (visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>👻 魔法未激活但可见';
                    } else {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>💤 魔法处于休眠状态';
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                templateCards.forEach(card => {
                    const name = card.querySelector('.template-name').textContent.toLowerCase();
                    const desc = card.querySelector('.template-desc').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || desc.includes(searchTerm)) {
                        card.style.display = 'block';
                        card.style.animation = 'slideInUpEnterprise 0.3s ease';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });

            // 添加悬浮效果
            const cards = document.querySelectorAll('.config-card, .template-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.filter = 'brightness(1.05)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.filter = '';
                });
            });
        });
    </script>
</body>
</html> 