/**
 * 后端文档模板原始数据接口
 */
export interface BackendDocumentTemplate {
  id: string
  name: string
  description?: string
  extension: string
  category_name?: string
  category_id?: string
  status: 'enabled' | 'disabled'
  file_size?: number
  created_at: string
  updated_at: string
  created_by: string
}

/**
 * 后端配置模板原始数据接口
 */
export interface BackendConfigTemplate {
  id: string
  name: string
  description?: string
  permissions: {
    edit: boolean
    download: boolean
    review: boolean
    comment: boolean
    fillForms: boolean
    modifyFilter: boolean
    modifyContentControl: boolean
    chat: boolean
    copy: boolean
    print: boolean
    protect: boolean
  }
  customization: {
    autosave: boolean
    forcesave: boolean
    chat: boolean
    comments: boolean
    help: boolean
    about: boolean
    feedback: boolean
    review: boolean
    toolbarNoTabs: boolean
    toolbarHideFileName: boolean
  }
  coEditing: {
    mode: 'fast' | 'strict'
    change: boolean
  }
  user: {
    id: string
    name: string
  }
  created_at: string
  updated_at: string
  is_default: boolean
}

/**
 * 后端分类原始数据接口
 */
export interface BackendTemplateCategory {
  id: string
  name: string
  description?: string
  parent_id?: string | null
  created_at: string
  template_count?: number
}

/**
 * 后端API响应包装接口
 */
export interface BackendApiResponse<T> {
  success: boolean
  data: T
  message?: string
}

/**
 * 后端分页响应接口
 */
export interface BackendPaginationResponse<T> {
  templates: T[]
  total: number
  limit: number
  offset: number
}
