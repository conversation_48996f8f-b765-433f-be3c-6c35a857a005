<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 紧凑版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #2d3748;
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* 紧凑型头部 */
        .header {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 12px 0;
            margin-bottom: 16px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .app-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .header-text h1 {
            font-size: 20px;
            font-weight: 700;
            color: #1a202c;
            margin: 0;
        }

        .header-text p {
            font-size: 12px;
            color: #718096;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
        }

        .btn-outline {
            background: white;
            color: #4f46e5;
            border: 1px solid #4f46e5;
        }

        .btn-outline:hover {
            background: #4f46e5;
            color: white;
        }

        /* 紧凑型主布局 */
        .main-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        /* 左侧模板区域 - 紧凑设计 */
        .templates-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            height: fit-content;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e2e8f0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #1a202c;
        }

        .section-subtitle {
            font-size: 11px;
            color: #718096;
            margin-top: 2px;
        }

        .add-btn {
            width: 28px;
            height: 28px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .add-btn:hover {
            background: #4338ca;
            transform: scale(1.05);
        }

        .search-container {
            position: relative;
            margin-bottom: 12px;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 32px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 12px;
            background: #f7fafc;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #4f46e5;
            background: white;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 12px;
        }

        .template-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .template-card:hover {
            border-color: #4f46e5;
            background: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
        }

        .template-card.active {
            border-color: #4f46e5;
            background: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 6px;
        }

        .template-name {
            font-size: 14px;
            font-weight: 600;
            color: #1a202c;
            line-height: 1.3;
        }

        .template-status {
            display: flex;
            gap: 4px;
        }

        .status-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 9px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-default {
            background: #fed7d7;
            color: #c53030;
        }

        .status-active {
            background: #c6f6d5;
            color: #38a169;
        }

        .status-inactive {
            background: #e2e8f0;
            color: #718096;
        }

        .template-desc {
            color: #718096;
            font-size: 11px;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 10px;
            color: #a0aec0;
        }

        .template-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            width: 20px;
            height: 20px;
            border: none;
            border-radius: 4px;
            background: #e2e8f0;
            color: #718096;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 10px;
        }

        .action-btn:hover {
            background: #cbd5e0;
            color: #4a5568;
        }

        /* 右侧配置区域 - 紧凑设计 */
        .config-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            height: fit-content;
        }

        .config-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 16px;
        }

        .config-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .config-subtitle {
            opacity: 0.9;
            font-size: 12px;
        }

        .config-nav {
            background: #f7fafc;
            padding: 0 12px;
            display: flex;
            gap: 4px;
            overflow-x: auto;
        }

        .nav-tab {
            padding: 8px 12px;
            background: transparent;
            border: none;
            color: #718096;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .nav-tab.active {
            background: white;
            color: #4f46e5;
        }

        .nav-tab:hover {
            color: #4f46e5;
        }

        .config-content {
            padding: 16px;
            max-height: 480px;
            overflow-y: auto;
        }

        .config-group {
            margin-bottom: 20px;
        }

        .group-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .group-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .group-title {
            font-size: 14px;
            font-weight: 700;
            color: #1a202c;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 12px;
        }

        .config-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
        }

        .config-card:hover {
            border-color: #cbd5e0;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .config-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .config-item-title {
            font-size: 13px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 2px;
        }

        .config-item-desc {
            font-size: 10px;
            color: #718096;
            line-height: 1.3;
        }

        .required-indicator {
            background: #f56565;
            color: white;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 8px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .switch-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .switch-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .switch-label {
            font-size: 11px;
            font-weight: 500;
            color: #4a5568;
        }

        .toggle-switch {
            position: relative;
            width: 32px;
            height: 18px;
            background: #e2e8f0;
            border-radius: 9px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toggle-switch.active {
            background: #4f46e5;
        }

        .toggle-thumb {
            position: absolute;
            top: 1px;
            left: 1px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-thumb {
            transform: translateX(14px);
        }

        .config-status {
            margin-top: 8px;
            padding: 6px;
            background: rgba(79, 70, 229, 0.05);
            border-radius: 4px;
            border-left: 2px solid #4f46e5;
        }

        .status-text {
            font-size: 9px;
            color: #4f46e5;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-icon {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-enabled {
            background: #48bb78;
        }

        .status-disabled {
            background: #cbd5e0;
        }

        /* 紧凑型底部操作栏 */
        .action-bar {
            background: white;
            padding: 12px 16px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .save-info {
            color: #718096;
            font-size: 10px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }

        .empty-icon {
            font-size: 32px;
            margin-bottom: 12px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 6px;
            color: #4a5568;
        }

        .empty-desc {
            font-size: 12px;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 280px 1fr;
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .templates-section {
                order: 2;
            }

            .config-section {
                order: 1;
            }

            .header-content {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }

            .config-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条紧凑样式 */
        .config-content::-webkit-scrollbar {
            width: 4px;
        }

        .config-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .config-content::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 2px;
        }

        .config-content::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* 加载动画 */
        @keyframes slideInCompact {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .template-card,
        .config-card {
            animation: slideInCompact 0.2s ease;
        }
    </style>
</head>
<body>
    <!-- 紧凑型头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="header-left">
                    <div class="app-logo">OO</div>
                    <div class="header-text">
                        <h1>配置管理控制台</h1>
                        <p>高效紧凑的配置管理界面</p>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-download"></i>
                        导入
                    </a>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        新建
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <div class="container">
        <div class="main-layout">
            <!-- 左侧模板列表 -->
            <aside class="templates-section">
                <div class="section-header">
                    <div>
                        <h2 class="section-title">配置模板</h2>
                        <p class="section-subtitle">快速选择和编辑</p>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索模板...">
                </div>

                <div class="templates-list">
                    <div class="template-card active">
                        <div class="template-header">
                            <div class="template-name">标准编辑模板</div>
                            <div class="template-status">
                                <span class="status-badge status-default">默认</span>
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            基础的文档编辑配置，包含常用功能和权限设置。
                        </div>
                        <div class="template-meta">
                            <span>今天</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">团队协作模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            为团队协作优化的配置，支持实时编辑和评论功能。
                        </div>
                        <div class="template-meta">
                            <span>昨天</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">只读查看模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            仅查看权限的配置，适用于文档分享和展示。
                        </div>
                        <div class="template-meta">
                            <span>3天前</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">受限编辑模板</div>
                            <div class="template-status">
                                <span class="status-badge status-inactive">禁用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            限制高级功能的配置，适用于外部用户。
                        </div>
                        <div class="template-meta">
                            <span>1周前</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧配置区域 -->
            <main class="config-section">
                <div class="config-header">
                    <h2 class="config-title">标准编辑模板配置</h2>
                    <p class="config-subtitle">快速配置文档编辑器的权限和功能</p>
                </div>

                <nav class="config-nav">
                    <button class="nav-tab active">权限</button>
                    <button class="nav-tab">界面</button>
                    <button class="nav-tab">功能</button>
                    <button class="nav-tab">协作</button>
                    <button class="nav-tab">高级</button>
                </nav>

                <div class="config-content">
                    <div class="config-group">
                        <div class="group-header">
                            <div class="group-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="group-title">基础权限设置</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">编辑权限</div>
                                        <div class="config-item-desc">控制文档编辑功能</div>
                                    </div>
                                    <span class="required-indicator">必需</span>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许编辑</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示功能</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">下载权限</div>
                                        <div class="config-item-desc">控制文档下载功能</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许下载</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示功能</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">打印权限</div>
                                        <div class="config-item-desc">控制文档打印功能</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许打印</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示功能</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-disabled"></div>
                                        已禁用且隐藏
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">评论权限</div>
                                        <div class="config-item-desc">控制评论和批注功能</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许评论</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示功能</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">审阅权限</div>
                                        <div class="config-item-desc">控制修订追踪功能</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">启用审阅</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示功能</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">填写表单</div>
                                        <div class="config-item-desc">控制表单填写功能</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许填写</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示功能</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-group">
                        <div class="group-header">
                            <div class="group-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h3 class="group-title">界面显示设置</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">工具栏</div>
                                        <div class="config-item-desc">控制工具栏显示</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">显示工具栏</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">允许自定义</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">状态栏</div>
                                        <div class="config-item-desc">控制状态栏显示</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">显示状态栏</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">显示信息</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        已启用并可见
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-bar">
                    <div class="save-info">
                        <i class="fas fa-clock"></i>
                        最后保存: 刚刚
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模板卡片选择
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    templateCards.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 标签页切换
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    navTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 开关切换
            const toggleSwitches = document.querySelectorAll('.toggle-switch');
            toggleSwitches.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 更新状态显示
                    const card = this.closest('.config-card');
                    const statusText = card.querySelector('.status-text');
                    const switches = card.querySelectorAll('.toggle-switch');
                    
                    const enableSwitch = switches[0];
                    const visibilitySwitch = switches[1];
                    
                    if (enableSwitch.classList.contains('active') && visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>已启用并可见';
                    } else if (enableSwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>已启用但隐藏';
                    } else if (visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>已禁用但可见';
                    } else {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>已禁用且隐藏';
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                templateCards.forEach(card => {
                    const name = card.querySelector('.template-name').textContent.toLowerCase();
                    const desc = card.querySelector('.template-desc').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || desc.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html> 