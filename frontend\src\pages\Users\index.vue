<template>
  <div class="users-page">
    <a-page-header title="用户管理" sub-title="管理系统用户和权限">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCreateUser">
            <template #icon>
              <plus-outlined />
            </template>
            新建用户
          </a-button>
          <a-button @click="handleImportUsers">
            <template #icon>
              <upload-outlined />
            </template>
            批量导入
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-wrapper">
      <!-- 搜索和筛选区域 -->
      <div class="search-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input-search
              v-model:value="searchQuery"
              placeholder="搜索用户名、邮箱..."
              @search="handleSearch"
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="selectedRole"
              placeholder="选择角色"
              allow-clear
              @change="handleRoleChange"
            >
              <a-select-option value="admin"> 管理员 </a-select-option>
              <a-select-option value="editor"> 编辑员 </a-select-option>
              <a-select-option value="viewer"> 查看员 </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="selectedStatus"
              placeholder="选择状态"
              allow-clear
              @change="handleStatusChange"
            >
              <a-select-option value="active"> 已激活 </a-select-option>
              <a-select-option value="inactive"> 未激活 </a-select-option>
              <a-select-option value="suspended"> 已停用 </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="10">
            <a-space>
              <a-button @click="handleRefresh">
                <template #icon>
                  <reload-outlined />
                </template>
                刷新
              </a-button>
              <a-button @click="handleExport">
                <template #icon>
                  <download-outlined />
                </template>
                导出用户
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 用户列表 -->
      <a-table
        :columns="columns"
        :data-source="filteredUsers"
        :loading="loading"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number, range: [number, number]) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 个用户`,
        }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'user'">
            <div class="user-info">
              <a-avatar :size="40" :src="record.avatar">
                {{ record.username.charAt(0).toUpperCase() }}
              </a-avatar>
              <div class="user-details">
                <div class="username">
                  {{ record.username }}
                </div>
                <div class="email">
                  {{ record.email }}
                </div>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'role'">
            <a-tag :color="getRoleColor(record.role)">
              <component :is="getRoleIcon(record.role)" />
              {{ getRoleName(record.role) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              <component :is="getStatusIcon(record.status)" />
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'lastLogin'">
            <span v-if="record.lastLogin">
              {{ formatRelativeTime(record.lastLogin) }}
            </span>
            <span v-else class="text-gray">从未登录</span>
          </template>

          <template v-else-if="column.key === 'createTime'">
            {{ formatDate(record.createTime) }}
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="text" size="small" @click="handleEdit(record)">
                <template #icon>
                  <edit-outlined />
                </template>
                编辑
              </a-button>

              <a-button
                type="text"
                size="small"
                :disabled="record.status === 'suspended'"
                @click="handleResetPassword(record)"
              >
                <template #icon>
                  <key-outlined />
                </template>
                重置密码
              </a-button>

              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleViewDetails(record)">
                      <eye-outlined />
                      查看详情
                    </a-menu-item>
                    <a-menu-item @click="handleManageRoles(record)">
                      <team-outlined />
                      管理角色
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item
                      v-if="record.status === 'active'"
                      class="warning-item"
                      @click="handleSuspend(record)"
                    >
                      <stop-outlined />
                      停用用户
                    </a-menu-item>
                    <a-menu-item v-else @click="handleActivate(record)" class="success-item">
                      <check-circle-outlined />
                      激活用户
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item class="danger-item" @click="handleDelete(record)">
                      <delete-outlined />
                      删除用户
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  <template #icon>
                    <more-outlined />
                  </template>
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新建/编辑用户弹窗 -->
    <a-modal
      v-model:open="userModalVisible"
      :title="isEdit ? '编辑用户' : '新建用户'"
      width="600px"
      @ok="handleUserSubmit"
      @cancel="handleUserCancel"
    >
      <a-form :model="userForm" layout="vertical" :rules="userFormRules">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username" required>
              <a-input
                v-model:value="userForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮箱" name="email" required>
              <a-input v-model:value="userForm.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="姓名" name="realName">
              <a-input v-model:value="userForm.realName" placeholder="请输入真实姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="userForm.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="角色" name="role" required>
              <a-select v-model:value="userForm.role" placeholder="选择用户角色">
                <a-select-option value="admin"> 管理员 </a-select-option>
                <a-select-option value="editor"> 编辑员 </a-select-option>
                <a-select-option value="viewer"> 查看员 </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="userForm.status" placeholder="选择用户状态">
                <a-select-option value="active"> 已激活 </a-select-option>
                <a-select-option value="inactive"> 未激活 </a-select-option>
                <a-select-option value="suspended"> 已停用 </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item v-if="!isEdit" label="初始密码" name="password" required>
          <a-input-password v-model:value="userForm.password" placeholder="请输入初始密码" />
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea v-model:value="userForm.remarks" :rows="3" placeholder="用户备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 角色管理弹窗 -->
    <a-modal
      v-model:open="roleModalVisible"
      title="管理用户角色"
      width="500px"
      @ok="handleRoleSubmit"
      @cancel="handleRoleCancel"
    >
      <div class="role-management">
        <div class="current-user">
          <a-avatar :size="50" :src="selectedUser?.avatar">
            {{ selectedUser?.username?.charAt(0).toUpperCase() }}
          </a-avatar>
          <div class="user-info">
            <h3>{{ selectedUser?.username }}</h3>
            <p>{{ selectedUser?.email }}</p>
          </div>
        </div>

        <a-divider />

        <a-form layout="vertical">
          <a-form-item label="选择角色">
            <a-radio-group v-model:value="selectedUserRole">
              <a-space direction="vertical">
                <a-radio value="admin"> <strong>管理员</strong> - 拥有系统所有权限 </a-radio>
                <a-radio value="editor"> <strong>编辑员</strong> - 可以编辑和管理文档 </a-radio>
                <a-radio value="viewer"> <strong>查看员</strong> - 只能查看文档 </a-radio>
              </a-space>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  ReloadOutlined,
  DownloadOutlined,
  EditOutlined,
  KeyOutlined,
  EyeOutlined,
  TeamOutlined,
  StopOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  MoreOutlined,
  UserOutlined,
  CrownOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons-vue'
import type { UserInfo, PaginationParams } from '@/types/api.types'
import type { Component } from 'vue'
import { UsersApiService } from '@/services'

// API返回的用户数据格式
interface ApiUserData {
  id: string
  username: string
  email: string
  full_name?: string
  fullName?: string
  phone?: string
  role_id: string
  status: string
  created_at: string
  last_login_at: string
}

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedRole = ref<string>()
const selectedStatus = ref<string>()
const userModalVisible = ref(false)
const roleModalVisible = ref(false)
const isEdit = ref(false)
const selectedUser = ref<UserInfo | null>(null)
const selectedUserRole = ref('')

// 分页配置
const pagination = ref<PaginationParams>({
  current: 1,
  pageSize: 20,
  total: 0,
})

// 用户表单数据类型
interface UserFormData {
  username: string
  email: string
  realName: string
  phone: string
  role: string
  status: string
  password: string
  remarks: string
}

// 用户表单
const userForm = ref<UserFormData>({
  username: '',
  email: '',
  realName: '',
  phone: '',
  role: '',
  status: 'active',
  password: '',
  remarks: '',
})

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择用户角色', trigger: 'change' }],
  password: [
    { required: true, message: '请输入初始密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' },
  ],
}

// 扩展用户信息类型
interface ExtendedUserInfo extends UserInfo {
  realName?: string
  lastLogin?: string | null
  createTime?: string
  remarks?: string
}

// 用户数据 - 现在从API获取
const users = ref<ExtendedUserInfo[]>([])

// 加载用户数据
const loadUsers = async () => {
  loading.value = true
  try {
    const result = await UsersApiService.getUsers({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })

    // 转换API数据格式为前端需要的格式
    users.value = result.data.map((user: ApiUserData) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name || user.fullName,
      realName: user.full_name || user.fullName,
      phone: user.phone || '',
      role:
        user.role_id === 'role-super-admin'
          ? 'admin'
          : user.role_id === 'role-editor'
            ? 'editor'
            : 'viewer',
      status: user.status === 'active' ? 'active' : 'inactive',
      createdAt: user.created_at,
      lastLoginAt: user.last_login_at,
      lastLogin: user.last_login_at,
      createTime: user.created_at,
      permissions: user.role_id === 'role-super-admin' ? ['all'] : ['document:read'],
      remarks: '',
    }))

    pagination.value.total = result.total
  } catch (error) {
    console.error('加载用户数据失败:', error)
    message.error('加载用户数据失败')

    // 如果API调用失败，使用空数据而不是假数据
    users.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '用户信息',
    key: 'user',
    width: '25%',
  },
  {
    title: '角色',
    key: 'role',
    width: '12%',
    filters: [
      { text: '管理员', value: 'admin' },
      { text: '编辑员', value: 'editor' },
      { text: '查看员', value: 'viewer' },
    ],
  },
  {
    title: '状态',
    key: 'status',
    width: '12%',
    filters: [
      { text: '已激活', value: 'active' },
      { text: '未激活', value: 'inactive' },
      { text: '已停用', value: 'suspended' },
    ],
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: '12%',
  },
  {
    title: '最后登录',
    key: 'lastLogin',
    width: '15%',
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: '12%',
  },
  {
    title: '操作',
    key: 'actions',
    width: '12%',
  },
]

// 计算属性 - 过滤后的用户
const filteredUsers = computed(() => {
  let filtered = users.value

  if (searchQuery.value) {
    filtered = filtered.filter(
      user =>
        user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        user.realName?.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedRole.value) {
    filtered = filtered.filter(user => user.role === selectedRole.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(user => user.status === selectedStatus.value)
  }

  return filtered
})

// 工具函数
const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getRoleName = (role: string): string => {
  const roleMap: Record<string, string> = {
    admin: '管理员',
    editor: '编辑员',
    viewer: '查看员',
  }
  return roleMap[role] || role
}

const getRoleColor = (role: string): string => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    editor: 'blue',
    viewer: 'green',
  }
  return colorMap[role] || 'default'
}

const getRoleIcon = (role: string): Component => {
  const iconMap: Record<string, Component> = {
    admin: CrownOutlined,
    editor: EditOutlined,
    viewer: UserOutlined,
  }
  return iconMap[role] || UserOutlined
}

const getStatusName = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: '已激活',
    inactive: '未激活',
    suspended: '已停用',
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    suspended: 'error',
  }
  return colorMap[status] || 'default'
}

const getStatusIcon = (status: string): Component => {
  const iconMap: Record<string, Component> = {
    active: CheckCircleOutlined,
    inactive: ClockCircleOutlined,
    suspended: StopOutlined,
  }
  return iconMap[status] || ClockCircleOutlined
}

// 事件处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleRoleChange = () => {
  // 角色筛选逻辑已在计算属性中处理
}

const handleStatusChange = () => {
  // 状态筛选逻辑已在计算属性中处理
}

const handleRefresh = async () => {
  await loadUsers()
  message.success('刷新成功')
}

const handleExport = () => {
  message.info('用户数据导出功能开发中...')
}

const handleTableChange = (pag: PaginationParams) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  loadUsers() // 重新加载数据
}

const handleCreateUser = () => {
  isEdit.value = false
  userForm.value = {
    username: '',
    email: '',
    realName: '',
    phone: '',
    role: '',
    status: 'active',
    password: '',
    remarks: '',
  }
  userModalVisible.value = true
}

const handleImportUsers = () => {
  message.info('批量导入用户功能开发中...')
}

const handleEdit = (record: ExtendedUserInfo) => {
  isEdit.value = true
  userForm.value = {
    username: record.username,
    email: record.email,
    realName: record.realName || record.fullName,
    phone: record.phone || '',
    role: record.role,
    status: record.status,
    password: '',
    remarks: record.remarks || '',
  }
  userModalVisible.value = true
}

const handleResetPassword = (record: ExtendedUserInfo) => {
  message.info(`重置用户 ${record.username} 的密码`)
}

const handleViewDetails = (record: ExtendedUserInfo) => {
  message.info(`查看用户 ${record.username} 的详情`)
}

const handleManageRoles = (record: ExtendedUserInfo) => {
  selectedUser.value = record
  selectedUserRole.value = record.role
  roleModalVisible.value = true
}

const handleSuspend = (record: ExtendedUserInfo) => {
  message.info(`停用用户 ${record.username}`)
}

const handleActivate = (record: ExtendedUserInfo) => {
  message.info(`激活用户 ${record.username}`)
}

const handleDelete = (record: ExtendedUserInfo) => {
  message.info(`删除用户 ${record.username}`)
}

const handleUserSubmit = () => {
  message.success(isEdit.value ? '用户更新成功！' : '用户创建成功！')
  userModalVisible.value = false
}

const handleUserCancel = () => {
  userModalVisible.value = false
}

const handleRoleSubmit = () => {
  message.success('用户角色更新成功！')
  roleModalVisible.value = false
}

const handleRoleCancel = () => {
  roleModalVisible.value = false
}

// 生命周期 - 修改为加载真实数据
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.users-page {
  padding: 0;
}

.content-wrapper {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}

.search-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details .username {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.user-details .email {
  font-size: 12px;
  color: #8c8c8c;
}

.text-gray {
  color: #8c8c8c;
}

.role-management {
  padding: 8px 0;
}

.current-user {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.current-user .user-info h3 {
  margin: 0 0 4px;
  color: #262626;
}

.current-user .user-info p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

:deep(.danger-item) {
  color: #ff4d4f;
}

:deep(.danger-item:hover) {
  background-color: #fff2f0;
}

:deep(.warning-item) {
  color: #fa8c16;
}

:deep(.warning-item:hover) {
  background-color: #fff7e6;
}

:deep(.success-item) {
  color: #52c41a;
}

:deep(.success-item:hover) {
  background-color: #f6ffed;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px 8px;
}

:deep(.ant-radio-group) {
  width: 100%;
}

:deep(.ant-radio) {
  margin-bottom: 12px;
}
</style>
