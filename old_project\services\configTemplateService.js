/**
 * 配置模板管理服务
 */
const db = require('./database');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取所有活跃的配置模板
 */
async function getAllTemplates() {
    try {
        const templates = await db.query(`
            SELECT id, name, description, is_default, is_active, created_at, updated_at
            FROM config_templates 
            WHERE is_active = TRUE 
            ORDER BY is_default DESC, created_at ASC
        `);
        return templates;
    } catch (error) {
        console.error('获取配置模板列表失败:', error);
        throw new Error('获取配置模板列表失败');
    }
}

/**
 * 根据ID获取配置模板及其配置项
 */
async function getTemplateById(templateId) {
    try {
        // 获取模板基本信息
        const template = await db.queryOne(`
            SELECT id, name, description, is_default, is_active, created_at, updated_at
            FROM config_templates 
            WHERE id = ? AND is_active = TRUE
        `, [templateId]);

        if (!template) {
            throw new Error('配置模板不存在');
        }

        // 获取模板的配置项
        const items = await db.query(`
            SELECT id, config_group, config_key, config_value, value_type, 
                   is_enabled, is_required, description
            FROM config_template_items 
            WHERE template_id = ?
            ORDER BY config_group, config_key
        `, [templateId]);

        template.items = items;
        return template;
    } catch (error) {
        console.error('获取配置模板失败:', error);
        throw error;
    }
}

/**
 * 根据名称获取配置模板
 */
async function getTemplateByName(templateName) {
    try {
        const template = await db.queryOne(`
            SELECT id, name, description, is_default, is_active, created_at, updated_at
            FROM config_templates 
            WHERE name = ? AND is_active = TRUE
        `, [templateName]);

        if (!template) {
            throw new Error('配置模板不存在');
        }

        return await getTemplateById(template.id);
    } catch (error) {
        console.error('根据名称获取配置模板失败:', error);
        throw error;
    }
}

/**
 * 获取默认配置模板
 */
async function getDefaultTemplate() {
    try {
        const template = await db.queryOne(`
            SELECT id, name, description, is_default, is_active, created_at, updated_at
            FROM config_templates 
            WHERE is_default = TRUE AND is_active = TRUE
        `);

        if (!template) {
            // 如果没有默认模板，获取第一个活跃模板
            const firstTemplate = await db.queryOne(`
                SELECT id, name, description, is_default, is_active, created_at, updated_at
                FROM config_templates 
                WHERE is_active = TRUE 
                ORDER BY created_at ASC
            `);

            if (!firstTemplate) {
                throw new Error('没有可用的配置模板');
            }
            return await getTemplateById(firstTemplate.id);
        }

        return await getTemplateById(template.id);
    } catch (error) {
        console.error('获取默认配置模板失败:', error);
        throw error;
    }
}

/**
 * 解析配置项为嵌套对象结构
 */
function parseConfigItems(items) {
    const config = {};

    items.forEach(item => {
        // 只处理启用的配置项
        if (!item.is_enabled) {
            return;
        }

        const group = item.config_group;
        const key = item.config_key;
        let value = item.config_value;

        // 根据类型转换值
        switch (item.value_type) {
            case 'boolean':
                value = value === 'true' || value === true;
                break;
            case 'number':
                value = parseFloat(value) || 0;
                break;
            case 'object':
            case 'array':
                try {
                    value = JSON.parse(value);
                } catch (e) {
                    console.warn(`配置项 ${group}.${key} 的JSON解析失败:`, value);
                    value = {};
                }
                break;
            default:
                // string 类型保持原样
                break;
        }

        if (!config[group]) {
            config[group] = {};
        }
        config[group][key] = value;
    });

    return config;
}

/**
 * 构建编辑器配置
 */
async function buildEditorConfig(templateIdOrName, fileId, overrides = {}, isApiCall = false) {
    try {
        // 直接构建基础文档配置，不依赖document服务
        let baseConfig;

        if (fileId && fileId.includes('sample-file-id')) {
            // 测试文件ID的处理
            baseConfig = {
                document: {
                    fileType: "docx",
                    key: fileId + "-" + Date.now(),
                    title: "测试文档.docx",
                    url: `http://192.168.107.7:3000/api/documents/download/${fileId}`
                    // 不在这里预设 permissions，让模板配置完全控制
                },
                documentType: "word",
                editorConfig: {
                    mode: "edit",
                    lang: "zh",
                    callbackUrl: `http://192.168.107.7:3000/api/editor/callback`
                    // 不在这里预设 user，让模板配置完全控制
                }
            };
        } else {
            // 实际文件ID的处理 - 直接从数据库获取文件信息并构建配置
            const db = require('./database');
            const config = require('../config');

            const fileInfo = await db.queryOne(`
                SELECT id, fn_doc_id, original_name, file_size, mime_type, extension, version 
                FROM filenet_documents 
                WHERE id = ? AND is_deleted = FALSE
            `, [fileId]);

            if (!fileInfo) {
                throw new Error('找不到对应的文件');
            }

            // 创建文件访问URL
            const fileUrl = `http://${config.server.host}:${config.server.port}/api/documents/${fileId}`;
            const fileExt = fileInfo.extension.toLowerCase();
            const fileKey = `${fileId}-${Date.now()}`;

            // 确定文档类型
            let documentType = 'word';
            if (['xlsx', 'xls'].includes(fileExt)) {
                documentType = 'cell';
            } else if (['pptx', 'ppt'].includes(fileExt)) {
                documentType = 'slide';
            } else if (fileExt === 'pdf') {
                documentType = 'pdf';
            }

            // 构建基础配置 - 只提供必要的文档信息和结构，不预设任何权限和功能配置
            baseConfig = {
                document: {
                    fileType: fileExt,
                    key: fileKey,
                    title: fileInfo.original_name,
                    url: fileUrl
                    // 不在这里预设 permissions，让模板配置完全控制
                },
                documentType: documentType,
                editorConfig: {
                    mode: fileExt === 'pdf' ? 'view' : 'edit',
                    lang: 'zh',
                    callbackUrl: config.editor.callbackUrl
                    // 不在这里预设 customization、user、coEditing，让模板配置完全控制
                }
            };
        }

        // 获取配置模板
        let template;
        if (templateIdOrName) {
            // URL解码模板名称（支持中文）
            const decodedName = decodeURIComponent(templateIdOrName);

            // 改进的模板识别逻辑
            try {
                // 先尝试按ID查找
                if (templateIdOrName.includes('-') && !decodedName.includes('编辑') && !decodedName.includes('只读')) {
                    template = await getTemplateById(templateIdOrName);
                } else {
                    // 按名称查找（支持中文）
                    template = await getTemplateByName(decodedName);
                }
            } catch (firstError) {
                try {
                    // 如果第一种方式失败，尝试另一种方式
                    if (templateIdOrName.includes('-') && !decodedName.includes('编辑') && !decodedName.includes('只读')) {
                        template = await getTemplateByName(decodedName);
                    } else {
                        template = await getTemplateById(templateIdOrName);
                    }
                } catch (secondError) {
                    console.error('两种方式都失败了，使用默认模板');
                    template = await getDefaultTemplate();
                }
            }
        } else {
            // 使用默认模板
            template = await getDefaultTemplate();
        }

        // 解析模板配置项
        const templateConfig = parseConfigItems(template.items);

        // 合并配置：基础配置 -> 模板配置 -> 覆盖参数
        const mergedConfig = mergeConfigs(baseConfig, templateConfig, overrides);

        if (isApiCall) {
            console.log(`🔄 API配置请求 - 模板: ${template.name}`);
        } else {
            console.log(`✅ 编辑器配置生成成功 - 模板: ${template.name}`);
            // 临时启用调试模式 - 始终显示详细配置
            console.log('📋 详细编辑器配置:', JSON.stringify(mergedConfig, null, 2));
        }

        return mergedConfig;

    } catch (error) {
        console.error('构建编辑器配置失败:', error);

        // 如果配置模板完全失败，提供一个基本的配置
        if (fileId && fileId.includes('sample-file-id')) {
            return {
                document: {
                    fileType: "docx",
                    key: fileId + "-" + Date.now(),
                    title: "测试文档.docx",
                    url: `http://192.168.107.7:3000/api/documents/download/${fileId}`
                    // 不在这里预设 permissions，让模板配置完全控制
                },
                documentType: "word",
                editorConfig: {
                    mode: "edit",
                    lang: "zh",
                    callbackUrl: `http://192.168.107.7:3000/api/editor/callback`
                    // 不在这里预设 user，让模板配置完全控制
                }
            };
        }

        // 如果还是失败，尝试调用原有的document服务作为最后的回退
        try {
            const documentService = require('./document');
            return await documentService.getDocumentConfig(fileId);
        } catch (fallbackError) {
            console.error('连回退方案都失败了:', fallbackError);
            throw new Error('无法生成编辑器配置');
        }
    }
}

/**
 * 深度合并配置对象
 */
function mergeConfigs(baseConfig, templateConfig, overrides) {
    const result = JSON.parse(JSON.stringify(baseConfig));

    // 先进行基本的深度合并
    mergeDeep(result, templateConfig);

    // 特别处理：确保权限配置正确映射到 document.permissions
    if (templateConfig.permissions) {
        if (!result.document) result.document = {};
        result.document.permissions = { ...result.document.permissions, ...templateConfig.permissions };
    }

    // 特别处理：确保自定义配置正确映射到 editorConfig.customization
    if (templateConfig.customization) {
        if (!result.editorConfig) result.editorConfig = {};
        result.editorConfig.customization = { ...result.editorConfig.customization, ...templateConfig.customization };
    }

    // 特别处理：确保协作配置正确映射到 editorConfig.coEditing
    if (templateConfig.coEditing) {
        if (!result.editorConfig) result.editorConfig = {};
        result.editorConfig.coEditing = { ...result.editorConfig.coEditing, ...templateConfig.coEditing };
    }

    // 特别处理：确保用户配置正确映射到 editorConfig.user
    if (templateConfig.user) {
        if (!result.editorConfig) result.editorConfig = {};
        result.editorConfig.user = { ...result.editorConfig.user, ...templateConfig.user };
    }

    // 特别处理：确保服务器配置正确映射到顶层
    if (templateConfig.server) {
        if (templateConfig.server.lang) {
            result.editorConfig.lang = templateConfig.server.lang;
        }
        if (templateConfig.server.region) {
            result.editorConfig.region = templateConfig.server.region;
        }
    }

    // 应用覆盖参数
    applyOverrides(result, overrides);

    return result;
}

/**
 * 深度合并对象
 */
function mergeDeep(target, source) {
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                if (!target[key] || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                mergeDeep(target[key], source[key]);
            } else {
                target[key] = source[key];
            }
        }
    }
}

/**
 * 应用覆盖参数
 */
function applyOverrides(config, overrides) {
    for (const path in overrides) {
        const keys = path.split('.');
        let current = config;

        // 导航到父对象
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }

        // 设置最终值
        current[keys[keys.length - 1]] = overrides[path];
    }
}

/**
 * 创建新的配置模板
 */
async function createTemplate(templateData) {
    try {
        const templateId = uuidv4();

        // 如果设为默认模板，先清除其他默认模板
        if (templateData.is_default) {
            await db.query('UPDATE config_templates SET is_default = FALSE');
        }

        // 插入模板主记录
        await db.query(`
            INSERT INTO config_templates (id, name, description, is_default, is_active)
            VALUES (?, ?, ?, ?, TRUE)
        `, [templateId, templateData.name, templateData.description || '', templateData.is_default || false]);

        // 插入配置项
        if (templateData.items && templateData.items.length > 0) {
            for (const item of templateData.items) {
                const itemId = uuidv4();
                await db.query(`
                    INSERT INTO config_template_items 
                    (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    itemId, templateId, item.config_group, item.config_key,
                    item.config_value, item.value_type || 'string',
                    item.is_enabled !== undefined ? item.is_enabled : true, item.is_required || false, item.description || ''
                ]);
            }
        }

        return await getTemplateById(templateId);
    } catch (error) {
        console.error('创建配置模板失败:', error);
        throw new Error('创建配置模板失败');
    }
}

/**
 * 更新配置模板
 */
async function updateTemplate(templateId, templateData) {
    try {
        // 如果设为默认模板，先清除其他默认模板
        if (templateData.is_default) {
            await db.query('UPDATE config_templates SET is_default = FALSE WHERE id != ?', [templateId]);
        }

        // 更新模板主记录
        await db.query(`
            UPDATE config_templates 
            SET name = ?, description = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [templateData.name, templateData.description || '', templateData.is_default || false, templateId]);

        // 删除旧的配置项
        await db.query('DELETE FROM config_template_items WHERE template_id = ?', [templateId]);

        // 插入新的配置项
        if (templateData.items && templateData.items.length > 0) {
            for (const item of templateData.items) {
                const itemId = uuidv4();
                await db.query(`
                    INSERT INTO config_template_items 
                    (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    itemId, templateId, item.config_group, item.config_key,
                    item.config_value, item.value_type || 'string',
                    item.is_enabled !== undefined ? item.is_enabled : true, item.is_required || false, item.description || ''
                ]);
            }
        }

        return await getTemplateById(templateId);
    } catch (error) {
        console.error('更新配置模板失败:', error);
        throw new Error('更新配置模板失败');
    }
}

/**
 * 删除配置模板（软删除）
 */
async function deleteTemplate(templateId) {
    try {
        // 检查是否为默认模板
        const template = await db.queryOne('SELECT is_default FROM config_templates WHERE id = ?', [templateId]);
        if (template && template.is_default) {
            throw new Error('不能删除默认模板');
        }

        // 软删除模板
        await db.query('UPDATE config_templates SET is_active = FALSE WHERE id = ?', [templateId]);

        // 软删除相关配置项
        await db.query('UPDATE config_template_items SET is_enabled = FALSE WHERE template_id = ?', [templateId]);

        return { success: true };
    } catch (error) {
        console.error('删除配置模板失败:', error);
        throw error;
    }
}

/**
 * 获取配置分组和描述信息（基于1.js的完整配置）
 */
function getConfigGroups() {
    return {
        permissions: {
            description: '文档权限设置',
            items: {
                edit: { type: 'boolean', description: '允许编辑文档', default: true },
                download: { type: 'boolean', description: '允许下载文档', default: true },
                print: { type: 'boolean', description: '允许打印文档', default: true },
                comment: { type: 'boolean', description: '允许添加评论', default: true },
                chat: { type: 'boolean', description: '允许聊天功能', default: false },
                review: { type: 'boolean', description: '允许审阅模式', default: true },
                copy: { type: 'boolean', description: '允许复制内容', default: true },
                deleteCommentAuthorOnly: { type: 'boolean', description: '仅作者可删除评论', default: false },
                editCommentAuthorOnly: { type: 'boolean', description: '仅作者可编辑评论', default: false },
                fillForms: { type: 'boolean', description: '允许填写表单', default: true },
                modifyContentControl: { type: 'boolean', description: '允许修改内容控件', default: true },
                modifyFilter: { type: 'boolean', description: '允许修改过滤器', default: true },
                protect: { type: 'boolean', description: '允许保护文档', default: true }
            }
        },
        customization: {
            description: '界面自定义设置',
            items: {
                about: { type: 'boolean', description: '显示关于按钮', default: true },
                autosave: { type: 'boolean', description: '启用自动保存', default: true },
                comments: { type: 'boolean', description: '显示评论功能', default: true },
                compactHeader: { type: 'boolean', description: '使用紧凑头部', default: false },
                compactToolbar: { type: 'boolean', description: '使用紧凑工具栏', default: false },
                compatibleFeatures: { type: 'boolean', description: '启用兼容性功能', default: false },
                feedback: { type: 'boolean', description: '显示反馈按钮', default: false },
                forcesave: { type: 'boolean', description: '启用强制保存', default: true },
                help: { type: 'boolean', description: '显示帮助按钮', default: true },
                hideNotes: { type: 'boolean', description: '隐藏批注', default: false },
                hideRightMenu: { type: 'boolean', description: '隐藏右侧菜单', default: false },
                hideRulers: { type: 'boolean', description: '隐藏标尺', default: false },
                integrationMode: { type: 'string', description: '集成模式', default: 'embed' },
                macros: { type: 'boolean', description: '启用宏功能', default: true },
                macrosMode: { type: 'string', description: '宏安全模式', default: 'warn', options: ['warn', 'enable', 'disable'] },
                mentionShare: { type: 'boolean', description: '启用提及分享', default: true },
                plugins: { type: 'boolean', description: '启用插件', default: false },
                showHorizontalScroll: { type: 'boolean', description: '显示水平滚动条', default: true },
                showVerticalScroll: { type: 'boolean', description: '显示垂直滚动条', default: true },
                toolbarHideFileName: { type: 'boolean', description: '工具栏隐藏文件名', default: false },
                uiTheme: { type: 'string', description: 'UI主题', default: 'theme-light', options: ['theme-light', 'theme-dark'] },
                unit: { type: 'string', description: '测量单位', default: 'cm', options: ['cm', 'pt', 'inch'] },
                zoom: { type: 'number', description: '默认缩放比例', default: 100, min: 25, max: 500 }
            }
        },
        layout: {
            description: '布局设置',
            items: {
                'header.editMode': { type: 'boolean', description: '头部编辑模式', default: true },
                'header.save': { type: 'boolean', description: '头部保存按钮', default: true },
                'header.users': { type: 'boolean', description: '头部用户信息', default: true },
                'leftMenu.mode': { type: 'boolean', description: '左侧菜单模式', default: true },
                'leftMenu.navigation': { type: 'boolean', description: '左侧导航', default: true },
                'leftMenu.spellcheck': { type: 'boolean', description: '左侧拼写检查', default: true },
                'rightMenu.mode': { type: 'boolean', description: '右侧菜单模式', default: true },
                'statusBar.actionStatus': { type: 'boolean', description: '状态栏操作状态', default: true },
                'statusBar.docLang': { type: 'boolean', description: '状态栏文档语言', default: true },
                'statusBar.textLang': { type: 'boolean', description: '状态栏文本语言', default: true },
                'toolbar.collaboration.mailmerge': { type: 'boolean', description: '工具栏邮件合并', default: true },
                'toolbar.draw': { type: 'boolean', description: '工具栏绘图', default: true },
                'toolbar.file.close': { type: 'boolean', description: '工具栏文件关闭', default: true },
                'toolbar.file.info': { type: 'boolean', description: '工具栏文件信息', default: true },
                'toolbar.file.save': { type: 'boolean', description: '工具栏文件保存', default: true },
                'toolbar.file.settings': { type: 'boolean', description: '工具栏文件设置', default: true },
                'toolbar.layout': { type: 'boolean', description: '工具栏布局', default: true },
                'toolbar.plugins': { type: 'boolean', description: '工具栏插件', default: true },
                'toolbar.protect': { type: 'boolean', description: '工具栏保护', default: true },
                'toolbar.references': { type: 'boolean', description: '工具栏引用', default: true },
                'toolbar.save': { type: 'boolean', description: '工具栏保存', default: true },
                'toolbar.view.navigation': { type: 'boolean', description: '工具栏视图导航', default: true }
            }
        },
        features: {
            description: '功能特性设置',
            items: {
                'spellcheck.mode': { type: 'boolean', description: '拼写检查模式', default: true },
                'spellcheck.change': { type: 'boolean', description: '拼写检查变更', default: true },
                'tabBackground.mode': { type: 'string', description: '标签背景模式', default: 'header', options: ['header', 'toolbar'] },
                'tabBackground.change': { type: 'boolean', description: '标签背景变更', default: true },
                'tabStyle.mode': { type: 'string', description: '标签样式模式', default: 'fill', options: ['fill', 'line'] },
                'tabStyle.change': { type: 'boolean', description: '标签样式变更', default: true },
                featuresTips: { type: 'boolean', description: '功能提示', default: true },
                roles: { type: 'boolean', description: '角色功能', default: true }
            }
        },
        review: {
            description: '审阅设置',
            items: {
                hideReviewDisplay: { type: 'boolean', description: '隐藏审阅显示', default: false },
                showReviewChanges: { type: 'boolean', description: '显示审阅变更', default: false },
                reviewDisplay: { type: 'string', description: '审阅显示模式', default: 'original', options: ['original', 'markup', 'final'] },
                trackChanges: { type: 'boolean', description: '跟踪变更', default: true },
                hoverMode: { type: 'boolean', description: '悬停模式', default: false }
            }
        },
        mobile: {
            description: '移动端设置',
            items: {
                forceView: { type: 'boolean', description: '强制视图模式', default: false },
                info: { type: 'boolean', description: '显示信息', default: true },
                standardView: { type: 'boolean', description: '标准视图', default: true }
            }
        },
        user: {
            description: '用户设置',
            items: {
                id: { type: 'string', description: '用户ID', default: 'user-1' },
                name: { type: 'string', description: '用户姓名', default: 'OnlyOffice用户' },
                group: { type: 'string', description: '用户组', default: 'editors' },
                image: { type: 'string', description: '用户头像URL', default: '' }
            }
        },
        server: {
            description: '服务器设置',
            items: {
                lang: { type: 'string', description: '界面语言', default: 'zh', options: ['zh', 'en', 'zh-CN', 'en-US'] },
                region: { type: 'string', description: '地区设置', default: 'zh-CN', options: ['zh-CN', 'en-US', 'en-GB'] }
            }
        },
        coEditing: {
            description: '协作编辑设置',
            items: {
                mode: { type: 'string', description: '协作模式', default: 'fast', options: ['fast', 'strict'] },
                change: { type: 'boolean', description: '允许变更协作模式', default: true }
            }
        },
        customer: {
            description: '客户信息设置',
            items: {
                address: { type: 'string', description: '地址', default: '' },
                info: { type: 'string', description: '附加信息', default: '' },
                logo: { type: 'string', description: 'Logo URL', default: '' },
                logoDark: { type: 'string', description: '深色Logo URL', default: '' },
                mail: { type: 'string', description: '邮箱', default: '' },
                name: { type: 'string', description: '客户名称', default: 'OnlyOffice Integration' },
                phone: { type: 'string', description: '电话', default: '' },
                www: { type: 'string', description: '网站', default: '' }
            }
        }
    };
}

/**
 * 设置默认配置模板
 */
async function setDefaultTemplate(templateId) {
    try {
        // 首先清除所有默认模板标记
        await db.query('UPDATE config_templates SET is_default = FALSE');

        // 设置指定模板为默认
        await db.query('UPDATE config_templates SET is_default = TRUE WHERE id = ?', [templateId]);

        return { success: true };
    } catch (error) {
        console.error('设置默认模板失败:', error);
        throw new Error('设置默认模板失败');
    }
}

/**
 * 清除所有默认模板标记
 */
async function clearDefaultTemplates() {
    try {
        await db.query('UPDATE config_templates SET is_default = FALSE');
        return { success: true };
    } catch (error) {
        console.error('清除默认模板标记失败:', error);
        throw new Error('清除默认模板标记失败');
    }
}

module.exports = {
    getAllTemplates,
    getTemplateById,
    getTemplateByName,
    getDefaultTemplate,
    parseConfigItems,
    buildEditorConfig,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    getConfigGroups,
    setDefaultTemplate,
    clearDefaultTemplates
}; 