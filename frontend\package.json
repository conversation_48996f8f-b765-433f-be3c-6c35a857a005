{"name": "onlyoffice-frontend", "version": "1.0.0", "description": "OnlyOffice集成系统前端 - Vue 3 + Ant Design Pro", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:check": "eslint src --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "format": "prettier --write \"src/**/*.{vue,ts,js,json,css,scss}\"", "format:check": "prettier --check \"src/**/*.{vue,ts,js,json,css,scss}\"", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^7.0.0", "ant-design-vue": "^4.0.0", "axios": "^1.6.0", "dayjs": "^1.11.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "onlyoffice-frontend": "file:", "pinia": "^2.1.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.8.0", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.50.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "typescript": "^5.2.0", "vite": "^4.4.0", "vue-tsc": "^1.8.0"}}