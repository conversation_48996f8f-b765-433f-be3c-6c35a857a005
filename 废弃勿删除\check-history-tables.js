const mysql = require('mysql2/promise');

async function checkHistoryTables() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库检查历史记录表...');
    
    connection = await mysql.createConnection({
      host: '*************',
      port: 3306,
      user: 'onlyfile_user',
      password: '0nlyF!le$ecure#123',
      database: 'onlyfile'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 查看所有表
    console.log('\n📊 1. 查看数据库中的所有表...');
    const [tables] = await connection.query(`SHOW TABLES`);
    console.log('数据库中的表:');
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`  ${index + 1}. ${tableName}`);
    });
    
    // 2. 查找可能的历史记录表
    console.log('\n🔍 2. 查找历史记录相关的表...');
    const historyTables = tables.filter(table => {
      const tableName = Object.values(table)[0].toLowerCase();
      return tableName.includes('history') || 
             tableName.includes('log') || 
             tableName.includes('audit') ||
             tableName.includes('version');
    });
    
    if (historyTables.length > 0) {
      console.log('找到历史记录相关的表:');
      historyTables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`  ${index + 1}. ${tableName}`);
      });
      
      // 检查每个历史表的结构
      for (const table of historyTables) {
        const tableName = Object.values(table)[0];
        console.log(`\n📋 表 ${tableName} 的结构:`);
        const [columns] = await connection.query(`DESCRIBE ${tableName}`);
        columns.forEach(col => {
          console.log(`    - ${col.Field}: ${col.Type}`);
        });
        
        // 检查记录数
        const [count] = await connection.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`    记录数: ${count[0].count}`);
      }
    } else {
      console.log('❌ 没有找到历史记录相关的表');
    }
    
    // 3. 检查 system_settings 表的具体结构
    console.log('\n📋 3. 详细检查 system_settings 表...');
    const [systemColumns] = await connection.query(`DESCRIBE system_settings`);
    console.log('system_settings 表结构:');
    systemColumns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}) ${col.Key ? `[${col.Key}]` : ''} ${col.Default !== null ? `默认值: ${col.Default}` : ''}`);
    });
    
    // 4. 检查是否有配置变更的记录
    console.log('\n⏰ 4. 检查配置的时间戳信息...');
    const [timeInfo] = await connection.query(`
      SELECT 
        setting_key,
        updated_at,
        description
      FROM system_settings 
      ORDER BY updated_at DESC 
      LIMIT 10
    `);
    
    console.log('最近更新的10个配置项:');
    timeInfo.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.setting_key}`);
      console.log(`     更新时间: ${config.updated_at}`);
      console.log(`     描述: ${config.description || '无'}`);
      console.log('');
    });
    
    // 5. 建议解决方案
    console.log('\n💡 分析结果:');
    console.log('system_settings 表只是当前配置表，不是历史记录表。');
    console.log('每次更新配置时，旧值被直接覆盖，没有保留历史版本。');
    console.log('\n🔧 建议解决方案:');
    console.log('1. 创建专门的配置历史表 (config_history)');
    console.log('2. 在更新配置时，先将旧值保存到历史表');
    console.log('3. 或者利用现有的 audit_logs 表记录配置变更');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

console.log('🚀 开始检查历史记录表...\n');
checkHistoryTables(); 