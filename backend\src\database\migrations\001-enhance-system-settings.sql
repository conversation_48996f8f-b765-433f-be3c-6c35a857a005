-- 增强system_settings表结构以支持新的配置管理
-- 迁移脚本: 001-enhance-system-settings.sql
-- 创建时间: 2024-12-19
-- 描述: 为system_settings表添加配置分类、类型、加密等字段

-- 1. 添加新字段
ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS category ENUM('onlyoffice', 'filenet', 'system', 'jwt') DEFAULT 'system' AFTER setting_value;

ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' AFTER category;

ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS encrypted BOOLEAN DEFAULT FALSE AFTER type;

ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER encrypted;

-- 2. 创建配置索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);
CREATE INDEX IF NOT EXISTS idx_system_settings_type ON system_settings(type);

-- 3. 插入默认JWT配置
INSERT IGNORE INTO system_settings (setting_key, setting_value, category, type, description) VALUES 
('jwt.api.secret', 'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV', 'jwt', 'string', 'API认证JWT密钥'),
('jwt.api.expires_in', '24h', 'jwt', 'string', 'API JWT过期时间'),
('jwt.api.algorithm', 'HS256', 'jwt', 'string', 'API JWT加密算法'),
('jwt.api.issuer', 'onlyoffice-system', 'jwt', 'string', 'JWT发行者'),
('jwt.api.audience', 'onlyoffice-client', 'jwt', 'string', 'JWT接收者'),

('jwt.onlyoffice.secret', 'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV', 'jwt', 'string', 'OnlyOffice JWT密钥'),
('jwt.onlyoffice.header', 'Authorization', 'jwt', 'string', 'OnlyOffice JWT头部名称'),
('jwt.onlyoffice.in_body', 'true', 'jwt', 'boolean', '是否在请求体中包含JWT'),
('jwt.onlyoffice.algorithm', 'HS256', 'jwt', 'string', 'OnlyOffice JWT加密算法');

-- 4. 插入默认OnlyOffice配置
INSERT IGNORE INTO system_settings (setting_key, setting_value, category, type, description) VALUES 
('onlyoffice.server.url', 'http://*************/', 'onlyoffice', 'string', 'OnlyOffice服务器URL'),
('onlyoffice.server.document_server_url', 'http://*************/', 'onlyoffice', 'string', '文档服务器URL'),
('onlyoffice.server.port', '80', 'onlyoffice', 'number', 'OnlyOffice服务器端口'),
('onlyoffice.server.health_check_url', 'http://*************/healthcheck', 'onlyoffice', 'string', '健康检查URL'),

('onlyoffice.editor.callback_url', 'http://*************:3000/api/editor/callback', 'onlyoffice', 'string', '编辑器回调URL'),
('onlyoffice.editor.lang', 'zh', 'onlyoffice', 'string', '编辑器语言'),
('onlyoffice.editor.mode', 'edit', 'onlyoffice', 'string', '编辑器模式'),
('onlyoffice.editor.theme', 'default', 'onlyoffice', 'string', '编辑器主题'),

('onlyoffice.permissions.download', 'true', 'onlyoffice', 'boolean', '允许下载'),
('onlyoffice.permissions.edit', 'true', 'onlyoffice', 'boolean', '允许编辑'),
('onlyoffice.permissions.print', 'true', 'onlyoffice', 'boolean', '允许打印'),
('onlyoffice.permissions.review', 'true', 'onlyoffice', 'boolean', '允许审阅'),
('onlyoffice.permissions.fill_forms', 'true', 'onlyoffice', 'boolean', '允许填写表单'),
('onlyoffice.permissions.modify_filter', 'true', 'onlyoffice', 'boolean', '允许修改过滤器'),
('onlyoffice.permissions.modify_content_control', 'true', 'onlyoffice', 'boolean', '允许修改内容控制'),
('onlyoffice.permissions.comment', 'true', 'onlyoffice', 'boolean', '允许评论'),
('onlyoffice.permissions.chat', 'true', 'onlyoffice', 'boolean', '允许聊天'),
('onlyoffice.permissions.copy', 'true', 'onlyoffice', 'boolean', '允许复制'),

('onlyoffice.coediting.mode', 'fast', 'onlyoffice', 'string', '协作编辑模式'),
('onlyoffice.coediting.change', 'true', 'onlyoffice', 'boolean', '允许协作更改'),

('onlyoffice.user.id', 'system', 'onlyoffice', 'string', '默认用户ID'),
('onlyoffice.user.name', '系统用户', 'onlyoffice', 'string', '默认用户名'),
('onlyoffice.user.group', 'admin', 'onlyoffice', 'string', '默认用户组'),

('onlyoffice.customization.about', 'true', 'onlyoffice', 'boolean', '显示关于信息'),
('onlyoffice.customization.comments', 'true', 'onlyoffice', 'boolean', '显示评论'),
('onlyoffice.customization.feedback', 'false', 'onlyoffice', 'boolean', '显示反馈'),
('onlyoffice.customization.forcesave', 'true', 'onlyoffice', 'boolean', '强制保存'),
('onlyoffice.customization.help', 'true', 'onlyoffice', 'boolean', '显示帮助'),
('onlyoffice.customization.toolbar_no_tabs', 'false', 'onlyoffice', 'boolean', '工具栏无标签');

-- 5. 插入默认FileNet配置
INSERT IGNORE INTO system_settings (setting_key, setting_value, category, type, description) VALUES 
('filenet.connection.host', '*************', 'filenet', 'string', 'FileNet主机地址'),
('filenet.connection.port', '8090', 'filenet', 'number', 'FileNet端口'),
('filenet.connection.username', 'your-filenet-username', 'filenet', 'string', 'FileNet用户名'),
('filenet.connection.password', 'your-filenet-password', 'filenet', 'string', 'FileNet密码'),
('filenet.connection.health_check_url', 'http://*************:8090/common/health', 'filenet', 'string', 'FileNet健康检查URL'),

('filenet.defaults.folder', '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}', 'filenet', 'string', '默认文件夹'),
('filenet.defaults.doc_class', 'SimpleDocument', 'filenet', 'string', '默认文档类'),
('filenet.defaults.source_type', 'MaxOffice', 'filenet', 'string', '默认源类型'),
('filenet.defaults.biz_tag', 'office_file', 'filenet', 'string', '默认业务标签'),

('filenet.features.enabled', 'true', 'filenet', 'boolean', '是否启用FileNet'),
('filenet.features.auto_sync', 'true', 'filenet', 'boolean', '自动同步'),
('filenet.features.version_control', 'true', 'filenet', 'boolean', '版本控制'),
('filenet.features.audit_log', 'true', 'filenet', 'boolean', '审计日志');

-- 6. 插入默认系统配置
INSERT IGNORE INTO system_settings (setting_key, setting_value, category, type, description) VALUES 
('system.security.rate_limit_window_ms', '900000', 'system', 'number', '限流窗口时间(毫秒)'),
('system.security.rate_limit_max_requests', '100', 'system', 'number', '限流最大请求数'),
('system.security.enable_security_headers', 'true', 'system', 'boolean', '启用安全头'),
('system.security.request_timeout', '30000', 'system', 'number', '请求超时时间(毫秒)'),

('system.performance.enable_monitoring', 'true', 'system', 'boolean', '启用性能监控'),
('system.performance.slow_query_threshold', '1000', 'system', 'number', '慢查询阈值(毫秒)'),
('system.performance.cache_type', 'memory', 'system', 'string', '缓存类型'),
('system.performance.cache_ttl', '3600', 'system', 'number', '缓存TTL(秒)'),
('system.performance.cache_max_size', '100', 'system', 'number', '缓存最大条目数'),

('system.features.hot_reload', 'true', 'system', 'boolean', '热重载'),
('system.features.debug_mode', 'true', 'system', 'boolean', '调试模式'),
('system.features.api_docs_enabled', 'true', 'system', 'boolean', 'API文档启用'),
('system.features.swagger_endpoint', '/api-docs', 'system', 'string', 'Swagger端点');

-- 7. 更新已存在的database_initialized记录
UPDATE system_settings 
SET category = 'system', type = 'boolean' 
WHERE setting_key = 'database_initialized'; 