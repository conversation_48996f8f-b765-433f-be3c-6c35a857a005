<template>
  <div class="document-actions">
    <a-space :size="4">
      <!-- 预览按钮（原新窗口编辑） -->
      <a-button size="small" @click="handlePopupEdit" :loading="popupLoading">
        <template #icon>
          <eye-outlined />
        </template>
        预览
      </a-button>

      <!-- 配置模板选择下拉 -->
      <a-select
        v-model:value="selectedTemplateId"
        size="small"
        style="width: 90px"
        :loading="templateLoading"
        placeholder="配置"
        @change="handleTemplateChange"
      >
        <a-select-option
          v-for="template in configTemplates"
          :key="template.id"
          :value="template.id"
        >
          {{ template.name }}
        </a-select-option>
      </a-select>

      <!-- 更多操作下拉菜单 -->
      <a-dropdown>
        <a-button size="small">
          <template #icon>
            <more-outlined />
          </template>
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item
              key="embedded-edit"
              @click="handleEmbeddedEdit"
              :disabled="embeddedLoading"
            >
              <edit-outlined />
              内嵌编辑
            </a-menu-item>
            <a-menu-item key="rename" @click="handleRename">
              <edit-outlined />
              重命名
            </a-menu-item>
            <a-menu-item key="download" @click="handleDownload">
              <download-outlined />
              下载
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="delete" @click="handleDelete" class="delete-item">
              <delete-outlined />
              删除
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </a-space>

    <!-- 重命名对话框 -->
    <a-modal
      v-model:open="renameModalVisible"
      title="重命名文档"
      ok-text="确定"
      cancel-text="取消"
      @ok="handleRenameSubmit"
      @cancel="handleRenameCancel"
      :confirm-loading="renameLoading"
    >
      <a-form layout="vertical">
        <a-form-item label="文档名称">
          <a-input
            v-model:value="renameForm.name"
            placeholder="请输入文档名称"
            :maxlength="255"
            show-count
            @keyup.enter="handleRenameSubmit"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  DownloadOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import { openEmbeddedEditor, openPopupEditor } from '@/utils/editor-utils'
import { DocumentsApiService } from '@/services'
import type { EditorConfigQuery } from '@/utils/editor-utils'

/**
 * 文档操作组件
 *
 * @description 提供文档编辑操作，支持内嵌和弹窗两种模式，以及配置模板选择、重命名、删除等功能
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

interface Props {
  /** 文档ID */
  documentId: string
  /** 文档名称（用于重命名和确认删除） */
  documentName?: string
}

interface ConfigTemplate {
  id: string
  name: string
  description: string
  isDefault: boolean
  isActive: boolean
  updatedAt: string
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits<{
  refreshList: []
}>()

// 加载状态
const embeddedLoading = ref(false)
const popupLoading = ref(false)
const templateLoading = ref(false)
const renameLoading = ref(false)

// 配置模板选择
const configTemplates = ref<ConfigTemplate[]>([])
const selectedTemplateId = ref<string>('')

// 重命名相关
const renameModalVisible = ref(false)
const renameForm = ref({
  name: '',
})

/**
 * 加载配置模板列表
 */
const loadConfigTemplates = async (): Promise<void> => {
  templateLoading.value = true
  try {
    const response = await fetch('http://*************:3000/api/config-templates')
    const result = await response.json()

    if (result.success && result.data) {
      configTemplates.value = result.data.filter((template: ConfigTemplate) => template.isActive)

      // 设置默认选中的模板
      const defaultTemplate = configTemplates.value.find(template => template.isDefault)
      if (defaultTemplate) {
        selectedTemplateId.value = defaultTemplate.id
      } else if (configTemplates.value.length > 0) {
        selectedTemplateId.value = configTemplates.value[0].id
      }

      console.log('✅ [DocumentActions] 配置模板加载成功:', configTemplates.value)
    } else {
      console.warn('⚠️ [DocumentActions] 配置模板加载失败:', result)
      message.warning('加载配置模板失败，将使用默认配置')
    }
  } catch (error) {
    console.error('❌ [DocumentActions] 配置模板加载错误:', error)
    message.error('加载配置模板失败')
  } finally {
    templateLoading.value = false
  }
}

/**
 * 获取当前选中的配置模板ID
 * @returns 配置模板ID，如果没有选中则返回默认模板ID
 */
const getCurrentTemplateId = (): string | undefined => {
  if (selectedTemplateId.value) {
    return selectedTemplateId.value
  }

  // 如果没有选中模板，尝试使用默认模板
  const defaultTemplate = configTemplates.value.find(template => template.isDefault)
  if (defaultTemplate) {
    selectedTemplateId.value = defaultTemplate.id
    return defaultTemplate.id
  }

  // 如果没有默认模板，使用第一个可用模板
  if (configTemplates.value.length > 0) {
    selectedTemplateId.value = configTemplates.value[0].id
    return configTemplates.value[0].id
  }

  return undefined
}

/**
 * 处理配置模板变更
 */
const handleTemplateChange = (templateId: string): void => {
  selectedTemplateId.value = templateId
  const template = configTemplates.value.find(t => t.id === templateId)
  if (template) {
    message.success(`已选择配置模板: ${template.name}`)
  }
}

/**
 * 处理内嵌编辑
 */
const handleEmbeddedEdit = async (): Promise<void> => {
  embeddedLoading.value = true

  try {
    // 获取当前配置模板
    const templateId = getCurrentTemplateId()
    const configQuery: EditorConfigQuery = templateId ? { template: templateId } : {}

    console.log('🚀 [DocumentActions] handleEmbeddedEdit 开始内嵌编辑')
    console.log('📋 [DocumentActions] 文档ID:', props.documentId)
    console.log('🔧 [DocumentActions] 配置模板ID:', templateId)

    await openEmbeddedEditor(props.documentId, configQuery)
    message.success('正在打开编辑器...')
  } catch (error) {
    console.error('❌ [DocumentActions] 内嵌编辑失败:', error)
    message.error('内嵌编辑器打开失败')
  } finally {
    embeddedLoading.value = false
  }
}

/**
 * 处理弹窗编辑
 */
const handlePopupEdit = async (): Promise<void> => {
  popupLoading.value = true

  try {
    // 获取当前配置模板
    const templateId = getCurrentTemplateId()
    const configQuery: EditorConfigQuery = templateId ? { template: templateId } : {}

    console.log('🚀 [DocumentActions] handlePopupEdit 开始弹窗编辑')
    console.log('📋 [DocumentActions] 文档ID:', props.documentId)
    console.log('🔧 [DocumentActions] 配置模板ID:', templateId)

    const editorWindow = await openPopupEditor(props.documentId, configQuery)

    if (editorWindow) {
      message.success('编辑器已在新窗口中打开')

      // 监听窗口关闭事件
      const checkClosed = setInterval(() => {
        if (editorWindow.closed) {
          clearInterval(checkClosed)
          message.info('编辑器窗口已关闭')
        }
      }, 1000)
    }
  } catch (error) {
    console.error('❌ [DocumentActions] 弹窗编辑失败:', error)
    message.error(`打开编辑器失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    popupLoading.value = false
  }
}

/**
 * 处理重命名
 */
const handleRename = (): void => {
  renameForm.value.name = props.documentName || ''
  renameModalVisible.value = true
}

/**
 * 处理重命名提交
 */
const handleRenameSubmit = async (): Promise<void> => {
  if (!renameForm.value.name) {
    message.warning('文档名称不能为空')
    return
  }

  renameLoading.value = true
  try {
    await DocumentsApiService.updateDocumentName(props.documentId, renameForm.value.name)
    message.success('文档重命名成功')
    emit('refreshList')
    renameModalVisible.value = false
  } catch (error) {
    console.error('❌ [DocumentActions] 重命名失败:', error)
    message.error('文档重命名失败')
  } finally {
    renameLoading.value = false
  }
}

/**
 * 处理重命名取消
 */
const handleRenameCancel = (): void => {
  renameModalVisible.value = false
}

/**
 * 处理下载文档
 */
const handleDownload = async (): Promise<void> => {
  try {
    console.log('[DocumentActions] 开始下载文档:', props.documentId)

    // 构建下载URL
    const downloadUrl = `http://*************:3000/api/documents/${props.documentId}/download`

    // 创建一个隐藏的链接来触发下载
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = props.documentName || `document_${props.documentId}`
    link.style.display = 'none'

    // 添加到页面并点击
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)

    message.success('文档下载已开始')
    console.log('✅ [DocumentActions] 文档下载成功')
  } catch (error) {
    console.error('❌ [DocumentActions] 下载失败:', error)
    message.error(`下载失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 处理删除文档
 */
const handleDelete = (): void => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除文档 "${props.documentName || props.documentId}" 吗？此操作不可逆。`,
    okText: '删除',
    cancelText: '取消',
    onOk: async () => {
      try {
        await DocumentsApiService.deleteDocument(props.documentId)
        message.success('文档删除成功')
        emit('refreshList')
      } catch (error) {
        console.error('❌ [DocumentActions] 删除文档失败:', error)
        message.error('文档删除失败')
      }
    },
  })
}

// 组件挂载时加载配置模板
onMounted(() => {
  loadConfigTemplates()
})
</script>

<style scoped>
.document-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 优化下拉选择器样式 */
:deep(.ant-select-selector) {
  border-radius: 4px;
  font-size: 12px;
}

:deep(.ant-select-selection-item) {
  font-size: 12px;
}

/* 优化按钮样式 */
:deep(.ant-btn-sm) {
  height: 24px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 22px;
}

:deep(.ant-btn-sm .anticon) {
  font-size: 12px;
}

/* 删除菜单项样式 */
:deep(.delete-item) {
  color: #ff4d4f;
}

:deep(.delete-item:hover) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 配置模板选择器样式 */
:deep(.ant-select-sm) {
  font-size: 12px;
}

:deep(.ant-select-sm .ant-select-selector) {
  height: 24px;
  padding: 0 6px;
}

:deep(.ant-select-sm .ant-select-selection-item) {
  line-height: 22px;
}
</style>
