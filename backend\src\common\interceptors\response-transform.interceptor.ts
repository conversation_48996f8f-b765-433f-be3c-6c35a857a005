import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';

/**
 * 统一API响应格式接口
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
  requestId?: string;
}

/**
 * 响应转换拦截器
 * 
 * 将所有控制器返回的数据转换为统一的API响应格式
 * 
 * @class ResponseTransformInterceptor
 * @implements {NestInterceptor}
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Injectable()
export class ResponseTransformInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    
    // 检查是否是OnlyOffice回调路由，如果是则跳过转换
    if (this.isOnlyOfficeCallback(request.url)) {
      return next.handle(); // 直接返回原始响应，不进行转换
    }
    
    // 获取请求ID
    const requestId = (request as Request & { requestId?: string }).requestId;

    return next.handle().pipe(
      map((data: T) => {
        // 如果返回的数据已经是标准格式，直接返回
        if (this.isApiResponse(data)) {
          return {
            ...data,
            requestId: data.requestId || requestId,
            timestamp: data.timestamp || new Date().toISOString(),
          } as ApiResponse<T>;
        }

        // 转换为标准API响应格式
        return {
          success: true,
          data,
          message: this.getSuccessMessage(context, data),
          timestamp: new Date().toISOString(),
          requestId,
        } as ApiResponse<T>;
      }),
    );
  }

  /**
   * 检查是否是OnlyOffice回调路由
   */
  private isOnlyOfficeCallback(url: string): boolean {
    return url.includes('/api/editor/callback');
  }

  /**
   * 检查数据是否已经是API响应格式
   */
  private isApiResponse(data: unknown): data is ApiResponse {
    return (
      data &&
      typeof data === 'object' &&
      typeof (data as Record<string, unknown>).success === 'boolean' &&
      typeof (data as Record<string, unknown>).message === 'string'
    );
  }

  /**
   * 根据上下文和数据生成成功消息
   */
  private getSuccessMessage(context: ExecutionContext, _data: unknown): string {
    const handler = context.getHandler();
    const _controller = context.getClass();
    
    // 从Swagger装饰器获取操作描述
    const operationMetadata = Reflect.getMetadata('swagger/apiOperation', handler);
    if (operationMetadata && operationMetadata.summary) {
      return `${operationMetadata.summary}成功`;
    }

    // 根据HTTP方法生成默认消息
    const request = context.switchToHttp().getRequest<Request>();
    const method = request.method.toUpperCase();
    
    switch (method) {
      case 'GET':
        return '查询成功';
      case 'POST':
        return '创建成功';
      case 'PUT':
      case 'PATCH':
        return '更新成功';
      case 'DELETE':
        return '删除成功';
      default:
        return '操作成功';
    }
  }
} 