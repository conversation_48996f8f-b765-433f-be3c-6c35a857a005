import { 
  Controller, 
  Post, 
  Get, 
  Delete, 
  Put,
  Param, 
  Body, 
  Query,
  UseInterceptors,
  UploadedFile,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FilenetService } from '../services/filenet.service';
import { Express } from 'express';

/**
 * FileNet集成控制器
 * 提供FileNet文档管理的RESTful API
 */
@ApiTags('FileNet集成')
@Controller('filenet')
export class FilenetController {
  constructor(private readonly filenetService: FilenetService) {}

  /**
   * 上传文档到FileNet
   */
  @Post('upload')
  @ApiOperation({ 
    summary: '上传文档到FileNet',
    description: '将文档上传到IBM FileNet Content Manager并同步到数据库'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '文档文件',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        templateId: {
          type: 'string',
          description: '配置模板ID'
        },
        createdBy: {
          type: 'string',
          description: '创建者'
        }
      },
      required: ['file']
    },
  })
  @ApiResponse({ status: 201, description: '文档上传成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body('templateId') templateId?: string,
    @Body('createdBy') createdBy?: string
  ) {
    try {
      if (!file) {
        throw new HttpException('请选择要上传的文件', HttpStatus.BAD_REQUEST);
      }

      console.log(`[FilenetController] 开始上传文档: ${file.originalname}`);

      // 直接使用FilenetService的uploadDocument方法，它已经包含了数据库同步
      const result = await this.filenetService.uploadDocument(
        file,  // 传递整个file对象
        file.originalname,
        createdBy || 'system'
      );

      return {
        success: true,
        message: '文档上传成功',
        data: {
          documentId: result.dbId,  // 使用正确的属性名
          fnDocId: result.docId,    // 使用正确的属性名
          originalName: result.originalName,
          fileSize: file.size,
          fileHash: result.fileHash,
          contentWasReused: result.contentWasReused
        },
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] 上传文档失败:', error.message);
      throw new HttpException(
        error.message || '上传文档失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 从FileNet下载文档
   */
  @Get(':fnDocId/download')
  @ApiOperation({ 
    summary: '下载FileNet文档',
    description: '从IBM FileNet Content Manager下载指定文档'
  })
  @ApiResponse({ status: 200, description: '文档下载成功' })
  @ApiResponse({ status: 404, description: '文档不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async downloadDocument(@Param('fnDocId') fnDocId: string) {
    try {
      console.log(`[FilenetController] 下载FileNet文档: ${fnDocId}`);

      const downloadResult = await this.filenetService.downloadDocument(fnDocId);

      if (downloadResult && downloadResult.stream) {
        // 对于文件下载，我们应该直接返回流而不是JSON
        // 这里暂时返回信息，实际应用中可能需要流式响应
        return {
          success: true,
          message: '文档下载成功',
          data: {
            fnDocId,
            fileName: downloadResult.fileName
          },
          timestamp: new Date().toISOString(),
          requestId: Date.now().toString()
        };
      } else {
        throw new HttpException('下载文档失败', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      console.error('[FilenetController] 下载文档失败:', error.message);
      throw new HttpException(
        error.message || '下载文档失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取FileNet文档信息
   */
  @Get(':fnDocId/info')
  @ApiOperation({ 
    summary: '获取FileNet文档信息',
    description: '获取指定FileNet文档的详细信息'
  })
  @ApiResponse({ status: 200, description: '获取文档信息成功' })
  @ApiResponse({ status: 404, description: '文档不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getDocumentInfo(@Param('fnDocId') fnDocId: string) {
    try {
      console.log(`[FilenetController] 获取FileNet文档信息: ${fnDocId}`);

      const documentInfo = await this.filenetService.getDocumentInfo(fnDocId);

      return {
        success: true,
        message: '获取文档信息成功',
        data: documentInfo,
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] 获取文档信息失败:', error.message);
      throw new HttpException(
        error.message || '获取文档信息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除FileNet文档
   */
  @Delete(':fnDocId')
  @ApiOperation({ 
    summary: '删除FileNet文档',
    description: '从IBM FileNet Content Manager删除指定文档'
  })
  @ApiResponse({ status: 200, description: '文档删除成功' })
  @ApiResponse({ status: 404, description: '文档不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async deleteDocument(@Param('fnDocId') fnDocId: string) {
    try {
      console.log(`[FilenetController] 删除FileNet文档: ${fnDocId}`);

      await this.filenetService.deleteDocument(fnDocId);

      return {
        success: true,
        message: '文档删除成功',
        data: { fnDocId },
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] 删除文档失败:', error.message);
      throw new HttpException(
        error.message || '删除文档失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新FileNet文档版本
   */
  @Put(':fnDocId/version')
  @ApiOperation({ 
    summary: '更新FileNet文档版本',
    description: '为指定FileNet文档创建新版本'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '新版本文档文件',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        versionComment: {
          type: 'string',
          description: '版本说明'
        }
      },
      required: ['file']
    },
  })
  @ApiResponse({ status: 200, description: '文档版本更新成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '文档不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UseInterceptors(FileInterceptor('file'))
  async updateDocumentVersion(
    @Param('fnDocId') fnDocId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('versionComment') versionComment?: string
  ) {
    try {
      if (!file) {
        throw new HttpException('请选择要上传的文件', HttpStatus.BAD_REQUEST);
      }

      console.log(`[FilenetController] 更新FileNet文档版本: ${fnDocId}`);

      const result = await this.filenetService.updateDocumentVersion(
        fnDocId,
        file.buffer,
        versionComment
      );

      return {
        success: true,
        message: '文档版本更新成功',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] 更新文档版本失败:', error.message);
      throw new HttpException(
        error.message || '更新文档版本失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取FileNet文档版本列表
   */
  @Get(':fnDocId/versions')
  @ApiOperation({ 
    summary: '获取FileNet文档版本列表',
    description: '获取指定FileNet文档的所有版本信息'
  })
  @ApiResponse({ status: 200, description: '获取版本列表成功' })
  @ApiResponse({ status: 404, description: '文档不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getDocumentVersions(@Param('fnDocId') fnDocId: string) {
    try {
      console.log(`[FilenetController] 获取FileNet文档版本列表: ${fnDocId}`);

      const versionsResult = await this.filenetService.getDocumentVersions(fnDocId);

      return {
        success: true,
        message: '获取版本列表成功',
        data: {
          fnDocId,
          versions: versionsResult.data,
          total: versionsResult.data ? versionsResult.data.length : 0
        },
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] 获取版本列表失败:', error.message);
      throw new HttpException(
        error.message || '获取版本列表失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 搜索FileNet文档
   */
  @Get('search')
  @ApiOperation({ 
    summary: '搜索FileNet文档',
    description: '在IBM FileNet Content Manager中搜索文档'
  })
  @ApiResponse({ status: 200, description: '搜索完成' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async searchDocuments(
    @Query('query') query?: string,
    @Query('mimeType') mimeType?: string,
    @Query('createdAfter') createdAfter?: string,
    @Query('createdBefore') createdBefore?: string,
    @Query('limit') limit: number = 50,
    @Query('offset') offset: number = 0
  ) {
    try {
      console.log(`[FilenetController] 搜索FileNet文档: ${query}`);

      const searchOptions = {
        query,
        mimeType,
        createdAfter,
        createdBefore,
        limit: Number(limit),
        offset: Number(offset)
      };

      const result = await this.filenetService.searchDocuments(searchOptions);

      return {
        success: true,
        message: '搜索完成',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] 搜索文档失败:', error.message);
      throw new HttpException(
        error.message || '搜索文档失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 测试FileNet连接
   */
  @Get('health')
  @ApiOperation({ 
    summary: '测试FileNet连接',
    description: '检查与IBM FileNet Content Manager的连接状态'
  })
  @ApiResponse({ status: 200, description: 'FileNet连接正常' })
  @ApiResponse({ status: 503, description: 'FileNet连接异常' })
  async checkHealth() {
    try {
      console.log('[FilenetController] 检查FileNet连接状态');

      const healthStatus = await this.filenetService.testConnection();

      // 如果状态是up，返回成功；如果是down但包含405错误，也认为是成功（服务器在运行）
      const isSuccess = healthStatus.status === 'up' || 
                       (healthStatus.message && healthStatus.message.includes('405'));

      return {
        success: isSuccess,
        message: isSuccess ? 'FileNet服务器运行正常' : healthStatus.message,
        data: {
          ...healthStatus,
          // 如果收到405错误，修正状态为up
          status: isSuccess ? 'up' : healthStatus.status,
          note: healthStatus.message && healthStatus.message.includes('405') ? 
                'FileNet服务器正在运行 (405错误是预期的，因为上传端点不支持GET方法)' : 
                undefined
        },
        timestamp: new Date().toISOString(),
        requestId: Date.now().toString()
      };
    } catch (error) {
      console.error('[FilenetController] FileNet健康检查失败:', error.message);
      throw new HttpException(
        error.message || 'FileNet健康检查失败',
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }
} 