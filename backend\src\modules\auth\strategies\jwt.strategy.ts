import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UserJwtPayload } from '../services/auth.service';

/**
 * JWT令牌的原始payload结构（解码后）
 */
interface JwtTokenPayload {
  sub?: string;
  username?: string;
  role?: string;
  type?: string;
  iss?: string;
  aud?: string;
  iat?: number;
  exp?: number;
  nbf?: number;
}

/**
 * JWT策略
 * 
 * 实现PassportJS的JWT策略，用于验证和解析JWT令牌
 * 当使用JwtAuthGuard时，会自动调用此策略进行用户验证
 * 
 * @class JwtStrategy
 * @extends PassportStrategy
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      // 从Authorization Bearer token中提取JWT
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      
      // 不忽略过期令牌
      ignoreExpiration: false,
      
      // JWT密钥
      secretOrKey: configService.get<string>('JWT_SECRET') || 'your-secret-key',
      
      // 验证发行者 - 修复为与token生成时一致
      issuer: configService.get<string>('JWT_ISSUER') || 'onlyoffice-nestjs',
      
      // 验证受众 - 修复为与token生成时一致
      audience: configService.get<string>('JWT_AUDIENCE') || 'onlyoffice-client',
    });
  }

  /**
   * 验证JWT payload
   * 
   * 当JWT验证通过后，此方法会被调用来进一步验证用户信息
   * 返回的用户对象会被附加到request.user上
   * 
   * @param payload JWT解码后的payload
   * @returns 验证后的用户信息
   */
  async validate(payload: JwtTokenPayload): Promise<UserJwtPayload> {
    console.log('🔍 [JwtStrategy] validate 开始:', {
      payloadType: typeof payload,
      payloadKeys: payload ? Object.keys(payload) : null,
      payload: {
        sub: payload?.sub,
        username: payload?.username,
        type: payload?.type,
        iss: payload?.iss,
        aud: payload?.aud,
        iat: payload?.iat,
        exp: payload?.exp,
        nbf: payload?.nbf,
        isExpired: payload?.exp ? payload.exp < Math.floor(Date.now() / 1000) : false,
        currentTime: Math.floor(Date.now() / 1000),
      },
      timestamp: new Date().toISOString(),
    });

    // 验证payload结构
    if (!payload || !payload.sub || !payload.username) {
      console.error('❌ [JwtStrategy] payload结构无效:', {
        hasPayload: !!payload,
        hasSub: !!payload?.sub,
        hasUsername: !!payload?.username,
        payload: payload,
      });
      throw new UnauthorizedException('无效的令牌结构');
    }

    // 检查令牌类型
    if (payload.type !== 'access') {
      console.error('❌ [JwtStrategy] 令牌类型错误:', {
        expectedType: 'access',
        actualType: payload.type,
      });
      throw new UnauthorizedException('令牌类型错误');
    }

    // 验证令牌是否在有效期内
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < currentTime) {
      console.error('❌ [JwtStrategy] 令牌已过期:', {
        exp: payload.exp,
        currentTime: currentTime,
        expiredBy: currentTime - payload.exp,
      });
      throw new UnauthorizedException('令牌已过期');
    }

    // 检查令牌是否已生效
    if (payload.nbf && payload.nbf > currentTime) {
      console.error('❌ [JwtStrategy] 令牌尚未生效:', {
        nbf: payload.nbf,
        currentTime: currentTime,
        timeUntilValid: payload.nbf - currentTime,
      });
      throw new UnauthorizedException('令牌尚未生效');
    }

    // 构造用户信息对象
    const user: UserJwtPayload = {
      sub: payload.sub,
      username: payload.username,
      role: payload.role || 'user',
      type: payload.type,
      iss: payload.iss,
      aud: payload.aud,
      iat: payload.iat,
      exp: payload.exp,
      nbf: payload.nbf,
    };

    console.log('✅ [JwtStrategy] 验证成功，返回用户信息:', {
      user: {
        sub: user.sub,
        username: user.username,
        role: user.role,
        type: user.type,
      },
      timestamp: new Date().toISOString(),
    });

    // 可以在这里添加额外的用户验证逻辑
    // 例如：检查用户是否仍然存在于数据库中，是否被禁用等
    // const isUserValid = await this.validateUserFromDatabase(user.sub);
    // if (!isUserValid) {
    //   throw new UnauthorizedException('用户账户无效或已被禁用');
    // }

    return user;
  }

  /**
   * 从数据库验证用户（可选实现）
   * @param userId 用户ID
   * @returns 用户是否有效
   */
  private async validateUserFromDatabase(_userId: string): Promise<boolean> {
    // TODO: 实现数据库用户验证逻辑
    // 检查用户是否存在且状态正常
    return true;
  }
} 