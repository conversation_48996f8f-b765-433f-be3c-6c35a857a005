import { Controller, Post, Get, Body, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService, AuthResponse, UserJwtPayload } from '../services/auth.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { LoginDto, RefreshTokenDto, AuthResponseDto, UserInfoDto } from '../dto/auth.dto';

/**
 * 认证请求接口 (扩展Request)
 */
export interface AuthenticatedRequest extends Request {
  user: UserJwtPayload;
}

/**
 * 认证控制器
 *
 * 处理用户认证相关的RESTful API请求，包括登录、令牌刷新等
 *
 * @class AuthController
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * 用户登录
   * POST /api/auth/login
   */
  @Post('login')
  @ApiOperation({
    summary: '用户登录',
    description: '验证用户凭据并返回访问令牌',
  })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: '用户名或密码错误',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponse> {
    return await this.authService.login(loginDto.username, loginDto.password);
  }

  /**
   * 刷新访问令牌
   * POST /api/auth/refresh
   */
  @Post('refresh')
  @ApiOperation({
    summary: '刷新访问令牌',
    description: '使用刷新令牌获取新的访问令牌',
  })
  @ApiResponse({
    status: 200,
    description: '令牌刷新成功',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: '无效的刷新令牌',
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<AuthResponse> {
    return await this.authService.refreshToken(refreshTokenDto.refreshToken);
  }

  /**
   * 获取当前用户信息
   * GET /api/auth/me
   */
  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '获取当前用户信息',
    description: '根据JWT令牌获取当前登录用户的详细信息',
  })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
    type: UserInfoDto,
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  async getCurrentUser(@Request() req: AuthenticatedRequest) {
    const user = await this.authService.getCurrentUser(req.user.sub);
    return user;
  }

  /**
   * 用户登出
   * POST /api/auth/logout
   */
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '用户登出',
    description: '登出当前用户(客户端应删除本地存储的令牌)',
  })
  @ApiResponse({
    status: 200,
    description: '登出成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '登出成功' },
      },
    },
  })
  async logout(@Request() req: AuthenticatedRequest) {
    // 注意: 在JWT架构中，登出主要是客户端行为
    // 服务端可以将令牌加入黑名单(可选实现)
    return {
      message: '登出成功，请清除本地存储的令牌',
      user: {
        id: req.user.sub,
        username: req.user.username,
      },
    };
  }

  /**
   * 调试Token状态（开发环境使用）
   */
  @Get('debug-token')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '调试Token状态' })
  @ApiResponse({ status: 200, description: 'Token调试信息' })
  async debugToken(@Request() req: AuthenticatedRequest): Promise<{
    success: boolean;
    message: string;
    data: Record<string, unknown>;
  }> {
    const token = (req.headers as unknown as Record<string, string>)?.authorization?.replace('Bearer ', '');
    const user = req.user;
    
    // 解析token信息
    let tokenPayload = null;
    try {
      if (token) {
        const parts = token.split('.');
        if (parts.length === 3) {
          tokenPayload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        }
      }
    } catch (error) {
      console.error('解析token失败:', error);
    }

    const currentTime = Math.floor(Date.now() / 1000);

    return {
      success: true,
      message: 'Token调试信息',
      data: {
        tokenInfo: {
          hasToken: !!token,
          tokenLength: token?.length,
          tokenPrefix: token?.substring(0, 20) + '...',
          isValidStructure: token?.split('.').length === 3,
        },
        payload: tokenPayload ? {
          sub: tokenPayload.sub,
          username: tokenPayload.username,
          role: tokenPayload.role,
          type: tokenPayload.type,
          iss: tokenPayload.iss,
          aud: tokenPayload.aud,
          iat: tokenPayload.iat,
          exp: tokenPayload.exp,
          nbf: tokenPayload.nbf,
        } : null,
        validation: tokenPayload ? {
          isExpired: tokenPayload.exp < currentTime,
          isNotYetValid: tokenPayload.nbf > currentTime,
          timeUntilExpiry: tokenPayload.exp - currentTime,
          timeFromIssued: currentTime - tokenPayload.iat,
        } : null,
        userFromGuard: user ? {
          sub: user.sub,
          username: user.username,
          role: user.role,
          type: user.type,
        } : null,
        requestInfo: {
          url: req.url,
          method: req.method,
          userAgent: req.headers?.['user-agent'],
        },
        serverTime: {
          timestamp: currentTime,
          iso: new Date().toISOString(),
        },
      },
    };
  }
} 