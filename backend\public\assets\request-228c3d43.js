import{a8 as n,m as t}from"./index-5218909a.js";const a=n.create({baseURL:"/api",timeout:15e3,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{const r=localStorage.getItem("token");return r&&(e.headers.Authorization=`Bearer ${r}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e)));a.interceptors.response.use(e=>{const{data:r}=e;if(typeof r=="object"&&r!==null&&"success"in r&&r.success===!1){const o=r.message||"请求失败";return t.error(o),Promise.reject(new Error(o))}return r},e=>{if(console.error("响应拦截器错误:",e),!e.response)return t.error("网络错误，请检查网络连接"),Promise.reject(e);const{status:r,data:o}=e.response;let s="请求失败";switch(r){case 401:s="未授权，请重新登录",localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login";break;case 403:s="权限不足";break;case 404:s="请求的资源不存在";break;case 500:s="服务器内部错误";break;default:s=(o==null?void 0:o.message)||`请求失败 (${r})`}return t.error(s),Promise.reject(new Error(s))});export{a as r};
