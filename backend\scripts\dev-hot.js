#!/usr/bin/env node

/**
 * 热更新开发服务器启动脚本
 * 
 * 功能特点：
 * - 智能文件监控
 * - 快速热重载
 * - 详细的变更日志
 * - 错误恢复机制
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const chokidar = require('chokidar');

class HotReloadServer {
  constructor() {
    this.process = null;
    this.isRestarting = false;
    this.restartDelay = 1000; // 1秒延迟重启
    this.lastRestart = 0;
    
    this.watchedExtensions = ['.ts', '.js', '.json'];
    this.ignoredPaths = [
      'node_modules',
      'dist',
      '.git',
      'logs',
      '*.log'
    ];
  }

  start() {
    console.log('🚀 启动 OnlyOffice 热更新开发服务器...\n');
    
    this.startServer();
    this.setupFileWatcher();
    this.setupSignalHandlers();
  }

  startServer() {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] 🔄 启动 NestJS 应用...`);

    this.process = spawn('npm', ['run', 'start:dev:hot'], {
      stdio: 'inherit',
      shell: true,
      cwd: path.resolve(__dirname, '..')
    });

    this.process.on('exit', (code, signal) => {
      if (code !== null) {
        console.log(`\n[${new Date().toLocaleTimeString()}] 🔴 服务器退出，代码: ${code}`);
      }
      if (signal) {
        console.log(`[${new Date().toLocaleTimeString()}] 📡 接收到信号: ${signal}`);
      }
    });

    this.process.on('error', (error) => {
      console.error(`\n[${new Date().toLocaleTimeString()}] ❌ 启动失败:`, error.message);
    });
  }

  setupFileWatcher() {
    const srcPath = path.resolve(__dirname, '../src');
    
    console.log(`📁 监控目录: ${srcPath}`);
    console.log(`📄 监控文件类型: ${this.watchedExtensions.join(', ')}`);
    console.log('');

    const watcher = chokidar.watch(srcPath, {
      ignored: this.ignoredPaths.map(p => `**/${p}/**`),
      persistent: true,
      ignoreInitial: true,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 50
      }
    });

    watcher.on('change', (filePath) => {
      const relativePath = path.relative(process.cwd(), filePath);
      const ext = path.extname(filePath);
      
      if (this.watchedExtensions.includes(ext)) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] 📝 文件变更: ${relativePath}`);
        
        this.restartServer();
      }
    });

    watcher.on('add', (filePath) => {
      const relativePath = path.relative(process.cwd(), filePath);
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ➕ 新文件: ${relativePath}`);
      
      this.restartServer();
    });

    watcher.on('unlink', (filePath) => {
      const relativePath = path.relative(process.cwd(), filePath);
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ➖ 删除文件: ${relativePath}`);
      
      this.restartServer();
    });

    watcher.on('error', (error) => {
      console.error(`\n[${new Date().toLocaleTimeString()}] ❌ 文件监控错误:`, error);
    });
  }

  restartServer() {
    if (this.isRestarting) {
      return;
    }

    const now = Date.now();
    if (now - this.lastRestart < this.restartDelay) {
      return;
    }

    this.isRestarting = true;
    this.lastRestart = now;

    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] 🔄 重启服务器中...`);

    if (this.process) {
      this.process.kill('SIGTERM');
    }

    setTimeout(() => {
      this.startServer();
      this.isRestarting = false;
      
      const restartTime = new Date().toLocaleTimeString();
      console.log(`[${restartTime}] ✅ 服务器重启完成\n`);
    }, this.restartDelay);
  }

  setupSignalHandlers() {
    process.on('SIGINT', () => {
      console.log('\n🛑 接收到退出信号，正在关闭服务器...');
      
      if (this.process) {
        this.process.kill('SIGTERM');
      }
      
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 接收到终止信号，正在关闭服务器...');
      
      if (this.process) {
        this.process.kill('SIGTERM');
      }
      
      process.exit(0);
    });
  }
}

// 启动热更新服务器
const server = new HotReloadServer();
server.start(); 