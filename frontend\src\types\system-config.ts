/**
 * 系统配置类型定义
 */

export interface SystemConfig {
  setting_key: string
  setting_value: string
  description?: string
  updated_at?: string
}

export interface ConfigCategory {
  key: string
  name: string
  icon?: string
  description?: string
  advanced?: boolean
  configs?: SystemConfig[]
}

export interface ConfigUpdateRequest {
  setting_value: string
  description?: string
}

export interface ConfigHistoryItem {
  id: string
  setting_key: string
  old_value: string
  new_value: string
  changed_by: string
  changed_at: string
  description?: string
}

/**
 * 配置测试结果详细信息
 */
export interface ConfigTestDetails {
  host?: string
  port?: number
  database?: string
  responseTime?: string
  pingResponse?: string
  timeout?: string
  serverUrl?: string
  version?: string
  [key: string]: unknown
}

export interface ConfigTestResult {
  success: boolean
  message: string
  details?: ConfigTestDetails
  responseTime?: number
}

/**
 * 配置验证规则值的类型
 */
export type ConfigValidationValue = string | number | boolean | RegExp

export interface ConfigValidationRule {
  type: 'required' | 'regex' | 'range' | 'custom'
  value?: ConfigValidationValue
  message: string
}

export interface ConfigField {
  key: string
  name: string
  type: 'input' | 'textarea' | 'select' | 'switch' | 'number' | 'password'
  defaultValue: string
  description: string
  placeholder?: string
  options?: Array<{ label: string; value: string }>
  validation?: ConfigValidationRule[]
  sensitive?: boolean // 是否为敏感信息（如密码）
  testable?: boolean // 是否可以测试连接
}
