const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

// 数据库配置
const dbConfig = {
    host: '*************',
    port: 3306,
    user: 'onlyfile_user', 
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
};

async function testFullLogin() {
    let connection;
    
    try {
        console.log('🔄 开始完整登录测试...');
        
        // 1. 连接数据库
        console.log('🔄 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 2. 查找用户（模拟UserService.findByUsername）
        const username = 'admin';
        console.log('🔄 查找用户:', username);
        
        const query = `
            SELECT u.*, ur.name as role_name, ur.display_name as role_display_name, ur.permissions as role_permissions
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            WHERE u.username = ? AND u.deleted_at IS NULL
        `;
        
        console.log('📋 执行查询:', query);
        const [results] = await connection.execute(query, [username]);
        console.log('📋 查询结果数量:', results.length);
        
        if (results.length === 0) {
            console.log('❌ 用户不存在');
            return;
        }
        
        const user = results[0];
        console.log('✅ 找到用户:', {
            id: user.id,
            username: user.username,
            email: user.email,
            status: user.status,
            role_name: user.role_name,
            role_display_name: user.role_display_name,
            password_hash_preview: user.password_hash.substring(0, 30) + '...'
        });
        
        // 3. 验证密码
        const password = 'admin123';
        console.log('🔄 验证密码:', password);
        console.log('📋 数据库中的哈希:', user.password_hash);
        
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        console.log('✅ 密码验证结果:', isPasswordValid);
        
        if (!isPasswordValid) {
            console.log('❌ 密码验证失败');
            return;
        }
        
        // 4. 检查用户状态
        console.log('🔄 检查用户状态:', user.status);
        if (user.status !== 'active') {
            console.log('❌ 用户状态不正确，期望: active，实际:', user.status);
            return;
        }
        
        // 5. 检查锁定状态
        if (user.locked_until && new Date() < new Date(user.locked_until)) {
            console.log('❌ 用户账户已被锁定到:', user.locked_until);
            return;
        }
        
        console.log('🎉 登录验证成功！');
        console.log('👤 用户信息:');
        console.log('  - ID:', user.id);
        console.log('  - 用户名:', user.username);
        console.log('  - 邮箱:', user.email);
        console.log('  - 姓名:', user.full_name);
        console.log('  - 状态:', user.status);
        console.log('  - 角色:', user.role_name, '(' + user.role_display_name + ')');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('🔧 完整错误:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 执行测试
testFullLogin(); 