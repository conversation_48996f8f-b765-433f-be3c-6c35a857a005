<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 企业级商务风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #0f1419;
      color: #ffffff;
      line-height: 1.6;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
    }

    /* 顶部欢迎区域 */
    .welcome-section {
      background: linear-gradient(135deg, #1e2a3a 0%, #2d3748 100%);
      border-radius: 12px;
      padding: 30px;
      margin-bottom: 24px;
      border: 1px solid #2d3748;
      position: relative;
      overflow: hidden;
    }

    .welcome-section::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
      border-radius: 50%;
    }

    .welcome-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;
    }

    .welcome-info h1 {
      font-size: 28px;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 8px;
    }

    .welcome-info p {
      color: #9ca3af;
      font-size: 16px;
    }

    .user-status {
      display: flex;
      align-items: center;
      gap: 16px;
      background: rgba(59, 130, 246, 0.1);
      padding: 12px 20px;
      border-radius: 10px;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      background: #10b981;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 16px;
    }

    /* 核心指标区域 */
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 24px;
    }

    .metric-card {
      background: linear-gradient(135deg, #1e2a3a 0%, #374151 100%);
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #374151;
      position: relative;
      transition: all 0.3s ease;
    }

    .metric-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      border-color: #3b82f6;
    }

    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .metric-icon.documents { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
    .metric-icon.users { background: linear-gradient(135deg, #10b981 0%, #047857 100%); }
    .metric-icon.storage { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
    .metric-icon.efficiency { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }

    .metric-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;
    }

    .trend-up {
      background: rgba(16, 185, 129, 0.2);
      color: #10b981;
    }

    .trend-down {
      background: rgba(239, 68, 68, 0.2);
      color: #ef4444;
    }

    .metric-value {
      font-size: 32px;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 4px;
    }

    .metric-label {
      color: #9ca3af;
      font-size: 14px;
      margin-bottom: 12px;
    }

    .metric-details {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #6b7280;
    }

    /* 主要功能区域 */
    .main-content {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
      margin-bottom: 24px;
    }

    .content-card {
      background: linear-gradient(135deg, #1e2a3a 0%, #374151 100%);
      border-radius: 12px;
      border: 1px solid #374151;
      overflow: hidden;
    }

    .card-header {
      padding: 20px 24px;
      border-bottom: 1px solid #374151;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
    }

    .card-action {
      color: #3b82f6;
      font-size: 14px;
      cursor: pointer;
      transition: color 0.3s ease;
    }

    .card-action:hover {
      color: #60a5fa;
    }

    /* 快捷操作 */
    .quick-actions {
      padding: 24px;
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .action-item {
      background: rgba(59, 130, 246, 0.1);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 10px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-item:hover {
      background: rgba(59, 130, 246, 0.2);
      transform: translateY(-2px);
    }

    .action-icon {
      font-size: 28px;
      margin-bottom: 8px;
      display: block;
    }

    .action-label {
      color: #ffffff;
      font-size: 14px;
      font-weight: 500;
    }

    /* 最近活动 */
    .activity-list {
      padding: 0 24px 24px;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #374151;
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
    }

    .activity-content {
      flex: 1;
    }

    .activity-title {
      color: #ffffff;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .activity-desc {
      color: #9ca3af;
      font-size: 12px;
    }

    .activity-time {
      color: #6b7280;
      font-size: 12px;
    }

    /* 侧边栏 */
    .sidebar-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* 系统状态 */
    .system-status {
      padding: 20px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .status-item:last-child {
      margin-bottom: 0;
    }

    .status-label {
      color: #9ca3af;
      font-size: 14px;
    }

    .status-value {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-indicator-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .status-online { background: #10b981; }
    .status-warning { background: #f59e0b; }
    .status-error { background: #ef4444; }

    /* 通知中心 */
    .notification-list {
      padding: 0 20px 20px;
    }

    .notification-item {
      background: rgba(59, 130, 246, 0.05);
      border: 1px solid rgba(59, 130, 246, 0.1);
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
    }

    .notification-item:last-child {
      margin-bottom: 0;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .notification-type {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 10px;
      background: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
    }

    .notification-time {
      color: #6b7280;
      font-size: 12px;
    }

    .notification-content {
      color: #d1d5db;
      font-size: 14px;
      line-height: 1.4;
    }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .main-content {
        grid-template-columns: 1fr;
      }
      
      .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 16px;
      }
      
      .welcome-header {
        flex-direction: column;
        gap: 16px;
      }
      
      .metrics-grid {
        grid-template-columns: 1fr;
      }
      
      .action-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-header">
        <div class="welcome-info">
          <h1>OnlyOffice企业管理系统</h1>
          <p>企业级文档管理与协作平台 - 提升工作效率，保障数据安全</p>
        </div>
        <div class="user-status">
          <div class="status-indicator"></div>
          <div class="user-info">
            <div class="user-avatar">管</div>
            <div>
              <div style="font-size: 14px; font-weight: 600;">管理员</div>
              <div style="font-size: 12px; color: #9ca3af;">系统管理员</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心指标 -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon documents">📄</div>
          <div class="metric-trend trend-up">↗ +12.3%</div>
        </div>
        <div class="metric-value">1,247</div>
        <div class="metric-label">文档总数</div>
        <div class="metric-details">
          <span>本月新增: 156</span>
          <span>活跃: 892</span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon users">👥</div>
          <div class="metric-trend trend-up">↗ +8.7%</div>
        </div>
        <div class="metric-value">89</div>
        <div class="metric-label">活跃用户</div>
        <div class="metric-details">
          <span>在线: 24</span>
          <span>本周登录: 67</span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon storage">💾</div>
          <div class="metric-trend trend-up">↗ +15.2%</div>
        </div>
        <div class="metric-value">847GB</div>
        <div class="metric-label">存储使用</div>
        <div class="metric-details">
          <span>可用: 153GB</span>
          <span>使用率: 84.7%</span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon efficiency">📈</div>
          <div class="metric-trend trend-up">↗ +5.4%</div>
        </div>
        <div class="metric-value">95.8%</div>
        <div class="metric-label">系统可用性</div>
        <div class="metric-details">
          <span>平均响应: 180ms</span>
          <span>故障率: 0.2%</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 快捷操作和最近活动 -->
      <div>
        <!-- 快捷操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">🚀 快捷操作</h3>
            <span class="card-action">查看全部</span>
          </div>
          <div class="quick-actions">
            <div class="action-grid">
              <div class="action-item">
                <span class="action-icon">📝</span>
                <div class="action-label">创建文档</div>
              </div>
              <div class="action-item">
                <span class="action-icon">📤</span>
                <div class="action-label">上传文件</div>
              </div>
              <div class="action-item">
                <span class="action-icon">📋</span>
                <div class="action-label">模板管理</div>
              </div>
              <div class="action-item">
                <span class="action-icon">👥</span>
                <div class="action-label">用户管理</div>
              </div>
              <div class="action-item">
                <span class="action-icon">⚙️</span>
                <div class="action-label">系统配置</div>
              </div>
              <div class="action-item">
                <span class="action-icon">📊</span>
                <div class="action-label">数据报表</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="content-card" style="margin-top: 20px;">
          <div class="card-header">
            <h3 class="card-title">📋 最近活动</h3>
            <span class="card-action">查看更多</span>
          </div>
          <div class="activity-list">
            <div class="activity-item">
              <div class="activity-icon">📄</div>
              <div class="activity-content">
                <div class="activity-title">项目需求文档.docx 已更新</div>
                <div class="activity-desc">张三编辑了文档内容，添加了新的功能需求</div>
              </div>
              <div class="activity-time">2分钟前</div>
            </div>
            <div class="activity-item">
              <div class="activity-icon">👥</div>
              <div class="activity-content">
                <div class="activity-title">新用户加入系统</div>
                <div class="activity-desc">李四已成功注册，角色设置为编辑员</div>
              </div>
              <div class="activity-time">15分钟前</div>
            </div>
            <div class="activity-item">
              <div class="activity-icon">📊</div>
              <div class="activity-content">
                <div class="activity-title">财务报表.xlsx 共享给团队</div>
                <div class="activity-desc">王五将Q4财务报表共享给财务部门</div>
              </div>
              <div class="activity-time">1小时前</div>
            </div>
            <div class="activity-item">
              <div class="activity-icon">⚙️</div>
              <div class="activity-content">
                <div class="activity-title">系统配置已更新</div>
                <div class="activity-desc">管理员修改了文件上传大小限制</div>
              </div>
              <div class="activity-time">3小时前</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="sidebar-content">
        <!-- 系统状态 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">🔧 系统状态</h3>
            <span class="card-action">详情</span>
          </div>
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">数据库连接</span>
              <div class="status-value">
                <span class="status-indicator-dot status-online"></span>
                <span style="color: #10b981; font-size: 14px;">正常</span>
              </div>
            </div>
            <div class="status-item">
              <span class="status-label">OnlyOffice服务</span>
              <div class="status-value">
                <span class="status-indicator-dot status-online"></span>
                <span style="color: #10b981; font-size: 14px;">运行中</span>
              </div>
            </div>
            <div class="status-item">
              <span class="status-label">FileNet连接</span>
              <div class="status-value">
                <span class="status-indicator-dot status-warning"></span>
                <span style="color: #f59e0b; font-size: 14px;">警告</span>
              </div>
            </div>
            <div class="status-item">
              <span class="status-label">备份状态</span>
              <div class="status-value">
                <span class="status-indicator-dot status-online"></span>
                <span style="color: #10b981; font-size: 14px;">已完成</span>
              </div>
            </div>
            <div class="status-item">
              <span class="status-label">系统负载</span>
              <div class="status-value">
                <span style="color: #ffffff; font-size: 14px;">23%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知中心 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">🔔 通知中心</h3>
            <span class="card-action">全部标记已读</span>
          </div>
          <div class="notification-list">
            <div class="notification-item">
              <div class="notification-header">
                <span class="notification-type">系统</span>
                <span class="notification-time">10分钟前</span>
              </div>
              <div class="notification-content">
                系统将于今晚23:00进行定期维护，预计耗时30分钟
              </div>
            </div>
            <div class="notification-item">
              <div class="notification-header">
                <span class="notification-type">安全</span>
                <span class="notification-time">2小时前</span>
              </div>
              <div class="notification-content">
                检测到异常登录尝试，已自动阻止并记录日志
              </div>
            </div>
            <div class="notification-item">
              <div class="notification-header">
                <span class="notification-type">更新</span>
                <span class="notification-time">1天前</span>
              </div>
              <div class="notification-content">
                OnlyOffice编辑器已更新至最新版本，新增协作功能
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 简单的交互效果
    document.querySelectorAll('.action-item').forEach(item => {
      item.addEventListener('click', () => {
        console.log('执行操作:', item.querySelector('.action-label').textContent);
      });
    });

    document.querySelectorAll('.card-action').forEach(action => {
      action.addEventListener('click', () => {
        console.log('查看详情:', action.textContent);
      });
    });

    // 实时时间更新
    function updateActivityTimes() {
      const timeElements = document.querySelectorAll('.activity-time');
      // 模拟时间更新逻辑
    }

    setInterval(updateActivityTimes, 60000); // 每分钟更新一次
  </script>
</body>
</html> 