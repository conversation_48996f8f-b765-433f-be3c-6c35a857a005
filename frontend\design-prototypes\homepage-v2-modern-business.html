<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 现代商务风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f8fafc;
      color: #334155;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 24px;
    }

    /* 顶部概览区域 */
    .header-overview {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
      border-radius: 16px;
      padding: 32px;
      margin-bottom: 24px;
      color: white;
      position: relative;
      overflow: hidden;
    }

    .header-overview::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -10%;
      width: 400px;
      height: 400px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      border-radius: 50%;
    }

    .overview-content {
      position: relative;
      z-index: 1;
    }

    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .company-info h1 {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .company-info p {
      font-size: 16px;
      opacity: 0.9;
    }

    .time-info {
      text-align: right;
    }

    .current-time {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .current-date {
      font-size: 14px;
      opacity: 0.8;
    }

    /* 核心数据概览 */
    .data-overview {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-top: 32px;
    }

    .data-item {
      text-align: center;
      padding: 16px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
    }

    .data-number {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .data-label {
      font-size: 14px;
      opacity: 0.8;
    }

    /* 主要内容区域 */
    .main-layout {
      display: grid;
      grid-template-columns: 1fr 350px;
      gap: 24px;
    }

    /* 工作台区域 */
    .workspace-section {
      background: white;
      border-radius: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .section-header {
      padding: 24px 24px 0;
      border-bottom: 1px solid #e2e8f0;
      margin-bottom: 0;
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 16px;
    }

    .section-tabs {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;
    }

    .tab-item {
      padding: 12px 0;
      color: #64748b;
      font-weight: 500;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .tab-item.active {
      color: #3b82f6;
      border-bottom-color: #3b82f6;
    }

    /* 快捷操作网格 */
    .actions-grid {
      padding: 24px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
    }

    .action-card {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-card:hover {
      border-color: #3b82f6;
      background: #eff6ff;
      transform: translateY(-2px);
    }

    .action-icon {
      width: 48px;
      height: 48px;
      margin: 0 auto 12px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .action-title {
      font-size: 14px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .action-desc {
      font-size: 12px;
      color: #64748b;
    }

    /* 最近文档 */
    .recent-documents {
      padding: 0 24px 24px;
    }

    .document-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f1f5f9;
    }

    .document-item:last-child {
      border-bottom: none;
    }

    .doc-type-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: white;
    }

    .doc-word { background: #2563eb; }
    .doc-excel { background: #059669; }
    .doc-ppt { background: #dc2626; }
    .doc-pdf { background: #7c3aed; }

    .doc-info {
      flex: 1;
    }

    .doc-name {
      font-size: 14px;
      font-weight: 500;
      color: #1e293b;
      margin-bottom: 2px;
    }

    .doc-meta {
      font-size: 12px;
      color: #64748b;
    }

    .doc-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    .status-editing { background: #fef3c7; color: #92400e; }
    .status-shared { background: #d1fae5; color: #065f46; }
    .status-complete { background: #e0e7ff; color: #3730a3; }

    /* 侧边栏 */
    .sidebar {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .sidebar-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    /* 个人信息卡片 */
    .profile-card {
      padding: 24px;
      text-align: center;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    }

    .profile-avatar {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
      color: white;
    }

    .profile-name {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .profile-role {
      font-size: 14px;
      color: #64748b;
      margin-bottom: 16px;
    }

    .profile-stats {
      display: flex;
      justify-content: space-around;
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }

    .stat-label {
      font-size: 12px;
      color: #64748b;
    }

    /* 待办事项 */
    .todo-list {
      padding: 24px;
    }

    .todo-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f1f5f9;
    }

    .todo-item:last-child {
      border-bottom: none;
    }

    .todo-checkbox {
      width: 18px;
      height: 18px;
      border: 2px solid #d1d5db;
      border-radius: 4px;
      cursor: pointer;
    }

    .todo-checkbox.checked {
      background: #3b82f6;
      border-color: #3b82f6;
      position: relative;
    }

    .todo-checkbox.checked::after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 2px;
      color: white;
      font-size: 12px;
    }

    .todo-text {
      flex: 1;
      font-size: 14px;
      color: #374151;
    }

    .todo-text.completed {
      text-decoration: line-through;
      color: #9ca3af;
    }

    .todo-priority {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .priority-high { background: #ef4444; }
    .priority-medium { background: #f59e0b; }
    .priority-low { background: #10b981; }

    /* 系统监控 */
    .monitor-grid {
      padding: 24px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    .monitor-item {
      text-align: center;
      padding: 16px;
      background: #f8fafc;
      border-radius: 12px;
    }

    .monitor-value {
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .monitor-label {
      font-size: 12px;
      color: #64748b;
    }

    .monitor-status {
      margin-top: 8px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    .status-normal { background: #d1fae5; color: #065f46; }
    .status-warning { background: #fef3c7; color: #92400e; }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .main-layout {
        grid-template-columns: 1fr;
      }
      
      .data-overview {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .actions-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 16px;
      }
      
      .overview-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
      }
      
      .data-overview {
        grid-template-columns: 1fr;
      }
      
      .actions-grid {
        grid-template-columns: 1fr;
      }
      
      .monitor-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 顶部概览 -->
    <div class="header-overview">
      <div class="overview-content">
        <div class="overview-header">
          <div class="company-info">
            <h1>🏢 OnlyOffice企业管理系统</h1>
            <p>专业的企业级文档管理与协作平台</p>
          </div>
          <div class="time-info">
            <div class="current-time" id="currentTime">14:30</div>
            <div class="current-date" id="currentDate">2024年12月19日 星期四</div>
          </div>
        </div>
        
        <!-- 核心数据概览 -->
        <div class="data-overview">
          <div class="data-item">
            <div class="data-number">1,247</div>
            <div class="data-label">文档总数</div>
          </div>
          <div class="data-item">
            <div class="data-number">89</div>
            <div class="data-label">活跃用户</div>
          </div>
          <div class="data-item">
            <div class="data-number">24</div>
            <div class="data-label">在线用户</div>
          </div>
          <div class="data-item">
            <div class="data-number">98.5%</div>
            <div class="data-label">系统可用性</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容布局 -->
    <div class="main-layout">
      <!-- 工作台区域 -->
      <div class="workspace-section">
        <!-- 标签页 -->
        <div class="section-header">
          <h2 class="section-title">📋 我的工作台</h2>
          <div class="section-tabs">
            <div class="tab-item active">快捷操作</div>
            <div class="tab-item">最近文档</div>
            <div class="tab-item">我的收藏</div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div id="quickActions" class="tab-content">
          <div class="actions-grid">
            <div class="action-card">
              <div class="action-icon">📝</div>
              <div class="action-title">创建文档</div>
              <div class="action-desc">新建Word、Excel、PPT文档</div>
            </div>
            <div class="action-card">
              <div class="action-icon">📤</div>
              <div class="action-title">上传文件</div>
              <div class="action-desc">批量上传本地文档</div>
            </div>
            <div class="action-card">
              <div class="action-icon">📋</div>
              <div class="action-title">文档模板</div>
              <div class="action-desc">使用现有模板创建</div>
            </div>
            <div class="action-card">
              <div class="action-icon">🔍</div>
              <div class="action-title">文档搜索</div>
              <div class="action-desc">全文检索文档内容</div>
            </div>
            <div class="action-card">
              <div class="action-icon">📊</div>
              <div class="action-title">数据报表</div>
              <div class="action-desc">查看统计分析报告</div>
            </div>
            <div class="action-card">
              <div class="action-icon">⚙️</div>
              <div class="action-title">系统设置</div>
              <div class="action-desc">配置系统参数</div>
            </div>
          </div>
        </div>

        <!-- 最近文档 -->
        <div id="recentDocs" class="tab-content" style="display: none;">
          <div class="recent-documents">
            <div class="document-item">
              <div class="doc-type-icon doc-word">W</div>
              <div class="doc-info">
                <div class="doc-name">项目需求分析文档.docx</div>
                <div class="doc-meta">2小时前 · 张三 · 2.4MB</div>
              </div>
              <div class="doc-status status-editing">编辑中</div>
            </div>
            <div class="document-item">
              <div class="doc-type-icon doc-excel">E</div>
              <div class="doc-info">
                <div class="doc-name">Q4财务报表.xlsx</div>
                <div class="doc-meta">1天前 · 李四 · 1.8MB</div>
              </div>
              <div class="doc-status status-shared">已共享</div>
            </div>
            <div class="document-item">
              <div class="doc-type-icon doc-ppt">P</div>
              <div class="doc-info">
                <div class="doc-name">产品发布会演示.pptx</div>
                <div class="doc-meta">3天前 · 王五 · 15.2MB</div>
              </div>
              <div class="doc-status status-complete">已完成</div>
            </div>
            <div class="document-item">
              <div class="doc-type-icon doc-pdf">P</div>
              <div class="doc-info">
                <div class="doc-name">用户操作手册.pdf</div>
                <div class="doc-meta">1周前 · 赵六 · 8.9MB</div>
              </div>
              <div class="doc-status status-complete">已完成</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="sidebar">
        <!-- 个人信息 -->
        <div class="sidebar-card">
          <div class="profile-card">
            <div class="profile-avatar">管</div>
            <div class="profile-name">系统管理员</div>
            <div class="profile-role">Administrator</div>
            <div class="profile-stats">
              <div class="stat-item">
                <div class="stat-number">156</div>
                <div class="stat-label">我的文档</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">24</div>
                <div class="stat-label">协作项目</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">89</div>
                <div class="stat-label">团队成员</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 待办事项 -->
        <div class="sidebar-card">
          <div class="section-header" style="padding: 20px 24px;">
            <h3 class="section-title">✅ 待办事项</h3>
          </div>
          <div class="todo-list">
            <div class="todo-item">
              <div class="todo-checkbox"></div>
              <div class="todo-text">审核项目需求文档</div>
              <div class="todo-priority priority-high"></div>
            </div>
            <div class="todo-item">
              <div class="todo-checkbox checked"></div>
              <div class="todo-text completed">更新系统配置模板</div>
              <div class="todo-priority priority-medium"></div>
            </div>
            <div class="todo-item">
              <div class="todo-checkbox"></div>
              <div class="todo-text">准备月度工作报告</div>
              <div class="todo-priority priority-medium"></div>
            </div>
            <div class="todo-item">
              <div class="todo-checkbox"></div>
              <div class="todo-text">检查系统备份状态</div>
              <div class="todo-priority priority-low"></div>
            </div>
          </div>
        </div>

        <!-- 系统监控 -->
        <div class="sidebar-card">
          <div class="section-header" style="padding: 20px 24px;">
            <h3 class="section-title">📊 系统监控</h3>
          </div>
          <div class="monitor-grid">
            <div class="monitor-item">
              <div class="monitor-value">23%</div>
              <div class="monitor-label">CPU使用率</div>
              <div class="monitor-status status-normal">正常</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-value">67%</div>
              <div class="monitor-label">内存使用率</div>
              <div class="monitor-status status-normal">正常</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-value">180ms</div>
              <div class="monitor-label">响应时间</div>
              <div class="monitor-status status-normal">正常</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-value">84%</div>
              <div class="monitor-label">存储使用</div>
              <div class="monitor-status status-warning">注意</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 实时时间更新
    function updateTime() {
      const now = new Date();
      const time = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      const date = now.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });
      
      document.getElementById('currentTime').textContent = time;
      document.getElementById('currentDate').textContent = date;
    }

    // 标签页切换
    document.querySelectorAll('.tab-item').forEach((tab, index) => {
      tab.addEventListener('click', () => {
        // 移除所有活跃状态
        document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.style.display = 'none');
        
        // 激活当前标签
        tab.classList.add('active');
        
        // 显示对应内容
        if (index === 0) {
          document.getElementById('quickActions').style.display = 'block';
        } else if (index === 1) {
          document.getElementById('recentDocs').style.display = 'block';
        }
      });
    });

    // 待办事项交互
    document.querySelectorAll('.todo-checkbox').forEach(checkbox => {
      checkbox.addEventListener('click', () => {
        checkbox.classList.toggle('checked');
        const todoText = checkbox.nextElementSibling;
        todoText.classList.toggle('completed');
      });
    });

    // 快捷操作点击
    document.querySelectorAll('.action-card').forEach(card => {
      card.addEventListener('click', () => {
        const title = card.querySelector('.action-title').textContent;
        console.log('执行操作:', title);
      });
    });

    // 初始化
    updateTime();
    setInterval(updateTime, 1000);
  </script>
</body>
</html> 