import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';
import { User, UserListResult, UserRole, UserStats } from '../entities/user.entity';
import { CreateUserDto, UpdateUserDto, ChangePasswordDto, ResetPasswordDto, UserQueryDto, UserPermissionQueryDto } from '../dto/user.dto';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

// 用户服务专用的查询结果类型定义
interface UserQueryResult {
  id: string;
  username: string;
  email: string;
  full_name: string;
  phone?: string;
  avatar_url?: string;
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  role_id: string;
  role_name: string;
  role_display_name: string;
  role_permissions: string;
  role_color: string;
  password_hash: string;
  failed_login_attempts: number;
  email_verified: boolean;
  phone_verified: boolean;
  two_factor_enabled: boolean;
  department?: string;
  position?: string;
  last_login_at: Date | null;
  password_changed_at: Date | null;
  locked_until: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

interface CountQueryResult {
  total?: number;
  count?: number;
  total_users?: number;
  active_users?: number;
  inactive_users?: number;
  suspended_users?: number;
  online_users?: number;
  new_users_today?: number;
  new_users_this_week?: number;
  new_users_this_month?: number;
}

interface UserPermissionQueryResult {
  id: string;
  username: string;
  email: string;
  fullName: string;
  lastLoginAt: Date | null;
  role_id?: string;
  role_name?: string;
  role_display_name?: string;
  role_permissions?: string;
  role_color?: string;
}

@Injectable()
export class UserService {
  constructor(private readonly databaseService: DatabaseService) {}

  // 创建用户
  async createUser(createUserDto: CreateUserDto, currentUserId?: string): Promise<User> {
    const { username, email, password, ...userData } = createUserDto;

    // 检查用户名是否已存在
    const existingUser = await this.findByUsername(username);
    if (existingUser) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await this.findByEmail(email);
      if (existingEmail) {
        throw new ConflictException('邮箱地址已存在');
      }
    }

    // 验证角色是否存在
    if (userData.role_id) {
      const role = await this.findRoleById(userData.role_id);
      if (!role) {
        throw new BadRequestException('指定的角色不存在');
      }
    }

    // 密码加密
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);

    const userId = uuidv4();
    const now = new Date();

    const query = `
      INSERT INTO users (
        id, username, email, password_hash, full_name, phone, avatar_url,
        status, role_id, department, position, email_verified, phone_verified,
        two_factor_enabled, created_by, created_at, updated_at, password_changed_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      userId,
      username,
      email || null,
      password_hash,
      userData.full_name || null,
      userData.phone || null,
      userData.avatar_url || null,
      userData.status || 'active',
      userData.role_id || null,
      userData.department || null,
      userData.position || null,
      userData.email_verified || false,
      userData.phone_verified || false,
      userData.two_factor_enabled || false,
      currentUserId || 'system',
      now,
      now,
      now
    ];

    await this.databaseService.query(query, values);

    // 记录密码历史
    await this.savePasswordHistory(userId, password_hash);

    return this.findById(userId);
  }

  // 根据ID查找用户
  async findById(id: string): Promise<User | null> {
    const query = `
      SELECT u.*, ur.name as role_name, ur.display_name as role_display_name, ur.permissions as role_permissions
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.id = ? AND u.deleted_at IS NULL
    `;
    
    const results = await this.databaseService.query(query, [id]) as UserQueryResult[];
    if (results.length === 0) {
      return null;
    }

    const user = results[0];
    return {
      ...user,
      role_permissions: this.parsePermissions(user.role_permissions),
      created_at: new Date(user.created_at),
      updated_at: new Date(user.updated_at),
      last_login_at: user.last_login_at ? new Date(user.last_login_at) : null,
      password_changed_at: user.password_changed_at ? new Date(user.password_changed_at) : null,
      locked_until: user.locked_until ? new Date(user.locked_until) : null,
      deleted_at: user.deleted_at ? new Date(user.deleted_at) : null
    };
  }

  /**
   * 解析权限字符串为数组
   * @param permissions 权限数据，可能是字符串、JSON数组、Buffer等类型
   * @returns 权限数组
   */
  private parsePermissions(permissions: string | Buffer | string[] | Record<string, unknown> | null | undefined): string[] {
    if (!permissions) {
      return [];
    }
    
    let permissionStr: string;
    
    // 处理不同类型的权限数据
    if (typeof permissions === 'string') {
      permissionStr = permissions;
    } else if (Buffer.isBuffer(permissions)) {
      // MySQL可能返回Buffer类型
      permissionStr = permissions.toString('utf8');
    } else if (typeof permissions === 'object') {
      // MySQL JSON字段可能返回对象或数组
      try {
        if (Array.isArray(permissions)) {
          // 如果是数组，直接返回
          return permissions.filter(p => typeof p === 'string' && p.length > 0);
        } else {
          // 如果是对象，尝试JSON序列化
          permissionStr = JSON.stringify(permissions);
        }
      } catch (parseError) {
        console.warn('[UserService] 无法解析权限对象:', permissions, parseError);
        return [];
      }
    } else {
      // 其他类型，强制转换为字符串
      permissionStr = String(permissions);
    }
    
    // 确保有有效的字符串
    permissionStr = permissionStr.trim();
    if (!permissionStr) {
      return [];
    }
    
    // 如果是 "*" 表示所有权限
    if (permissionStr === '*') {
      return ['*'];
    }
    
    // 处理JSON数组字符串格式，如: ["users.*", "documents.*"]
    if (permissionStr.startsWith('[') && permissionStr.endsWith(']')) {
      try {
        const parsed = JSON.parse(permissionStr);
        if (Array.isArray(parsed)) {
          return parsed.filter(p => typeof p === 'string' && p.length > 0);
        }
      } catch (parseError) {
        console.warn('[UserService] 无法解析JSON权限数组:', permissionStr, parseError);
      }
    }
    
    // 如果是逗号分隔的权限列表，分割为数组
    return permissionStr.split(',').map(p => p.trim()).filter(p => p.length > 0);
  }

  // 根据用户名查找用户
  async findByUsername(username: string): Promise<User | null> {
    const query = `
      SELECT u.*, ur.name as role_name, ur.display_name as role_display_name, ur.permissions as role_permissions
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username = ? AND u.deleted_at IS NULL
    `;
    
    console.log('[UserService] 执行查询:');
    console.log(query);
    console.log('[UserService] 查询参数:', [username]);
    
    const results = await this.databaseService.query(query, [username]);
    console.log('[UserService] 查询结果数量:', results.length);
    
    if (results.length === 0) {
      return null;
    }

    const user = results[0] as UserQueryResult;
    console.log('[UserService] 找到用户原始数据:', {
      id: user.id,
      username: user.username,
      email: user.email,
      status: user.status,
      role_name: user.role_name,
      password_hash_preview: user.password_hash ? user.password_hash.substring(0, 20) + '...' : 'null'
    });
    
    const processedUser = {
      ...user,
      role_permissions: this.parsePermissions(user.role_permissions),
      created_at: new Date(user.created_at),
      updated_at: new Date(user.updated_at),
      last_login_at: user.last_login_at ? new Date(user.last_login_at) : null,
      password_changed_at: user.password_changed_at ? new Date(user.password_changed_at) : null,
      locked_until: user.locked_until ? new Date(user.locked_until) : null,
      deleted_at: user.deleted_at ? new Date(user.deleted_at) : null
    };
    
    console.log('[UserService] 处理后的用户数据:', {
      id: processedUser.id,
      username: processedUser.username,
      status: processedUser.status,
      role_name: processedUser.role_name,
      password_hash_exists: !!processedUser.password_hash
    });
    
    return processedUser;
  }

  // 根据邮箱查找用户
  async findByEmail(email: string): Promise<User | null> {
    const query = `
      SELECT u.*, ur.name as role_name, ur.display_name as role_display_name, ur.permissions as role_permissions
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.email = ? AND u.deleted_at IS NULL
    `;
    
    const results = await this.databaseService.query(query, [email]);
    if (results.length === 0) {
      return null;
    }

    const user = results[0] as UserQueryResult;
    return {
      ...user,
      role_permissions: this.parsePermissions(user.role_permissions),
      created_at: new Date(user.created_at),
      updated_at: new Date(user.updated_at),
      last_login_at: user.last_login_at ? new Date(user.last_login_at) : null,
      password_changed_at: user.password_changed_at ? new Date(user.password_changed_at) : null,
      locked_until: user.locked_until ? new Date(user.locked_until) : null,
      deleted_at: user.deleted_at ? new Date(user.deleted_at) : null
    };
  }

  // 分页查询用户列表
  async findMany(queryDto: UserQueryDto): Promise<UserListResult> {
    const { page = 1, pageSize = 10, keyword, status, role_id, department, created_start, created_end, sort_by = 'created_at', sort_order = 'DESC' } = queryDto;
    
    const whereConditions = ['u.deleted_at IS NULL'];
    const queryParams: (string | number | Date)[] = [];

    // 构建查询条件
    if (keyword) {
      whereConditions.push('(u.username LIKE ? OR u.email LIKE ? OR u.full_name LIKE ?)');
      const keywordPattern = `%${keyword}%`;
      queryParams.push(keywordPattern, keywordPattern, keywordPattern);
    }

    if (status) {
      whereConditions.push('u.status = ?');
      queryParams.push(status);
    }

    if (role_id) {
      whereConditions.push('u.role_id = ?');
      queryParams.push(role_id);
    }

    if (department) {
      whereConditions.push('u.department = ?');
      queryParams.push(department);
    }

    if (created_start) {
      whereConditions.push('u.created_at >= ?');
      queryParams.push(created_start);
    }

    if (created_end) {
      whereConditions.push('u.created_at <= ?');
      queryParams.push(created_end);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      WHERE ${whereClause}
    `;
    const countResult = await this.databaseService.query(countQuery, queryParams);
    const total = (countResult[0] as { total: number }).total;

    // 查询数据
    const offset = (page - 1) * pageSize;
    const dataQuery = `
      SELECT u.*, ur.name as role_name, ur.display_name as role_display_name, ur.permissions as role_permissions
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE ${whereClause}
      ORDER BY u.${sort_by} ${sort_order}
      LIMIT ? OFFSET ?
    `;
    queryParams.push(pageSize, offset);

    const results = await this.databaseService.query(dataQuery, queryParams) as UserQueryResult[];
    const data = results.map(user => ({
      ...user,
      role_permissions: this.parsePermissions(user.role_permissions),
      created_at: new Date(user.created_at),
      updated_at: new Date(user.updated_at),
      last_login_at: user.last_login_at ? new Date(user.last_login_at) : null,
      password_changed_at: user.password_changed_at ? new Date(user.password_changed_at) : null,
      locked_until: user.locked_until ? new Date(user.locked_until) : null
    }));

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // 更新用户信息
  async updateUser(id: string, updateUserDto: UpdateUserDto, currentUserId?: string): Promise<User> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const { username, email, ...userData } = updateUserDto;

    // 检查用户名是否已被其他用户使用
    if (username && username !== user.username) {
      const existingUser = await this.findByUsername(username);
      if (existingUser && existingUser.id !== id) {
        throw new ConflictException('用户名已存在');
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (email && email !== user.email) {
      const existingEmail = await this.findByEmail(email);
      if (existingEmail && existingEmail.id !== id) {
        throw new ConflictException('邮箱地址已存在');
      }
    }

    // 验证角色是否存在
    if (userData.role_id && userData.role_id !== user.role_id) {
      const role = await this.findRoleById(userData.role_id);
      if (!role) {
        throw new BadRequestException('指定的角色不存在');
      }
    }

    // 构建更新字段
    const updateFields: string[] = [];
    const updateValues: (string | number | Date | boolean | null)[] = [];

    Object.entries({ username, email, ...userData }).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });

    if (updateFields.length === 0) {
      return user;
    }

    updateFields.push('updated_by = ?', 'updated_at = ?');
    updateValues.push(currentUserId || 'system', new Date());

    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;
    updateValues.push(id);

    await this.databaseService.query(query, updateValues);

    return this.findById(id);
  }

  // 软删除用户
  async deleteUser(id: string, currentUserId?: string): Promise<void> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const query = `
      UPDATE users 
      SET deleted_at = ?, updated_by = ?, updated_at = ?
      WHERE id = ?
    `;

    await this.databaseService.query(query, [new Date(), currentUserId || 'system', new Date(), id]);
  }

  // 修改用户密码
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { current_password, new_password, confirm_password } = changePasswordDto;

    if (new_password !== confirm_password) {
      throw new BadRequestException('新密码与确认密码不匹配');
    }

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(current_password, user.password_hash);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('当前密码不正确');
    }

    // 检查新密码是否与历史密码重复
    const isPasswordReused = await this.checkPasswordHistory(userId, new_password);
    if (isPasswordReused) {
      throw new BadRequestException('不能使用最近使用过的密码');
    }

    // 加密新密码
    const saltRounds = 12;
    const new_password_hash = await bcrypt.hash(new_password, saltRounds);

    // 更新密码
    const query = `
      UPDATE users 
      SET password_hash = ?, password_changed_at = ?, updated_at = ?, failed_login_attempts = 0
      WHERE id = ?
    `;

    await this.databaseService.query(query, [new_password_hash, new Date(), new Date(), userId]);

    // 保存密码历史
    await this.savePasswordHistory(userId, new_password_hash);
  }

  // 重置用户密码（管理员操作）
  async resetPassword(resetPasswordDto: ResetPasswordDto, currentUserId?: string): Promise<void> {
    const { user_id, new_password } = resetPasswordDto;

    const user = await this.findById(user_id);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 加密新密码
    const saltRounds = 12;
    const new_password_hash = await bcrypt.hash(new_password, saltRounds);

    // 更新密码
    const query = `
      UPDATE users 
      SET password_hash = ?, password_changed_at = ?, updated_at = ?, updated_by = ?, failed_login_attempts = 0, locked_until = NULL
      WHERE id = ?
    `;

    await this.databaseService.query(query, [new_password_hash, new Date(), new Date(), currentUserId || 'system', user_id]);

    // 保存密码历史
    await this.savePasswordHistory(user_id, new_password_hash);
  }

  // 更新用户登录信息
  async updateLoginInfo(userId: string, ipAddress?: string): Promise<void> {
    const query = `
      UPDATE users 
      SET last_login_at = ?, last_login_ip = ?, failed_login_attempts = 0, locked_until = NULL, updated_at = ?
      WHERE id = ?
    `;

    await this.databaseService.query(query, [new Date(), ipAddress, new Date(), userId]);
  }

  // 增加失败登录次数
  async incrementFailedLoginAttempts(userId: string): Promise<void> {
    const user = await this.findById(userId);
    if (!user) return;

    const failedAttempts = user.failed_login_attempts + 1;
    let lockedUntil = null;

    // 如果失败次数达到5次，锁定账户30分钟
    if (failedAttempts >= 5) {
      lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后解锁
    }

    const query = `
      UPDATE users 
      SET failed_login_attempts = ?, locked_until = ?, updated_at = ?
      WHERE id = ?
    `;

    await this.databaseService.query(query, [failedAttempts, lockedUntil, new Date(), userId]);
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(): Promise<UserStats> {
    const queries = [
      'SELECT COUNT(*) as total_users FROM users WHERE deleted_at IS NULL',
      'SELECT COUNT(*) as active_users FROM users WHERE status = "active" AND deleted_at IS NULL',
      'SELECT COUNT(*) as inactive_users FROM users WHERE status = "inactive" AND deleted_at IS NULL',
      'SELECT COUNT(*) as suspended_users FROM users WHERE status = "suspended" AND deleted_at IS NULL',
      'SELECT COUNT(*) as online_users FROM user_sessions WHERE is_active = TRUE AND expires_at > NOW()',
      'SELECT COUNT(*) as new_users_today FROM users WHERE DATE(created_at) = CURDATE() AND deleted_at IS NULL',
      'SELECT COUNT(*) as new_users_this_week FROM users WHERE YEARWEEK(created_at) = YEARWEEK(NOW()) AND deleted_at IS NULL',
      'SELECT COUNT(*) as new_users_this_month FROM users WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW()) AND deleted_at IS NULL'
    ];

    const results = await Promise.all(queries.map(query => this.databaseService.query(query)));

    return {
      total_users: (results[0][0] as CountQueryResult).total_users || 0,
      active_users: (results[1][0] as CountQueryResult).active_users || 0,
      inactive_users: (results[2][0] as CountQueryResult).inactive_users || 0,
      suspended_users: (results[3][0] as CountQueryResult).suspended_users || 0,
      online_users: (results[4][0] as CountQueryResult).online_users || 0,
      new_users_today: (results[5][0] as CountQueryResult).new_users_today || 0,
      new_users_this_week: (results[6][0] as CountQueryResult).new_users_this_week || 0,
      new_users_this_month: (results[7][0] as CountQueryResult).new_users_this_month || 0
    };
  }

  /**
   * 获取用户权限列表
   * @param query 查询参数
   * @returns 用户权限列表
   */
  async getUserPermissions(query: UserPermissionQueryDto): Promise<{
    data: Array<{
      id: string;
      username: string;
      email: string;
      fullName: string;
      lastLoginAt: Date | null;
      roles: Array<{
        id: string;
        name: string;
        displayName: string;
        color: string;
      }>;
      permissions: string[];
    }>;
    total: number;
    page: number;
    pageSize: number;
  }> {
    console.log('🔍 [UserService] getUserPermissions 开始:', {
      query,
      timestamp: new Date().toISOString(),
    });

    try {
      const { page = 1, pageSize = 10, keyword, roleId } = query;
      const offset = (page - 1) * pageSize;

      const whereConditions = ['u.deleted_at IS NULL'];
      const queryParams: (string | number)[] = [];

      // 构建查询条件
      if (keyword) {
        whereConditions.push('(u.username LIKE ? OR u.email LIKE ? OR u.full_name LIKE ?)');
        const keywordPattern = `%${keyword}%`;
        queryParams.push(keywordPattern, keywordPattern, keywordPattern);
      }

      if (roleId) {
        whereConditions.push('u.role_id = ?');
        queryParams.push(roleId);
      }

      const whereClause = whereConditions.join(' AND ');

      console.log('🔍 [UserService] 构建查询条件完成:', {
        whereClause,
        queryParams,
        page,
        pageSize,
        offset,
      });

      // 第一步：查询总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM users u
        WHERE ${whereClause}
      `;
      
      console.log('🔍 [UserService] 开始执行计数查询...');
      const countResult = await this.databaseService.query(countQuery, queryParams);
      const total = (countResult[0] as CountQueryResult).total || 0;
      console.log('✅ [UserService] 计数查询成功:', { total });

      // 第二步：查询数据
      const dataQuery = `
        SELECT 
          u.id,
          u.username,
          u.email,
          u.full_name as fullName,
          u.last_login_at as lastLoginAt,
          ur.id as role_id,
          ur.name as role_name,
          ur.display_name as role_display_name,
          ur.permissions as role_permissions,
          ur.color as role_color
        FROM users u
        LEFT JOIN user_roles ur ON u.role_id = ur.id
        WHERE ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const dataQueryParams = [...queryParams, pageSize, offset];
      console.log('🔍 [UserService] 开始执行数据查询...', { 
        dataQuery: dataQuery.replace(/\s+/g, ' ').trim(),
        dataQueryParams
      });

      const results = await this.databaseService.query(dataQuery, dataQueryParams) as unknown as UserPermissionQueryResult[];
      console.log('✅ [UserService] 数据查询成功:', { 
        resultCount: results.length,
        sampleData: results.length > 0 ? {
          id: results[0].id,
          username: results[0].username,
          role_name: results[0].role_name
        } : null 
      });

      // 第三步：格式化数据
      console.log('🔍 [UserService] 开始格式化数据...');
      const data = results.map((user, index) => {
        try {
          const formattedUser = {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            lastLoginAt: user.lastLoginAt,
            roles: user.role_id ? [{
              id: user.role_id,
              name: user.role_name,
              displayName: user.role_display_name,
              color: user.role_color,
            }] : [],
            permissions: user.role_permissions ? this.parsePermissions(user.role_permissions) : [],
          };
          
          if (index === 0) {
            console.log('✅ [UserService] 数据格式化示例:', formattedUser);
          }
          
          return formattedUser;
        } catch (formatError) {
          console.error('❌ [UserService] 单个用户数据格式化失败:', {
            error: formatError.message,
            user: user,
            index
          });
          throw formatError;
        }
      });

      const result = {
        data,
        total,
        page,
        pageSize,
      };

      console.log('✅ [UserService] getUserPermissions 完成:', {
        resultDataCount: result.data.length,
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      console.error('❌ [UserService] getUserPermissions 失败:', {
        error: error.message,
        stack: error.stack,
        query,
        timestamp: new Date().toISOString(),
      });
      
      // 重新抛出具体的错误信息
      throw new Error(`获取用户权限列表失败: ${error.message}`);
    }
  }

  /**
   * 获取当前用户的权限信息
   * @param userId 用户ID
   * @returns 用户权限信息
   */
  async getCurrentUserPermissions(userId: string): Promise<{
    user: {
      id: string;
      username: string;
      email: string;
      fullName: string;
      lastLoginAt: Date | null;
      roles: Array<{
        id: string;
        name: string;
        displayName: string;
        color: string;
      }>;
    permissions: string[];
    };
    permissions: string[];
    roles: Array<{
      id: string;
      name: string;
      displayName: string;
      color: string;
    }>;
  }> {
    console.log('🔍 [UserService] getCurrentUserPermissions 开始:', {
      userId,
      userIdType: typeof userId,
      userIdLength: userId?.length,
      timestamp: new Date().toISOString(),
    });

    // 获取用户基本信息
    const userQuery = `
      SELECT 
        u.id,
        u.username,
        u.email,
        u.full_name as fullName,
        u.last_login_at as lastLoginAt,
        ur.id as role_id,
        ur.name as role_name,
        ur.display_name as role_display_name,
        ur.permissions as role_permissions,
        ur.color as role_color
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.id = ? AND u.deleted_at IS NULL
    `;
    
    console.log('📝 [UserService] 执行用户查询:', {
      query: userQuery.replace(/\s+/g, ' ').trim(),
      queryParams: [userId],
      timestamp: new Date().toISOString(),
    });

    try {
      const userResults = await this.databaseService.query(userQuery, [userId]) as UserQueryResult[];
      
      console.log('✅ [UserService] 数据库查询结果:', {
        resultCount: userResults.length,
        userId,
        resultsSample: userResults.length > 0 ? {
          id: userResults[0].id,
          username: userResults[0].username,
          email: userResults[0].email,
          hasRole: !!userResults[0].role_id,
          roleId: userResults[0].role_id,
          roleName: userResults[0].role_name,
          hasPermissions: !!userResults[0].role_permissions,
        } : null,
        timestamp: new Date().toISOString(),
      });

      if (userResults.length === 0) {
        console.error('❌ [UserService] 用户不存在:', {
          searchedUserId: userId,
          queryUsed: userQuery.replace(/\s+/g, ' ').trim(),
          timestamp: new Date().toISOString(),
        });
        
        // 额外调试：查询所有用户看看数据库中有什么
        try {
          const allUsersQuery = 'SELECT id, username, email, deleted_at FROM users LIMIT 10';
          const allUsers = await this.databaseService.query(allUsersQuery, []) as UserQueryResult[];
          console.log('🔍 [UserService] 数据库中现有的用户（前10个）:', {
            totalFound: allUsers.length,
            users: allUsers.map(u => ({
              id: u.id,
              username: u.username,
              email: u.email,
              isDeleted: !!u.deleted_at,
            })),
          });
        } catch (debugError) {
          console.error('❌ [UserService] 调试查询失败:', debugError);
        }
        
        throw new Error('用户不存在');
      }

      const user = userResults[0];
      
      // 通过role_permissions表获取权限列表
      let permissions: string[] = [];
      if (user.role_id) {
        const permissionQuery = `
          SELECT DISTINCT up.code
          FROM role_permissions rp
          INNER JOIN user_permissions up ON rp.permission_id = up.id AND up.is_active = 1
          WHERE rp.role_id = ?
          ORDER BY up.sort_order
        `;
        const permissionResults = await this.databaseService.query(permissionQuery, [user.role_id]) as Array<{ code: string }>;
        permissions = permissionResults.map((row) => row.code);
      }

      // 获取角色信息
      const roles = user.role_id ? [{
        id: user.role_id,
        name: user.role_name,
        displayName: user.role_display_name,
        color: user.role_color,
      }] : [];

      // 格式化用户信息
      const userInfo = {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        lastLoginAt: user.last_login_at,
        roles: roles,
        permissions: permissions,
      };

      const finalPermissions = permissions;

      const result = {
        user: userInfo,
        permissions: finalPermissions,
        roles,
      };

      console.log('✅ [UserService] getCurrentUserPermissions 成功完成:', {
        userId,
        foundUser: {
          id: result.user.id,
          username: result.user.username,
          email: result.user.email,
        },
        rolesCount: result.roles.length,
        permissionsCount: result.permissions.length,
        isAdminWithEmptyPermissions: user.username === 'admin' && permissions.length === 0,
        appliedAdminFix: user.username === 'admin' && permissions.length === 0,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      console.error('❌ [UserService] getCurrentUserPermissions 查询失败:', {
        userId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  // ===== 私有方法 =====

  // 根据ID查找角色
  private async findRoleById(roleId: string): Promise<UserRole | null> {
    const query = 'SELECT * FROM user_roles WHERE id = ? AND is_active = TRUE';
    const results = await this.databaseService.query(query, [roleId]) as Array<Record<string, unknown>>;
    
    if (results.length === 0) {
      return null;
    }

    const role = results[0];
    return {
      ...role,
      permissions: role.permissions ? JSON.parse(role.permissions as string) : [],
      created_at: new Date(role.created_at as string),
      updated_at: new Date(role.updated_at as string)
    } as UserRole;
  }

  // 保存密码历史
  private async savePasswordHistory(userId: string, passwordHash: string): Promise<void> {
    const query = `
      INSERT INTO password_history (id, user_id, password_hash, created_at)
      VALUES (?, ?, ?, ?)
    `;

    await this.databaseService.query(query, [uuidv4(), userId, passwordHash, new Date()]);

    // 只保留最近5个密码历史记录
    const deleteOldQuery = `
      DELETE FROM password_history 
      WHERE user_id = ? AND id NOT IN (
        SELECT id FROM (
          SELECT id FROM password_history 
          WHERE user_id = ? 
          ORDER BY created_at DESC 
          LIMIT 5
        ) as recent_passwords
      )
    `;

    await this.databaseService.query(deleteOldQuery, [userId, userId]);
  }

  // 检查密码历史
  private async checkPasswordHistory(userId: string, newPassword: string): Promise<boolean> {
    const query = `
      SELECT password_hash FROM password_history 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT 5
    `;

    const results = await this.databaseService.query(query, [userId]) as Array<{ password_hash: string }>;

    for (const record of results) {
      const isMatch = await bcrypt.compare(newPassword, record.password_hash);
      if (isMatch) {
        return true;
      }
    }

    return false;
  }
} 