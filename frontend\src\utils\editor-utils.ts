/**
 * 编辑器打开模式枚举
 */
export enum EditorOpenMode {
  /** 内嵌模式 - 在当前页面中打开 */
  EMBEDDED = 'embedded',
  /** 弹窗模式 - 在新窗口中打开 */
  POPUP = 'popup',
}

/**
 * 编辑器配置查询参数
 */
export interface EditorConfigQuery {
  /** 配置模板名称 */
  template?: string
  /** 是否隐藏聊天功能 */
  hideChat?: boolean
  /** 是否隐藏评论功能 */
  hideComments?: boolean
  /** 是否设置为只读模式 */
  readonly?: boolean
  /** 用户ID */
  userId?: string
  /** 用户名称 */
  userName?: string
}

/**
 * 编辑器打开配置
 */
export interface EditorOpenConfig {
  /** 文档ID */
  documentId: string
  /** 打开模式 */
  mode: EditorOpenMode
  /** 编辑器配置查询参数 */
  configQuery?: EditorConfigQuery
  /** 弹窗窗口配置（仅在popup模式下有效） */
  windowOptions?: {
    width?: number
    height?: number
    left?: number
    top?: number
    resizable?: boolean
    scrollbars?: boolean
    menubar?: boolean
    toolbar?: boolean
    location?: boolean
    status?: boolean
  }
}

/**
 * 默认弹窗窗口配置
 */
const DEFAULT_POPUP_OPTIONS = {
  width: 1200,
  height: 800,
  resizable: true,
  scrollbars: false,
  menubar: false,
  toolbar: false,
  location: false,
  status: false,
}

/**
 * 构建查询参数字符串
 * @param configQuery 配置查询参数
 * @returns 查询参数字符串
 */
const buildQueryString = (configQuery?: EditorConfigQuery): string => {
  if (!configQuery) return ''

  const params = new URLSearchParams()
  Object.entries(configQuery).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value))
    }
  })

  const queryString = params.toString()
  return queryString ? `?${queryString}` : ''
}

/**
 * 打开内嵌编辑器（内部函数）
 *
 * @param documentId 文档ID
 * @param configQuery 编辑器配置查询参数
 * @returns Promise<null>
 */
const _openEmbeddedEditor = async (
  documentId: string,
  configQuery?: EditorConfigQuery
): Promise<null> => {
  // 使用Vue Router进行页面导航
  const router = await import('@/router')
  const queryString = buildQueryString(configQuery)
  await router.default.push(`/documents/editor/${documentId}${queryString}`)
  return null
}

/**
 * 打开弹窗编辑器（内部函数）
 *
 * @param documentId 文档ID
 * @param configQuery 编辑器配置查询参数
 * @param options 窗口选项
 * @returns Promise<Window | null>
 */
const _openPopupEditor = async (
  documentId: string,
  configQuery?: EditorConfigQuery,
  options?: EditorOpenConfig['windowOptions']
): Promise<Window | null> => {
  const finalOptions = { ...DEFAULT_POPUP_OPTIONS, ...options }

  // 计算窗口位置（居中显示）
  const screenWidth = window.screen.availWidth
  const screenHeight = window.screen.availHeight
  const left = finalOptions.left ?? Math.round((screenWidth - finalOptions.width) / 2)
  const top = finalOptions.top ?? Math.round((screenHeight - finalOptions.height) / 2)

  // 构造窗口特性字符串
  const features = [
    `width=${finalOptions.width}`,
    `height=${finalOptions.height}`,
    `left=${left}`,
    `top=${top}`,
    `resizable=${finalOptions.resizable ? 'yes' : 'no'}`,
    `scrollbars=${finalOptions.scrollbars ? 'yes' : 'no'}`,
    `menubar=${finalOptions.menubar ? 'yes' : 'no'}`,
    `toolbar=${finalOptions.toolbar ? 'yes' : 'no'}`,
    `location=${finalOptions.location ? 'yes' : 'no'}`,
    `status=${finalOptions.status ? 'yes' : 'no'}`,
  ].join(',')

  // 构造编辑器URL
  const baseUrl = window.location.origin
  const queryString = buildQueryString(configQuery)
  const editorUrl = `${baseUrl}/editor/fullscreen/${documentId}${queryString}`

  // 打开新窗口
  const newWindow = window.open(editorUrl, `editor_${documentId}`, features)

  if (!newWindow) {
    throw new Error('无法打开新窗口，请检查浏览器弹窗拦截设置')
  }

  // 聚焦到新窗口
  newWindow.focus()

  return newWindow
}

/**
 * 打开OnlyOffice编辑器
 *
 * @param config 编辑器打开配置
 * @returns Promise<Window | null> 如果是弹窗模式，返回新窗口对象；内嵌模式返回null
 */
export const openEditor = async (config: EditorOpenConfig): Promise<Window | null> => {
  const { documentId, mode, configQuery, windowOptions } = config

  switch (mode) {
    case EditorOpenMode.EMBEDDED:
      return _openEmbeddedEditor(documentId, configQuery)

    case EditorOpenMode.POPUP:
      return _openPopupEditor(documentId, configQuery, windowOptions)

    default:
      throw new Error(`不支持的编辑器打开模式: ${mode}`)
  }
}

/**
 * 快捷方法：打开内嵌编辑器
 *
 * @param documentId 文档ID
 * @param configQuery 编辑器配置查询参数
 * @returns Promise<null>
 */
export const openEmbeddedEditor = (
  documentId: string,
  configQuery?: EditorConfigQuery
): Promise<null> => {
  return openEditor({
    documentId,
    mode: EditorOpenMode.EMBEDDED,
    configQuery,
  }) as Promise<null>
}

/**
 * 快捷方法：打开弹窗编辑器
 *
 * @param documentId 文档ID
 * @param configQuery 编辑器配置查询参数
 * @param windowOptions 窗口选项
 * @returns Promise<Window | null>
 */
export const openPopupEditor = (
  documentId: string,
  configQuery?: EditorConfigQuery,
  windowOptions?: EditorOpenConfig['windowOptions']
): Promise<Window | null> => {
  return openEditor({
    documentId,
    mode: EditorOpenMode.POPUP,
    configQuery,
    windowOptions,
  })
}

/**
 * 检查当前是否为弹窗模式
 *
 * @returns boolean
 */
export const isPopupMode = (): boolean => {
  return window.location.pathname.startsWith('/editor/fullscreen/')
}

/**
 * 检查当前是否为内嵌模式
 *
 * @returns boolean
 */
export const isEmbeddedMode = (): boolean => {
  return window.location.pathname.includes('/documents/editor/')
}

/**
 * 获取当前编辑器模式
 *
 * @returns EditorOpenMode | null
 */
export const getCurrentEditorMode = (): EditorOpenMode | null => {
  if (isPopupMode()) {
    return EditorOpenMode.POPUP
  }
  if (isEmbeddedMode()) {
    return EditorOpenMode.EMBEDDED
  }
  return null
}
