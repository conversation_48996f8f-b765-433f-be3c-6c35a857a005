# 老代码引用修复报告

> **问题**: Editor服务中引用了老项目路径，导致编译失败  
> **修复时间**: 2024年12月19日  
> **状态**: ✅ 已完全修复  

## 🚨 发现的问题

### 编译错误
```bash
ERROR in ./src/modules/editor/services/editor.service.ts
Module not found: Error: Can't resolve '../../../../../services/document'
```

### 根本原因
在`editor.service.ts`中发现3处老代码引用：
1. `require('../../../../../services/document')` - 第50行和第287行
2. `require('../../../../../services/configService')` - 第94行

## 🔧 修复方案

### 1. 替换Document Service引用

**修复前:**
```typescript
// 使用老项目的document service
const documentService = require('../../../../../services/document');
return await documentService.getDocumentById(fileId);
```

**修复后:**
```typescript
// 导入新的DocumentService
import { DocumentService } from '../../documents/services/document.service';

// 构造函数注入
constructor(
  private documentService: DocumentService,
) {}

// 使用新的DocumentService
return await this.documentService.getDocumentById(fileId);
```

### 2. 替换Config Service引用

**修复前:**
```typescript
// 使用老项目的configService来获取配置
const configService = require('../../../../../services/configService');
return await configService.getConfig(templateId);
```

**修复后:**
```typescript
// 导入新的ConfigTemplateService
import { ConfigTemplateService } from '../../config/services/config-template.service';

// 构造函数注入
constructor(
  private configTemplateService: ConfigTemplateService,
) {}

// 使用新的服务和方法
if (templateId) {
  return await this.configTemplateService.getTemplateConfig(templateId);
} else {
  const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
  if (defaultTemplate) {
    return await this.configTemplateService.getTemplateConfig(defaultTemplate.id);
  }
}
```

### 3. 更新模块依赖

确保`EditorModule`正确导入了所需的模块：
```typescript
@Module({
  imports: [
    DocumentsModule,  // ✅ 提供DocumentService
    ConfigModule,     // ✅ 提供ConfigTemplateService
  ],
  // ...
})
export class EditorModule {}
```

## ✅ 修复结果

### 编译成功
```bash
> nest build
webpack 5.97.1 compiled successfully in 2867 ms
```

### 代码质量提升
- ❌ **删除了3个require()调用**
- ✅ **使用了正确的NestJS依赖注入**
- ✅ **提高了类型安全性**
- ✅ **遵循了项目架构规范**

## 📋 修复的具体文件

### backend/src/modules/editor/services/editor.service.ts
```diff
+ import { DocumentService } from '../../documents/services/document.service';
+ import { ConfigTemplateService } from '../../config/services/config-template.service';

  constructor(
    private configService: ConfigService,
    private onlyOfficeJwtService: OnlyOfficeJwtService,
+   private documentService: DocumentService,
+   private configTemplateService: ConfigTemplateService,
  ) {}

  private async getDocumentById(fileId: string): Promise<any> {
-   const documentService = require('../../../../../services/document');
-   return await documentService.getDocumentById(fileId);
+   return await this.documentService.getDocumentById(fileId);
  }

  private async getConfigFromTemplate(templateId?: string): Promise<any> {
-   const configService = require('../../../../../services/configService');
-   return await configService.getConfig(templateId);
+   if (templateId) {
+     return await this.configTemplateService.getTemplateConfig(templateId);
+   } else {
+     const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
+     if (defaultTemplate) {
+       return await this.configTemplateService.getTemplateConfig(defaultTemplate.id);
+     }
+   }
  }

  async handleCallback(callbackData: CallbackDto, fileId?: string) {
-   const documentService = require('../../../../../services/document');
-   const result = await documentService.handleCallback(callbackData);
+   const result = await this.documentService.handleCallback(callbackData);
  }
```

## 🔍 技术细节

### 依赖验证
验证了新服务确实存在所需的方法：

**DocumentService:**
- ✅ `getDocumentById(documentId: string)` - 第98行
- ✅ `handleCallback(callbackData: any)` - 第263行

**ConfigTemplateService:**
- ✅ `getTemplateConfig(templateId: string)` - 第53行
- ✅ `getDefaultTemplate()` - 第40行

### 模块导出验证
确认了模块正确导出了服务：
- ✅ `DocumentsModule` 导出 `DocumentService`
- ✅ `ConfigModule` 导出 `ConfigTemplateService`

## 💡 经验总结

### 问题产生原因
1. **架构迁移不彻底**: 从Express.js升级到NestJS时，部分代码仍然使用老的require方式
2. **代码审查不足**: 编译检查没有覆盖到动态require引用
3. **依赖关系混乱**: 新老代码混合使用

### 预防措施
1. **禁用动态require**: 在NestJS项目中应该完全避免require()调用
2. **强化编译检查**: 确保所有import路径都能在编译时解析
3. **代码审查规范**: 重点检查../../../等深层路径引用
4. **渐进式重构**: 在升级架构时应该彻底替换，不要留下老代码引用

### 最佳实践
1. ✅ **使用TypeScript导入**: 始终使用import语句而不是require
2. ✅ **依赖注入**: 利用NestJS的依赖注入系统管理服务依赖
3. ✅ **模块化设计**: 通过模块exports/imports管理服务可见性
4. ✅ **类型安全**: 利用TypeScript的类型检查避免运行时错误

---

## 🚀 后续任务

- [x] 修复编译错误
- [x] 验证功能正常
- [ ] 测试文档编辑器功能
- [ ] 确认OnlyOffice JWT认证正常工作
- [ ] 继续评估config-template.service.ts的冗余情况 