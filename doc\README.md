# OnlyOffice 集成系统

> 基于 Node.js + TypeScript + Vue 3 的现代化文档协作平台

## 🌟 项目简介

OnlyOffice 集成系统是一个企业级文档协作解决方案，提供在线文档编辑、存储管理、版本控制和企业集成等功能。系统采用前后端分离架构，支持多种文档格式，与 FileNet 企业内容管理系统深度集成。

### 🏗️ 技术架构

- **后端**: Node.js + TypeScript + Express.js
- **前端**: Vue 3 + TypeScript + Ant Design Pro  
- **数据库**: MySQL 8.0+
- **文档引擎**: OnlyOffice Document Server
- **企业集成**: IBM FileNet Content Manager

## 🚀 快速开始

### 📋 环境要求

- **Node.js**: v18.0.0 或更高版本
- **npm**: v9.0.0 或更高版本  
- **MySQL**: v8.0.0 或更高版本
- **OnlyOffice Document Server**: v7.0.0 或更高版本

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd OnlyOffice

# 2. 安装依赖
npm install                    # 根目录依赖
cd backend && npm install      # 后端依赖
cd ../frontend && npm install  # 前端依赖

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入必要的配置信息

# 4. 启动开发环境
npm run dev:backend            # 启动后端服务 (端口3000)
npm run dev:frontend           # 启动前端服务 (端口8080)
```

### 🔧 环境配置

#### 必填配置项

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=onlyoffice_db
DB_USER=root
DB_PASSWORD=your_password

# OnlyOffice 服务器
ONLYOFFICE_URL=http://localhost:80
ONLYOFFICE_SECRET=your_secret_key

# FileNet 集成 (可选)
FILENET_URL=http://your-filenet-server:9080
FILENET_USERNAME=your_username
FILENET_PASSWORD=your_password
```

#### 可选配置项

```env
# 应用配置
NODE_ENV=development
PORT=3000
FRONTEND_PORT=8080

# CORS 配置
CORS_ORIGIN=http://localhost:8080

# JWT 认证
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
```

## 📚 项目结构

```
OnlyOffice/
├── backend/                   # 后端 TypeScript 项目
│   ├── src/
│   │   ├── app.ts            # 应用入口
│   │   ├── config/           # 配置文件
│   │   ├── controllers/      # 控制器层
│   │   ├── services/         # 业务逻辑层
│   │   ├── middleware/       # 中间件
│   │   ├── types/            # TypeScript 类型定义
│   │   └── utils/            # 工具函数
│   ├── dist/                 # 编译输出
│   ├── package.json
│   └── tsconfig.json
├── frontend/                  # 前端 Vue 3 项目
│   ├── src/
│   │   ├── components/       # Vue 组件
│   │   ├── views/            # 页面视图
│   │   ├── store/            # 状态管理
│   │   ├── router/           # 路由配置
│   │   └── utils/            # 工具函数
│   └── package.json
├── config/                    # 原有配置文件
├── services/                  # 原有业务逻辑
├── routes/                    # 原有路由文件
├── .env                       # 环境变量
└── README.md                  # 项目文档
```

## 🔥 核心功能

### 📝 文档管理
- **在线编辑**: 支持 Word、Excel、PowerPoint 在线编辑
- **版本控制**: 文档版本管理和历史记录追踪
- **协同编辑**: 多人实时协作编辑
- **格式转换**: 多种文档格式互转

### 👥 用户管理
- **权限控制**: 细粒度权限管理系统
- **角色管理**: 灵活的角色分配机制
- **单点登录**: 企业 SSO 集成支持

### 🏢 企业集成
- **FileNet 集成**: 与 IBM FileNet 无缝对接
- **API 接口**: RESTful API 支持第三方集成
- **数据同步**: 企业系统数据同步机制

### 📊 系统监控
- **性能监控**: 实时系统性能指标
- **日志管理**: 完整的操作日志记录
- **健康检查**: 系统状态实时监控

## 🛠️ 开发指南

### 🏃‍♂️ 本地开发

```bash
# 启动后端开发服务器
cd backend
npm run dev              # 启动热重载开发服务器
npm run build            # 构建生产版本
npm run test             # 运行测试用例

# 启动前端开发服务器
cd frontend  
npm run dev              # 启动开发服务器
npm run build            # 构建生产版本
npm run preview          # 预览生产构建
```

### 🧪 测试

```bash
# 后端测试
cd backend
npm run test             # 运行单元测试
npm run test:coverage    # 生成测试覆盖率报告

# 前端测试
cd frontend
npm run test:unit        # 运行单元测试
npm run test:e2e         # 运行端到端测试
```

### 📋 代码规范

```bash
# 代码检查
npm run lint             # ESLint 检查
npm run lint:fix         # 自动修复代码问题

# 代码格式化
npm run format           # Prettier 格式化
```

## 🚀 部署指南

### 🔄 开发环境部署

```bash
# 1. 安装依赖
npm install

# 2. 构建项目
npm run build:backend
npm run build:frontend

# 3. 启动服务
npm run start:backend    # 启动后端 (端口3000)
npm run start:frontend   # 启动前端 (端口8080)
```

### 🏭 生产环境部署

#### 使用 PM2 部署

```bash
# 1. 安装 PM2
npm install -g pm2

# 2. 启动应用
pm2 start ecosystem.config.js

# 3. 监控服务
pm2 status
pm2 logs
pm2 restart all
```

#### 使用 Docker 部署

```bash
# 1. 构建镜像
docker build -t onlyoffice-system .

# 2. 运行容器
docker run -d \
  --name onlyoffice-app \
  -p 3000:3000 \
  -p 8080:8080 \
  --env-file .env \
  onlyoffice-system

# 3. 查看状态
docker ps
docker logs onlyoffice-app
```

### 🌐 负载均衡配置

#### Nginx 反向代理

```nginx
upstream backend {
    server 127.0.0.1:3000;
}

upstream frontend {
    server 127.0.0.1:8080;
}

server {
    listen 80;
    server_name your-domain.com;

    # 前端静态资源
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 后端 API
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 📊 系统监控

### 🔍 健康检查

```bash
# 检查后端服务状态
curl http://localhost:3000/api/health

# 检查前端服务状态  
curl http://localhost:8080

# 检查数据库连接
curl http://localhost:3000/api/health | jq '.services.database'
```

### 📈 性能指标

- **响应时间**: API 平均响应时间 < 200ms
- **并发用户**: 支持 1000+ 并发用户
- **文档大小**: 支持最大 100MB 文档
- **可用性**: 99.9% 系统可用性保证

## 🆘 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库配置
cat .env | grep DB_

# 测试数据库连接
mysql -h localhost -u root -p -e "SELECT 1"
```

#### 2. OnlyOffice 服务连接失败
```bash
# 检查 OnlyOffice 服务状态
curl http://localhost:80/healthcheck

# 检查网络连通性
ping your-onlyoffice-server
```

#### 3. 前端页面无法访问
```bash
# 检查前端构建状态
cd frontend && npm run build

# 检查端口占用
netstat -an | grep :8080
```

### 📞 技术支持

- **文档**: [在线文档](docs/)
- **问题反馈**: [Issues](issues/)
- **邮件支持**: <EMAIL>
- **QQ 群**: 123456789

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议发布。

## 🤝 贡献指南

我们欢迎各种形式的贡献，包括但不限于：

- 🐛 Bug 反馈和修复
- ✨ 新功能建议和实现  
- 📝 文档改进
- 🎨 UI/UX 优化
- 🧪 测试用例补充

请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

## 📊 项目状态

![Version](https://img.shields.io/badge/version-2.0.0-blue)
![Node](https://img.shields.io/badge/node-%3E%3D18.0.0-green)
![License](https://img.shields.io/badge/license-MIT-yellow)
![Build](https://img.shields.io/badge/build-passing-brightgreen)

---

**🎉 感谢使用 OnlyOffice 集成系统！**

如有任何问题或建议，请随时联系我们。 