# MCP MySQL 远程服务后台启动脚本

Write-Host "启动MCP MySQL远程服务（后台模式）..." -ForegroundColor Green

# 设置环境变量
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "onlyfile_user"
$env:MYSQL_PASS = "0nlyF!le`$ecure#123"
$env:MYSQL_DB = "onlyfile"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"
$env:MYSQL_ENABLE_LOGGING = "true"
$env:IS_REMOTE_MCP = "true"
$env:PORT = "3001"
$env:REMOTE_SECRET_KEY = "mcp-mysql-secret-1310849776"

Write-Host "配置信息:" -ForegroundColor Yellow
Write-Host "  MySQL主机: $env:MYSQL_HOST" -ForegroundColor Cyan
Write-Host "  MySQL端口: $env:MYSQL_PORT" -ForegroundColor Cyan
Write-Host "  服务端口: $env:PORT" -ForegroundColor Cyan
Write-Host "  远程模式: $env:IS_REMOTE_MCP" -ForegroundColor Cyan
Write-Host ""

# 启动后台进程
$job = Start-Job -ScriptBlock {
    $env:MYSQL_HOST = "*************"
    $env:MYSQL_PORT = "3306"
    $env:MYSQL_USER = "onlyfile_user"
    $env:MYSQL_PASS = "0nlyF!le`$ecure#123"
    $env:MYSQL_DB = "onlyfile"
    $env:ALLOW_INSERT_OPERATION = "true"
    $env:ALLOW_UPDATE_OPERATION = "true"
    $env:ALLOW_DELETE_OPERATION = "false"
    $env:MYSQL_ENABLE_LOGGING = "true"
    $env:IS_REMOTE_MCP = "true"
    $env:PORT = "3001"
    $env:REMOTE_SECRET_KEY = "mcp-mysql-secret-1310849776"
    
    try {
        # 使用完整路径启动
        & "npx" "@benborla29/mcp-server-mysql" 2>&1
    }
    catch {
        Write-Output "错误: $($_.Exception.Message)"
    }
}

Write-Host "后台作业已启动，作业ID: $($job.Id)" -ForegroundColor Green

# 等待5秒查看输出
Start-Sleep -Seconds 5

# 检查作业状态
$jobState = $job.State
Write-Host "作业状态: $jobState" -ForegroundColor Yellow

# 获取输出
$output = Receive-Job -Job $job
if ($output) {
    Write-Host "服务输出:" -ForegroundColor Cyan
    Write-Host $output -ForegroundColor White
}

# 检查端口是否开放
try {
    $connection = Test-NetConnection -ComputerName "localhost" -Port 3001 -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✓ 服务启动成功！端口3001已开放" -ForegroundColor Green
        
        # 保存作业ID以便后续管理
        $job.Id | Out-File -FilePath "mcp-remote-job.txt" -Encoding UTF8
        Write-Host "作业ID已保存到 mcp-remote-job.txt" -ForegroundColor Cyan
        
        Write-Host ""
        Write-Host "服务信息:" -ForegroundColor Yellow
        Write-Host "  服务地址: http://localhost:3001/mcp" -ForegroundColor White
        Write-Host "  认证密钥: mcp-mysql-secret-1310849776" -ForegroundColor White
        Write-Host ""
        Write-Host "要停止服务，请运行:" -ForegroundColor Yellow
        Write-Host "  Stop-Job -Id $($job.Id); Remove-Job -Id $($job.Id)" -ForegroundColor White
        
    } else {
        Write-Host "✗ 服务启动失败，端口3001未开放" -ForegroundColor Red
        
        # 清理失败的作业
        Stop-Job -Job $job -ErrorAction SilentlyContinue
        Remove-Job -Job $job -Force -ErrorAction SilentlyContinue
    }
}
catch {
    Write-Host "✗ 无法检查端口状态: $($_.Exception.Message)" -ForegroundColor Red
}
