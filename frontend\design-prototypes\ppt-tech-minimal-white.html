<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        :root {
            --tech-white: #ffffff;
            --tech-gray-50: #f8fafc;
            --tech-gray-100: #f1f5f9;
            --tech-gray-300: #cbd5e1;
            --tech-gray-600: #475569;
            --tech-gray-900: #0f172a;
            --tech-blue: #3b82f6;
            --tech-cyan: #06b6d4;
            --tech-shadow: rgba(15, 23, 42, 0.1);
        }

        html {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--tech-white) 0%, var(--tech-gray-50) 100%);
        }

        body {
            width: 1280px;
            height: 720px;
            margin: 0;
            padding: 0;
            position: relative;
            overflow: hidden;
            background: var(--tech-white);
            font-family: 'SF Pro Display', 'Helvetica Neue', sans-serif;
            flex-shrink: 0;
        }
        
        .slide-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: var(--tech-gray-900);
            position: relative;
            background: var(--tech-white);
            border: 1px solid var(--tech-gray-300);
        }
        
        .slide-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
        }
        
        .slide-header {
            padding: 60px 80px 40px 80px;
            position: relative;
            z-index: 1;
        }
        
        .slide-title {
            font-size: clamp(2.5rem, 4vw, 4.2rem);
            font-weight: 200;
            color: var(--tech-gray-900);
            margin: 0;
            line-height: 1.1;
            letter-spacing: -0.03em;
        }
        
        .slide-content {
            flex: 1;
            padding: 20px 80px 60px 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .content-main {
            font-size: clamp(1.2rem, 2.5vw, 1.8rem);
            line-height: 1.6;
            color: var(--tech-gray-600);
            font-weight: 300;
        }
        
        .content-points {
            list-style: none;
            padding: 0;
            margin: 40px 0 0 0;
        }
        
        .content-points li {
            margin-bottom: 30px;
            padding-left: 50px;
            position: relative;
            font-size: 1.5rem;
            color: var(--tech-gray-900);
            font-weight: 300;
        }
        
        .content-points li:before {
            content: "";
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--tech-blue);
            border-radius: 50%;
        }
        
        .content-points li:after {
            content: "";
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            border: 1px solid var(--tech-blue);
            border-radius: 50%;
            opacity: 0.2;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .tech-card {
            background: var(--tech-white);
            border: 1px solid var(--tech-gray-300);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .tech-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--tech-blue), var(--tech-cyan));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .tech-card:hover::before {
            transform: scaleX(1);
        }
        
        .tech-card:hover {
            border-color: var(--tech-blue);
            box-shadow: 0 10px 40px var(--tech-shadow);
            transform: translateY(-3px);
        }
        
        .tech-number {
            font-size: 3rem;
            font-weight: 100;
            color: var(--tech-blue);
            display: block;
            margin-bottom: 10px;
        }
        
        .tech-label {
            font-size: 1rem;
            color: var(--tech-gray-600);
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }
        
        .slide-footer {
            position: absolute;
            bottom: 40px;
            right: 80px;
            font-size: 16px;
            color: var(--tech-gray-600);
            font-weight: 300;
            z-index: 1;
        }

        .tech-accent {
            position: absolute;
            pointer-events: none;
        }
        
        .tech-accent.corner-tl {
            top: 0;
            left: 0;
            width: 120px;
            height: 120px;
            border-top: 2px solid var(--tech-blue);
            border-left: 2px solid var(--tech-blue);
            opacity: 0.3;
        }
        
        .tech-accent.corner-br {
            bottom: 0;
            right: 0;
            width: 120px;
            height: 120px;
            border-bottom: 2px solid var(--tech-cyan);
            border-right: 2px solid var(--tech-cyan);
            opacity: 0.3;
        }
        
        @media (max-width: 1280px) {
            body {
                width: 100vw;
                height: 56.25vw;
                max-height: 100vh;
            }
            .slide-container {
                border: none;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="tech-accent corner-tl"></div>
        <div class="tech-accent corner-br"></div>
        
        <header class="slide-header">
            <h1 class="slide-title">{{ main_heading }}</h1>
        </header>
        
        <main class="slide-content">
            <div class="content-main">
                {{ page_content }}

                <!--
                <ul class="content-points">
                    <li>先进的人工智能技术架构</li>
                    <li>云原生微服务解决方案</li>
                    <li>实时数据处理与分析</li>
                    <li>企业级安全保障体系</li>
                </ul>
                -->

                <!--
                <div class="tech-grid">
                    <div class="tech-card">
                        <span class="tech-number">99.9%</span>
                        <span class="tech-label">系统可用性</span>
                    </div>
                    <div class="tech-card">
                        <span class="tech-number">1.2ms</span>
                        <span class="tech-label">响应时间</span>
                    </div>
                    <div class="tech-card">
                        <span class="tech-number">50K+</span>
                        <span class="tech-label">并发处理</span>
                    </div>
                </div>
                -->
            </div>
        </main>
        
        <footer class="slide-footer">
            {{ current_page_number }} / {{ total_page_count }}
        </footer>
    </div>
</body>
</html> 