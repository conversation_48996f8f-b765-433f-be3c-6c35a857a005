/**
 * OnlyOffice配置管理服务 - 重构版本
 * @description 集成配置模板系统，支持动态配置管理
 * @version 2.0.0
 */
const fs = require('fs-extra');
const path = require('path');

// 获取数据库连接
const db = require('./database');

// 配置文件路径（兼容性保留）
const CONFIG_FILE = path.join(__dirname, '../config/onlyoffice-settings.json');

// 默认配置模板（向后兼容）
const DEFAULT_CONFIG = {
    // 文档权限配置
    permissions: {
        chat: false,
        comment: true,
        copy: true,
        deleteCommentAuthorOnly: false,
        download: true,
        edit: true,
        editCommentAuthorOnly: false,
        fillForms: true,
        modifyContentControl: true,
        modifyFilter: true,
        print: true,
        protect: true,
        review: true
    },
    // 编辑器自定义配置
    customization: {
        about: true,
        anonymous: {
            request: true,
            label: "Guest"
        },
        autosave: true,
        close: {
            visible: true,
            text: "Close file"
        },
        comments: true,
        compactHeader: false,
        compactToolbar: false,
        compatibleFeatures: false,
        customer: {
            address: "My City, 123a-45",
            info: "Some additional information",
            logo: "",
            logoDark: "",
            mail: "<EMAIL>",
            name: "OnlyOffice Integration",
            phone: "123456789",
            www: "example.com"
        },
        features: {
            featuresTips: true,
            roles: true,
            spellcheck: {
                mode: true,
                change: true
            },
            tabBackground: {
                mode: "header",
                change: true
            },
            tabStyle: {
                mode: "fill",
                change: true
            }
        },
        feedback: {
            url: "",
            visible: false
        },
        font: {
            name: "Arial",
            size: "11px"
        },
        forcesave: true,
        goback: {
            blank: true,
            text: "Open file location",
            url: ""
        },
        help: true,
        hideNotes: false,
        hideRightMenu: false,
        hideRulers: false,
        integrationMode: "embed",
        layout: {
            header: {
                editMode: true,
                save: true,
                users: true
            },
            leftMenu: {
                mode: true,
                navigation: true,
                spellcheck: true
            },
            rightMenu: {
                mode: true
            },
            statusBar: {
                actionStatus: true,
                docLang: true,
                textLang: true
            },
            toolbar: {
                collaboration: {
                    mailmerge: true
                },
                draw: true,
                file: {
                    close: true,
                    info: true,
                    save: true,
                    settings: true
                },
                home: {},
                layout: true,
                plugins: true,
                protect: true,
                references: true,
                save: true,
                view: {
                    navigation: true
                }
            }
        },
        loaderLogo: "",
        loaderName: "文档正在加载，请稍候...",
        logo: {
            image: "",
            imageDark: "",
            imageLight: "",
            url: "",
            visible: false
        },
        macros: true,
        macrosMode: "warn",
        mentionShare: true,
        mobile: {
            forceView: false,
            info: true,
            standardView: true
        },
        plugins: false,
        pointerMode: "select",
        review: {
            hideReviewDisplay: false,
            showReviewChanges: false,
            reviewDisplay: "original",
            trackChanges: true,
            hoverMode: false
        },
        showHorizontalScroll: true,
        showVerticalScroll: true,
        slidePlayerBackground: "#000000",
        submitForm: {
            visible: true,
            resultMessage: "text"
        },
        toolbarHideFileName: false,
        uiTheme: "theme-light",
        unit: "cm",
        wordHeadingsColor: "#00ff00",
        zoom: 100
    },
    // 协作编辑配置
    coEditing: {
        mode: "fast",
        change: true
    },
    // 语言和地区配置
    lang: "zh",
    region: "zh-CN",
    // 用户配置
    user: {
        id: "user-1",
        name: "OnlyOffice用户",
        group: "editors"
    }
};

/**
 * 从配置模板系统获取配置
 * @param {string} templateId 模板ID，默认使用默认模板
 * @returns {Promise<Object>} 配置对象
 */
async function getConfigFromTemplate(templateId = null) {
    try {
        // 获取模板ID
        let targetTemplateId = templateId;
        if (!targetTemplateId) {
            // 获取默认模板
            const defaultTemplate = await db.queryOne(`
                SELECT id FROM config_templates 
                WHERE is_default = TRUE AND is_active = TRUE 
                LIMIT 1
            `);
            
            if (defaultTemplate) {
                targetTemplateId = defaultTemplate.id;
            } else {
                console.warn('未找到默认配置模板，使用内置默认配置');
                return DEFAULT_CONFIG;
            }
        }

        // 获取模板配置项
        const configItems = await db.query(`
            SELECT config_group, config_key, config_value, value_type
            FROM config_template_items
            WHERE template_id = ? AND is_enabled = TRUE
            ORDER BY config_group, config_key
        `, [targetTemplateId]);

        if (configItems.length === 0) {
            console.warn(`模板 ${targetTemplateId} 没有配置项，使用内置默认配置`);
            return DEFAULT_CONFIG;
        }

        // 构建配置对象
        const config = {};
        
        configItems.forEach(item => {
            const { config_group, config_key, config_value, value_type } = item;
            
            // 确保配置组存在
            if (!config[config_group]) {
                config[config_group] = {};
            }
            
            // 类型转换
            let value = config_value;
            try {
                switch (value_type) {
                    case 'boolean':
                        value = config_value === 'true' || config_value === '1';
                        break;
                    case 'number':
                        value = Number(config_value);
                        break;
                    case 'object':
                    case 'array':
                        value = JSON.parse(config_value);
                        break;
                    case 'string':
                    default:
                        value = config_value;
                        break;
                }
            } catch (parseError) {
                console.warn(`配置项解析失败: ${config_group}.${config_key}`, parseError);
                value = config_value; // 保持原始字符串值
            }
            
            // 处理嵌套配置
            if (config_key.includes('.')) {
                setNestedProperty(config[config_group], config_key, value);
            } else {
                config[config_group][config_key] = value;
            }
        });

        console.log(`已加载配置模板: ${targetTemplateId}, 包含 ${configItems.length} 个配置项`);
        return config;
        
    } catch (error) {
        console.error('从配置模板获取配置失败:', error);
        console.log('回退到内置默认配置');
        return DEFAULT_CONFIG;
    }
}

/**
 * 设置嵌套属性
 * @param {Object} obj 目标对象
 * @param {string} path 属性路径，如 'customer.name'
 * @param {any} value 值
 */
function setNestedProperty(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!current[key] || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
}

/**
 * 获取OnlyOffice配置 - 主要入口函数
 * @param {string} templateId 可选的模板ID
 * @returns {Promise<Object>} 配置对象
 */
async function getConfig(templateId = null) {
    try {
        // 优先从配置模板系统获取
        const templateConfig = await getConfigFromTemplate(templateId);
        
        // 如果模板配置成功获取，使用模板配置
        if (templateConfig && Object.keys(templateConfig).length > 0) {
            return mergeConfig(DEFAULT_CONFIG, templateConfig);
        }
        
        // 回退到文件配置（向后兼容）
        if (await fs.pathExists(CONFIG_FILE)) {
            console.log('从文件配置回退加载');
            const fileConfig = await fs.readJson(CONFIG_FILE);
            return mergeConfig(DEFAULT_CONFIG, fileConfig);
        }
        
        // 最终回退到默认配置
        console.log('使用内置默认配置');
        return DEFAULT_CONFIG;
        
    } catch (error) {
        console.error('获取OnlyOffice配置失败:', error);
        return DEFAULT_CONFIG;
    }
}

/**
 * 保存OnlyOffice配置
 */
async function saveConfig(config) {
    try {
        // 确保配置目录存在
        await fs.ensureDir(path.dirname(CONFIG_FILE));

        // 验证并清理配置数据
        const cleanConfig = cleanConfigData(config);

        // 保存配置
        await fs.writeJson(CONFIG_FILE, cleanConfig, { spaces: 2 });

        return { success: true, message: '配置保存成功' };
    } catch (error) {
        console.error('保存OnlyOffice配置失败:', error);
        return { success: false, message: '配置保存失败: ' + error.message };
    }
}

/**
 * 重置为默认配置
 */
async function resetConfig() {
    try {
        await fs.writeJson(CONFIG_FILE, DEFAULT_CONFIG, { spaces: 2 });
        return { success: true, message: '配置已重置为默认值' };
    } catch (error) {
        console.error('重置配置失败:', error);
        return { success: false, message: '重置配置失败: ' + error.message };
    }
}

/**
 * 深度合并配置对象
 */
function mergeConfig(defaultConfig, userConfig) {
    const result = JSON.parse(JSON.stringify(defaultConfig));

    function merge(target, source) {
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    if (!target[key] || typeof target[key] !== 'object') {
                        target[key] = {};
                    }
                    merge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        }
    }

    merge(result, userConfig);
    return result;
}

/**
 * 清理配置数据，确保类型正确
 */
function cleanConfigData(config) {
    const cleaned = JSON.parse(JSON.stringify(config));

    // 确保布尔值不是字符串
    function cleanBooleans(obj) {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (typeof obj[key] === 'string') {
                    if (obj[key] === 'true') {
                        obj[key] = true;
                    } else if (obj[key] === 'false') {
                        obj[key] = false;
                    }
                } else if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                    cleanBooleans(obj[key]);
                }
            }
        }
    }

    cleanBooleans(cleaned);

    // 确保数字类型正确
    if (cleaned.customization && cleaned.customization.zoom) {
        cleaned.customization.zoom = parseInt(cleaned.customization.zoom) || 100;
    }

    return cleaned;
}

/**
 * 获取配置选项的描述信息
 */
function getConfigDescriptions() {
    return {
        permissions: {
            chat: "允许聊天功能",
            comment: "允许添加评论",
            copy: "允许复制内容",
            deleteCommentAuthorOnly: "仅作者可删除评论",
            download: "允许下载文档",
            edit: "允许编辑文档",
            editCommentAuthorOnly: "仅作者可编辑评论",
            fillForms: "允许填写表单",
            modifyContentControl: "允许修改内容控件",
            modifyFilter: "允许修改过滤器",
            print: "允许打印文档",
            protect: "允许保护文档",
            review: "允许审阅模式"
        },
        customization: {
            about: "显示关于按钮",
            autosave: "启用自动保存",
            comments: "显示评论功能",
            compactHeader: "使用紧凑头部",
            compactToolbar: "使用紧凑工具栏",
            compatibleFeatures: "启用兼容性功能",
            forcesave: "启用强制保存",
            help: "显示帮助按钮",
            hideNotes: "隐藏批注",
            hideRightMenu: "隐藏右侧菜单",
            hideRulers: "隐藏标尺",
            macros: "启用宏功能",
            mentionShare: "启用提及分享",
            plugins: "启用插件",
            showHorizontalScroll: "显示水平滚动条",
            showVerticalScroll: "显示垂直滚动条",
            toolbarHideFileName: "工具栏隐藏文件名"
        }
    };
}

/**
 * 获取当前配置（别名方法，方便API使用）
 */
async function getCurrentConfig() {
    return await getConfig();
}

// 导出方法
module.exports = {
    getConfig,
    saveConfig,
    resetConfig,
    getCurrentConfig,
    getConfigDescriptions,
    DEFAULT_CONFIG
}; 