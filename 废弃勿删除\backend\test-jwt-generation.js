/**
 * JWT生成测试脚本
 * 测试OnlyOffice JWT服务是否正确生成JWT token
 */

async function testJwtGeneration() {
  console.log('=== OnlyOffice JWT生成测试 ===');
  
  try {
    // 直接测试JWT配置从数据库读取
    const mysql = require('mysql2/promise');
    const jwt = require('jsonwebtoken');
    
    // 数据库连接配置
    const dbConfig = {
      host: process.env.DB_HOST || '*************',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'onlyfile_user', 
      password: process.env.DB_PASSWORD || '0nlyF!le$ecure#123',
      database: process.env.DB_NAME || 'onlyfile',
    };
    
    console.log('📋 连接数据库...', dbConfig.host + ':' + dbConfig.port);
    const connection = await mysql.createConnection(dbConfig);
    
    // 从数据库获取JWT配置
    const [jwtConfigRows] = await connection.execute(`
      SELECT setting_key, setting_value 
      FROM system_settings 
      WHERE setting_key LIKE 'jwt.onlyoffice%'
    `);
    
    const jwtConfig = {};
    jwtConfigRows.forEach(row => {
      const key = row.setting_key.replace('jwt.onlyoffice.', '');
      jwtConfig[key] = row.setting_value;
    });
    
    console.log('🔧 数据库JWT配置:');
    console.log('- 密钥:', jwtConfig.secret ? (jwtConfig.secret.substring(0, 10) + '...') : '未设置');
    console.log('- 算法:', jwtConfig.algorithm);
    console.log('- 头部:', jwtConfig.header);
    console.log('- 包含在body中:', jwtConfig.in_body);
    console.log('');
    
    await connection.end();
    
    // 创建测试配置
    const testConfig = {
      document: {
        fileType: 'docx',
        key: 'test-doc-123-1640123456789',
        title: '测试文档.docx',
        url: 'http://*************:3000/api/documents/test-doc-123',
        permissions: {
          edit: true,
          download: true,
          review: true,
          comment: true,
          fillForms: true,
          modifyFilter: true,
          modifyContentControl: true,
        },
        dbId: 'test-doc-123',
      },
      documentType: 'word',
      editorConfig: {
        callbackUrl: 'http://*************:3000/api/editor/callback/test-doc-123',
        lang: 'zh',
        mode: 'edit',
        customization: {
          chat: true,
          comments: true,
          help: true,
          about: true,
          feedback: false,
          forcesave: true,
          review: true,
          toolbarNoTabs: false,
          toolbarHideFileName: false,
        },
        user: {
          id: 'user-1',
          name: '测试用户',
        },
        coEditing: {
          mode: 'fast',
          change: true,
        },
      },
      apiUrl: 'http://*************/web-apps/apps/api/documents/api.js',
    };
    
    console.log('📋 测试配置:');
    console.log('- 文档类型:', testConfig.documentType);
    console.log('- 文档标题:', testConfig.document.title);
    console.log('- 文档URL:', testConfig.document.url);
    console.log('- 回调URL:', testConfig.editorConfig.callbackUrl);
    console.log('');
    
         // 生成JWT token
     console.log('🔐 生成JWT token...');
     
     // 手动构建JWT载荷
     const serverHost = process.env.SERVER_HOST || '*************';
     const serverPort = process.env.PORT || '3000';
     const documentServerUrl = process.env.ONLYOFFICE_DOCUMENT_SERVER_URL || 'http://*************/';
     
     const jwtPayload = {
       ...testConfig,
       iss: `http://${serverHost}:${serverPort}`, // 签发者
       aud: documentServerUrl, // 接收者
       exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
       nbf: Math.floor(Date.now() / 1000) - 60, // 提前60秒生效
       iat: Math.floor(Date.now() / 1000), // 签发时间
     };
     
     // 生成JWT token
     const token = jwt.sign(jwtPayload, jwtConfig.secret, {
       algorithm: jwtConfig.algorithm,
       header: {
         alg: jwtConfig.algorithm,
         typ: 'JWT'
       }
     });
     
     console.log('✅ JWT token生成成功!');
     console.log('Token长度:', token.length);
     console.log('Token开头:', token.substring(0, 50) + '...');
     console.log('');
     
     // 验证JWT token
     console.log('🔍 验证JWT token...');
     try {
       const decoded = jwt.verify(token, jwtConfig.secret, {
         algorithms: [jwtConfig.algorithm]
       });
       
       console.log('✅ JWT token验证成功!');
       console.log('载荷信息:');
       console.log('- 签发者:', decoded.iss);
       console.log('- 接收者:', decoded.aud);
       console.log('- 签发时间:', new Date(decoded.iat * 1000).toLocaleString());
       console.log('- 过期时间:', new Date(decoded.exp * 1000).toLocaleString());
       console.log('- 文档标题:', decoded.document?.title);
     } catch (verifyError) {
       console.log('❌ JWT token验证失败:', verifyError.message);
     }
    console.log('');
    console.log('🎉 测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

// 运行测试
testJwtGeneration(); 