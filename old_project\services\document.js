/**
 * 文档服务
 * 提供文档管理的核心功能
 */
const fs = require('fs-extra');
const path = require('path');
const config = require('../config');
const jwtService = require('./jwt');
const fileStorage = require('./fileStorage');
const { v4: uuidv4 } = require('uuid');
const fileNetService = require('./filenetService');

/**
 * 获取文档列表
 * @param {Object} options 查询选项
 * @returns {Promise<Array>} 文档列表
 */
async function getDocumentList(options = {}) {
    try {
        // 从FileNet文档列表中获取数据
        const result = await fileNetService.getFileNetDocumentsFromDB(options);

        return result.documents.map(file => ({
            id: file.id,
            name: file.original_name,
            type: file.extension,
            size: file.file_size,
            lastModified: file.updated_at,
            url: `/api/documents/${file.id}`,
            editUrl: `/editor/${file.id}`,
            version: file.version,
            createdAt: file.created_at,
            createdBy: file.created_by,
            modifiedBy: file.last_modified_by,
            fileHash: file.file_hash,
            fnDocId: file.fn_doc_id,
            templateId: file.template_id
        }));
    } catch (error) {
        console.error('获取文档列表失败:', error);
        throw error;
    }
}

/**
 * 删除文档
 * @param {string} fileId 文件ID
 * @returns {Promise<boolean>} 是否成功删除
 */
async function deleteDocument(fileId) {
    try {
        console.log('请求删除文件ID:', fileId);

        // 查询文件信息
        const db = require('./database');
        const fileInfo = await db.queryOne(
            'SELECT id, fn_doc_id FROM filenet_documents WHERE id = ? AND is_deleted = FALSE',
            [fileId]
        );

        if (!fileInfo) {
            console.error('找不到要删除的文件:', fileId);
            return false;
        }

        // 软删除文件记录
        await db.query(
            'UPDATE filenet_documents SET is_deleted = TRUE WHERE id = ?',
            [fileId]
        );

        console.log(`文件已标记为删除: ID=${fileId}, FileNet DocID=${fileInfo.fn_doc_id}`);
        return true;
    } catch (error) {
        console.error('删除文件失败:', error);
        throw error;
    }
}

/**
 * 获取文档配置
 * @param {string} fileId 文件ID
 * @returns {Promise<Object>} 文档配置
 */
async function getDocumentConfig(fileId) {
    const db = require('./database');
    const configService = require('./configService'); // 添加配置服务

    const fileInfo = await db.queryOne(`
        SELECT id, fn_doc_id, original_name, file_size, mime_type, extension, version 
        FROM filenet_documents 
        WHERE id = ? AND is_deleted = FALSE
    `, [fileId]);

    if (!fileInfo) {
        throw new Error('找不到对应的文件');
    }

    // 获取Web配置管理中的设置
    const webConfig = await configService.getConfig();

    // 创建可供OnlyOffice访问的绝对URL
    const fileUrl = `http://${config.server.host}:${config.server.port}/api/documents/${fileId}`;
    console.log('OnlyOffice将使用的URL:', fileUrl);

    const fileExt = fileInfo.extension.toLowerCase();
    const fileKey = `${fileId}-${Date.now()}`;

    // 确定文档类型 - 更精确地匹配文件类型
    let documentType = 'word';
    if (['xlsx', 'xls'].includes(fileExt)) {
        documentType = 'cell';
    } else if (['pptx', 'ppt'].includes(fileExt)) {
        documentType = 'slide';
    } else if (fileExt === 'pdf') {
        documentType = 'pdf';
    }

    console.log('文档类型:', documentType, '文件扩展名:', fileExt);

    // 创建文档配置 - 使用Web配置管理的设置
    const docConfig = {
        document: {
            fileType: fileExt,
            key: fileKey,
            title: fileInfo.original_name,
            url: fileUrl,
            permissions: webConfig.permissions // 使用Web配置的权限
        },
        documentType: documentType,
        editorConfig: {
            callbackUrl: config.editor.callbackUrl,
            lang: webConfig.lang || config.editor.lang,
            mode: fileExt === 'pdf' ? 'view' : 'edit', // PDF只能查看
            customization: webConfig.customization || config.editor.customization,
            user: webConfig.user || config.editor.user, // 使用Web配置的用户信息
            coEditing: webConfig.coEditing || { mode: "fast", change: true }
        }
    };

    console.log('使用Web配置生成OnlyOffice配置');

    return docConfig;
}

/**
 * 处理保存回调
 * @param {Object} body 回调数据
 * @returns {Promise<boolean>} 处理结果
 * //*-不到带有标识符的文档
 * //*1 正在编辑文档文件
 * //*2 已准备好保存
 * //*3发生了文档保存错误文件关闭，没有变化
 * //*4 用户关闭文档，没有变化
 * //*6正在编辑文档，但保存当前文档状态
 * //*7-强制保存文档时发生错误
 */
async function handleCallback(body) {
    let tempFilePath = null;
    try {
        console.log(`收到OnlyOffice回调请求: status=${body.status}, key=${body.key}, forcesavetype=${body.forcesavetype}`);

        // 处理强制保存的情况
        if (body.forcesavetype) {
            console.log(`检测到强制保存请求 (forcesavetype=${body.forcesavetype})`);
        }

        if ((body.status === 2 || body.status === 6) && body.url) {
            if (body.status === 6) {
                console.warn(`警告: 收到 status 6 (编辑服务内部保存错误)，但仍将尝试处理 URL: ${body.url}`);
            } else {
                console.log(`文档状态 ${body.status}，URL: ${body.url}`);
            }

            const urlObj = new URL(body.url);
            let callbackFileName = body.filename || urlObj.searchParams.get('filename') || path.basename(urlObj.pathname);
            if (callbackFileName) {
                callbackFileName = decodeURIComponent(callbackFileName);
            }
            console.log('回调中的文件名:', callbackFileName);

            let fileId = null;
            const shardKey = urlObj.searchParams.get('shardkey');
            if (shardKey) {
                const parts = shardKey.split('-');
                if (parts.length >= 5) {
                    fileId = parts.slice(0, 5).join('-');
                }
            }

            if (!fileId && body.key) {
                const keyParts = body.key.split('-');
                if (keyParts.length > 0) {
                    fileId = keyParts[0];
                    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(fileId) && keyParts.length > 4) {
                        const potentialUuid = keyParts.slice(0, 5).join('-');
                        if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(potentialUuid)) {
                            fileId = potentialUuid;
                        }
                    }
                    console.log(`从 body.key 中提取到 File ID 作为备用: ${fileId}`);
                }
            }

            // 兼容大括号格式的 fileId（如 {UUID}），只允许数据库主键 UUID
            if (fileId && typeof fileId === 'string' && fileId.startsWith('{') && fileId.endsWith('}')) {
                fileId = fileId.slice(1, -1);
                console.log('去除大括号后的 fileId:', fileId);
            }

            if (!fileId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(fileId)) {
                console.error('在 status 2 或 6 回调中，无法从URL的shardkey或body.key中提取有效的UUID格式文件ID。提取到的值:', fileId);
                return false;
            }
            console.log('最终提取的被编辑文档的 filenet_documents.id:', fileId);

            const db = require('./database');
            const originalDocInfo = await db.queryOne(`
                SELECT id, fn_doc_id, original_name, file_size, version, extension 
                FROM filenet_documents 
                WHERE id = ? AND is_deleted = FALSE
            `, [fileId]);

            if (!originalDocInfo) {
                console.error('无法找到被编辑文档的记录 (filenet_documents.id):', fileId);
                return false;
            }
            console.log('找到被编辑文档的原始信息:', originalDocInfo.original_name, '当前版本:', originalDocInfo.version);

            let actualNewNameFromCallback = body.filename; // 仅考虑 body.filename 作为潜在的新名称
            if (actualNewNameFromCallback) {
                try {
                    actualNewNameFromCallback = decodeURIComponent(actualNewNameFromCallback);
                    console.log('从回调 body.filename 中明确提供的文件名:', actualNewNameFromCallback);
                } catch (e) {
                    console.warn('解码 body.filename 失败，将忽略该名称:', body.filename, e);
                    actualNewNameFromCallback = null; // 如果解码失败，则视为没有新名称
                }
            }

            // effectiveOriginalName 应该是 body.filename (如果通过 "Save As" 提供)，否则是数据库中的现有名称
            const effectiveOriginalName = actualNewNameFromCallback || originalDocInfo.original_name;
            console.log('本次保存操作将使用的有效原始文件名:', effectiveOriginalName);

            tempFilePath = path.join(config.storage.tmpDir, `${Date.now()}-${uuidv4()}.${originalDocInfo.extension || 'tmp'}`);
            console.log('临时文件路径:', tempFilePath);

            let retryCount = 0;
            const maxRetries = 3;
            let success = false;

            while (retryCount <= maxRetries && !success) {
                try {
                    if (retryCount > 0) {
                        console.log(`尝试第 ${retryCount} 次下载文件...`);
                    }
                    const axios = require('axios');
                    const response = await axios({
                        method: 'get',
                        url: body.url,
                        responseType: 'arraybuffer',
                        timeout: 60000,
                        headers: { 'Cache-Control': 'no-cache', 'Pragma': 'no-cache' }
                    });
                    await fs.ensureDir(path.dirname(tempFilePath));
                    await fs.writeFile(tempFilePath, response.data);
                    const newFileStats = await fs.stat(tempFilePath);

                    if (newFileStats.size === 0) {
                        console.warn(`下载的文件 (源 status ${body.status}) 大小为0。URL: ${body.url}`);
                        if (body.status === 2) throw new Error('下载的文件大小为0 (status 2)');
                    }

                    const userId = body.users && body.users.length > 0 ? body.users[0] : (config.editor.user ? config.editor.user.id : 'system');
                    console.log('文件将以上传者处理:', userId);

                    const fileNetContentInfo = await fileNetService.uploadContentToFileNetAndGetIds(
                        { path: tempFilePath, size: newFileStats.size },
                        effectiveOriginalName,
                        userId
                    );
                    console.log('FileNet 内容处理结果:', fileNetContentInfo);

                    const newVersionNumber = (originalDocInfo.version || 0) + 1;
                    await db.query(`
                        UPDATE filenet_documents
                        SET file_size = ?, 
                            version = ?,
                            file_hash = ?,
                            fn_doc_id = ?,
                            original_name = ?, 
                            extension = ?, 
                            mime_type = ?, 
                            last_modified_by = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    `, [
                        fileNetContentInfo.file_size,
                        newVersionNumber,
                        fileNetContentInfo.file_hash,
                        fileNetContentInfo.fn_doc_id,
                        originalDocInfo.original_name,
                        fileNetContentInfo.extension,
                        fileNetContentInfo.mime_type,
                        userId,
                        fileId
                    ]);
                    console.log(`主文档记录 (ID: ${fileId}) 已更新，版本: ${newVersionNumber}, FileNet ID: ${fileNetContentInfo.fn_doc_id}`);

                    const versionsTableExists = await db.queryOne("SHOW TABLES LIKE 'filenet_document_versions'");
                    if (versionsTableExists) {
                        const versionId = uuidv4();
                        // 根据 forcesavetype 生成不同的版本注释
                        let versionComment = 'OnlyOffice编辑更新';
                        if (body.forcesavetype) {
                            switch (body.forcesavetype) {
                                case 0:
                                    versionComment = '用户手动强制保存';
                                    break;
                                case 1:
                                    versionComment = '自动强制保存';
                                    break;
                                case 2:
                                    versionComment = '关闭文档时强制保存';
                                    break;
                                default:
                                    versionComment = `强制保存 (类型: ${body.forcesavetype})`;
                            }
                        } else {
                            versionComment = `自动保存 (status: ${body.status})`;
                        }

                        const versionFields = ['id', 'doc_id', 'fn_doc_id', 'version', 'file_hash', 'modified_by', 'file_size', 'comment', 'extension', 'mime_type', 'original_name'];
                        const versionValues = [
                            versionId,
                            fileId,
                            fileNetContentInfo.fn_doc_id,
                            newVersionNumber,
                            fileNetContentInfo.file_hash,
                            userId,
                            fileNetContentInfo.file_size,
                            versionComment,
                            fileNetContentInfo.extension,
                            fileNetContentInfo.mime_type,
                            originalDocInfo.original_name
                        ];

                        const versionColumnsInfo = await db.query("SHOW COLUMNS FROM filenet_document_versions");
                        const existingVersionColumns = versionColumnsInfo.map(col => col.Field);

                        const filteredVersionFields = [];
                        const filteredVersionValues = [];
                        versionFields.forEach((field, index) => {
                            if (existingVersionColumns.includes(field)) {
                                filteredVersionFields.push(field);
                                filteredVersionValues.push(versionValues[index]);
                            }
                        });

                        if (filteredVersionFields.length > 0) {
                            const insertVersionSQL = `INSERT INTO filenet_document_versions (${filteredVersionFields.join(', ')}) VALUES (${filteredVersionFields.map(() => '?').join(', ')})`;
                            await db.query(insertVersionSQL, filteredVersionValues);
                            console.log(`新版本记录 (ID: ${versionId}) 已保存到 filenet_document_versions`);
                        } else {
                            console.warn('filenet_document_versions 表没有匹配的列可插入，跳过版本记录。');
                        }
                    } else {
                        console.log('filenet_document_versions 表不存在，跳过版本记录创建。');
                    }

                    console.log(`文件已成功通过FileNet处理并更新数据库 (源 status ${body.status}):`, effectiveOriginalName);
                    success = true;
                } catch (downloadError) {
                    retryCount++;
                    console.error(`文件下载或更新失败 (源 status ${body.status}, 尝试 ${retryCount}/${maxRetries}):`, downloadError.message);
                    if (retryCount > maxRetries) {
                        if (body.status === 6) {
                            console.error(`即使 status 为 6，多次尝试下载/更新文件失败。原始错误:`, downloadError.message);
                            return false;
                        }
                        throw downloadError;
                    }
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            return success;

        } else if (body.status === 1 || body.status === 0) {
            console.log(`文档状态: ${body.status} (正在编辑或准备编辑)，无需服务器端文件操作`);
            return true;
        } else if (body.status === 4) {
            console.log('文档状态: 4 (用户关闭，无更改保存到存储服务)，无需服务器端文件操作');
            return true;
        } else if (body.status === 7) {
            console.error(`文档状态: 7 (强制保存再次失败). Body:`, JSON.stringify(body));
            return false;
        }

        console.log(`收到未直接处理的回调状态: ${body.status}. Body:`, JSON.stringify(body));
        return true;
    } catch (error) {
        console.error('处理回调主逻辑失败:', error);
        return false;
    } finally {
        if (tempFilePath) {
            try {
                if (await fs.pathExists(tempFilePath)) {
                    await fs.unlink(tempFilePath);
                    console.log('已清理临时文件:', tempFilePath);
                }
            } catch (cleanupError) {
                console.error('清理临时文件失败:', tempFilePath, cleanupError);
            }
        }
    }
}

/**
 * 上传新文档
 * @param {Object} fileData 文件数据
 * @param {string} userId 用户ID（可选）
 * @returns {Promise<Object>} 上传结果
 */
async function uploadDocument(fileData, userId = 'system') {
    try {
        // 调用FileNet服务上传文件
        const result = await fileNetService.uploadToFileNet(fileData, fileData.originalname, userId);

        return {
            success: true,
            fileId: result.dbId,
            fileName: result.originalName,
            fileSize: result.fileSize,
            extension: result.extension,
            mimeType: result.mimeType,
            fileHash: result.fileHash,
            fnDocId: result.docId
        };
    } catch (error) {
        console.error('上传文档失败:', error);
        throw error;
    }
}

/**
 * 获取FileNet文档的编辑配置
 * @param {string} fnDocId FileNet文档ID
 * @returns {Promise<Object>} 文档配置
 */
async function getFileNetDocumentConfig(fnDocId) {
    const db = require('./database');
    const configService = require('./configService'); // 添加配置服务

    const fileInfo = await db.queryOne(
        `SELECT id, fn_doc_id, original_name, file_size, mime_type, extension, version 
         FROM filenet_documents 
         WHERE fn_doc_id = ? AND is_deleted = FALSE`,
        [fnDocId]
    );

    if (!fileInfo) {
        throw new Error(`找不到对应的FileNet文档：${fnDocId}`);
    }

    console.log('[getFileNetDocumentConfig] 获取到FileNet文档信息:', fileInfo);

    // 获取Web配置管理中的设置
    const webConfig = await configService.getConfig();

    // --- 调试日志 ---
    console.log('>>> DEBUG STEP 1: 即将生成 FileNet 特定的 document.url <<<');
    const fileUrl = `http://${config.server.host}:${config.server.port}/api/documents/filenet/${encodeURIComponent(fileInfo.fn_doc_id)}`;
    console.log('>>> DEBUG STEP 2: 已生成 FileNet 特定的 document.url:', fileUrl);
    // --- 调试日志结束 ---

    console.log('[getFileNetDocumentConfig] OnlyOffice将使用的正确URL:', fileUrl);

    const fileExt = fileInfo.extension.toLowerCase();
    // 使用内部数据库ID (fileInfo.id) 和时间戳生成 key，确保编辑器会话的唯一性，并与回调逻辑中提取 fileId 的方式保持一致
    const fileKey = `${fileInfo.id}-${Date.now()}`;

    let documentType = 'word';
    if (['xlsx', 'xls'].includes(fileExt)) {
        documentType = 'cell';
    } else if (['pptx', 'ppt'].includes(fileExt)) {
        documentType = 'slide';
    } else if (fileExt === 'pdf') {
        documentType = 'pdf';
    }

    console.log('[getFileNetDocumentConfig] 文档类型:', documentType, '文件扩展名:', fileExt);

    let fileName = fileInfo.original_name;
    try {
        if (/%[0-9A-F]{2}/i.test(fileName)) {
            const decodedName = decodeURIComponent(fileName);
            if (decodedName !== fileName) {
                console.log('[getFileNetDocumentConfig] original_name URL解码后:', decodedName);
                fileName = decodedName;
            }
        }
    } catch (e) {
        console.warn('[getFileNetDocumentConfig] original_name 解码失败 (已忽略):', e.message);
    }

    // 使用Web配置管理的设置
    const docConfig = {
        document: {
            fileType: fileExt,
            key: fileKey, // 使用包含内部数据库ID的 key
            title: fileName,
            url: fileUrl, // 确保这里使用的是上面生成的正确的 fileUrl
            dbId: fileInfo.id, // <--- 添加内部数据库主键 ID
            permissions: webConfig.permissions // 使用Web配置的权限
        },
        documentType: documentType,
        editorConfig: {
            callbackUrl: config.editor.callbackUrl,
            lang: webConfig.lang || config.editor.lang,
            mode: fileExt === 'pdf' ? 'view' : 'edit',
            customization: webConfig.customization || config.editor.customization,
            user: webConfig.user || config.editor.user, // 使用Web配置的用户信息
            coEditing: webConfig.coEditing || { mode: "fast", change: true }
        }
    };
    console.log('[getFileNetDocumentConfig] 使用Web配置生成OnlyOffice配置');

    return docConfig;
}

/**
 * 根据内部数据库ID获取原始文档信息 (主要供内部服务使用)
 * @param {string} fileId - 文件在 filenet_documents 表中的ID
 * @returns {Promise<object|null>} 数据库中的原始文件记录或null
 */
async function getDocumentRawById(fileId) {
    const db = require('./database');
    try {
        const fileInfo = await db.queryOne(`
            SELECT * 
            FROM filenet_documents 
            WHERE id = ? AND is_deleted = FALSE
        `, [fileId]);
        return fileInfo; // 返回原始记录
    } catch (error) {
        console.error(`Error fetching raw document by ID ${fileId}:`, error);
        throw new Error('获取原始文档信息失败');
    }
}

/**
 * 根据内部数据库ID获取处理后的文档信息
 * @param {string} fileId - 文件在 filenet_documents 表中的ID
 * @returns {Promise<object|null>} 处理后的文件对象或null
 */
async function getDocumentById(fileId) {
    const rawDoc = await getDocumentRawById(fileId);
    if (!rawDoc) {
        return null;
    }
    // 可以根据需要进行字段映射或添加额外信息
    return {
        id: rawDoc.id,
        name: rawDoc.original_name,
        originalName: rawDoc.original_name,
        storageName: rawDoc.storage_name,
        type: rawDoc.extension,
        extension: rawDoc.extension,
        size: rawDoc.file_size,
        mimeType: rawDoc.mime_type,
        lastModified: rawDoc.updated_at,
        version: rawDoc.version,
        createdAt: rawDoc.created_at,
        createdBy: rawDoc.created_by,
        modifiedBy: rawDoc.last_modified_by,
        fileHash: rawDoc.file_hash,
        fnDocId: rawDoc.fn_doc_id,
        isDeleted: rawDoc.is_deleted,
        templateId: rawDoc.template_id,
        // 根据需要添加下载URL或编辑URL
        downloadUrl: `/api/documents/${rawDoc.id}`,
        editUrl: `/editor/${rawDoc.id}`
    };
}

/**
 * 在数据库中创建新的文档记录 (不处理文件上传，假设文件已存在)
 * @param {object} docData - 文档元数据
 * @param {string} docData.id - (可选) UUID, 如果不提供则生成一个新的
 * @param {string} docData.original_name - 原始文件名
 * @param {string} docData.storage_name - 存储系统中的文件名
 * @param {number} docData.file_size - 文件大小 (bytes)
 * @param {string} docData.mime_type - MIME 类型
 * @param {string} docData.extension - 文件扩展名
 * @param {string} [docData.created_by] - 创建者ID
 * @param {string} [docData.last_modified_by] - 最后修改者ID
 * @param {string} [docData.template_id] - (可选) 源模板ID
 * @param {string} [docData.fn_doc_id] - (可选) FileNet 文档ID
 * @param {string} [docData.file_hash] - (可选) 文件哈希
 * @param {number} [docData.version=1] - 版本号
 * @returns {Promise<object>} 创建后的文档对象 (通过 getDocumentById 获取)
 */
async function createDocumentRecord(docData) {
    const db = require('./database');
    const documentId = docData.id || uuidv4();

    const {
        original_name,
        file_size,
        mime_type,
        extension,
        created_by = 'system',
        last_modified_by = 'system',
        template_id = null,
        fn_doc_id = null,
        file_hash = null,
        version = 1
    } = docData;

    if (!original_name || !file_size || !mime_type || !extension) {
        throw new Error('创建文档记录失败：缺少必要的元数据 (original_name, file_size, mime_type, extension)');
    }

    const insertQuery = `
        INSERT INTO filenet_documents 
        (id, original_name, file_size, mime_type, extension, 
        created_by, last_modified_by, template_id, fn_doc_id, file_hash, version, 
        created_at, updated_at, is_deleted)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, FALSE)
    `;

    try {
        await db.query(insertQuery, [
            documentId,
            original_name,
            file_size,
            mime_type,
            extension,
            created_by,
            last_modified_by,
            template_id,
            fn_doc_id,
            file_hash,
            version
        ]);
        return await getDocumentById(documentId);
    } catch (error) {
        console.error('Error creating document record in DB:', error);
        if (error.code === 'ER_DUP_ENTRY' && fn_doc_id && error.message.includes('unique_fn_doc_id')) {
            throw new Error(`创建文档记录失败：FileNet Document ID '${fn_doc_id}' 已存在。`);
        }
        throw new Error('创建文档数据库记录失败');
    }
}

module.exports = {
    getDocumentList,
    deleteDocument,
    getDocumentConfig,
    getDocumentRawById,
    getDocumentById,
    createDocumentRecord,
    handleCallback,
    uploadDocument,
    getFileNetDocumentConfig
}; 