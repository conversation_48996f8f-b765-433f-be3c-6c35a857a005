<template>
  <div class="document-templates-page">
    <a-page-header title="文档模板管理" sub-title="管理文档内容模板，用于创建标准化文档">
      <template #extra>
        <a-space>
          <a-button @click="handleManageCategories">
            <template #icon>
              <folder-outlined />
            </template>
            分类管理
          </a-button>
          <a-button type="primary" @click="handleCreateTemplate">
            <template #icon>
              <plus-outlined />
            </template>
            创建模板
          </a-button>
          <a-button @click="handleImportTemplate">
            <template #icon>
              <upload-outlined />
            </template>
            导入模板
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-wrapper">
      <!-- 搜索和筛选区域 -->
      <div class="search-bar">
        <a-row :gutter="16" align="middle">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchParams.keyword"
              placeholder="搜索模板名称、描述..."
              size="large"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchParams.categoryId"
              placeholder="选择分类"
              size="large"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option
                v-for="category in categories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchParams.status"
              placeholder="选择状态"
              size="large"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="active">启用</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchParams.sortBy"
              placeholder="排序方式"
              size="large"
              @change="handleSearch"
            >
              <a-select-option value="createdAt">创建时间</a-select-option>
              <a-select-option value="updatedAt">更新时间</a-select-option>
              <a-select-option value="usageCount">使用次数</a-select-option>
              <a-select-option value="name">名称</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button @click="handleReset" size="large">重置</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 分类统计卡片 -->
      <div class="category-stats">
        <a-row :gutter="16">
          <!-- 显示分类树结构 -->
          <template v-for="category in buildCategoryTree(categoriesWithCount)" :key="category.id">
            <!-- 顶级分类 -->
            <a-col :span="4">
              <a-card
                size="small"
                :class="{ 'selected-category': searchParams.categoryId === category.id }"
                @click="handleCategorySelect(category.id)"
                style="cursor: pointer; border-left: 4px solid #1890ff"
              >
                <div class="category-card">
                  <div class="category-info">
                    <div class="category-name">📁 {{ category.name }}</div>
                    <div class="category-count">{{ category.count }} 个模板</div>
                  </div>
                  <div class="category-icon" :style="{ color: category.color }">
                    <component :is="getCategoryIcon(category.icon || 'FileTextOutlined')" />
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 子分类 -->
            <a-col v-for="child in category.children" :key="child.id" :span="4">
              <a-card
                size="small"
                :class="{ 'selected-category': searchParams.categoryId === child.id }"
                @click="handleCategorySelect(child.id)"
                style="cursor: pointer; border-left: 3px solid #52c41a; margin-left: 8px"
              >
                <div class="category-card">
                  <div class="category-info">
                    <div class="category-name">┗ {{ child.name }}</div>
                    <div class="category-count">{{ child.count || 0 }} 个模板</div>
                  </div>
                  <div class="category-icon" :style="{ color: child.color }">
                    <component :is="getCategoryIcon(child.icon || 'FileTextOutlined')" />
                  </div>
                </div>
              </a-card>
            </a-col>
          </template>
        </a-row>
      </div>

      <!-- 文档模板表格 -->
      <div class="templates-table">
        <a-table
          :columns="documentTemplateColumns"
          :data-source="documentTemplates"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          row-key="id"
          size="middle"
        >
          <!-- 模板名称列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="template-name">
                <div class="name-info">
                  <div class="name">
                    {{ record.name }}
                    <a-tag v-if="record.isDefault" color="blue" size="small">默认</a-tag>
                  </div>
                  <div class="description" v-if="record.description">
                    {{ record.description }}
                  </div>
                </div>
              </div>
            </template>

            <!-- 分类列 -->
            <template v-else-if="column.key === 'category'">
              <a-tag :color="getCategoryColor(record.category)">
                {{ getCategoryName(record.category) }}
              </a-tag>
            </template>

            <!-- 文件大小列 -->
            <template v-else-if="column.key === 'size'">
              {{ formatFileSize(record.size) }}
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="record.status === 'active' ? 'green' : 'red'">
                {{ record.status === 'active' ? '启用' : '禁用' }}
              </a-tag>
            </template>

            <!-- 使用次数列 -->
            <template v-else-if="column.key === 'usageCount'">
              <a-badge :count="record.usageCount" :number-style="{ backgroundColor: '#52c41a' }" />
            </template>

            <!-- 创建者列 -->
            <template v-else-if="column.key === 'creator'">
              {{ record.createdBy }}
            </template>

            <!-- 更新时间列 -->
            <template v-else-if="column.key === 'updateTime'">
              {{ formatDate(new Date(record.updatedAt)) }}
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-tooltip title="预览">
                  <a-button type="text" size="small" @click="handlePreview(record)">
                    <template #icon>
                      <eye-outlined />
                    </template>
                  </a-button>
                </a-tooltip>

                <a-tooltip title="下载">
                  <a-button type="text" size="small" @click="handleDownload(record)">
                    <template #icon>
                      <download-outlined />
                    </template>
                  </a-button>
                </a-tooltip>

                <a-tooltip title="基于模板创建文档">
                  <a-button type="text" size="small" @click="handleCreateDocument(record)">
                    <template #icon>
                      <file-add-outlined />
                    </template>
                  </a-button>
                </a-tooltip>

                <a-dropdown>
                  <a-button type="text" size="small">
                    <template #icon>
                      <more-outlined />
                    </template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleEdit(record)">
                        <template #icon>
                          <edit-outlined />
                        </template>
                        编辑
                      </a-menu-item>
                      <a-menu-item @click="handleDuplicate(record)">
                        <template #icon>
                          <copy-outlined />
                        </template>
                        复制
                      </a-menu-item>
                      <a-menu-item @click="handleSetDefault(record)" v-if="!record.isDefault">
                        <template #icon>
                          <star-outlined />
                        </template>
                        设为默认
                      </a-menu-item>
                      <a-menu-item @click="handleToggleStatus(record)">
                        <template #icon>
                          <component
                            :is="record.status === 'active' ? StopOutlined : PlayCircleOutlined"
                          />
                        </template>
                        {{ record.status === 'active' ? '禁用' : '启用' }}
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="handleDelete(record)" class="danger-item">
                        <template #icon>
                          <delete-outlined />
                        </template>
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 分类管理模态框 -->
    <a-modal
      v-model:open="categoryModalVisible"
      title="分类管理"
      width="800px"
      @ok="handleSaveCategories"
    >
      <div class="category-management">
        <div class="category-actions">
          <a-button type="primary" @click="handleAddCategory">
            <template #icon>
              <plus-outlined />
            </template>
            添加分类
          </a-button>
        </div>

        <a-table
          :columns="categoryColumns"
          :data-source="categories"
          :pagination="false"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'name'">
              <a-input
                v-model:value="record.name"
                placeholder="请输入分类名称"
                size="small"
                @blur="validateCategoryName(record)"
              />
            </template>
            <template v-else-if="column.key === 'parentCategory'">
              <a-select
                v-model:value="record.parentId"
                :options="getParentCategoryOptions()"
                placeholder="选择父分类"
                size="small"
                style="width: 100%"
                allow-clear
              >
                <template #option="{ label, value }">
                  <div style="display: flex; align-items: center; gap: 8px">
                    <span v-if="!value">🏠</span>
                    <span v-else>📁</span>
                    <span>{{ label }}</span>
                  </div>
                </template>
              </a-select>
            </template>
            <template v-else-if="column.key === 'description'">
              <a-input
                v-model:value="record.description"
                placeholder="请输入分类描述"
                size="small"
              />
            </template>
            <template v-else-if="column.key === 'icon'">
              <a-select
                v-model:value="record.icon"
                size="small"
                style="width: 100%"
                :options="iconOptions"
              >
                <template #option="{ label, value }">
                  <div style="display: flex; align-items: center; gap: 8px">
                    <component :is="getCategoryIcon(value)" />
                    <span>{{ label }}</span>
                  </div>
                </template>
              </a-select>
            </template>
            <template v-else-if="column.key === 'color'">
              <a-select
                v-model:value="record.color"
                size="small"
                style="width: 100%"
                :options="colorOptions"
              >
                <template #option="{ label, value }">
                  <div style="display: flex; align-items: center; gap: 8px">
                    <div
                      :style="{
                        backgroundColor: value,
                        width: '16px',
                        height: '16px',
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9',
                      }"
                    ></div>
                    <span>{{ label }}</span>
                  </div>
                </template>
              </a-select>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button
                  type="text"
                  size="small"
                  @click="handleSaveCategory(record)"
                  :disabled="!record.name"
                >
                  <template #icon>
                    <edit-outlined />
                  </template>
                </a-button>
                <a-button type="text" size="small" danger @click="handleDeleteCategory(index)">
                  <template #icon>
                    <delete-outlined />
                  </template>
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileAddOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  MoreOutlined,
  StarOutlined,
  FolderOutlined,
  StopOutlined,
  PlayCircleOutlined,
  ContainerOutlined,
  BarChartOutlined,
  MailOutlined,
  FormOutlined,
  FileTextOutlined,
} from '@ant-design/icons-vue'
import { TemplatesApiService } from '@/services/templates.api'
import type { TemplateCategory, DocumentTemplateListQueryParams } from '@/services/templates.api'
import type { TemplateInfo } from '@/types/api.types'

// 响应式数据
const loading = ref(false)
const categoryModalVisible = ref(false)

// 搜索参数
const searchParams = ref({
  keyword: '',
  categoryId: undefined as string | undefined,
  status: undefined as string | undefined,
  sortBy: 'updatedAt' as string,
  order: 'desc' as string,
})

// 分页参数
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 分类数据 - 扩展TemplateCategory类型以包含icon和color
interface ExtendedTemplateCategory extends TemplateCategory {
  icon?: string
  color?: string
}

const categories = ref<ExtendedTemplateCategory[]>([])

// 文档模板数据
const documentTemplates = ref<TemplateInfo[]>([])

// 表格列定义
const documentTemplateColumns = [
  {
    title: '模板名称',
    key: 'name',
    width: 250,
    ellipsis: true,
  },
  {
    title: '分类',
    key: 'category',
    width: 120,
  },
  {
    title: '文件大小',
    key: 'size',
    width: 100,
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
  },
  {
    title: '使用次数',
    key: 'usageCount',
    width: 100,
  },
  {
    title: '创建者',
    key: 'creator',
    width: 100,
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 150,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
  },
]

const categoryColumns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: 180,
  },
  {
    title: '父分类',
    key: 'parentCategory',
    width: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
  },
  {
    title: '图标',
    key: 'icon',
    width: 60,
  },
  {
    title: '颜色',
    key: 'color',
    width: 60,
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
  },
]

// 计算属性
const categoriesWithCount = computed(() => {
  return categories.value.map((category: ExtendedTemplateCategory) => ({
    ...category,
    count: documentTemplates.value.filter(
      (template: TemplateInfo) => template.category === category.id
    ).length,
  }))
})

// API调用函数

/**
 * 获取文档模板列表
 */
const fetchDocumentTemplates = async () => {
  loading.value = true
  try {
    const params: DocumentTemplateListQueryParams = {
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
      category: searchParams.value.categoryId || undefined,
      search: searchParams.value.keyword || undefined,
    }

    console.log('📋 获取文档模板列表，参数:', params)
    const response = await TemplatesApiService.getDocumentTemplates(params)

    console.log('📋 文档模板API响应:', response)

    if (response && response.list) {
      documentTemplates.value = response.list
      pagination.value.total = response.total || response.list.length
    } else if (Array.isArray(response)) {
      // 处理旧版API格式
      documentTemplates.value = response as TemplateInfo[]
      pagination.value.total = response.length
    } else {
      console.warn('⚠️ 意外的API响应格式:', response)
      documentTemplates.value = []
      pagination.value.total = 0
    }

    console.log(`✅ 成功获取 ${documentTemplates.value.length} 个文档模板`)
  } catch (error) {
    console.error('❌ 获取文档模板列表失败:', error)
    message.error('获取文档模板列表失败')
    documentTemplates.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 获取模板分类列表
 */
const fetchTemplateCategories = async () => {
  try {
    console.log('📁 获取模板分类列表...')
    const response = await TemplatesApiService.getTemplateCategories()

    console.log('📁 分类API响应:', response)

    if (Array.isArray(response)) {
      // 为每个分类添加默认的icon和color
      categories.value = response.map((cat: TemplateCategory) => ({
        ...cat,
        icon: 'FileTextOutlined',
        color: '#1890ff',
      }))
    } else {
      console.warn('⚠️ 意外的分类API响应格式:', response)
      // 使用默认分类数据
      categories.value = [
        {
          id: 'contract',
          name: '合同模板',
          icon: 'ContainerOutlined',
          color: '#1890ff',
          description: '',
          templateCount: 0,
          createdAt: '',
        },
        {
          id: 'report',
          name: '报告模板',
          icon: 'BarChartOutlined',
          color: '#52c41a',
          description: '',
          templateCount: 0,
          createdAt: '',
        },
        {
          id: 'letter',
          name: '信函模板',
          icon: 'MailOutlined',
          color: '#fa8c16',
          description: '',
          templateCount: 0,
          createdAt: '',
        },
        {
          id: 'form',
          name: '表单模板',
          icon: 'FormOutlined',
          color: '#722ed1',
          description: '',
          templateCount: 0,
          createdAt: '',
        },
        {
          id: 'other',
          name: '其他模板',
          icon: 'FileTextOutlined',
          color: '#eb2f96',
          description: '',
          templateCount: 0,
          createdAt: '',
        },
      ]
    }

    console.log(`✅ 成功获取 ${categories.value.length} 个模板分类`)
  } catch (error) {
    console.error('❌ 获取模板分类失败:', error)
    message.error('获取模板分类失败')
    // 使用默认分类数据
    categories.value = [
      {
        id: 'contract',
        name: '合同模板',
        icon: 'ContainerOutlined',
        color: '#1890ff',
        description: '',
        templateCount: 0,
        createdAt: '',
      },
      {
        id: 'report',
        name: '报告模板',
        icon: 'BarChartOutlined',
        color: '#52c41a',
        description: '',
        templateCount: 0,
        createdAt: '',
      },
      {
        id: 'letter',
        name: '信函模板',
        icon: 'MailOutlined',
        color: '#fa8c16',
        description: '',
        templateCount: 0,
        createdAt: '',
      },
      {
        id: 'form',
        name: '表单模板',
        icon: 'FormOutlined',
        color: '#722ed1',
        description: '',
        templateCount: 0,
        createdAt: '',
      },
      {
        id: 'other',
        name: '其他模板',
        icon: 'FileTextOutlined',
        color: '#eb2f96',
        description: '',
        templateCount: 0,
        createdAt: '',
      },
    ]
  }
}

// 工具函数
const getCategoryName = (categoryId: string) => {
  const category = categories.value.find((cat: ExtendedTemplateCategory) => cat.id === categoryId)
  return category?.name || '未分类'
}

const getCategoryColor = (categoryId: string) => {
  const category = categories.value.find((cat: ExtendedTemplateCategory) => cat.id === categoryId)
  return category?.color || '#666666'
}

const getCategoryIcon = (iconName: string) => {
  const iconMap: Record<string, typeof FileTextOutlined> = {
    ContainerOutlined,
    BarChartOutlined,
    MailOutlined,
    FormOutlined,
    FileTextOutlined,
  }
  return iconMap[iconName] || FileTextOutlined
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 事件处理函数
const handleSearch = () => {
  pagination.value.current = 1
  fetchDocumentTemplates()
}

const handleCategorySelect = (categoryId: string) => {
  if (searchParams.value.categoryId === categoryId) {
    searchParams.value.categoryId = undefined
  } else {
    searchParams.value.categoryId = categoryId
  }
  handleSearch()
}

const handleTableChange = (pag: { current: number; pageSize: number }) => {
  pagination.value = { ...pagination.value, ...pag }
  fetchDocumentTemplates()
}

const handleReset = () => {
  searchParams.value = {
    keyword: '',
    categoryId: undefined,
    status: undefined,
    sortBy: 'updatedAt',
    order: 'desc',
  }
  pagination.value.current = 1
  fetchDocumentTemplates()
}

const handleManageCategories = () => {
  categoryModalVisible.value = true
}

const handleCreateTemplate = () => {
  message.info('创建模板功能开发中...')
}

const handleImportTemplate = () => {
  message.info('导入模板功能开发中...')
}

const handlePreview = async (record: TemplateInfo) => {
  try {
    console.log('🔍 预览模板:', record.name)
    // TODO: 实现模板预览功能
    message.info(`预览模板: ${record.name}`)
  } catch (error) {
    console.error('❌ 预览模板失败:', error)
    message.error('预览模板失败')
  }
}

const handleDownload = async (record: TemplateInfo) => {
  try {
    console.log('📥 下载模板:', record.name)
    await TemplatesApiService.downloadTemplate(record.id, record.name)
    message.success(`开始下载模板: ${record.name}`)
  } catch (error) {
    console.error('❌ 下载模板失败:', error)
    message.error('下载模板失败')
  }
}

const handleCreateDocument = async (record: TemplateInfo) => {
  try {
    console.log('📄 基于模板创建文档:', record.name)
    const response = await TemplatesApiService.createDocumentFromTemplate(record.id, {
      title: `基于${record.name}的新文档`,
      description: `从模板"${record.name}"创建的文档`,
    })

    if (response.editUrl) {
      // 打开编辑器
      window.open(response.editUrl, '_blank')
      message.success('文档创建成功，正在打开编辑器...')
    } else {
      message.success('文档创建成功')
    }
  } catch (error) {
    console.error('❌ 创建文档失败:', error)
    message.error('创建文档失败')
  }
}

const handleEdit = (record: TemplateInfo) => {
  message.info(`编辑模板: ${record.name}`)
}

const handleDuplicate = (record: TemplateInfo) => {
  message.info(`复制模板: ${record.name}`)
}

const handleSetDefault = (record: TemplateInfo) => {
  // 清除其他默认模板
  documentTemplates.value.forEach((template: TemplateInfo) => {
    if (template.category === record.category) {
      template.isDefault = false
    }
  })
  // 设置当前为默认
  record.isDefault = true
  message.success(`已将 ${record.name} 设为默认模板`)
}

const handleToggleStatus = (record: TemplateInfo) => {
  const newStatus = record.status === 'active' ? 'inactive' : 'active'
  record.status = newStatus
  message.success(`已${newStatus === 'active' ? '启用' : '禁用'}模板: ${record.name}`)
}

const handleDelete = async (record: TemplateInfo) => {
  try {
    console.log('🗑️ 删除模板:', record.name)
    await TemplatesApiService.deleteDocumentTemplate(record.id)
    message.success(`已删除模板: ${record.name}`)
    // 重新获取列表
    fetchDocumentTemplates()
  } catch (error) {
    console.error('❌ 删除模板失败:', error)
    message.error('删除模板失败')
  }
}

// 图标选项
const iconOptions = [
  { label: '文档', value: 'FileTextOutlined' },
  { label: '容器', value: 'ContainerOutlined' },
  { label: '柱状图', value: 'BarChartOutlined' },
  { label: '邮件', value: 'MailOutlined' },
  { label: '表单', value: 'FormOutlined' },
]

// 颜色选项
const colorOptions = [
  { label: '蓝色', value: '#1890ff' },
  { label: '绿色', value: '#52c41a' },
  { label: '橙色', value: '#fa8c16' },
  { label: '紫色', value: '#722ed1' },
  { label: '粉色', value: '#eb2f96' },
  { label: '青色', value: '#13c2c2' },
  { label: '红色', value: '#f5222d' },
]

/**
 * 获取顶级分类选项（用于父分类选择）
 */
const getParentCategoryOptions = () => {
  return [
    { label: '无父分类（顶级分类）', value: null },
    ...categories.value
      .filter(cat => !cat.parentId) // 只显示顶级分类
      .map(cat => ({ label: cat.name, value: cat.id })),
  ]
}

/**
 * 构建分类树结构
 */
const buildCategoryTree = (flatCategories: ExtendedTemplateCategory[]) => {
  const topLevel: ExtendedTemplateCategory[] = []
  const childrenMap = new Map<string, ExtendedTemplateCategory[]>()

  // 分离顶级分类和子分类
  flatCategories.forEach(category => {
    if (!category.parentId) {
      topLevel.push(category)
    } else {
      if (!childrenMap.has(category.parentId)) {
        childrenMap.set(category.parentId, [])
      }
      childrenMap.get(category.parentId)!.push(category)
    }
  })

  // 为每个顶级分类附加子分类
  topLevel.forEach(parent => {
    parent.children = childrenMap.get(parent.id) || []
  })

  return topLevel
}

/**
 * 验证分类名称
 */
const validateCategoryName = (record: ExtendedTemplateCategory) => {
  if (!record.name || record.name.trim() === '') {
    record.name = '新分类'
    message.warning('分类名称不能为空')
  }
}

/**
 * 保存单个分类
 */
const handleSaveCategory = async (record: ExtendedTemplateCategory) => {
  try {
    if (!record.name || record.name.trim() === '') {
      message.error('分类名称不能为空')
      return
    }

    // 如果是新建的分类（ID以category_开头），调用创建API
    if (record.id.startsWith('category_')) {
      console.log('🆕 创建新分类:', record.name, '父分类:', record.parentId)
      const newCategory = await TemplatesApiService.createTemplateCategory({
        name: record.name.trim(),
        description: record.description || '',
        parentId: record.parentId || null,
      })

      // 更新本地数据
      const index = categories.value.findIndex(cat => cat.id === record.id)
      if (index !== -1) {
        categories.value[index] = {
          ...newCategory,
          icon: record.icon,
          color: record.color,
        }
      }

      // 如果有父分类，显示层级信息
      const parentName = record.parentId
        ? categories.value.find(c => c.id === record.parentId)?.name || '未知'
        : '顶级分类'

      message.success(`分类 "${record.name}" 创建成功 (${parentName})`)
    } else {
      // TODO: 实现更新分类的API调用
      message.success(`分类 "${record.name}" 保存成功`)
    }
  } catch (error) {
    console.error('❌ 保存分类失败:', error)
    message.error('保存分类失败')
  }
}

const handleAddCategory = () => {
  const newCategory: ExtendedTemplateCategory = {
    id: `category_${Date.now()}`,
    name: '',
    icon: 'FileTextOutlined',
    color: '#1890ff',
    description: '',
    parentId: null, // 默认为顶级分类
    children: [],
    templateCount: 0,
    createdAt: new Date().toISOString(),
  }
  categories.value.push(newCategory)
}

const handleDeleteCategory = (index: number) => {
  categories.value.splice(index, 1)
}

const handleSaveCategories = async () => {
  try {
    // TODO: 实现保存分类到后端的逻辑
    categoryModalVisible.value = false
    message.success('分类设置已保存')
  } catch (error) {
    console.error('❌ 保存分类失败:', error)
    message.error('保存分类失败')
  }
}

// 生命周期
onMounted(() => {
  console.log('🚀 初始化文档模板管理页面')
  fetchTemplateCategories()
  fetchDocumentTemplates()
})
</script>

<style scoped>
.document-templates-page {
  padding: 16px;
}

.content-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
}

.search-bar {
  margin-bottom: 16px;
}

.category-stats {
  margin-bottom: 24px;
}

.category-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-info .category-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.category-info .category-count {
  color: #666;
  font-size: 12px;
}

.category-icon {
  font-size: 20px;
}

.selected-category {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.template-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.name-info .name {
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-info .description {
  color: #666;
  font-size: 12px;
}

.templates-table {
  margin-top: 16px;
}

.danger-item {
  color: #ff4d4f !important;
}

.category-management .category-actions {
  margin-bottom: 16px;
}

.color-preview {
  border: 1px solid #d9d9d9;
}
</style>
