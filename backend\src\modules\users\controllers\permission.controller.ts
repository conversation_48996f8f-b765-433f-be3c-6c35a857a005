import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { PermissionService } from '../services/permission.service';
import { DatabaseService } from '../../database/services/database.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PermissionPermissions } from '../../auth/decorators/permissions.decorator';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionQueryDto,
  BatchPermissionDto,
  PermissionResponseDto,
  PermissionListResponseDto,
  PermissionStatsResponseDto,
  ModulePermissionTreeDto,
  CheckPermissionDto,
  PermissionCheckResultDto,
} from '../dto/permission.dto';

// 用户信息查询结果类型定义
interface UserRoleQueryResult {
  username: string;
  role_name: string;
  display_name: string;
}

/**
 * 权限管理控制器
 * 
 * @description 提供完整的权限管理API接口，包括权限的CRUD操作、权限检查、统计分析等功能
 * @controller PermissionController
 * <AUTHOR> Team
 * @since 2024-12-19
 */
@ApiTags('权限管理')
@Controller('permissions')
@ApiBearerAuth('JWT-auth')
export class PermissionController {
  constructor(
    private readonly permissionService: PermissionService,
    private readonly databaseService: DatabaseService,
  ) {}

  /**
   * 获取权限列表
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取权限列表' })
  @ApiResponse({
    status: 200,
    description: '获取权限列表成功',
    type: PermissionListResponseDto,
  })
  async getPermissions(@Query() query: PermissionQueryDto): Promise<{ success: boolean; message: string; data?: PermissionListResponseDto; error?: string }> {
    try {
      const result = await this.permissionService.findMany(query);
      
      return {
        success: true,
        message: '获取权限列表成功',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取权限列表失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取权限统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取权限统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取权限统计信息成功',
    type: PermissionStatsResponseDto,
  })
  async getPermissionStats(): Promise<{ success: boolean; message: string; data?: PermissionStatsResponseDto; error?: string }> {
    try {
      const stats = await this.permissionService.getPermissionStats();
      return {
        success: true,
        message: '获取权限统计信息成功',
        data: stats,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取权限统计信息失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取模块权限树
   */
  @Get('tree')
  @ApiOperation({ summary: '获取模块权限树' })
  @ApiResponse({
    status: 200,
    description: '获取模块权限树成功',
    type: [ModulePermissionTreeDto],
  })
  async getModulePermissionTree(): Promise<{ success: boolean; message: string; data?: ModulePermissionTreeDto[]; error?: string }> {
    try {
      const tree = await this.permissionService.getModulePermissionTree();
      return {
        success: true,
        message: '获取模块权限树成功',
        data: tree,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取模块权限树失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取权限详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取权限详情' })
  @ApiParam({ name: 'id', description: '权限ID', example: 'perm-uuid-123' })
  @ApiResponse({
    status: 200,
    description: '获取权限详情成功',
    type: PermissionResponseDto,
  })
  async getPermissionById(@Param('id') id: string): Promise<{ success: boolean; message: string; data?: PermissionResponseDto; error?: string }> {
    try {
      const permission = await this.permissionService.findById(id);
      if (!permission) {
        return {
          success: false,
          message: '权限不存在',
        };
      }

      return {
        success: true,
        message: '获取权限详情成功',
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取权限详情失败',
        error: error.message,
      };
    }
  }

  /**
   * 根据权限代码获取权限详情
   */
  @Get('by-code/:code')
  @ApiOperation({ summary: '根据权限代码获取权限详情' })
  @ApiParam({ name: 'code', description: '权限代码', example: 'documents.read' })
  @ApiResponse({
    status: 200,
    description: '获取权限详情成功',
    type: PermissionResponseDto,
  })
  async getPermissionByCode(@Param('code') code: string): Promise<{ success: boolean; message: string; data?: PermissionResponseDto; error?: string }> {
    try {
      const permission = await this.permissionService.findByCode(code);
      if (!permission) {
        return {
          success: false,
          message: '权限不存在',
        };
      }

      return {
        success: true,
        message: '获取权限详情成功',
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取权限详情失败',
        error: error.message,
      };
    }
  }

  /**
   * 创建权限
   */
  @Post()
  @PermissionPermissions.Create()
  @ApiOperation({ summary: '创建权限' })
  @ApiResponse({
    status: 201,
    description: '创建权限成功',
    type: PermissionResponseDto,
  })
  async createPermission(@Body() createPermissionDto: CreatePermissionDto, @Request() req): Promise<{ success: boolean; message: string; data?: PermissionResponseDto; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      const permission = await this.permissionService.createPermission(createPermissionDto, currentUserId);

      return {
        success: true,
        message: '创建权限成功',
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        message: '创建权限失败',
        error: error.message,
      };
    }
  }

  /**
   * 更新权限
   */
  @Put(':id')
  @PermissionPermissions.Update()
  @ApiOperation({ summary: '更新权限信息' })
  @ApiParam({ name: 'id', description: '权限ID', example: 'perm-uuid-123' })
  @ApiResponse({
    status: 200,
    description: '更新权限信息成功',
    type: PermissionResponseDto,
  })
  async updatePermission(
    @Param('id') id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
    @Request() req,
  ): Promise<{ success: boolean; message: string; data?: PermissionResponseDto; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      const permission = await this.permissionService.updatePermission(id, updatePermissionDto, currentUserId);

      return {
        success: true,
        message: '更新权限信息成功',
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        message: '更新权限信息失败',
        error: error.message,
      };
    }
  }

  /**
   * 删除权限
   */
  @Delete(':id')
  @PermissionPermissions.Delete()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除权限' })
  @ApiParam({ name: 'id', description: '权限ID', example: 'perm-uuid-123' })
  @ApiResponse({
    status: 204,
    description: '删除权限成功',
  })
  async deletePermission(@Param('id') id: string, @Request() req): Promise<{ success: boolean; message: string; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      await this.permissionService.deletePermission(id, currentUserId);
      
      return {
        success: true,
        message: '删除权限成功',
      };
    } catch (error) {
      return {
        success: false,
        message: '删除权限失败',
        error: error.message,
      };
    }
  }

  /**
   * 批量操作权限
   */
  @Post('batch')
  @ApiOperation({ summary: '批量操作权限' })
  @ApiResponse({
    status: 200,
    description: '批量操作成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            success: { type: 'number', description: '成功数量' },
            failed: { type: 'number', description: '失败数量' },
            errors: { type: 'array', items: { type: 'string' }, description: '错误信息列表' },
          },
        },
      },
    },
  })
  async batchOperation(@Body() batchDto: BatchPermissionDto, @Request() req): Promise<{ success: boolean; message: string; data?: { success: number; failed: number }; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      const result = await this.permissionService.batchOperation(batchDto, currentUserId);

      return {
        success: true,
        message: `批量操作完成，成功: ${result.success}，失败: ${result.failed}`,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: '批量操作失败',
        error: error.message,
      };
    }
  }

  /**
   * 复制权限
   */
  @Post(':id/copy')
  @ApiOperation({ summary: '复制权限' })
  @ApiParam({ name: 'id', description: '源权限ID', example: 'perm-uuid-123' })
  @ApiResponse({
    status: 201,
    description: '复制权限成功',
    type: PermissionResponseDto,
  })
  async copyPermission(@Param('id') id: string, @Request() req): Promise<{ success: boolean; message: string; data?: PermissionResponseDto; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      const permission = await this.permissionService.copyPermission(id, currentUserId);

      return {
        success: true,
        message: '复制权限成功',
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        message: '复制权限失败',
        error: error.message,
      };
    }
  }

  /**
   * 切换权限状态
   */
  @Put(':id/toggle-status')
  @ApiOperation({ summary: '切换权限状态' })
  @ApiParam({ name: 'id', description: '权限ID', example: 'perm-uuid-123' })
  @ApiResponse({
    status: 200,
    description: '切换权限状态成功',
    type: PermissionResponseDto,
  })
  async togglePermissionStatus(@Param('id') id: string, @Request() req): Promise<{ success: boolean; message: string; data?: PermissionResponseDto; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      const permission = await this.permissionService.togglePermissionStatus(id, currentUserId);

      return {
        success: true,
        message: `权限状态已${permission.is_active ? '启用' : '禁用'}`,
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        message: '切换权限状态失败',
        error: error.message,
      };
    }
  }

  /**
   * 检查用户权限
   */
  @Post('check')
  @ApiOperation({ summary: '检查用户权限' })
  @ApiResponse({
    status: 200,
    description: '权限检查成功',
    type: PermissionCheckResultDto,
  })
  async checkPermissions(@Body() checkDto: CheckPermissionDto): Promise<{ success: boolean; message: string; data?: PermissionCheckResultDto; error?: string }> {
    try {
      const result = await this.permissionService.checkPermissions(checkDto);

      return {
        success: true,
        message: '权限检查完成',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: '权限检查失败',
        error: error.message,
      };
    }
  }

  /**
   * 测试端点 - 验证代码更新
   */
  @Get('test/debug')
  @ApiOperation({ summary: '测试权限调试端点' })
  async testDebug(): Promise<{ success: boolean; message: string; timestamp: string; version: string }> {
    console.log('🚀 [DEBUG] 测试端点被调用！代码已更新！', new Date().toISOString());
    return {
      success: true,
      message: '代码已更新，调试端点工作正常',
      timestamp: new Date().toISOString(),
      version: '1.0.1-debug'
    };
  }

  /**
   * 获取当前用户权限
   */
  @Get('user/my')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取当前用户权限' })
  @ApiResponse({
    status: 200,
    description: '获取用户权限成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            user_id: { type: 'string' },
            permissions: { type: 'array', items: { type: 'string' } },
            permission_count: { type: 'number' },
            has_super_admin: { type: 'boolean' },
          },
        },
      },
    },
  })
  async getCurrentUserPermissions(@Request() req): Promise<{ success: boolean; message: string; data?: { user_id: string; permissions: string[]; roles: string[]; permission_count: number; has_super_admin: boolean; debug_info?: Record<string, unknown> }; error?: string }> {
    console.log('=====================================');
    console.log('🚀 [PermissionController] getCurrentUserPermissions 被调用！');
    console.log('🔍 开始处理权限查询请求');
    console.log('=====================================');
    
    try {
      const currentUserId = req.user?.sub;
      const currentUsername = req.user?.username;
      
      console.log('📋 [PermissionController] 用户信息解析:');
      console.log('   currentUserId:', currentUserId);
      console.log('   currentUsername:', currentUsername);
      console.log('   userIdType:', typeof currentUserId);
      console.log('   requestPath:', req.url);
      console.log('   timestamp:', new Date().toISOString());
      
      // 获取用户权限和角色信息
      const [permissions, userInfo] = await Promise.all([
        this.permissionService.getUserPermissions(currentUserId),
        this.databaseService.query(
          'SELECT u.username, ur.name as role_name, ur.display_name FROM users u INNER JOIN user_roles ur ON u.role_id = ur.id WHERE u.id = ?',
          [currentUserId]
        ) as unknown as UserRoleQueryResult[]
      ]);
      
      const roles = userInfo.length > 0 && userInfo[0].role_name ? [userInfo[0].role_name] : [];
      
      console.log('✅ [PermissionController] getUserPermissions 返回结果:', {
        currentUserId,
        permissions,
        roles,
        permissionCount: permissions.length,
        hasSuperAdmin: permissions.includes('*'),
        timestamp: new Date().toISOString(),
      });

      const response = {
        success: true,
        message: '获取用户权限成功(调试版本)',
        data: {
          user_id: currentUserId,
          permissions,
          roles,
          permission_count: permissions.length,
          has_super_admin: permissions.includes('*'),
          debug_info: {
            code_updated: true,
            service_call_count: 1,
            timestamp: new Date().toISOString()
          }
        },
      };
      
      console.log('📤 [PermissionController] 最终响应数据:', {
        responseData: response.data,
        timestamp: new Date().toISOString(),
      });
      
      return response;
    } catch (error) {
      console.error('❌ [PermissionController] getCurrentUserPermissions 失败:', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
      
      return {
        success: false,
        message: '获取用户权限失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取指定用户权限
   */
  @Get('user/:userId')
  @ApiOperation({ summary: '获取指定用户权限' })
  @ApiParam({ name: 'userId', description: '用户ID', example: 'user-uuid-123' })
  @ApiResponse({
    status: 200,
    description: '获取用户权限成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            user_id: { type: 'string' },
            permissions: { type: 'array', items: { type: 'string' } },
            permission_count: { type: 'number' },
            has_super_admin: { type: 'boolean' },
          },
        },
      },
    },
  })
  async getUserPermissions(@Param('userId') userId: string): Promise<{ success: boolean; message: string; data?: { user_id: string; permissions: string[]; permission_count: number; has_super_admin: boolean }; error?: string }> {
    try {
      const permissions = await this.permissionService.getUserPermissions(userId);

      return {
        success: true,
        message: '获取用户权限成功',
        data: {
          user_id: userId,
          permissions,
          permission_count: permissions.length,
          has_super_admin: permissions.includes('*'),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: '获取用户权限失败',
        error: error.message,
      };
    }
  }

  /**
   * 检查当前用户是否具有指定权限
   */
  @Post('check/my')
  @ApiOperation({ summary: '检查当前用户是否具有指定权限' })
  @ApiResponse({
    status: 200,
    description: '权限检查成功',
    type: PermissionCheckResultDto,
  })
  async checkMyPermissions(
    @Body() body: { permissions: string[]; context?: Record<string, unknown> },
    @Request() req,
  ): Promise<{ success: boolean; message: string; data?: PermissionCheckResultDto; error?: string }> {
    try {
      const currentUserId = req.user?.sub;
      const checkDto: CheckPermissionDto = {
        userId: currentUserId,
        permissions: body.permissions,
        context: body.context,
      };

      const result = await this.permissionService.checkPermissions(checkDto);

      return {
        success: true,
        message: '权限检查完成',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: '权限检查失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取所有可用的模块列表
   */
  @Get('modules/list')
  @ApiOperation({ summary: '获取所有可用的模块列表' })
  @ApiResponse({
    status: 200,
    description: '获取模块列表成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              module: { type: 'string' },
              module_name: { type: 'string' },
              permission_count: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getModuleList(): Promise<{ success: boolean; message: string; data?: Array<{ module: string; module_name: string; permission_count: number }>; error?: string }> {
    try {
      const tree = await this.permissionService.getModulePermissionTree();
      const modules = tree.map(item => ({
        module: item.module,
        module_name: item.module_name,
        permission_count: item.permission_count,
      }));

      return {
        success: true,
        message: '获取模块列表成功',
        data: modules,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取模块列表失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取所有可用的操作类型列表
   */
  @Get('actions/list')
  @ApiOperation({ summary: '获取所有可用的操作类型列表' })
  @ApiResponse({
    status: 200,
    description: '获取操作类型列表成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              action: { type: 'string' },
              action_name: { type: 'string' },
              permission_count: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getActionList(): Promise<{ success: boolean; message: string; data?: Array<{ action: string; action_name: string; permission_count: number }>; error?: string }> {
    try {
      const stats = await this.permissionService.getPermissionStats();
      
      // 操作类型名称映射
      const actionNameMap: Record<string, string> = {
        create: '创建',
        read: '查看',
        update: '更新',
        delete: '删除',
        manage: '管理',
        execute: '执行',
      };

      const actions = Object.entries(stats.by_action).map(([action, count]) => ({
        action,
        action_name: actionNameMap[action] || action,
        permission_count: count,
      }));

      return {
        success: true,
        message: '获取操作类型列表成功',
        data: actions,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取操作类型列表失败',
        error: error.message,
      };
    }
  }
} 