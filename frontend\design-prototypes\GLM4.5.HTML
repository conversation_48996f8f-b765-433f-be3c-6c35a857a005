	<!DOCTYPE html>
	<html lang="zh-CN">
	<head>
	    <meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>新品展示 - 智能无线耳机 Pro</title>
	    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
	    <style>
	        :root {
	            --primary-color: #4361ee;
	            --secondary-color: #3f37c9;
	            --accent-color: #f72585;
	            --light-color: #f8f9fa;
	            --dark-color: #212529;
	        }
	        body {
	            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	            color: var(--dark-color);
	            background-color: #f5f7fa;
	        }
	        .navbar {
	            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	        }
	        .navbar-brand {
	            font-weight: 700;
	            color: var(--primary-color) !important;
	        }
	        .product-showcase {
	            padding: 40px 0;
	            background-color: white;
	        }
	        .product-image-container {
	            position: relative;
	            overflow: hidden;
	            border-radius: 10px;
	            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
	        }
	        .main-image {
	            width: 100%;
	            height: auto;
	            display: block;
	            transition: transform 0.3s ease;
	        }
	        .image-zoom {
	            position: absolute;
	            top: 0;
	            left: 0;
	            width: 100%;
	            height: 100%;
	            background-color: rgba(0, 0, 0, 0.7);
	            display: flex;
	            align-items: center;
	            justify-content: center;
	            opacity: 0;
	            transition: opacity 0.3s ease;
	            cursor: pointer;
	        }
	        .product-image-container:hover .image-zoom {
	            opacity: 1;
	        }
	        .thumbnail-container {
	            display: flex;
	            margin-top: 15px;
	            gap: 10px;
	            overflow-x: auto;
	            padding-bottom: 10px;
	        }
	        .thumbnail {
	            width: 80px;
	            height: 80px;
	            object-fit: cover;
	            border-radius: 5px;
	            cursor: pointer;
	            border: 2px solid transparent;
	            transition: border-color 0.3s ease;
	        }
	        .thumbnail.active {
	            border-color: var(--primary-color);
	        }
	        .product-info {
	            padding: 20px;
	        }
	        .product-title {
	            font-size: 2rem;
	            font-weight: 700;
	            margin-bottom: 15px;
	            color: var(--dark-color);
	        }
	        .product-price {
	            font-size: 1.8rem;
	            font-weight: 700;
	            color: var(--accent-color);
	            margin-bottom: 15px;
	        }
	        .product-price .original-price {
	            font-size: 1.2rem;
	            color: #6c757d;
	            text-decoration: line-through;
	            margin-left: 10px;
	        }
	        .product-rating {
	            margin-bottom: 20px;
	        }
	        .product-description {
	            margin-bottom: 25px;
	            line-height: 1.6;
	            color: #495057;
	        }
	        .features-section {
	            background-color: white;
	            padding: 40px 0;
	            margin-top: 30px;
	        }
	        .section-title {
	            font-size: 1.8rem;
	            font-weight: 700;
	            margin-bottom: 30px;
	            text-align: center;
	            position: relative;
	            padding-bottom: 15px;
	        }
	        .section-title::after {
	            content: '';
	            position: absolute;
	            bottom: 0;
	            left: 50%;
	            transform: translateX(-50%);
	            width: 60px;
	            height: 3px;
	            background-color: var(--primary-color);
	        }
	        .feature-card {
	            text-align: center;
	            padding: 25px 15px;
	            border-radius: 10px;
	            transition: transform 0.3s ease, box-shadow 0.3s ease;
	            height: 100%;
	            background-color: #f8f9fa;
	        }
	        .feature-card:hover {
	            transform: translateY(-10px);
	            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
	        }
	        .feature-icon {
	            font-size: 2.5rem;
	            color: var(--primary-color);
	            margin-bottom: 15px;
	        }
	        .feature-title {
	            font-size: 1.2rem;
	            font-weight: 600;
	            margin-bottom: 10px;
	        }
	        .feature-description {
	            color: #6c757d;
	            font-size: 0.9rem;
	        }
	        .purchase-section {
	            background-color: white;
	            padding: 30px;
	            border-radius: 10px;
	            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	            margin-top: 30px;
	        }
	        .btn-purchase {
	            background-color: var(--primary-color);
	            color: white;
	            border: none;
	            padding: 12px 30px;
	            font-size: 1.1rem;
	            font-weight: 600;
	            border-radius: 50px;
	            transition: background-color 0.3s ease, transform 0.2s ease;
	            display: inline-block;
	            text-decoration: none;
	            margin-right: 15px;
	        }
	        .btn-purchase:hover {
	            background-color: var(--secondary-color);
	            color: white;
	            transform: translateY(-3px);
	        }
	        .btn-add-cart {
	            background-color: white;
	            color: var(--primary-color);
	            border: 2px solid var(--primary-color);
	            padding: 10px 25px;
	            font-size: 1rem;
	            font-weight: 600;
	            border-radius: 50px;
	            transition: all 0.3s ease;
	            display: inline-block;
	            text-decoration: none;
	        }
	        .btn-add-cart:hover {
	            background-color: var(--primary-color);
	            color: white;
	        }
	        .product-tags {
	            display: flex;
	            flex-wrap: wrap;
	            gap: 10px;
	            margin-top: 20px;
	        }
	        .product-tag {
	            background-color: #e9ecef;
	            color: #495057;
	            padding: 5px 12px;
	            border-radius: 20px;
	            font-size: 0.85rem;
	        }
	        .related-products {
	            padding: 40px 0;
	            background-color: white;
	            margin-top: 30px;
	        }
	        .related-product-card {
	            border-radius: 10px;
	            overflow: hidden;
	            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	            transition: transform 0.3s ease, box-shadow 0.3s ease;
	            height: 100%;
	        }
	        .related-product-card:hover {
	            transform: translateY(-10px);
	            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
	        }
	        .related-product-img {
	            width: 100%;
	            height: 200px;
	            object-fit: cover;
	        }
	        .related-product-body {
	            padding: 15px;
	        }
	        .related-product-title {
	            font-size: 1rem;
	            font-weight: 600;
	            margin-bottom: 10px;
	        }
	        .related-product-price {
	            font-weight: 700;
	            color: var(--accent-color);
	        }
	        .footer {
	            background-color: var(--dark-color);
	            color: white;
	            padding: 40px 0 20px;
	            margin-top: 50px;
	        }
	        .footer h5 {
	            font-size: 1.2rem;
	            margin-bottom: 20px;
	            position: relative;
	            padding-bottom: 10px;
	        }
	        .footer h5::after {
	            content: '';
	            position: absolute;
	            bottom: 0;
	            left: 0;
	            width: 40px;
	            height: 2px;
	            background-color: var(--primary-color);
	        }
	        .footer-links {
	            list-style: none;
	            padding: 0;
	        }
	        .footer-links li {
	            margin-bottom: 10px;
	        }
	        .footer-links a {
	            color: #adb5bd;
	            text-decoration: none;
	            transition: color 0.3s ease;
	        }
	        .footer-links a:hover {
	            color: white;
	        }
	        .social-icons {
	            display: flex;
	            gap: 15px;
	            margin-top: 20px;
	        }
	        .social-icon {
	            width: 40px;
	            height: 40px;
	            border-radius: 50%;
	            background-color: rgba(255, 255, 255, 0.1);
	            display: flex;
	            align-items: center;
	            justify-content: center;
	            color: white;
	            transition: background-color 0.3s ease;
	        }
	        .social-icon:hover {
	            background-color: var(--primary-color);
	        }
	        .copyright {
	            margin-top: 30px;
	            padding-top: 20px;
	            border-top: 1px solid rgba(255, 255, 255, 0.1);
	            text-align: center;
	            color: #adb5bd;
	            font-size: 0.9rem;
	        }
	        /* 图片放大模态框 */
	        .image-modal {
	            display: none;
	            position: fixed;
	            z-index: 1000;
	            left: 0;
	            top: 0;
	            width: 100%;
	            height: 100%;
	            background-color: rgba(0, 0, 0, 0.9);
	            justify-content: center;
	            align-items: center;
	        }
	        .modal-image {
	            max-width: 90%;
	            max-height: 90%;
	            object-fit: contain;
	        }
	        .close-modal {
	            position: absolute;
	            top: 20px;
	            right: 35px;
	            color: #f1f1f1;
	            font-size: 40px;
	            font-weight: bold;
	            cursor: pointer;
	        }
	        .close-modal:hover {
	            color: var(--primary-color);
	        }
	        /* 响应式调整 */
	        @media (max-width: 768px) {
	            .product-title {
	                font-size: 1.5rem;
	            }
	            .product-price {
	                font-size: 1.5rem;
	            }
	            .section-title {
	                font-size: 1.5rem;
	            }
	            .feature-card {
	                margin-bottom: 20px;
	            }
	        }
	    </style>
	</head>
	<body>
	    <!-- 导航栏 -->
	    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
	        <div class="container">
	            <a class="navbar-brand" href="#">TechShop</a>
	            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
	                <span class="navbar-toggler-icon"></span>
	            </button>
	            <div class="collapse navbar-collapse" id="navbarNav">
	                <ul class="navbar-nav ms-auto">
	                    <li class="nav-item">
	                        <a class="nav-link" href="#">首页</a>
	                    </li>
	                    <li class="nav-item">
	                        <a class="nav-link" href="#">商品分类</a>
	                    </li>
	                    <li class="nav-item">
	                        <a class="nav-link active" href="#">新品上市</a>
	                    </li>
	                    <li class="nav-item">
	                        <a class="nav-link" href="#">热销排行</a>
	                    </li>
	                    <li class="nav-item">
	                        <a class="nav-link" href="#">关于我们</a>
	                    </li>
	                    <li class="nav-item">
	                        <a class="nav-link" href="#"><i class="bi bi-cart3"></i></a>
	                    </li>
	                    <li class="nav-item">
	                        <a class="nav-link" href="#"><i class="bi bi-person-circle"></i></a>
	                    </li>
	                </ul>
	            </div>
	        </div>
	    </nav>
	    <!-- 商品展示区 -->
	    <section class="product-showcase">
	        <div class="container">
	            <div class="row">
	                <div class="col-lg-6">
	                    <div class="product-image-container">
	                        <img id="main-product-image" src="https://picsum.photos/seed/earphone1/600/600.jpg" alt="智能无线耳机 Pro" class="main-image">
	                        <div class="image-zoom">
	                            <i class="bi bi-zoom-in" style="color: white; font-size: 2rem;"></i>
	                        </div>
	                    </div>
	                    <div class="thumbnail-container">
	                        <img src="https://picsum.photos/seed/earphone1/600/600.jpg" alt="产品图1" class="thumbnail active" onclick="changeImage(this)">
	                        <img src="https://picsum.photos/seed/earphone2/600/600.jpg" alt="产品图2" class="thumbnail" onclick="changeImage(this)">
	                        <img src="https://picsum.photos/seed/earphone3/600/600.jpg" alt="产品图3" class="thumbnail" onclick="changeImage(this)">
	                        <img src="https://picsum.photos/seed/earphone4/600/600.jpg" alt="产品图4" class="thumbnail" onclick="changeImage(this)">
	                    </div>
	                </div>
	                <div class="col-lg-6">
	                    <div class="product-info">
	                        <h1 class="product-title">智能无线耳机 Pro</h1>
	                        <div class="product-price">
	                            ¥899 <span class="original-price">¥1299</span>
	                        </div>
	                        <div class="product-rating">
	                            <i class="bi bi-star-fill text-warning"></i>
	                            <i class="bi bi-star-fill text-warning"></i>
	                            <i class="bi bi-star-fill text-warning"></i>
	                            <i class="bi bi-star-fill text-warning"></i>
	                            <i class="bi bi-star-half text-warning"></i>
	                            <span>(128 评价)</span>
	                        </div>
	                        <p class="product-description">
	                            全新升级的智能无线耳机 Pro，采用最新的降噪技术和高品质音频驱动单元，为您带来沉浸式的音乐体验。超长续航能力，单次充电可使用8小时，配合充电盒总续航时间可达32小时。IPX5级防水设计，让您在运动时也能享受音乐。
	                        </p>
	                        <div class="product-tags">
	                            <span class="product-tag">主动降噪</span>
	                            <span class="product-tag">高保真音质</span>
	                            <span class="product-tag">超长续航</span>
	                            <span class="product-tag">快速充电</span>
	                            <span class="product-tag">IPX5防水</span>
	                        </div>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </section>
	    <!-- 主要功能特点 -->
	    <section class="features-section">
	        <div class="container">
	            <h2 class="section-title">主要功能特点</h2>
	            <div class="row">
	                <div class="col-md-3 col-sm-6">
	                    <div class="feature-card">
	                        <i class="bi bi-volume-up feature-icon"></i>
	                        <h3 class="feature-title">主动降噪技术</h3>
	                        <p class="feature-description">采用先进的主动降噪技术，有效隔绝外界噪音，让您专注于音乐世界。</p>
	                    </div>
	                </div>
	                <div class="col-md-3 col-sm-6">
	                    <div class="feature-card">
	                        <i class="bi bi-battery-charging feature-icon"></i>
	                        <h3 class="feature-title">超长续航</h3>
	                        <p class="feature-description">单次充电可使用8小时，配合充电盒总续航时间可达32小时，满足全天使用需求。</p>
	                    </div>
	                </div>
	                <div class="col-md-3 col-sm-6">
	                    <div class="feature-card">
	                        <i class="bi bi-moisture feature-icon"></i>
	                        <h3 class="feature-title">防水设计</h3>
	                        <p class="feature-description">IPX5级防水设计，无惧汗水和雨水，运动时也能享受音乐。</p>
	                    </div>
	                </div>
	                <div class="col-md-3 col-sm-6">
	                    <div class="feature-card">
	                        <i class="bi bi-bluetooth feature-icon"></i>
	                        <h3 class="feature-title">稳定连接</h3>
	                        <p class="feature-description">蓝牙5.2技术，连接更稳定，传输距离更远，延迟更低。</p>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </section>
	    <!-- 购买区域 -->
	    <section class="container">
	        <div class="purchase-section">
	            <h3 class="mb-4">选择购买方式</h3>
	            <div class="row align-items-center">
	                <div class="col-md-8">
	                    <div class="mb-3">
	                        <label class="form-label">颜色选择：</label>
	                        <div class="btn-group" role="group">
	                            <input type="radio" class="btn-check" name="color" id="color1" checked>
	                            <label class="btn btn-outline-primary" for="color1">经典黑</label>
	                            <input type="radio" class="btn-check" name="color" id="color2">
	                            <label class="btn btn-outline-primary" for="color2">珍珠白</label>
	                            <input type="radio" class="btn-check" name="color" id="color3">
	                            <label class="btn btn-outline-primary" for="color3">海洋蓝</label>
	                        </div>
	                    </div>
	                    <div class="mb-3">
	                        <label class="form-label">数量：</label>
	                        <div class="input-group" style="width: 150px;">
	                            <button class="btn btn-outline-secondary" type="button" onclick="decreaseQuantity()">-</button>
	                            <input type="text" class="form-control text-center" id="quantity" value="1" readonly>
	                            <button class="btn btn-outline-secondary" type="button" onclick="increaseQuantity()">+</button>
	                        </div>
	                    </div>
	                </div>
	                <div class="col-md-4 text-md-end mt-3 mt-md-0">
	                    <a href="#" class="btn-purchase" onclick="purchaseNow()">立即购买</a>
	                    <a href="#" class="btn-add-cart" onclick="addToCart()"><i class="bi bi-cart-plus"></i> 加入购物车</a>
	                </div>
	            </div>
	        </div>
	    </section>
	    <!-- 相关商品推荐 -->
	    <section class="related-products">
	        <div class="container">
	            <h2 class="section-title">相关商品推荐</h2>
	            <div class="row">
	                <div class="col-lg-3 col-md-6 mb-4">
	                    <div class="related-product-card">
	                        <img src="https://picsum.photos/seed/speaker1/400/300.jpg" alt="蓝牙音箱" class="related-product-img">
	                        <div class="related-product-body">
	                            <h4 class="related-product-title">便携蓝牙音箱</h4>
	                            <p class="related-product-price">¥299</p>
	                        </div>
	                    </div>
	                </div>
	                <div class="col-lg-3 col-md-6 mb-4">
	                    <div class="related-product-card">
	                        <img src="https://picsum.photos/seed/watch1/400/300.jpg" alt="智能手表" class="related-product-img">
	                        <div class="related-product-body">
	                            <h4 class="related-product-title">运动智能手表</h4>
	                            <p class="related-product-price">¥1299</p>
	                        </div>
	                    </div>
	                </div>
	                <div class="col-lg-3 col-md-6 mb-4">
	                    <div class="related-product-card">
	                        <img src="https://picsum.photos/seed/phone1/400/300.jpg" alt="手机支架" class="related-product-img">
	                        <div class="related-product-body">
	                            <h4 class="related-product-title">多功能手机支架</h4>
	                            <p class="related-product-price">¥89</p>
	                        </div>
	                    </div>
	                </div>
	                <div class="col-lg-3 col-md-6 mb-4">
	                    <div class="related-product-card">
	                        <img src="https://picsum.photos/seed/cable1/400/300.jpg" alt="数据线" class="related-product-img">
	                        <div class="related-product-body">
	                            <h4 class="related-product-title">快充数据线</h4>
	                            <p class="related-product-price">¥39</p>
	                        </div>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </section>
	    <!-- 页脚 -->
	    <footer class="footer">
	        <div class="container">
	            <div class="row">
	                <div class="col-lg-4 mb-4">
	                    <h5>关于我们</h5>
	                    <p>TechShop是一家专注于高品质科技产品的在线商店，致力于为客户提供最新、最优质的科技产品和最贴心的服务。</p>
	                    <div class="social-icons">
	                        <a href="#" class="social-icon"><i class="bi bi-facebook"></i></a>
	                        <a href="#" class="social-icon"><i class="bi bi-twitter"></i></a>
	                        <a href="#" class="social-icon"><i class="bi bi-instagram"></i></a>
	                        <a href="#" class="social-icon"><i class="bi bi-wechat"></i></a>
	                    </div>
	                </div>
	                <div class="col-lg-2 col-md-6 mb-4">
	                    <h5>快速链接</h5>
	                    <ul class="footer-links">
	                        <li><a href="#">首页</a></li>
	                        <li><a href="#">商品分类</a></li>
	                        <li><a href="#">新品上市</a></li>
	                        <li><a href="#">热销排行</a></li>
	                        <li><a href="#">关于我们</a></li>
	                    </ul>
	                </div>
	                <div class="col-lg-2 col-md-6 mb-4">
	                    <h5>客户服务</h5>
	                    <ul class="footer-links">
	                        <li><a href="#">联系我们</a></li>
	                        <li><a href="#">退换货政策</a></li>
	                        <li><a href="#">配送信息</a></li>
	                        <li><a href="#">常见问题</a></li>
	                        <li><a href="#">隐私政策</a></li>
	                    </ul>
	                </div>
	                <div class="col-lg-4 mb-4">
	                    <h5>联系我们</h5>
	                    <ul class="footer-links">
	                        <li><i class="bi bi-geo-alt"></i> 北京市朝阳区科技园区88号</li>
	                        <li><i class="bi bi-telephone"></i> ************</li>
	                        <li><i class="bi bi-envelope"></i> <EMAIL></li>
	                    </ul>
	                </div>
	            </div>
	            <div class="copyright">
	                <p>&copy; 2025 TechShop. 保留所有权利。</p>
	            </div>
	        </div>
	    </footer>
	    <!-- 图片放大模态框 -->
	    <div id="imageModal" class="image-modal">
	        <span class="close-modal">&times;</span>
	        <img class="modal-image" id="modalImage">
	    </div>
	    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
	    <script>
	        // 切换主图
	        function changeImage(thumbnail) {
	            const mainImage = document.getElementById('main-product-image');
	            mainImage.src = thumbnail.src;
	            // 更新缩略图活动状态
	            const thumbnails = document.querySelectorAll('.thumbnail');
	            thumbnails.forEach(thumb => {
	                thumb.classList.remove('active');
	            });
	            thumbnail.classList.add('active');
	        }
	        // 增加数量
	        function increaseQuantity() {
	            const quantityInput = document.getElementById('quantity');
	            let quantity = parseInt(quantityInput.value);
	            quantity++;
	            quantityInput.value = quantity;
	        }
	        // 减少数量
	        function decreaseQuantity() {
	            const quantityInput = document.getElementById('quantity');
	            let quantity = parseInt(quantityInput.value);
	            if (quantity > 1) {
	                quantity--;
	                quantityInput.value = quantity;
	            }
	        }
	        // 立即购买
	        function purchaseNow() {
	            const quantity = document.getElementById('quantity').value;
	            const color = document.querySelector('input[name="color"]:checked').nextElementSibling.innerText;
	            alert(`您已选择：${color}，数量：${quantity}\n即将跳转到支付页面...`);
	            // 实际应用中这里应该跳转到支付页面
	        }
	        // 加入购物车
	        function addToCart() {
	            const quantity = document.getElementById('quantity').value;
	            const color = document.querySelector('input[name="color"]:checked').nextElementSibling.innerText;
	            alert(`已将 ${quantity} 件${color}的智能无线耳机 Pro 加入购物车！`);
	            // 实际应用中这里应该添加到购物车
	        }
	        // 图片放大功能
	        const productImageContainer = document.querySelector('.product-image-container');
	        const imageModal = document.getElementById('imageModal');
	        const modalImage = document.getElementById('modalImage');
	        const closeModal = document.querySelector('.close-modal');
	        productImageContainer.addEventListener('click', function() {
	            imageModal.style.display = 'flex';
	            modalImage.src = document.getElementById('main-product-image').src;
	        });
	        closeModal.addEventListener('click', function() {
	            imageModal.style.display = 'none';
	        });
	        window.addEventListener('click', function(event) {
	            if (event.target == imageModal) {
	                imageModal.style.display = 'none';
	            }
	        });
	    </script>
	</body>
	</html>