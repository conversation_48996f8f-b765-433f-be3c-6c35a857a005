import { NestFactory } from '@nestjs/core';
import { Val<PERSON><PERSON>Pipe, RequestMethod } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { ResponseTransformInterceptor } from './common/interceptors/response-transform.interceptor';
import envConfig, { initializeConfig } from './config/env.config';
import * as express from 'express';

async function bootstrap() {
  // 初始化统一配置系统
  initializeConfig();

  const app = await NestFactory.create(AppModule);

  // ====================================
  // Express中间件配置（处理中文编码）
  // ====================================
  
  // 配置body-parser支持中文字符
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '50mb',
    parameterLimit: 50000 
  }));

  // ====================================
  // 全局配置
  // ====================================

  // 全局前缀 (排除根路径和favicon)
  app.setGlobalPrefix('api', {
    exclude: [
      { path: '', method: RequestMethod.GET },          // 根路径
      { path: 'favicon.ico', method: RequestMethod.GET }, // favicon
    ],
  });

  // CORS配置 (使用统一配置)
  app.enableCors({
    origin: envConfig.cors.allowedOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });

  // ====================================
  // 全局管道、过滤器、拦截器
  // ====================================

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // 全局异常过滤器
  app.useGlobalFilters(new GlobalExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new ResponseTransformInterceptor(),
  );

  // ====================================
  // Swagger API 文档配置
  // ====================================

  if (envConfig.development.apiDocsEnabled) {
    const config = new DocumentBuilder()
      .setTitle('OnlyOffice集成系统 API')
      .setDescription(`
        OnlyOffice文档管理系统的RESTful API文档
        
        ## 功能模块
        - 🏥 **健康检查**: 系统状态监控
        - 📄 **文档管理**: OnlyOffice文档的CRUD操作
        - 👤 **用户认证**: JWT身份验证和权限管理
        - ⚙️ **配置管理**: 系统配置和模板管理
        - 🗄️ **FileNet集成**: 企业内容管理系统集成
        
        ## 认证方式
        - Bearer Token (JWT)
        
        ## 响应格式
        所有API响应遵循统一格式：
        \`\`\`json
        {
          "success": true,
          "data": {...},
          "message": "操作成功",
          "timestamp": "2024-12-19T10:30:00.000Z",
          "requestId": "uuid-string"
        }
        \`\`\`
      `)
      .setVersion('2.0.0')
      .setContact(
        'OnlyOffice Integration Team',
        'https://github.com/your-org/onlyoffice-integration',
        '<EMAIL>',
      )
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: '请输入JWT token (无需Bearer前缀)',
        },
        'JWT-auth',
      )
      .addTag('Health', '系统健康检查相关接口')
      .addTag('Auth', '用户认证相关接口')
      .addTag('系统信息', '系统信息和欢迎页面相关接口')
      .addTag('用户管理', '用户管理相关接口')
      .addTag('文档管理', '文档管理相关接口')
      .addTag('文档模板管理', '文档模板管理相关接口')
      .addTag('配置管理', '配置管理相关接口')
      .addTag('FileNet集成', 'FileNet集成相关接口')
      .addTag('文件上传', '文件上传管理相关接口')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup(envConfig.development.swaggerEndpoint.replace('/', ''), app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: 'OnlyOffice API 文档',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1890ff }
      `,
    });
  }

  // ====================================
  // 服务器启动
  // ====================================

  await app.listen(envConfig.port);

  // 启动日志 (使用统一配置)
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] INFO: 🚀 NestJS服务器启动成功`);
  console.log(`[${timestamp}] INFO: 📍 服务地址: http://${envConfig.server.host}:${envConfig.port}`);
  console.log(`[${timestamp}] INFO: 🏠 系统首页: http://${envConfig.server.host}:${envConfig.port}/`);
  console.log(`[${timestamp}] INFO: 🏥 健康检查: http://${envConfig.server.host}:${envConfig.port}/api/health`);
  
  if (envConfig.development.apiDocsEnabled) {
    console.log(`[${timestamp}] INFO: 📚 API文档: http://${envConfig.server.host}:${envConfig.port}${envConfig.development.swaggerEndpoint}`);
  }
  
  console.log(`[${timestamp}] INFO: 🌍 环境: ${envConfig.node_env}`);
  console.log(`[${timestamp}] INFO: ✅ OnlyOffice集成系统(NestJS版)已就绪`);
  console.log(`[${timestamp}] INFO: 📁 环境配置: 使用根目录 .env 文件`);
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('[服务器] 接收到SIGTERM信号，开始优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('[服务器] 接收到SIGINT信号，开始优雅关闭...');
  process.exit(0);
});

bootstrap();

// 热模块替换（HMR）支持
declare const module: {
  hot?: {
    accept(): void;
    dispose(callback: () => void): void;
  };
};
if (module.hot) {
  module.hot.accept();
  module.hot.dispose(() => {
    console.log('[HMR] 热模块替换：正在重新加载应用...');
  });
} 