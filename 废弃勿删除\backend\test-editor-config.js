const axios = require('axios');

/**
 * 测试OnlyOffice编辑器配置
 * 验证新后端的配置生成是否正确
 */
async function testEditorConfig() {
    const serverUrl = 'http://localhost:3000';
    
    // 测试用的文档ID（需要是数据库中存在的）
    const testDocId = 'YOUR_TEST_DOC_ID'; // 替换为实际的文档ID
    
    console.log('=== OnlyOffice编辑器配置测试 ===');
    console.log(`服务器地址: ${serverUrl}`);
    console.log(`测试文档ID: ${testDocId}`);
    console.log('');
    
    try {
        // 1. 测试编辑器配置获取
        console.log('1. 获取编辑器配置...');
        const configResponse = await axios.get(`${serverUrl}/api/editor/${testDocId}/config`, {
            timeout: 10000
        });
        
        console.log(`配置获取状态: ${configResponse.status}`);
        console.log(`响应格式检查:`);
        console.log(`- success: ${configResponse.data.success}`);
        console.log(`- message: ${configResponse.data.message}`);
        console.log(`- 包含data: ${!!configResponse.data.data}`);
        
        if (configResponse.data.success && configResponse.data.data) {
            const config = configResponse.data.data;
            console.log('');
            console.log('配置内容验证:');
            console.log(`- 文档URL: ${config.document?.url}`);
            console.log(`- 回调URL: ${config.editorConfig?.callbackUrl}`);
            console.log(`- 文档类型: ${config.documentType}`);
            console.log(`- 文档密钥: ${config.document?.key}`);
            console.log(`- API脚本URL: ${config.apiUrl}`);
            console.log(`- 权限配置: ${JSON.stringify(config.document?.permissions)}`);
            
            // 检查关键配置项
            const issues = [];
            if (!config.document?.url) issues.push('缺少文档URL');
            if (!config.editorConfig?.callbackUrl) issues.push('缺少回调URL');
            if (!config.document?.key) issues.push('缺少文档密钥');
            if (!config.apiUrl) issues.push('缺少API脚本URL');
            
            if (issues.length > 0) {
                console.log('');
                console.log('⚠️  配置问题:');
                issues.forEach(issue => console.log(`   - ${issue}`));
            } else {
                console.log('');
                console.log('✅ 配置验证通过');
            }
            
            // 验证URL格式
            console.log('');
            console.log('URL格式验证:');
            try {
                new URL(config.document.url);
                console.log('✅ 文档URL格式正确');
            } catch (e) {
                console.log('❌ 文档URL格式错误');
            }
            
            try {
                new URL(config.editorConfig.callbackUrl);
                console.log('✅ 回调URL格式正确');
            } catch (e) {
                console.log('❌ 回调URL格式错误');
            }
            
            try {
                new URL(config.apiUrl);
                console.log('✅ API脚本URL格式正确');
            } catch (e) {
                console.log('❌ API脚本URL格式错误');
            }
        }
        
        // 2. 测试文档直接访问
        console.log('');
        console.log('2. 测试文档直接访问...');
        try {
            const docResponse = await axios.head(`${serverUrl}/api/documents/${testDocId}`, {
                timeout: 10000
            });
            console.log(`✅ 文档直接访问成功 (状态: ${docResponse.status})`);
            console.log(`   Content-Type: ${docResponse.headers['content-type']}`);
            console.log(`   Content-Length: ${docResponse.headers['content-length']}`);
        } catch (docError) {
            console.log(`❌ 文档直接访问失败: ${docError.response?.status || docError.message}`);
        }
        
        // 3. 测试回调端点可访问性
        console.log('');
        console.log('3. 测试回调端点...');
        try {
            const callbackResponse = await axios.post(`${serverUrl}/api/editor/callback`, {
                key: 'test-key',
                status: 1
            }, {
                timeout: 5000
            });
            console.log(`✅ 回调端点可访问 (状态: ${callbackResponse.status})`);
        } catch (callbackError) {
            if (callbackError.response?.status === 200) {
                console.log('✅ 回调端点可访问');
            } else {
                console.log(`⚠️  回调端点问题: ${callbackError.response?.status || callbackError.message}`);
            }
        }
        
    } catch (error) {
        console.error('测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 如果直接运行脚本
if (require.main === module) {
    // 检查是否提供了文档ID
    const docId = process.argv[2];
    if (!docId) {
        console.log('使用方法: node test-editor-config.js <文档ID>');
        console.log('例如: node test-editor-config.js 123e4567-e89b-12d3-a456-426614174000');
        process.exit(1);
    }
    
    // 替换测试文档ID
    const script = `
const axios = require('axios');
${testEditorConfig.toString()}

testEditorConfig().catch(console.error);
`.replace('YOUR_TEST_DOC_ID', docId);
    
    eval(script);
} else {
    module.exports = { testEditorConfig };
} 