import{j as t,k as re,d as He,r as w,z as Je,o as Qe,q as i,c as N,s as a,e as f,b as z,m as b,h as _,v as y,P as Ge,i as o,R as We,B as Xe,t as k,x as h,H as Z,F as ee,E as te,O as Ye,K as ae,C as Ze,a1 as le,J as ne,_ as et}from"./index-5218909a.js";import{U as tt}from"./users.api-4d7d2ba0.js";import{T as at}from"./TeamOutlined-bfa2ee8d.js";import{U as lt}from"./UploadOutlined-25037a42.js";import{S as oe}from"./StopOutlined-6978af33.js";import{M as nt}from"./MoreOutlined-59e0773e.js";var ot={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};const st=ot;function se(v){for(var r=1;r<arguments.length;r++){var u=arguments[r]!=null?Object(arguments[r]):{},m=Object.keys(u);typeof Object.getOwnPropertySymbols=="function"&&(m=m.concat(Object.getOwnPropertySymbols(u).filter(function(p){return Object.getOwnPropertyDescriptor(u,p).enumerable}))),m.forEach(function(p){ut(v,p,u[p])})}return v}function ut(v,r,u){return r in v?Object.defineProperty(v,r,{value:u,enumerable:!0,configurable:!0,writable:!0}):v[r]=u,v}var K=function(r,u){var m=se({},r,u.attrs);return t(re,se({},m,{icon:st}),null)};K.displayName="CrownOutlined";K.inheritAttrs=!1;const rt=K;var it={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M608 112c-167.9 0-304 136.1-304 304 0 70.3 23.9 135 63.9 186.5l-41.1 41.1-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-44.9 44.9-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-65.3 65.3a8.03 8.03 0 000 11.3l42.3 42.3c3.1 3.1 8.2 3.1 11.3 0l253.6-253.6A304.06 304.06 0 00608 720c167.9 0 304-136.1 304-304S775.9 112 608 112zm161.2 465.2C726.2 620.3 668.9 644 608 644c-60.9 0-118.2-23.7-161.2-66.8-43.1-43-66.8-100.3-66.8-161.2 0-60.9 23.7-118.2 66.8-161.2 43-43.1 100.3-66.8 161.2-66.8 60.9 0 118.2 23.7 161.2 66.8 43.1 43 66.8 100.3 66.8 161.2 0 60.9-23.7 118.2-66.8 161.2z"}}]},name:"key",theme:"outlined"};const dt=it;function ue(v){for(var r=1;r<arguments.length;r++){var u=arguments[r]!=null?Object(arguments[r]):{},m=Object.keys(u);typeof Object.getOwnPropertySymbols=="function"&&(m=m.concat(Object.getOwnPropertySymbols(u).filter(function(p){return Object.getOwnPropertyDescriptor(u,p).enumerable}))),m.forEach(function(p){vt(v,p,u[p])})}return v}function vt(v,r,u){return r in v?Object.defineProperty(v,r,{value:u,enumerable:!0,configurable:!0,writable:!0}):v[r]=u,v}var H=function(r,u){var m=ue({},r,u.attrs);return t(re,ue({},m,{icon:dt}),null)};H.displayName="KeyOutlined";H.inheritAttrs=!1;const mt=H,pt={class:"users-page"},_t={class:"content-wrapper"},ft={class:"search-section"},ct={key:0,class:"user-info"},gt={class:"user-details"},wt={class:"username"},yt={class:"email"},Ct={key:0},bt={key:1,class:"text-gray"},kt={class:"role-management"},Ot={class:"current-user"},St={class:"user-info"},xt=He({__name:"index",setup(v){const r=w(!1),u=w(""),m=w(),p=w(),M=w(!1),L=w(!1),$=w(!1),R=w(null),B=w(""),O=w({current:1,pageSize:20,total:0}),d=w({username:"",email:"",realName:"",phone:"",role:"",status:"active",password:"",remarks:""}),ie={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度为3-20个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],role:[{required:!0,message:"请选择用户角色",trigger:"change"}],password:[{required:!0,message:"请输入初始密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20个字符",trigger:"blur"}]},V=w([]),I=async()=>{r.value=!0;try{const l=await tt.getUsers({page:O.value.current,pageSize:O.value.pageSize});V.value=l.data.map(e=>({id:e.id,username:e.username,email:e.email,fullName:e.full_name||e.fullName,realName:e.full_name||e.fullName,phone:e.phone||"",role:e.role_id==="role-super-admin"?"admin":e.role_id==="role-editor"?"editor":"viewer",status:e.status==="active"?"active":"inactive",createdAt:e.created_at,lastLoginAt:e.last_login_at,lastLogin:e.last_login_at,createTime:e.created_at,permissions:e.role_id==="role-super-admin"?["all"]:["document:read"],remarks:""})),O.value.total=l.total}catch(l){console.error("加载用户数据失败:",l),b.error("加载用户数据失败"),V.value=[],O.value.total=0}finally{r.value=!1}},de=[{title:"用户信息",key:"user",width:"25%"},{title:"角色",key:"role",width:"12%",filters:[{text:"管理员",value:"admin"},{text:"编辑员",value:"editor"},{text:"查看员",value:"viewer"}]},{title:"状态",key:"status",width:"12%",filters:[{text:"已激活",value:"active"},{text:"未激活",value:"inactive"},{text:"已停用",value:"suspended"}]},{title:"手机号",dataIndex:"phone",width:"12%"},{title:"最后登录",key:"lastLogin",width:"15%"},{title:"创建时间",key:"createTime",width:"12%"},{title:"操作",key:"actions",width:"12%"}],ve=Je(()=>{let l=V.value;return u.value&&(l=l.filter(e=>{var C;return e.username.toLowerCase().includes(u.value.toLowerCase())||e.email.toLowerCase().includes(u.value.toLowerCase())||((C=e.realName)==null?void 0:C.toLowerCase().includes(u.value.toLowerCase()))})),m.value&&(l=l.filter(e=>e.role===m.value)),p.value&&(l=l.filter(e=>e.status===p.value)),l}),me=l=>{const e=new Date(l),U=new Date().getTime()-e.getTime(),P=Math.floor(U/(1e3*60)),T=Math.floor(U/(1e3*60*60)),c=Math.floor(U/(1e3*60*60*24));return P<60?`${P}分钟前`:T<24?`${T}小时前`:`${c}天前`},pe=l=>new Date(l).toLocaleDateString("zh-CN"),_e=l=>({admin:"管理员",editor:"编辑员",viewer:"查看员"})[l]||l,fe=l=>({admin:"red",editor:"blue",viewer:"green"})[l]||"default",ce=l=>({admin:rt,editor:te,viewer:le})[l]||le,ge=l=>({active:"已激活",inactive:"未激活",suspended:"已停用"})[l]||l,we=l=>({active:"success",inactive:"warning",suspended:"error"})[l]||"default",ye=l=>({active:ae,inactive:ne,suspended:oe})[l]||ne,Ce=()=>{},be=()=>{},ke=()=>{},Oe=async()=>{await I(),b.success("刷新成功")},Se=()=>{b.info("用户数据导出功能开发中...")},xe=l=>{O.value.current=l.current,O.value.pageSize=l.pageSize,I()},Ue=()=>{$.value=!1,d.value={username:"",email:"",realName:"",phone:"",role:"",status:"active",password:"",remarks:""},M.value=!0},he=()=>{b.info("批量导入用户功能开发中...")},Me=l=>{$.value=!0,d.value={username:l.username,email:l.email,realName:l.realName||l.fullName,phone:l.phone||"",role:l.role,status:l.status,password:"",remarks:l.remarks||""},M.value=!0},$e=l=>{b.info(`重置用户 ${l.username} 的密码`)},Ne=l=>{b.info(`查看用户 ${l.username} 的详情`)},ze=l=>{R.value=l,B.value=l.role,L.value=!0},Le=l=>{b.info(`停用用户 ${l.username}`)},Re=l=>{b.info(`激活用户 ${l.username}`)},De=l=>{b.info(`删除用户 ${l.username}`)},Pe=()=>{b.success($.value?"用户更新成功！":"用户创建成功！"),M.value=!1},Te=()=>{M.value=!1},Ae=()=>{b.success("用户角色更新成功！"),L.value=!1},je=()=>{L.value=!1};return Qe(()=>{I()}),(l,e)=>{const C=i("a-button"),U=i("a-space"),P=i("a-page-header"),T=i("a-input-search"),c=i("a-col"),g=i("a-select-option"),A=i("a-select"),j=i("a-row"),J=i("a-avatar"),Q=i("a-tag"),D=i("a-menu-item"),G=i("a-menu-divider"),qe=i("a-menu"),Ee=i("a-dropdown"),Be=i("a-table"),q=i("a-input"),S=i("a-form-item"),Ve=i("a-input-password"),Ie=i("a-textarea"),W=i("a-form"),X=i("a-modal"),Fe=i("a-divider"),F=i("a-radio"),Ke=i("a-radio-group");return _(),N("div",pt,[t(P,{title:"用户管理","sub-title":"管理系统用户和权限"},{extra:a(()=>[t(U,null,{default:a(()=>[t(C,{type:"primary",onClick:Ue},{icon:a(()=>[t(y(Ge))]),default:a(()=>[e[14]||(e[14]=o(" 新建用户 "))]),_:1,__:[14]}),t(C,{onClick:he},{icon:a(()=>[t(y(lt))]),default:a(()=>[e[15]||(e[15]=o(" 批量导入 "))]),_:1,__:[15]})]),_:1})]),_:1}),f("div",_t,[z(" 搜索和筛选区域 "),f("div",ft,[t(j,{gutter:16},{default:a(()=>[t(c,{span:6},{default:a(()=>[t(T,{value:u.value,"onUpdate:value":e[0]||(e[0]=n=>u.value=n),placeholder:"搜索用户名、邮箱...",onSearch:Ce},null,8,["value"])]),_:1}),t(c,{span:4},{default:a(()=>[t(A,{value:m.value,"onUpdate:value":e[1]||(e[1]=n=>m.value=n),placeholder:"选择角色","allow-clear":"",onChange:be},{default:a(()=>[t(g,{value:"admin"},{default:a(()=>e[16]||(e[16]=[o(" 管理员 ")])),_:1,__:[16]}),t(g,{value:"editor"},{default:a(()=>e[17]||(e[17]=[o(" 编辑员 ")])),_:1,__:[17]}),t(g,{value:"viewer"},{default:a(()=>e[18]||(e[18]=[o(" 查看员 ")])),_:1,__:[18]})]),_:1},8,["value"])]),_:1}),t(c,{span:4},{default:a(()=>[t(A,{value:p.value,"onUpdate:value":e[2]||(e[2]=n=>p.value=n),placeholder:"选择状态","allow-clear":"",onChange:ke},{default:a(()=>[t(g,{value:"active"},{default:a(()=>e[19]||(e[19]=[o(" 已激活 ")])),_:1,__:[19]}),t(g,{value:"inactive"},{default:a(()=>e[20]||(e[20]=[o(" 未激活 ")])),_:1,__:[20]}),t(g,{value:"suspended"},{default:a(()=>e[21]||(e[21]=[o(" 已停用 ")])),_:1,__:[21]})]),_:1},8,["value"])]),_:1}),t(c,{span:10},{default:a(()=>[t(U,null,{default:a(()=>[t(C,{onClick:Oe},{icon:a(()=>[t(y(We))]),default:a(()=>[e[22]||(e[22]=o(" 刷新 "))]),_:1,__:[22]}),t(C,{onClick:Se},{icon:a(()=>[t(y(Xe))]),default:a(()=>[e[23]||(e[23]=o(" 导出用户 "))]),_:1,__:[23]})]),_:1})]),_:1})]),_:1})]),z(" 用户列表 "),t(Be,{columns:de,"data-source":ve.value,loading:r.value,pagination:{current:O.value.current,pageSize:O.value.pageSize,total:O.value.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(n,s)=>`第 ${s[0]}-${s[1]} 条，共 ${n} 个用户`},"row-key":"id",onChange:xe},{bodyCell:a(({column:n,record:s})=>[n.key==="user"?(_(),N("div",ct,[t(J,{size:40,src:s.avatar},{default:a(()=>[o(k(s.username.charAt(0).toUpperCase()),1)]),_:2},1032,["src"]),f("div",gt,[f("div",wt,k(s.username),1),f("div",yt,k(s.email),1)])])):n.key==="role"?(_(),h(Q,{key:1,color:fe(s.role)},{default:a(()=>[(_(),h(Z(ce(s.role)))),o(" "+k(_e(s.role)),1)]),_:2},1032,["color"])):n.key==="status"?(_(),h(Q,{key:2,color:we(s.status)},{default:a(()=>[(_(),h(Z(ye(s.status)))),o(" "+k(ge(s.status)),1)]),_:2},1032,["color"])):n.key==="lastLogin"?(_(),N(ee,{key:3},[s.lastLogin?(_(),N("span",Ct,k(me(s.lastLogin)),1)):(_(),N("span",bt,"从未登录"))],64)):n.key==="createTime"?(_(),N(ee,{key:4},[o(k(pe(s.createTime)),1)],64)):n.key==="actions"?(_(),h(U,{key:5},{default:a(()=>[t(C,{type:"text",size:"small",onClick:x=>Me(s)},{icon:a(()=>[t(y(te))]),default:a(()=>[e[24]||(e[24]=o(" 编辑 "))]),_:2,__:[24]},1032,["onClick"]),t(C,{type:"text",size:"small",disabled:s.status==="suspended",onClick:x=>$e(s)},{icon:a(()=>[t(y(mt))]),default:a(()=>[e[25]||(e[25]=o(" 重置密码 "))]),_:2,__:[25]},1032,["disabled","onClick"]),t(Ee,null,{overlay:a(()=>[t(qe,null,{default:a(()=>[t(D,{onClick:x=>Ne(s)},{default:a(()=>[t(y(Ye)),e[26]||(e[26]=o(" 查看详情 "))]),_:2,__:[26]},1032,["onClick"]),t(D,{onClick:x=>ze(s)},{default:a(()=>[t(y(at)),e[27]||(e[27]=o(" 管理角色 "))]),_:2,__:[27]},1032,["onClick"]),t(G),s.status==="active"?(_(),h(D,{key:0,class:"warning-item",onClick:x=>Le(s)},{default:a(()=>[t(y(oe)),e[28]||(e[28]=o(" 停用用户 "))]),_:2,__:[28]},1032,["onClick"])):(_(),h(D,{key:1,onClick:x=>Re(s),class:"success-item"},{default:a(()=>[t(y(ae)),e[29]||(e[29]=o(" 激活用户 "))]),_:2,__:[29]},1032,["onClick"])),t(G),t(D,{class:"danger-item",onClick:x=>De(s)},{default:a(()=>[t(y(Ze)),e[30]||(e[30]=o(" 删除用户 "))]),_:2,__:[30]},1032,["onClick"])]),_:2},1024)]),default:a(()=>[t(C,{type:"text",size:"small"},{icon:a(()=>[t(y(nt))]),_:1})]),_:2},1024)]),_:2},1024)):z("v-if",!0)]),_:1},8,["data-source","loading","pagination"])]),z(" 新建/编辑用户弹窗 "),t(X,{open:M.value,"onUpdate:open":e[11]||(e[11]=n=>M.value=n),title:$.value?"编辑用户":"新建用户",width:"600px",onOk:Pe,onCancel:Te},{default:a(()=>[t(W,{model:d.value,layout:"vertical",rules:ie},{default:a(()=>[t(j,{gutter:16},{default:a(()=>[t(c,{span:12},{default:a(()=>[t(S,{label:"用户名",name:"username",required:""},{default:a(()=>[t(q,{value:d.value.username,"onUpdate:value":e[3]||(e[3]=n=>d.value.username=n),placeholder:"请输入用户名",disabled:$.value},null,8,["value","disabled"])]),_:1})]),_:1}),t(c,{span:12},{default:a(()=>[t(S,{label:"邮箱",name:"email",required:""},{default:a(()=>[t(q,{value:d.value.email,"onUpdate:value":e[4]||(e[4]=n=>d.value.email=n),placeholder:"请输入邮箱"},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(j,{gutter:16},{default:a(()=>[t(c,{span:12},{default:a(()=>[t(S,{label:"姓名",name:"realName"},{default:a(()=>[t(q,{value:d.value.realName,"onUpdate:value":e[5]||(e[5]=n=>d.value.realName=n),placeholder:"请输入真实姓名"},null,8,["value"])]),_:1})]),_:1}),t(c,{span:12},{default:a(()=>[t(S,{label:"手机号",name:"phone"},{default:a(()=>[t(q,{value:d.value.phone,"onUpdate:value":e[6]||(e[6]=n=>d.value.phone=n),placeholder:"请输入手机号"},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(j,{gutter:16},{default:a(()=>[t(c,{span:12},{default:a(()=>[t(S,{label:"角色",name:"role",required:""},{default:a(()=>[t(A,{value:d.value.role,"onUpdate:value":e[7]||(e[7]=n=>d.value.role=n),placeholder:"选择用户角色"},{default:a(()=>[t(g,{value:"admin"},{default:a(()=>e[31]||(e[31]=[o(" 管理员 ")])),_:1,__:[31]}),t(g,{value:"editor"},{default:a(()=>e[32]||(e[32]=[o(" 编辑员 ")])),_:1,__:[32]}),t(g,{value:"viewer"},{default:a(()=>e[33]||(e[33]=[o(" 查看员 ")])),_:1,__:[33]})]),_:1},8,["value"])]),_:1})]),_:1}),t(c,{span:12},{default:a(()=>[t(S,{label:"状态",name:"status"},{default:a(()=>[t(A,{value:d.value.status,"onUpdate:value":e[8]||(e[8]=n=>d.value.status=n),placeholder:"选择用户状态"},{default:a(()=>[t(g,{value:"active"},{default:a(()=>e[34]||(e[34]=[o(" 已激活 ")])),_:1,__:[34]}),t(g,{value:"inactive"},{default:a(()=>e[35]||(e[35]=[o(" 未激活 ")])),_:1,__:[35]}),t(g,{value:"suspended"},{default:a(()=>e[36]||(e[36]=[o(" 已停用 ")])),_:1,__:[36]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),$.value?z("v-if",!0):(_(),h(S,{key:0,label:"初始密码",name:"password",required:""},{default:a(()=>[t(Ve,{value:d.value.password,"onUpdate:value":e[9]||(e[9]=n=>d.value.password=n),placeholder:"请输入初始密码"},null,8,["value"])]),_:1})),t(S,{label:"备注"},{default:a(()=>[t(Ie,{value:d.value.remarks,"onUpdate:value":e[10]||(e[10]=n=>d.value.remarks=n),rows:3,placeholder:"用户备注信息"},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","title"]),z(" 角色管理弹窗 "),t(X,{open:L.value,"onUpdate:open":e[13]||(e[13]=n=>L.value=n),title:"管理用户角色",width:"500px",onOk:Ae,onCancel:je},{default:a(()=>{var n,s,x;return[f("div",kt,[f("div",Ot,[t(J,{size:50,src:(n=R.value)==null?void 0:n.avatar},{default:a(()=>{var E,Y;return[o(k((Y=(E=R.value)==null?void 0:E.username)==null?void 0:Y.charAt(0).toUpperCase()),1)]}),_:1},8,["src"]),f("div",St,[f("h3",null,k((s=R.value)==null?void 0:s.username),1),f("p",null,k((x=R.value)==null?void 0:x.email),1)])]),t(Fe),t(W,{layout:"vertical"},{default:a(()=>[t(S,{label:"选择角色"},{default:a(()=>[t(Ke,{value:B.value,"onUpdate:value":e[12]||(e[12]=E=>B.value=E)},{default:a(()=>[t(U,{direction:"vertical"},{default:a(()=>[t(F,{value:"admin"},{default:a(()=>e[37]||(e[37]=[f("strong",null,"管理员",-1),o(" - 拥有系统所有权限 ")])),_:1,__:[37]}),t(F,{value:"editor"},{default:a(()=>e[38]||(e[38]=[f("strong",null,"编辑员",-1),o(" - 可以编辑和管理文档 ")])),_:1,__:[38]}),t(F,{value:"viewer"},{default:a(()=>e[39]||(e[39]=[f("strong",null,"查看员",-1),o(" - 只能查看文档 ")])),_:1,__:[39]})]),_:1})]),_:1},8,["value"])]),_:1})]),_:1})])]}),_:1},8,["open"])])}}});const Lt=et(xt,[["__scopeId","data-v-4ec9fb36"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Users/<USER>"]]);export{Lt as default};
