/**
 * 错误处理中间件
 */

/**
 * 统一错误处理中间件
 */
function errorHandler(err, req, res, next) {
    console.error('应用错误:', err);

    // 根据错误类型设置适当的状态码
    const statusCode = err.statusCode || 500;

    // 根据环境决定返回的错误详情
    const errorResponse = {
        success: false,
        message: err.message || '服务器内部错误',
    };

    // 只在开发环境返回错误堆栈
    if (process.env.NODE_ENV === 'development') {
        errorResponse.stack = err.stack;
        errorResponse.details = err.details || null;
    }

    res.status(statusCode).json(errorResponse);
}

/**
 * 404 处理中间件
 */
function notFoundHandler(req, res, next) {
    const err = new Error(`找不到请求的路径: ${req.originalUrl}`);
    err.statusCode = 404;
    next(err);
}

/**
 * API错误响应创建器
 * @param {string} message 错误信息
 * @param {number} statusCode HTTP状态码
 * @param {any} details 错误详情（仅开发环境显示）
 * @returns {Error} 自定义错误对象
 */
function apiError(message, statusCode = 400, details = null) {
    const error = new Error(message);
    error.statusCode = statusCode;
    error.details = details;
    return error;
}

module.exports = {
    errorHandler,
    notFoundHandler,
    apiError
}; 