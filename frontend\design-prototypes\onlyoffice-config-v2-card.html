<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 卡片版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #2d3748;
            line-height: 1.6;
        }

        .container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* 头部区域 */
        .header {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            margin-bottom: 32px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .app-logo {
            width: 56px;
            height: 56px;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .header-text h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 4px;
        }

        .header-text p {
            color: #718096;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }

        .btn-outline {
            background: white;
            color: #4f46e5;
            border: 2px solid #4f46e5;
        }

        .btn-outline:hover {
            background: #4f46e5;
            color: white;
        }

        /* 主要内容布局 */
        .main-layout {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 32px;
            margin-bottom: 32px;
        }

        /* 左侧模板卡片 */
        .templates-section {
            background: white;
            border-radius: 20px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            height: fit-content;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #1a202c;
        }

        .section-subtitle {
            font-size: 14px;
            color: #718096;
            margin-top: 4px;
        }

        .add-btn {
            width: 40px;
            height: 40px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .add-btn:hover {
            background: #4338ca;
            transform: scale(1.05);
        }

        .search-container {
            position: relative;
            margin-bottom: 24px;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: #f7fafc;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #4f46e5;
            background: white;
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 16px;
        }

        .template-card {
            background: #f7fafc;
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .template-card:hover {
            border-color: #4f46e5;
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
        }

        .template-card:hover::before {
            opacity: 1;
        }

        .template-card.active {
            border-color: #4f46e5;
            background: white;
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
        }

        .template-card.active::before {
            opacity: 1;
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .template-name {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .template-status {
            display: flex;
            gap: 8px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-default {
            background: #fed7d7;
            color: #c53030;
        }

        .status-active {
            background: #c6f6d5;
            color: #38a169;
        }

        .status-inactive {
            background: #e2e8f0;
            color: #718096;
        }

        .template-desc {
            color: #718096;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #a0aec0;
        }

        .template-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 8px;
            background: #e2e8f0;
            color: #718096;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .action-btn:hover {
            background: #cbd5e0;
            color: #4a5568;
        }

        /* 右侧配置区域 */
        .config-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            height: fit-content;
        }

        .config-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 32px;
        }

        .config-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .config-subtitle {
            opacity: 0.9;
            font-size: 16px;
        }

        .config-nav {
            background: #f7fafc;
            padding: 0 24px;
            display: flex;
            gap: 8px;
            overflow-x: auto;
        }

        .nav-tab {
            padding: 16px 24px;
            background: transparent;
            border: none;
            color: #718096;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 12px 12px 0 0;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .nav-tab.active {
            background: white;
            color: #4f46e5;
        }

        .nav-tab:hover {
            color: #4f46e5;
        }

        .config-content {
            padding: 32px;
            max-height: 600px;
            overflow-y: auto;
        }

        .config-group {
            margin-bottom: 40px;
        }

        .group-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .group-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .group-title {
            font-size: 20px;
            font-weight: 700;
            color: #1a202c;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
        }

        .config-card {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
        }

        .config-card:hover {
            border-color: #cbd5e0;
            background: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .config-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .config-item-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 4px;
        }

        .config-item-desc {
            font-size: 13px;
            color: #718096;
            line-height: 1.4;
        }

        .required-indicator {
            background: #f56565;
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .switch-controls {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .switch-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .switch-label {
            font-size: 14px;
            font-weight: 500;
            color: #4a5568;
        }

        .toggle-switch {
            position: relative;
            width: 52px;
            height: 28px;
            background: #e2e8f0;
            border-radius: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #4f46e5;
        }

        .toggle-thumb {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-thumb {
            transform: translateX(24px);
        }

        .config-status {
            margin-top: 16px;
            padding: 12px;
            background: rgba(79, 70, 229, 0.05);
            border-radius: 8px;
            border-left: 4px solid #4f46e5;
        }

        .status-text {
            font-size: 12px;
            color: #4f46e5;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .status-enabled {
            background: #48bb78;
        }

        .status-disabled {
            background: #cbd5e0;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: #718096;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 24px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #4a5568;
        }

        .empty-desc {
            font-size: 16px;
            line-height: 1.5;
        }

        /* 底部操作栏 */
        .action-bar {
            background: white;
            padding: 24px 32px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .save-info {
            color: #718096;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .templates-section {
                order: 2;
            }

            .config-section {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .config-grid {
                grid-template-columns: 1fr;
            }

            .config-content {
                padding: 24px;
            }

            .templates-section,
            .config-section {
                padding: 24px;
            }
        }

        /* 加载动画 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .template-card,
        .config-card {
            animation: slideInUp 0.3s ease;
        }

        /* 滚动条样式 */
        .config-content::-webkit-scrollbar {
            width: 6px;
        }

        .config-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .config-content::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .config-content::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="header-left">
                    <div class="app-logo">OO</div>
                    <div class="header-text">
                        <h1>OnlyOffice 配置中心</h1>
                        <p>统一管理文档编辑器的功能和权限配置</p>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-download"></i>
                        导入配置
                    </a>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i>
                        新建模板
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <div class="container">
        <div class="main-layout">
            <!-- 左侧模板列表 -->
            <aside class="templates-section">
                <div class="section-header">
                    <div>
                        <h2 class="section-title">配置模板</h2>
                        <p class="section-subtitle">选择要编辑的配置模板</p>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索模板名称或描述...">
                </div>

                <div class="templates-list">
                    <div class="template-card active">
                        <div class="template-header">
                            <div class="template-name">默认编辑模板</div>
                            <div class="template-status">
                                <span class="status-badge status-default">默认</span>
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            标准的文档编辑配置，包含完整的编辑功能和基本权限控制，适用于大多数编辑场景。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-19</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">协作审阅模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            专为团队协作设计，支持实时编辑、评论和修订追踪，提升团队协作效率。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-18</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">只读查看模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            仅允许查看和基本操作，适用于文档分享和只读访问场景。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-17</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">受限编辑模板</div>
                            <div class="template-status">
                                <span class="status-badge status-inactive">禁用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            限制部分高级功能，适用于外部用户或临时访问者。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-16</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧配置区域 -->
            <main class="config-section">
                <div class="config-header">
                    <h2 class="config-title">默认编辑模板配置</h2>
                    <p class="config-subtitle">配置文档编辑器的功能、权限和界面选项</p>
                </div>

                <nav class="config-nav">
                    <button class="nav-tab active">权限配置</button>
                    <button class="nav-tab">界面定制</button>
                    <button class="nav-tab">功能控制</button>
                    <button class="nav-tab">协作设置</button>
                    <button class="nav-tab">高级选项</button>
                </nav>

                <div class="config-content">
                    <div class="config-group">
                        <div class="group-header">
                            <div class="group-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="group-title">文档权限控制</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">编辑权限</div>
                                        <div class="config-item-desc">控制用户是否可以编辑文档内容</div>
                                    </div>
                                    <span class="required-indicator">必需</span>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许编辑</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">下载权限</div>
                                        <div class="config-item-desc">控制用户是否可以下载文档副本</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许下载</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">打印权限</div>
                                        <div class="config-item-desc">控制用户是否可以打印文档</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许打印</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-disabled"></div>
                                        功能已禁用且隐藏
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">评论权限</div>
                                        <div class="config-item-desc">控制用户是否可以添加和查看评论</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许评论</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">审阅权限</div>
                                        <div class="config-item-desc">控制修订模式和变更追踪功能</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">开启审阅</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">填写表单</div>
                                        <div class="config-item-desc">控制是否可以填写文档中的表单</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许填写</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-group">
                        <div class="group-header">
                            <div class="group-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h3 class="group-title">界面定制选项</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">显示工具栏</div>
                                        <div class="config-item-desc">控制编辑器顶部工具栏的显示</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">显示工具栏</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">显示状态栏</div>
                                        <div class="config-item-desc">控制编辑器底部状态栏的显示</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">显示状态栏</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">在界面中显示</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        功能已启用并可见
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-bar">
                    <div class="save-info">
                        <i class="fas fa-clock"></i>
                        最后保存于 12月19日 14:30
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            重置更改
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存配置
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模板卡片选择
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    templateCards.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 标签页切换
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    navTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 开关切换
            const toggleSwitches = document.querySelectorAll('.toggle-switch');
            toggleSwitches.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 更新状态显示
                    const card = this.closest('.config-card');
                    const statusText = card.querySelector('.status-text');
                    const statusIcon = card.querySelector('.status-icon');
                    const switches = card.querySelectorAll('.toggle-switch');
                    
                    const enableSwitch = switches[0];
                    const visibilitySwitch = switches[1];
                    
                    if (enableSwitch.classList.contains('active') && visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>功能已启用并可见';
                    } else if (enableSwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>功能已启用但隐藏';
                    } else if (visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>功能已禁用但可见';
                    } else {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>功能已禁用且隐藏';
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                templateCards.forEach(card => {
                    const name = card.querySelector('.template-name').textContent.toLowerCase();
                    const desc = card.querySelector('.template-desc').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || desc.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html> 