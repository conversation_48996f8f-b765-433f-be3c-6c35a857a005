<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档管理 - 卡片式布局风格 (版本B)</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }

    .container {
      max-width: 1600px;
      margin: 0 auto;
      padding: 20px;
    }

    /* 顶部卡片 */
    .top-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 30px;
      margin-bottom: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      flex-wrap: wrap;
      gap: 20px;
    }

    .title-section h1 {
      font-size: 32px;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 8px;
    }

    .title-section p {
      color: #666;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }

    .btn-secondary {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      border: 2px solid rgba(102, 126, 234, 0.2);
    }

    .btn-secondary:hover {
      background: rgba(102, 126, 234, 0.2);
    }

    /* 搜索和筛选区域 */
    .search-filter-card {
      background: white;
      border-radius: 16px;
      padding: 25px;
      margin-bottom: 20px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
    }

    .search-bar {
      display: flex;
      gap: 15px;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-input-group {
      flex: 1;
      min-width: 300px;
      position: relative;
    }

    .search-input {
      width: 100%;
      padding: 16px 50px 16px 20px;
      border: 2px solid #e8e8e8;
      border-radius: 16px;
      font-size: 15px;
      background: #f8f9fa;
      transition: all 0.3s ease;
    }

    .search-input:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }

    .search-icon {
      position: absolute;
      right: 18px;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 18px;
    }

    .filter-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .filter-chip {
      padding: 10px 18px;
      background: #f8f9fa;
      border: 2px solid #e8e8e8;
      border-radius: 25px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .filter-chip:hover,
    .filter-chip.active {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }

    /* 统计仪表板 */
    .stats-dashboard {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      border-radius: 18px;
      padding: 25px;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
    }

    .stat-card:nth-child(1)::before { background: linear-gradient(90deg, #667eea, #764ba2); }
    .stat-card:nth-child(2)::before { background: linear-gradient(90deg, #f093fb, #f5576c); }
    .stat-card:nth-child(3)::before { background: linear-gradient(90deg, #4facfe, #00f2fe); }
    .stat-card:nth-child(4)::before { background: linear-gradient(90deg, #43e97b, #38f9d7); }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      font-size: 32px;
      margin-bottom: 15px;
    }

    .stat-number {
      font-size: 36px;
      font-weight: 700;
      color: #333;
      margin-bottom: 8px;
    }

    .stat-label {
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }

    .stat-trend {
      font-size: 12px;
      margin-top: 8px;
      padding: 4px 8px;
      border-radius: 12px;
      display: inline-block;
    }

    .trend-up {
      background: #d4edda;
      color: #155724;
    }

    .trend-down {
      background: #f8d7da;
      color: #721c24;
    }

    /* 文档网格 */
    .documents-section {
      background: white;
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }

    .section-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .view-toggle {
      display: flex;
      background: #f8f9fa;
      border-radius: 12px;
      padding: 4px;
    }

    .view-btn {
      padding: 8px 16px;
      border: none;
      background: transparent;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .view-btn.active {
      background: white;
      color: #667eea;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .document-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 25px;
    }

    .document-card {
      background: white;
      border-radius: 16px;
      padding: 0;
      border: 2px solid #f0f0f0;
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;
      position: relative;
    }

    .document-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      border-color: #667eea;
    }

    .doc-header {
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e9ecef;
    }

    .doc-type-badge {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 15px;
    }

    .badge-word { background: #e3f2fd; color: #1976d2; }
    .badge-excel { background: #e8f5e8; color: #2e7d32; }
    .badge-ppt { background: #fff3e0; color: #f57c00; }
    .badge-pdf { background: #ffebee; color: #d32f2f; }

    .doc-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .doc-subtitle {
      font-size: 14px;
      color: #666;
    }

    .doc-body {
      padding: 20px;
    }

    .doc-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      font-size: 14px;
      color: #666;
    }

    .doc-progress {
      margin-bottom: 20px;
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
    }

    .progress-bar {
      height: 6px;
      background: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .doc-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .collaborators {
      display: flex;
      gap: -8px;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      border: 2px solid white;
      margin-left: -8px;
    }

    .doc-actions {
      display: flex;
      gap: 8px;
    }

    .action-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .action-icon:hover {
      background: #667eea;
      color: white;
      transform: scale(1.1);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .container {
        padding: 15px;
      }
      
      .page-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
      }
      
      .search-bar {
        flex-direction: column;
      }
      
      .search-input-group {
        min-width: auto;
      }
      
      .document-grid {
        grid-template-columns: 1fr;
      }
      
      .stats-dashboard {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 顶部标题卡片 -->
    <div class="top-card">
      <div class="page-header">
        <div class="title-section">
          <h1>📚 文档管理中心</h1>
          <p>智能文档管理，让协作更高效</p>
        </div>
        <div class="header-actions">
          <button class="btn btn-secondary">📊 数据分析</button>
          <button class="btn btn-primary">➕ 新建文档</button>
        </div>
      </div>

      <!-- 统计仪表板 -->
      <div class="stats-dashboard">
        <div class="stat-card">
          <div class="stat-icon">📄</div>
          <div class="stat-number">156</div>
          <div class="stat-label">总文档数</div>
          <div class="stat-trend trend-up">↗ +12%</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-number">24</div>
          <div class="stat-label">协作人数</div>
          <div class="stat-trend trend-up">↗ +8%</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">📈</div>
          <div class="stat-number">89%</div>
          <div class="stat-label">完成率</div>
          <div class="stat-trend trend-up">↗ +5%</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">💾</div>
          <div class="stat-number">2.4GB</div>
          <div class="stat-label">存储使用</div>
          <div class="stat-trend trend-down">↘ -3%</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选卡片 -->
    <div class="search-filter-card">
      <div class="search-bar">
        <div class="search-input-group">
          <input type="text" class="search-input" placeholder="搜索文档名称、内容或标签...">
          <span class="search-icon">🔍</span>
        </div>
        <div class="filter-buttons">
          <button class="filter-chip active">全部</button>
          <button class="filter-chip">Word</button>
          <button class="filter-chip">Excel</button>
          <button class="filter-chip">PowerPoint</button>
          <button class="filter-chip">PDF</button>
          <button class="filter-chip">最近编辑</button>
        </div>
      </div>
    </div>

    <!-- 文档列表区域 -->
    <div class="documents-section">
      <div class="section-header">
        <h2 class="section-title">我的文档</h2>
        <div class="view-toggle">
          <button class="view-btn active">🔲 卡片</button>
          <button class="view-btn">📋 列表</button>
        </div>
      </div>

      <div class="document-grid">
        <!-- Word文档卡片 -->
        <div class="document-card">
          <div class="doc-header">
            <div class="doc-type-badge badge-word">📄 Word</div>
            <div class="doc-title">项目需求分析文档</div>
            <div class="doc-subtitle">详细的功能需求和技术规格</div>
          </div>
          <div class="doc-body">
            <div class="doc-stats">
              <span>📅 2小时前</span>
              <span>📏 2.4 MB</span>
            </div>
            <div class="doc-progress">
              <div class="progress-label">
                <span>完成进度</span>
                <span>85%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 85%"></div>
              </div>
            </div>
            <div class="doc-footer">
              <div class="collaborators">
                <div class="avatar">张</div>
                <div class="avatar">李</div>
                <div class="avatar">王</div>
              </div>
              <div class="doc-actions">
                <button class="action-icon">👁️</button>
                <button class="action-icon">✏️</button>
                <button class="action-icon">📤</button>
                <button class="action-icon">⋯</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Excel文档卡片 -->
        <div class="document-card">
          <div class="doc-header">
            <div class="doc-type-badge badge-excel">📊 Excel</div>
            <div class="doc-title">Q4财务报表汇总</div>
            <div class="doc-subtitle">季度财务数据分析和预测</div>
          </div>
          <div class="doc-body">
            <div class="doc-stats">
              <span>📅 1天前</span>
              <span>📏 1.8 MB</span>
            </div>
            <div class="doc-progress">
              <div class="progress-label">
                <span>审核进度</span>
                <span>60%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 60%"></div>
              </div>
            </div>
            <div class="doc-footer">
              <div class="collaborators">
                <div class="avatar">财</div>
                <div class="avatar">务</div>
              </div>
              <div class="doc-actions">
                <button class="action-icon">👁️</button>
                <button class="action-icon">✏️</button>
                <button class="action-icon">📤</button>
                <button class="action-icon">⋯</button>
              </div>
            </div>
          </div>
        </div>

        <!-- PowerPoint文档卡片 -->
        <div class="document-card">
          <div class="doc-header">
            <div class="doc-type-badge badge-ppt">🎨 PPT</div>
            <div class="doc-title">产品发布会演示</div>
            <div class="doc-subtitle">新产品功能介绍和市场策略</div>
          </div>
          <div class="doc-body">
            <div class="doc-stats">
              <span>📅 3天前</span>
              <span>📏 15.2 MB</span>
            </div>
            <div class="doc-progress">
              <div class="progress-label">
                <span>制作进度</span>
                <span>95%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 95%"></div>
              </div>
            </div>
            <div class="doc-footer">
              <div class="collaborators">
                <div class="avatar">产</div>
                <div class="avatar">品</div>
                <div class="avatar">运</div>
                <div class="avatar">营</div>
              </div>
              <div class="doc-actions">
                <button class="action-icon">👁️</button>
                <button class="action-icon">✏️</button>
                <button class="action-icon">📤</button>
                <button class="action-icon">⋯</button>
              </div>
            </div>
          </div>
        </div>

        <!-- PDF文档卡片 -->
        <div class="document-card">
          <div class="doc-header">
            <div class="doc-type-badge badge-pdf">📋 PDF</div>
            <div class="doc-title">用户操作手册v2.0</div>
            <div class="doc-subtitle">完整的系统使用指南</div>
          </div>
          <div class="doc-body">
            <div class="doc-stats">
              <span>📅 1周前</span>
              <span>📏 8.9 MB</span>
            </div>
            <div class="doc-progress">
              <div class="progress-label">
                <span>发布状态</span>
                <span>已完成</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 100%"></div>
              </div>
            </div>
            <div class="doc-footer">
              <div class="collaborators">
                <div class="avatar">技</div>
                <div class="avatar">术</div>
              </div>
              <div class="doc-actions">
                <button class="action-icon">👁️</button>
                <button class="action-icon">📥</button>
                <button class="action-icon">📤</button>
                <button class="action-icon">⋯</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 更多卡片... -->
        <div class="document-card">
          <div class="doc-header">
            <div class="doc-type-badge badge-word">📄 Word</div>
            <div class="doc-title">会议纪要模板</div>
            <div class="doc-subtitle">标准化的会议记录格式</div>
          </div>
          <div class="doc-body">
            <div class="doc-stats">
              <span>📅 2周前</span>
              <span>📏 456 KB</span>
            </div>
            <div class="doc-progress">
              <div class="progress-label">
                <span>使用频率</span>
                <span>高</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 80%"></div>
              </div>
            </div>
            <div class="doc-footer">
              <div class="collaborators">
                <div class="avatar">行</div>
                <div class="avatar">政</div>
              </div>
              <div class="doc-actions">
                <button class="action-icon">👁️</button>
                <button class="action-icon">✏️</button>
                <button class="action-icon">📤</button>
                <button class="action-icon">⋯</button>
              </div>
            </div>
          </div>
        </div>

        <div class="document-card">
          <div class="doc-header">
            <div class="doc-type-badge badge-excel">📊 Excel</div>
            <div class="doc-title">员工考勤统计表</div>
            <div class="doc-subtitle">月度考勤数据汇总分析</div>
          </div>
          <div class="doc-body">
            <div class="doc-stats">
              <span>📅 3周前</span>
              <span>📏 3.2 MB</span>
            </div>
            <div class="doc-progress">
              <div class="progress-label">
                <span>数据更新</span>
                <span>75%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 75%"></div>
              </div>
            </div>
            <div class="doc-footer">
              <div class="collaborators">
                <div class="avatar">人</div>
                <div class="avatar">事</div>
              </div>
              <div class="doc-actions">
                <button class="action-icon">👁️</button>
                <button class="action-icon">✏️</button>
                <button class="action-icon">📤</button>
                <button class="action-icon">⋯</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 筛选功能
    document.querySelectorAll('.filter-chip').forEach(chip => {
      chip.addEventListener('click', () => {
        document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
        chip.classList.add('active');
        console.log('筛选:', chip.textContent);
      });
    });

    // 视图切换
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        console.log('切换视图:', btn.textContent);
      });
    });

    // 文档卡片点击
    document.querySelectorAll('.document-card').forEach(card => {
      card.addEventListener('click', (e) => {
        if (!e.target.closest('.action-icon')) {
          console.log('打开文档:', card.querySelector('.doc-title').textContent);
        }
      });
    });

    // 操作按钮
    document.querySelectorAll('.action-icon').forEach(icon => {
      icon.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('执行操作:', icon.textContent);
      });
    });
  </script>
</body>
</html> 