import{ad as f}from"./index-5218909a.js";const b={width:1200,height:800,resizable:!0,scrollbars:!1,menubar:!1,toolbar:!1,location:!1,status:!1},i=o=>{if(!o)return"";const e=new URLSearchParams;Object.entries(o).forEach(([t,r])=>{r!=null&&e.append(t,String(r))});const n=e.toString();return n?`?${n}`:""},$=async(o,e)=>{const n=await f(()=>import("./index-5218909a.js").then(r=>r.ae),["assets/index-5218909a.js","assets/index-fcf436de.css"]),t=i(e);return await n.default.push(`/documents/editor/${o}${t}`),null},m=async(o,e,n)=>{const t={...b,...n},r=window.screen.availWidth,d=window.screen.availHeight,c=t.left??Math.round((r-t.width)/2),l=t.top??Math.round((d-t.height)/2),u=[`width=${t.width}`,`height=${t.height}`,`left=${c}`,`top=${l}`,`resizable=${t.resizable?"yes":"no"}`,`scrollbars=${t.scrollbars?"yes":"no"}`,`menubar=${t.menubar?"yes":"no"}`,`toolbar=${t.toolbar?"yes":"no"}`,`location=${t.location?"yes":"no"}`,`status=${t.status?"yes":"no"}`].join(","),p=window.location.origin,h=i(e),w=`${p}/editor/fullscreen/${o}${h}`,s=window.open(w,`editor_${o}`,u);if(!s)throw new Error("无法打开新窗口，请检查浏览器弹窗拦截设置");return s.focus(),s},a=async o=>{const{documentId:e,mode:n,configQuery:t,windowOptions:r}=o;switch(n){case"embedded":return $(e,t);case"popup":return m(e,t,r);default:throw new Error(`不支持的编辑器打开模式: ${n}`)}},y=(o,e)=>a({documentId:o,mode:"embedded",configQuery:e}),E=(o,e,n)=>a({documentId:o,mode:"popup",configQuery:e,windowOptions:n}),_=()=>window.location.pathname.startsWith("/editor/fullscreen/");export{E as a,_ as i,y as o};
