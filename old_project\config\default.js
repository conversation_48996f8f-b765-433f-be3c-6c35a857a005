/**
 * OnlyOffice文档服务器集成应用默认配置
 */
const os = require('os');
const path = require('path');

const config = {
    // 服务器配置
    server: {
        port: 3300,
        host: "*************", // 修改为实际的Node.js服务器IP
    },

    // OnlyOffice文档服务器配置
    documentServer: {
        url: 'http://*************', // OnlyOffice服务器地址
    },

    // JWT配置
    jwt: {
        secret: 'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV', // 应在生产环境中使用更安全的密钥
        header: {
            alg: "HS256",
            typ: "JWT"
        },
        expiresIn: 3600 // 令牌有效期（秒）
    },

    // 文件存储配置
    storage: {
        uploadDir: path.join(process.cwd(), 'uploads'),
        tmpDir: path.join(process.cwd(), 'tmp'),
        fileSize: 50 * 1024 * 1024, // 50MB限制
    },

    // CORS配置
    cors: {
        origin: '*',
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization']
    },

    // 编辑器配置
    editor: {
        callbackUrl: null, // 将在运行时根据服务器地址动态设置
        lang: 'zh',
        customization: {
            chat: false,
            comments: false,
            compactHeader: false,
            feedback: false,
            forcesave: true,
            help: true,
            reviewDisplay: "markup"
        },
        user: {
            id: "user-2",
            name: "示例2用户"
        }
    },
    document: {
        permissions: {
            chat: false,
            comments: false,
            compactHeader: false,
            feedback: false,
            forcesave: true,
            help: true,
            reviewDisplay: "markup"
        }
    },

    // 日志配置
    logger: {
        level: 'info', // debug, info, warn, error
        format: 'dev', // dev, combined, common, short, tiny
    }
};

// 动态设置回调URL
config.editor.callbackUrl = `http://${config.server.host}:${config.server.port}/api/editor/callback`;

module.exports = config; 