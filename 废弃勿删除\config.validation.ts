import * as Joi from 'joi';

/**
 * 环境变量验证模式
 * 
 * 使用Joi验证库定义所有环境变量的验证规则
 * 确保应用启动时配置的正确性和完整性
 * 
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
export const configValidationSchema = Joi.object({
  // 应用基础配置
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development')
    .description('应用运行环境'),
  
  PORT: Joi.number()
    .port()
    .default(3000)
    .description('应用监听端口'),
  
  API_PREFIX: Joi.string()
    .default('api')
    .description('API路径前缀'),

  // CORS配置
  CORS_ORIGINS: Joi.string()
    .default('*')
    .description('CORS允许的源地址，多个用逗号分隔'),

  // 数据库配置
  DB_TYPE: Joi.string()
    .valid('mysql', 'postgresql')
    .default('mysql')
    .description('数据库类型'),
  
  DB_HOST: Joi.string()
    .hostname()
    .required()
    .description('数据库主机地址'),
  
  DB_PORT: Joi.number()
    .port()
    .default(3306)
    .description('数据库端口'),
  
  DB_USER: Joi.string()
    .required()
    .description('数据库用户名'),
  
  DB_PASSWORD: Joi.string()
    .allow('')
    .required()
    .description('数据库密码'),
  
  DB_NAME: Joi.string()
    .required()
    .description('数据库名称'),
  
  DB_SYNC: Joi.boolean()
    .default(false)
    .description('是否自动同步数据库结构'),
  
  DB_LOGGING: Joi.boolean()
    .default(false)
    .description('是否启用数据库查询日志'),

  // JWT配置
  JWT_SECRET: Joi.string()
    .min(32)
    .required()
    .description('JWT签名密钥，至少32个字符'),
  
  JWT_EXPIRES_IN: Joi.string()
    .default('24h')
    .description('JWT访问令牌过期时间'),
  
  JWT_REFRESH_EXPIRES_IN: Joi.string()
    .default('7d')
    .description('JWT刷新令牌过期时间'),

  // OnlyOffice配置
  ONLYOFFICE_DOCS_URL: Joi.string()
    .uri()
    .default('http://localhost:80')
    .description('OnlyOffice文档服务器地址'),
  
  ONLYOFFICE_CALLBACK_URL: Joi.string()
    .uri()
    .default('http://localhost:3000/api/documents/callback')
    .description('OnlyOffice回调地址'),
  
  ONLYOFFICE_JWT_SECRET: Joi.string()
    .min(16)
    .description('OnlyOffice JWT密钥，如果未设置则使用JWT_SECRET'),
  
  ONLYOFFICE_JWT_HEADER: Joi.string()
    .default('Authorization')
    .description('OnlyOffice JWT请求头名称'),

  // FileNet配置(可选)
  FILENET_URL: Joi.string()
    .uri()
    .description('FileNet服务器地址'),
  
  FILENET_USERNAME: Joi.string()
    .description('FileNet用户名'),
  
  FILENET_PASSWORD: Joi.string()
    .description('FileNet密码'),
  
  FILENET_DOMAIN: Joi.string()
    .description('FileNet域名'),

  // 文件存储配置
  UPLOAD_PATH: Joi.string()
    .default('./uploads')
    .description('文件上传路径'),
  
  MAX_FILE_SIZE: Joi.number()
    .min(1024)
    .default(104857600) // 100MB
    .description('最大文件上传大小(字节)'),

  // 日志配置
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('info')
    .description('日志级别'),
  
  LOG_FILE: Joi.string()
    .default('./logs/app.log')
    .description('日志文件路径'),

  // 缓存配置
  CACHE_TTL: Joi.number()
    .min(0)
    .default(300) // 5分钟
    .description('缓存过期时间(秒)'),
  
  CACHE_MAX_ITEMS: Joi.number()
    .min(1)
    .default(1000)
    .description('缓存最大条目数'),

  // Redis配置(可选，用于未来扩展)
  REDIS_HOST: Joi.string()
    .hostname()
    .description('Redis主机地址'),
  
  REDIS_PORT: Joi.number()
    .port()
    .default(6379)
    .description('Redis端口'),
  
  REDIS_PASSWORD: Joi.string()
    .allow('')
    .description('Redis密码'),
  
  REDIS_DB: Joi.number()
    .min(0)
    .max(15)
    .default(0)
    .description('Redis数据库索引'),

  // 限流配置
  THROTTLE_TTL: Joi.number()
    .min(1)
    .default(60)
    .description('限流时间窗口(秒)'),
  
  THROTTLE_LIMIT: Joi.number()
    .min(1)
    .default(100)
    .description('限流请求次数'),

  // 安全配置
  BCRYPT_ROUNDS: Joi.number()
    .min(8)
    .max(15)
    .default(12)
    .description('bcrypt哈希轮次'),

  // Swagger文档配置
  SWAGGER_ENABLED: Joi.boolean()
    .default(true)
    .description('是否启用Swagger文档'),
  
  SWAGGER_PATH: Joi.string()
    .default('api-docs')
    .description('Swagger文档路径'),
}); 