/**
 * 文件上传中间件
 */
const fileUpload = require('express-fileupload');
const path = require('path');
const fs = require('fs-extra');
const config = require('../config');

// 确保上传目录和临时目录存在
fs.ensureDirSync(config.storage.uploadDir);
fs.ensureDirSync(config.storage.tmpDir);

// 配置文件上传中间件
const uploadMiddleware = fileUpload({
    createParentPath: true,
    limits: { fileSize: config.storage.fileSize },
    parseNested: true,
    useTempFiles: true,
    tempFileDir: config.storage.tmpDir,
    debug: process.env.NODE_ENV === 'development'
});

/**
 * 处理文件名编码问题
 * @param {string} fileName 原始文件名
 * @returns {string} 处理后的文件名
 */
function sanitizeFileName(fileName) {
    console.log('[sanitizeFileName] 接收到的原始文件名:', fileName);
    console.log('[sanitizeFileName] 原始文件名二进制 (hex):', Buffer.from(fileName).toString('hex'));

    try {
        // 步骤 1: 处理可能的URL编码
        if (fileName.includes('%')) {
            try {
                const decodedName = decodeURIComponent(fileName);
                if (decodedName !== fileName) {
                    console.log('[sanitizeFileName] URL解码后的文件名:', decodedName);
                    fileName = decodedName;
                }
            } catch (e) {
                console.warn('[sanitizeFileName] URL解码失败 (忽略错误，继续处理):', e.message);
            }
        }

        // 步骤 2: 尝试从 binary (latin1) -> utf8 修复常见的mojibake
        // 检查文件名是否可能已经是正确的UTF-8，或者是否是纯ASCII
        const isLikelyPureAscii = /^[\x00-\x7F]*$/.test(fileName);
        let needsUtf8Fix = false;
        if (!isLikelyPureAscii) {
            // 如果包含非ASCII字符，检查它是否已经是有效的UTF-8
            // 通过将其编码为UTF-8字节，然后解码回UTF-8字符串，看是否相同
            const reEncodedBytes = Buffer.from(fileName, 'utf8');
            const reDecodedName = reEncodedBytes.toString('utf8');
            if (reDecodedName !== fileName) {
                // 如果不匹配，说明原始fileName不是有效的UTF-8字符串，可能是mojibake
                needsUtf8Fix = true;
                console.log('[sanitizeFileName] 检测到原始文件名可能不是有效的UTF-8，将尝试修复。');
            } else {
                console.log('[sanitizeFileName] 原始文件名似乎已经是有效的UTF-8。');
            }
        }

        if (needsUtf8Fix || (!isLikelyPureAscii && !Buffer.from(fileName, 'utf8').equals(Buffer.from(fileName, 'binary')))) {
            // 第二个条件 (Buffer.from(fileName, 'utf8').equals(Buffer.from(fileName, 'binary'))) 是一种启发式方法
            // 对于已经是正确UTF-8的多字节字符，其UTF-8字节表示和其按binary（每个字符一个字节）解释的字节表示通常不同
            // 但对于mojibake, 其UTF-8字节表示（如果尝试）可能很奇怪，而binary解释是"原始"的错误字节
            console.log('[sanitizeFileName] 文件名需要或可能受益于UTF-8修复，尝试转换 (binary -> utf8)...');
            const buf = Buffer.from(fileName, 'binary'); // 假设fileName是UTF-8字节被错误当作latin1/binary读取的结果
            const utf8Candidate = buf.toString('utf8');  // 尝试将其正确解码为UTF-8

            if (utf8Candidate !== fileName) {
                console.log('[sanitizeFileName] UTF-8修复候选:', utf8Candidate);
                // 这里可以添加更复杂的验证，例如检查utf8Candidate是否包含很多 (U+FFFD)
                // 但通常如果它与原始mojibake不同，它更可能是正确的
                fileName = utf8Candidate;
            } else {
                console.log('[sanitizeFileName] UTF-8修复尝试未改变文件名。');
            }
        }

    } catch (encodeError) {
        console.error('[sanitizeFileName] 文件名处理过程中发生错误:', encodeError);
        // 出错时，返回原始（可能已部分处理的）文件名，而不是抛出错误中断流程
    }

    // 步骤 3: 文件名安全化，移除不安全字符
    const finalCleanedName = fileName.replace(/[\/\?<>\\:\*\|"]/g, '_');
    console.log('[sanitizeFileName] 清理和安全化后的最终文件名:', finalCleanedName);

    return finalCleanedName;
}

module.exports = {
    uploadMiddleware,
    sanitizeFileName
}; 