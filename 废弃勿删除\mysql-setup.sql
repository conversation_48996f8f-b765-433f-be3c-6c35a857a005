-- MySQL用户权限设置脚本
-- 请在MySQL服务器上执行这些命令

-- 1. 检查用户是否存在
SELECT User, Host FROM mysql.user WHERE User = 'onlyfile_user';

-- 2. 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'onlyfile_user'@'%' IDENTIFIED BY '0nlyF!le$ecure#123';
CREATE USER IF NOT EXISTS 'onlyfile_user'@'*************' IDENTIFIED BY '0nlyF!le$ecure#123';

-- 3. 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS onlyfile;

-- 4. 授予权限
GRANT ALL PRIVILEGES ON onlyfile.* TO 'onlyfile_user'@'%';
GRANT ALL PRIVILEGES ON onlyfile.* TO 'onlyfile_user'@'*************';

-- 5. 刷新权限
FLUSH PRIVILEGES;

-- 6. 验证权限
SHOW GRANTS FOR 'onlyfile_user'@'%';

-- 7. 检查数据库是否存在
SHOW DATABASES LIKE 'onlyfile';

-- 8. 创建测试表（可选）
USE onlyfile;
CREATE TABLE IF NOT EXISTS test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO test_table (name) VALUES ('测试数据1'), ('测试数据2');

-- 9. 验证数据
SELECT * FROM test_table; 