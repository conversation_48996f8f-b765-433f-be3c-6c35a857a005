"use strict";
exports.id = 0;
exports.ids = null;
exports.modules = {

/***/ 72:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DocumentTemplateController = void 0;
const common_1 = __webpack_require__(5);
const platform_express_1 = __webpack_require__(57);
const swagger_1 = __webpack_require__(6);
const document_template_service_1 = __webpack_require__(73);
const multer_1 = __webpack_require__(59);
const path_1 = __webpack_require__(12);
const express_1 = __webpack_require__(48);
let DocumentTemplateController = class DocumentTemplateController {
    constructor(documentTemplateService) {
        this.documentTemplateService = documentTemplateService;
    }
    async getDocumentTemplates(limit, offset, categoryId, status, sortBy, order) {
        try {
            const options = {
                limit: limit ? parseInt(limit.toString()) : 10,
                offset: offset ? parseInt(offset.toString()) : 0,
                categoryId,
                status,
                sortBy,
                order
            };
            const result = await this.documentTemplateService.getDocumentTemplates(options);
            return {
                success: true,
                data: result.data,
                message: '获取文档模板列表成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: '获取文档模板列表失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDocumentTemplateById(templateId) {
        try {
            const result = await this.documentTemplateService.getDocumentTemplateById(templateId);
            return {
                success: true,
                data: result.data,
                message: '获取文档模板详情成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                success: false,
                message: '获取文档模板详情失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createDocumentTemplate(templateData, file) {
        try {
            const createData = {
                name: String(templateData.name || '').trim(),
                categoryId: templateData.categoryId ? String(templateData.categoryId) : undefined,
                description: templateData.description ? String(templateData.description) : undefined,
                createdBy: templateData.createdBy ? String(templateData.createdBy) : 'system'
            };
            if (!createData.name) {
                throw new common_1.HttpException({ success: false, message: '模板名称不能为空', timestamp: new Date().toISOString() }, common_1.HttpStatus.BAD_REQUEST);
            }
            const templateFile = {
                originalname: file.originalname,
                size: file.size,
                filename: file.filename,
                path: file.path,
                mimetype: file.mimetype,
                destination: file.destination
            };
            const result = await this.documentTemplateService.createDocumentTemplate(createData, templateFile);
            return {
                success: true,
                data: result.data,
                message: '文档模板创建成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                success: false,
                message: '文档模板创建失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateDocumentTemplate(templateId, updateData) {
        try {
            const result = await this.documentTemplateService.updateDocumentTemplate(templateId, updateData);
            return {
                success: true,
                data: result.data,
                message: '文档模板更新成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                success: false,
                message: '文档模板更新失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteDocumentTemplate(templateId) {
        try {
            const result = await this.documentTemplateService.deleteDocumentTemplate(templateId);
            return {
                success: true,
                data: result,
                message: '文档模板删除成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                success: false,
                message: '文档模板删除失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createDocumentFromTemplate(templateId, createData) {
        try {
            const { title, userId } = createData;
            const result = await this.documentTemplateService.createDocumentFromTemplate(templateId, title, userId);
            return {
                success: true,
                data: {
                    sessionId: result.data.sessionId,
                    templateId: result.data.templateId,
                    templateName: result.data.templateName,
                    templateFnDocId: result.data.templateFnDocId,
                    documentName: result.data.documentName,
                    userId: result.data.userId,
                    status: result.data.status,
                    editUrl: result.data.editUrl
                },
                message: result.message,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                success: false,
                message: '基于模板创建文档失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTemplateCategories() {
        try {
            const result = await this.documentTemplateService.getTemplateCategories();
            return {
                success: true,
                data: result.data,
                message: '获取模板分类列表成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: '获取模板分类列表失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createTemplateCategory(categoryData) {
        try {
            const createData = {
                name: String(categoryData.name || '').trim(),
                parentId: categoryData.parentId ? String(categoryData.parentId) : undefined,
                description: categoryData.description ? String(categoryData.description) : undefined,
                sortOrder: categoryData.sortOrder ? Number(categoryData.sortOrder) : undefined
            };
            if (!createData.name) {
                throw new common_1.HttpException({ success: false, message: '分类名称不能为空', timestamp: new Date().toISOString() }, common_1.HttpStatus.BAD_REQUEST);
            }
            const result = await this.documentTemplateService.createTemplateCategory(createData);
            return {
                success: true,
                data: result.data,
                message: '模板分类创建成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                success: false,
                message: '模板分类创建失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAllTemplatesOverview() {
        try {
            const result = await this.documentTemplateService.getAllTemplatesOverview();
            return {
                success: true,
                data: result.data,
                message: '获取模板概览成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: '获取模板概览失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async searchTemplates(keyword, type, limit) {
        try {
            const searchOptions = {
                keyword,
                type,
                limit: limit ? parseInt(limit.toString()) : 20
            };
            const result = await this.documentTemplateService.searchTemplates(searchOptions);
            return {
                success: true,
                data: result.data,
                message: '模板搜索完成',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: '模板搜索失败',
                error: error.message,
                timestamp: new Date().toISOString()
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.DocumentTemplateController = DocumentTemplateController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取文档模板列表',
        description: '获取系统中所有的文档模板，支持分页和筛选'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取文档模板列表',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                data: {
                    type: 'object',
                    properties: {
                        templates: { type: 'array' },
                        total: { type: 'number', example: 10 },
                        limit: { type: 'number', example: 10 },
                        offset: { type: 'number', example: 0 }
                    }
                },
                message: { type: 'string', example: '获取文档模板列表成功' }
            }
        }
    }),
    __param(0, (0, common_1.Query)('limit')),
    __param(1, (0, common_1.Query)('offset')),
    __param(2, (0, common_1.Query)('categoryId')),
    __param(3, (0, common_1.Query)('status')),
    __param(4, (0, common_1.Query)('sortBy')),
    __param(5, (0, common_1.Query)('order')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, String]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "getDocumentTemplates", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取文档模板详情',
        description: '根据模板ID获取详细信息'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '模板ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取文档模板详情'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '文档模板不存在'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "getDocumentTemplateById", null);
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        storage: (0, multer_1.diskStorage)({
            destination: './uploads',
            filename: (req, file, cb) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
                cb(null, file.fieldname + '-' + uniqueSuffix + (0, path_1.extname)(file.originalname));
            }
        }),
        limits: {
            fileSize: 50 * 1024 * 1024
        }
    })),
    (0, swagger_1.ApiOperation)({
        summary: '创建文档模板',
        description: '上传文件并创建新的文档模板'
    }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        description: '文档模板文件和元数据',
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                    description: '模板文件'
                },
                name: {
                    type: 'string',
                    description: '模板名称'
                },
                categoryId: {
                    type: 'string',
                    description: '分类ID'
                },
                description: {
                    type: 'string',
                    description: '模板描述'
                },
                createdBy: {
                    type: 'string',
                    description: '创建者'
                }
            },
            required: ['file', 'name']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '文档模板创建成功'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误'
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object, typeof (_d = typeof express_1.Express !== "undefined" && (_c = express_1.Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "createDocumentTemplate", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '更新文档模板',
        description: '更新指定ID的文档模板信息'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '模板ID' }),
    (0, swagger_1.ApiBody)({
        description: '更新的模板数据',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', description: '模板名称' },
                categoryId: { type: 'string', description: '分类ID' },
                description: { type: 'string', description: '模板描述' },
                status: { type: 'string', enum: ['enabled', 'disabled'], description: '模板状态' },
                updatedBy: { type: 'string', description: '更新者' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '文档模板更新成功'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '文档模板不存在'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "updateDocumentTemplate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '删除文档模板',
        description: '软删除指定ID的文档模板'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '模板ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '文档模板删除成功'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '文档模板不存在'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "deleteDocumentTemplate", null);
__decorate([
    (0, common_1.Post)(':id/create-document'),
    (0, swagger_1.ApiOperation)({
        summary: '基于模板创建新文档',
        description: '使用指定模板创建新的文档'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '模板ID' }),
    (0, swagger_1.ApiBody)({
        description: '新文档信息',
        schema: {
            type: 'object',
            properties: {
                title: { type: 'string', description: '新文档标题' },
                description: { type: 'string', description: '文档描述' },
                userId: { type: 'string', description: '用户ID' }
            },
            required: ['title']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '基于模板创建文档成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean' },
                data: {
                    type: 'object',
                    properties: {
                        documentId: { type: 'string' },
                        editUrl: { type: 'string' }
                    }
                },
                message: { type: 'string' },
                timestamp: { type: 'string' }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "createDocumentFromTemplate", null);
__decorate([
    (0, common_1.Get)('categories/list'),
    (0, swagger_1.ApiOperation)({
        summary: '获取模板分类列表',
        description: '获取所有可用的模板分类'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取模板分类列表'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "getTemplateCategories", null);
__decorate([
    (0, common_1.Post)('categories/create'),
    (0, swagger_1.ApiOperation)({
        summary: '创建模板分类',
        description: '创建新的模板分类'
    }),
    (0, swagger_1.ApiBody)({
        description: '分类数据',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', description: '分类名称' },
                parentId: { type: 'string', description: '父分类ID' },
                description: { type: 'string', description: '分类描述' },
                sortOrder: { type: 'number', description: '排序顺序' }
            },
            required: ['name']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '模板分类创建成功'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof Record !== "undefined" && Record) === "function" ? _f : Object]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "createTemplateCategory", null);
__decorate([
    (0, common_1.Get)('overview/all'),
    (0, swagger_1.ApiOperation)({
        summary: '获取所有模板概览',
        description: '获取包括文档模板和配置模板在内的所有模板概览'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取模板概览'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "getAllTemplatesOverview", null);
__decorate([
    (0, common_1.Get)('search/all'),
    (0, swagger_1.ApiOperation)({
        summary: '搜索模板',
        description: '在文档模板和配置模板中搜索'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '搜索完成'
    }),
    __param(0, (0, common_1.Query)('keyword')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], DocumentTemplateController.prototype, "searchTemplates", null);
exports.DocumentTemplateController = DocumentTemplateController = __decorate([
    (0, swagger_1.ApiTags)('文档模板管理'),
    (0, common_1.Controller)('document-templates'),
    __metadata("design:paramtypes", [typeof (_a = typeof document_template_service_1.DocumentTemplateService !== "undefined" && document_template_service_1.DocumentTemplateService) === "function" ? _a : Object])
], DocumentTemplateController);


/***/ }),

/***/ 73:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DocumentTemplateService = void 0;
const common_1 = __webpack_require__(5);
const database_service_1 = __webpack_require__(15);
const filenet_service_1 = __webpack_require__(50);
const uuid_1 = __webpack_require__(28);
let DocumentTemplateService = class DocumentTemplateService {
    constructor(databaseService, filenetService) {
        this.databaseService = databaseService;
        this.filenetService = filenetService;
    }
    async getDocumentTemplates(options = {}) {
        try {
            const { limit = 10, offset = 0, categoryId, status = 'enabled', sortBy = 'created_at', order = 'DESC' } = options;
            const conditions = ['t.is_deleted = FALSE'];
            const queryParams = [];
            if (status && status.toLowerCase() !== 'all') {
                conditions.push('t.status = ?');
                queryParams.push(status);
            }
            if (categoryId) {
                conditions.push('t.category_id = ?');
                queryParams.push(categoryId);
            }
            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
            const validSortColumns = ['name', 'created_at', 'updated_at'];
            const sortColumn = validSortColumns.includes(sortBy) ? `t.${sortBy}` : 't.created_at';
            const sortOrder = (order.toUpperCase() === 'ASC' || order.toUpperCase() === 'DESC') ? order.toUpperCase() : 'DESC';
            const mainQuery = `
        SELECT 
          t.*, 
          tc.name as category_name, 
          fd.original_name as source_document_name,
          fd.file_size,
          fd.extension
        FROM templates t 
        LEFT JOIN template_categories tc ON t.category_id = tc.id 
        LEFT JOIN filenet_documents fd ON t.source_doc_id = fd.id 
        ${whereClause} 
        ORDER BY ${sortColumn} ${sortOrder} 
        LIMIT ? OFFSET ?
      `;
            const mainParams = [...queryParams, limit, offset];
            const countQuery = `
        SELECT COUNT(*) as total 
        FROM templates t
        LEFT JOIN template_categories tc ON t.category_id = tc.id
        ${whereClause}
      `;
            const [templates, countResult] = await Promise.all([
                this.databaseService.query(mainQuery, mainParams),
                this.databaseService.query(countQuery, queryParams)
            ]);
            const total = countResult[0]?.total || 0;
            return {
                success: true,
                data: {
                    templates,
                    total,
                    limit,
                    offset
                }
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 获取文档模板列表失败:', error);
            throw new common_1.HttpException(`获取文档模板列表失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDocumentTemplateById(templateId) {
        try {
            const query = `
        SELECT 
          t.*, 
          tc.name as category_name, 
          fd.original_name as source_document_name, 
          fd.extension as source_document_extension,
          fd.file_size,
          fd.mime_type
        FROM templates t 
        LEFT JOIN template_categories tc ON t.category_id = tc.id 
        LEFT JOIN filenet_documents fd ON t.source_doc_id = fd.id 
        WHERE t.id = ? AND t.is_deleted = FALSE
      `;
            const result = await this.databaseService.queryOne(query, [templateId]);
            if (!result) {
                throw new common_1.HttpException('文档模板不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                data: result
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 获取文档模板详情失败:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`获取文档模板详情失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createDocumentTemplate(templateData, file) {
        try {
            const { name, categoryId, description, createdBy = 'system' } = templateData;
            if (!name) {
                throw new common_1.HttpException('模板名称不能为空', common_1.HttpStatus.BAD_REQUEST);
            }
            let docId;
            if (file) {
                console.log('[DocumentTemplateService] 上传模板文件到FileNet:', file.originalname);
                const uploadResult = await this.filenetService.uploadDocument(file, file.originalname, createdBy);
                if (!uploadResult.success) {
                    throw new common_1.HttpException('模板文件上传失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                }
                docId = uploadResult.dbId;
            }
            else {
                throw new common_1.HttpException('必须提供模板文件', common_1.HttpStatus.BAD_REQUEST);
            }
            const templateId = (0, uuid_1.v4)();
            await this.databaseService.query('INSERT INTO templates (id, name, doc_id, category_id, description, created_by, status) VALUES (?, ?, ?, ?, ?, ?, ?)', [templateId, name, docId, categoryId || null, description || '', createdBy, 'enabled']);
            return await this.getDocumentTemplateById(templateId);
        }
        catch (error) {
            console.error('[DocumentTemplateService] 创建文档模板失败:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`创建文档模板失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateDocumentTemplate(templateId, updateData) {
        try {
            const allowedUpdates = ['name', 'category_id', 'description', 'status', 'updated_by'];
            const updates = {};
            const querySet = [];
            for (const key in updateData) {
                if (allowedUpdates.includes(key) && updateData[key] !== undefined) {
                    updates[key] = updateData[key];
                    querySet.push(`${key} = ?`);
                }
            }
            if (querySet.length === 0) {
                throw new common_1.HttpException('没有提供有效的更新字段', common_1.HttpStatus.BAD_REQUEST);
            }
            const query = `UPDATE templates SET ${querySet.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_deleted = FALSE`;
            const params = [...Object.values(updates), templateId];
            const result = await this.databaseService.query(query, params);
            if (!result || result.affectedRows === 0) {
                throw new common_1.HttpException('文档模板不存在或未更新', common_1.HttpStatus.NOT_FOUND);
            }
            return await this.getDocumentTemplateById(templateId);
        }
        catch (error) {
            console.error('[DocumentTemplateService] 更新文档模板失败:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`更新文档模板失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteDocumentTemplate(templateId) {
        try {
            const query = 'UPDATE templates SET is_deleted = TRUE, status = \'disabled\', updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_deleted = FALSE';
            const result = await this.databaseService.query(query, [templateId]);
            if (!result || result.affectedRows === 0) {
                throw new common_1.HttpException('文档模板不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                message: '文档模板删除成功'
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 删除文档模板失败:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`删除文档模板失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createDocumentFromTemplate(templateId, newDocumentName, userId = 'system') {
        try {
            if (!templateId || !newDocumentName) {
                throw new common_1.HttpException('模板ID和新文档名称不能为空', common_1.HttpStatus.BAD_REQUEST);
            }
            console.log(`[DocumentTemplateService] 开始基于模板创建编辑会话: ${templateId} -> ${newDocumentName}`);
            const template = await this.getDocumentTemplateById(templateId);
            if (!template.success || !template.data || template.data.status !== 'enabled') {
                throw new common_1.HttpException('模板不存在、已被删除或已禁用', common_1.HttpStatus.NOT_FOUND);
            }
            const templateData = template.data;
            console.log(`[DocumentTemplateService] 模板信息: ${templateData.name}, FileNet ID: ${templateData.fn_doc_id}`);
            const defaultExtension = 'docx';
            const finalDocumentName = newDocumentName.toLowerCase().endsWith(`.${defaultExtension.toLowerCase()}`)
                ? newDocumentName
                : `${newDocumentName}.${defaultExtension}`;
            console.log(`[DocumentTemplateService] 文档名称处理: "${newDocumentName}" -> "${finalDocumentName}"`);
            const sessionId = (0, uuid_1.v4)();
            const result = {
                success: true,
                message: '基于模板创建编辑会话成功',
                data: {
                    sessionId,
                    templateId,
                    templateName: templateData.name,
                    templateFnDocId: templateData.fn_doc_id,
                    documentName: finalDocumentName,
                    userId,
                    status: 'template-session',
                    editUrl: `/documents/template-editor/${sessionId}?template=${templateId}&name=${encodeURIComponent(finalDocumentName)}`
                }
            };
            console.log(`[DocumentTemplateService] 基于模板创建编辑会话完成:`, result.data);
            return result;
        }
        catch (error) {
            console.error('[DocumentTemplateService] 基于模板创建编辑会话失败:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`基于模板创建编辑会话失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async convertTemplateSessionToDocument(templateId, documentName, userId = 'system') {
        try {
            console.log(`[DocumentTemplateService] 开始将模板会话转换为正式文档: ${templateId} -> ${documentName}`);
            const template = await this.getDocumentTemplateById(templateId);
            if (!template.success || !template.data || template.data.status !== 'enabled') {
                throw new common_1.HttpException('模板不存在、已被删除或已禁用', common_1.HttpStatus.NOT_FOUND);
            }
            const templateData = template.data;
            console.log(`[DocumentTemplateService] 模板信息: ${templateData.name}, FileNet ID: ${templateData.fn_doc_id}`);
            const filenetCopyResult = await this.filenetService.copyDocument(templateData.fn_doc_id, documentName, userId);
            if (!filenetCopyResult.success) {
                throw new common_1.HttpException('FileNet文档复制失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            console.log(`[DocumentTemplateService] FileNet复制成功, 新文档ID: ${filenetCopyResult.dbId}`);
            const newDocumentId = (0, uuid_1.v4)();
            const now = new Date();
            const defaultExtension = 'docx';
            const finalDocumentName = documentName.toLowerCase().endsWith(`.${defaultExtension.toLowerCase()}`)
                ? documentName
                : `${documentName}.${defaultExtension}`;
            await this.databaseService.query(`INSERT INTO filenet_documents (
          id, fn_doc_id, original_name, version, file_size, 
          mime_type, extension, created_by, created_at, 
          updated_at, template_id, is_deleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                newDocumentId,
                filenetCopyResult.filenetId,
                finalDocumentName,
                1,
                0,
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                defaultExtension,
                userId,
                now,
                now,
                templateId,
                false
            ]);
            console.log(`[DocumentTemplateService] 正式文档记录创建成功: ${newDocumentId}`);
            const result = {
                success: true,
                message: '模板会话转换为正式文档成功',
                data: {
                    documentId: newDocumentId,
                    filenetId: filenetCopyResult.filenetId,
                    name: finalDocumentName,
                    templateId,
                    templateName: templateData.name,
                    version: 1,
                    status: 'draft'
                }
            };
            console.log(`[DocumentTemplateService] 模板会话转换为正式文档完成:`, result.data);
            return result;
        }
        catch (error) {
            console.error('[DocumentTemplateService] 模板会话转换为正式文档失败:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`模板会话转换为正式文档失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTemplateCategories() {
        try {
            const categories = await this.databaseService.query('SELECT * FROM template_categories ORDER BY sort_order ASC, name ASC');
            return {
                success: true,
                data: categories
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 获取模板分类失败:', error);
            throw new common_1.HttpException(`获取模板分类失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createTemplateCategory(categoryData) {
        try {
            const { name, parentId, description, sortOrder = 0 } = categoryData;
            if (!name) {
                throw new common_1.HttpException('分类名称不能为空', common_1.HttpStatus.BAD_REQUEST);
            }
            const categoryId = (0, uuid_1.v4)();
            await this.databaseService.query('INSERT INTO template_categories (id, name, parent_id, description, sort_order) VALUES (?, ?, ?, ?, ?)', [categoryId, name, parentId || null, description || '', sortOrder]);
            const newCategory = await this.databaseService.queryOne('SELECT * FROM template_categories WHERE id = ?', [categoryId]);
            return {
                success: true,
                data: newCategory
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 创建模板分类失败:', error);
            throw new common_1.HttpException(`创建模板分类失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getConfigTemplates() {
        try {
            const configTemplates = await this.databaseService.query(`
        SELECT id, name, description, is_default, is_active, created_at, updated_at
        FROM config_templates 
        WHERE is_active = TRUE 
        ORDER BY is_default DESC, created_at ASC
      `);
            return configTemplates;
        }
        catch (error) {
            console.error('[DocumentTemplateService] 获取配置模板失败:', error);
            return [];
        }
    }
    async getAllTemplatesOverview() {
        try {
            const [documentTemplates, configTemplates] = await Promise.all([
                this.getDocumentTemplates({ limit: 100 }),
                this.getConfigTemplates()
            ]);
            return {
                success: true,
                data: {
                    documentTemplates: documentTemplates.data,
                    configTemplates,
                    summary: {
                        totalDocumentTemplates: documentTemplates.data.total,
                        totalConfigTemplates: configTemplates.length,
                        totalTemplates: documentTemplates.data.total + configTemplates.length
                    }
                }
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 获取模板概览失败:', error);
            throw new common_1.HttpException(`获取模板概览失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async searchTemplates(searchOptions) {
        try {
            const { keyword, type, limit = 20 } = searchOptions;
            const results = {
                documentTemplates: [],
                configTemplates: [],
                total: 0
            };
            if (!type || type === 'document') {
                const documentResult = await this.getDocumentTemplates({
                    limit,
                    status: 'all'
                });
                results.documentTemplates = documentResult.data.templates.filter((template) => !keyword ||
                    template.name.toLowerCase().includes(keyword.toLowerCase()) ||
                    (template.description && template.description.toLowerCase().includes(keyword.toLowerCase())));
            }
            if (!type || type === 'config') {
                const configResult = await this.getConfigTemplates();
                results.configTemplates = configResult.filter((template) => !keyword ||
                    template.name.toLowerCase().includes(keyword.toLowerCase()) ||
                    (template.description && template.description.toLowerCase().includes(keyword.toLowerCase())));
            }
            results.total = results.documentTemplates.length + results.configTemplates.length;
            return {
                success: true,
                data: results
            };
        }
        catch (error) {
            console.error('[DocumentTemplateService] 搜索模板失败:', error);
            throw new common_1.HttpException(`搜索模板失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.DocumentTemplateService = DocumentTemplateService;
exports.DocumentTemplateService = DocumentTemplateService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _a : Object, typeof (_b = typeof filenet_service_1.FilenetService !== "undefined" && filenet_service_1.FilenetService) === "function" ? _b : Object])
], DocumentTemplateService);


/***/ })

};
exports.runtime =
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("650261236c6efed65685")
/******/ })();
/******/ 
/******/ }
;