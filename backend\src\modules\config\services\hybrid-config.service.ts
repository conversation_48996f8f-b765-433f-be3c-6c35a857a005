import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';
import envConfig, { AppConfig } from '../../../config/env.config';

/**
 * 混合配置管理服务
 * 
 * 优先级策略：
 * 1. 数据库连接配置 - 始终从环境变量读取 (安全考虑)
 * 2. 其他配置 - 优先从数据库读取，回退到环境变量
 * 3. 缓存机制 - 减少数据库查询频率
 * 
 * @class HybridConfigService
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 1.0.0
 */
@Injectable()
export class HybridConfigService {
  private readonly logger = new Logger(HybridConfigService.name);
  private configCache = new Map<string, string>();
  private cacheUpdatedAt = new Date(0);
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
  private isInitialized = false;

  constructor(
    private readonly databaseService: DatabaseService,
  ) {
    // 延迟初始化，等待数据库服务准备就绪
    setTimeout(() => this.initializeCache(), 2000);
  }

  /**
   * 获取完整的应用配置
   * 结合数据库配置和环境变量配置
   */
  async getAppConfig(): Promise<AppConfig> {
    await this.ensureCacheValid();

    return {
      // 基础配置 - 保持从环境变量读取
      node_env: envConfig.node_env,
      port: envConfig.port,
      frontend_port: envConfig.frontend_port,

      // 数据库配置 - 始终从环境变量读取 (安全考虑)
      database: envConfig.database,

      // JWT配置 - 优先从数据库读取
      jwt: {
        secret: await this.getConfigValue('jwt.api.secret', envConfig.jwt.secret),
        expiresIn: await this.getConfigValue('jwt.api.expires_in', envConfig.jwt.expiresIn),
      },

      // CORS配置 - 优先从数据库读取
      cors: {
        origin: await this.getConfigValue('cors.origin', envConfig.cors.origin),
        allowedOrigins: await this.getConfigArrayValue('cors.allowed_origins', envConfig.cors.allowedOrigins),
      },

      // OnlyOffice配置 - 优先从数据库读取
      onlyoffice: {
        serverUrl: await this.getConfigValue('onlyoffice.server_url', envConfig.onlyoffice.serverUrl),
        documentServerUrl: await this.getConfigValue('onlyoffice.document_server_url', envConfig.onlyoffice.documentServerUrl),
        documentPort: await this.getConfigNumberValue('onlyoffice.document_port', envConfig.onlyoffice.documentPort),
        secretKey: await this.getConfigValue('jwt.onlyoffice.secret', envConfig.onlyoffice.secretKey),
      },

      // FileNet配置 - 优先从数据库读取
      filenet: {
        host: await this.getConfigValue('filenet.host', envConfig.filenet.host),
        port: await this.getConfigNumberValue('filenet.port', envConfig.filenet.port),
        username: await this.getConfigValue('filenet.username', envConfig.filenet.username),
        password: await this.getConfigValue('filenet.password', envConfig.filenet.password),
        defaultFolder: await this.getConfigValue('filenet.default_folder', envConfig.filenet.defaultFolder),
        defaultDocClass: await this.getConfigValue('filenet.default_doc_class', envConfig.filenet.defaultDocClass),
        defaultSourceType: await this.getConfigValue('filenet.default_source_type', envConfig.filenet.defaultSourceType),
        defaultBizTag: await this.getConfigValue('filenet.default_biz_tag', envConfig.filenet.defaultBizTag),
      },

      // 文件存储配置 - 优先从数据库读取
      storage: {
        uploadPath: await this.getConfigValue('storage.upload_path', envConfig.storage.uploadPath),
        tmpPath: await this.getConfigValue('storage.tmp_path', envConfig.storage.tmpPath),
        maxFileSize: await this.getConfigValue('storage.max_file_size', envConfig.storage.maxFileSize),
        allowedFileTypes: await this.getConfigArrayValue('storage.allowed_file_types', envConfig.storage.allowedFileTypes),
      },

      // 缓存配置 - 优先从数据库读取
      cache: {
        type: await this.getConfigValue('cache.type', envConfig.cache.type),
        ttl: await this.getConfigNumberValue('cache.ttl', envConfig.cache.ttl),
        maxSize: await this.getConfigNumberValue('cache.max_size', envConfig.cache.maxSize),
      },

      // Redis配置 - 优先从数据库读取
      redis: {
        host: await this.getConfigValue('redis.host', envConfig.redis.host),
        port: await this.getConfigNumberValue('redis.port', envConfig.redis.port),
        password: await this.getConfigValue('redis.password', envConfig.redis.password),
        db: await this.getConfigNumberValue('redis.db', envConfig.redis.db),
      },

      // 日志配置 - 优先从数据库读取
      logging: {
        level: await this.getConfigValue('logging.level', envConfig.logging.level),
        dir: await this.getConfigValue('logging.dir', envConfig.logging.dir),
        maxSize: await this.getConfigValue('logging.max_size', envConfig.logging.maxSize),
        maxFiles: await this.getConfigNumberValue('logging.max_files', envConfig.logging.maxFiles),
      },

      // 性能监控配置 - 优先从数据库读取
      monitoring: {
        enabled: await this.getConfigBooleanValue('monitoring.enabled', envConfig.monitoring.enabled),
        slowQueryThreshold: await this.getConfigNumberValue('monitoring.slow_query_threshold', envConfig.monitoring.slowQueryThreshold),
        requestTimeout: await this.getConfigNumberValue('monitoring.request_timeout', envConfig.monitoring.requestTimeout),
      },

      // 安全配置 - 优先从数据库读取
      security: {
        rateLimitWindowMs: await this.getConfigNumberValue('security.rate_limit_window_ms', envConfig.security.rateLimitWindowMs),
        rateLimitMaxRequests: await this.getConfigNumberValue('security.rate_limit_max_requests', envConfig.security.rateLimitMaxRequests),
        enableSecurityHeaders: await this.getConfigBooleanValue('security.enable_security_headers', envConfig.security.enableSecurityHeaders),
      },

      // 开发配置 - 保持从环境变量读取
      development: envConfig.development,

      // 服务器配置 - 优先从数据库读取
      server: {
        host: await this.getConfigValue('server.host', envConfig.server.host),
        callbackUrl: await this.getConfigValue('server.callback_url', envConfig.server.callbackUrl),
      },
    };
  }

  /**
   * 获取单个配置值 (字符串类型)
   */
  private async getConfigValue(key: string, defaultValue: string): Promise<string> {
    await this.ensureCacheValid();
    return this.configCache.get(key) || defaultValue;
  }

  /**
   * 获取数字类型配置值
   */
  private async getConfigNumberValue(key: string, defaultValue: number): Promise<number> {
    const value = await this.getConfigValue(key, defaultValue.toString());
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * 获取布尔类型配置值
   */
  private async getConfigBooleanValue(key: string, defaultValue: boolean): Promise<boolean> {
    const value = await this.getConfigValue(key, defaultValue.toString());
    return value.toLowerCase() === 'true';
  }

  /**
   * 获取数组类型配置值
   */
  private async getConfigArrayValue(key: string, defaultValue: string[]): Promise<string[]> {
    const value = await this.getConfigValue(key, defaultValue.join(','));
    if (!value) return defaultValue;
    return value.split(',').map(item => item.trim().replace(/^["']|["']$/g, ''));
  }

  /**
   * 确保缓存有效性
   */
  private async ensureCacheValid(): Promise<void> {
    if (!this.isInitialized || this.isCacheExpired()) {
      await this.refreshCache();
    }
  }

  /**
   * 检查缓存是否已过期
   */
  private isCacheExpired(): boolean {
    const now = new Date();
    return now.getTime() - this.cacheUpdatedAt.getTime() > this.CACHE_TTL;
  }

  /**
   * 初始化缓存
   */
  private async initializeCache(): Promise<void> {
    try {
      await this.refreshCache();
      this.isInitialized = true;
      this.logger.log('混合配置服务初始化完成');
    } catch (error) {
      this.logger.error('混合配置服务初始化失败:', error);
      // 初始化失败时，配置服务仍然可用，只是会使用环境变量默认值
      this.isInitialized = true;
    }
  }

  /**
   * 刷新配置缓存
   */
  private async refreshCache(): Promise<void> {
    if (!this.databaseService) {
      this.logger.warn('数据库服务未可用，使用环境变量配置');
      return;
    }

    try {
      const query = `
        SELECT setting_key, setting_value
        FROM system_settings
        WHERE setting_key NOT IN ('database_initialized')
      `;
      
      const configs = await this.databaseService.query(query);
      
      this.configCache.clear();
      configs.forEach((config: { setting_key: string; setting_value: string }) => {
        this.configCache.set(config.setting_key, config.setting_value);
      });
      
      this.cacheUpdatedAt = new Date();
      this.logger.log(`配置缓存已更新，从数据库加载 ${this.configCache.size} 项配置`);
    } catch (error) {
      this.logger.error('刷新配置缓存失败，将使用环境变量配置:', error);
    }
  }

  /**
   * 手动刷新缓存 (提供给外部调用)
   */
  async refreshConfigCache(): Promise<void> {
    await this.refreshCache();
  }

  /**
   * 获取配置来源信息
   */
  async getConfigSources(): Promise<{ [key: string]: 'database' | 'env' }> {
    await this.ensureCacheValid();
    
    const sources: { [key: string]: 'database' | 'env' } = {};
    
    // 标记数据库中存在的配置
    this.configCache.forEach((_, key) => {
      sources[key] = 'database';
    });
    
    // 标记必须从环境变量读取的配置
    sources['database.*'] = 'env';
    sources['development.*'] = 'env';
    sources['node_env'] = 'env';
    sources['port'] = 'env';
    sources['frontend_port'] = 'env';
    
    return sources;
  }

  /**
   * 获取配置统计信息
   */
  async getConfigStats(): Promise<{
    total: number;
    fromDatabase: number;
    fromEnv: number;
    cacheAge: number;
  }> {
    await this.ensureCacheValid();
    
    const sources = await this.getConfigSources();
    const fromDatabase = Object.values(sources).filter(source => source === 'database').length;
    const fromEnv = Object.values(sources).filter(source => source === 'env').length;
    const cacheAge = new Date().getTime() - this.cacheUpdatedAt.getTime();
    
    return {
      total: fromDatabase + fromEnv,
      fromDatabase,
      fromEnv,
      cacheAge,
    };
  }
} 