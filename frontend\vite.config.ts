import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://*************:3000',
        changeOrigin: true,
        preserveHeaderKeyCase: true,
        secure: false,
        ws: true,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🔗 [Vite代理] 代理请求:', {
              url: req.url,
              method: req.method,
              headers: {
                authorization: req.headers.authorization ? 'Bearer ***' : 'undefined',
                'content-type': req.headers['content-type'],
                'user-agent': req.headers['user-agent'] ? 'present' : 'undefined',
              },
            })
          })

          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('📥 [Vite代理] 代理响应:', {
              url: req.url,
              statusCode: proxyRes.statusCode,
              statusMessage: proxyRes.statusMessage,
            })
          })

          proxy.on('error', (err, req, res) => {
            console.error('❌ [Vite代理] 代理错误:', {
              url: req.url,
              error: err.message,
            })
          })
        },
      },
    },
  },
  build: {
    outDir: '../backend/public',
    emptyOutDir: true,
  },
})
