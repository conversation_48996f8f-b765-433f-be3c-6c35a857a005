import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';

/**
 * 扩展的请求接口，包含requestId
 */
interface RequestWithId extends Request {
  requestId?: string;
}

/**
 * 错误详情接口
 */
interface ErrorDetails {
  name?: string;
  stack?: string;
  [key: string]: unknown;
}

/**
 * 全局异常过滤器
 * 
 * 统一处理应用中的所有异常，提供一致的错误响应格式
 * 
 * @class GlobalExceptionFilter
 * @implements {ExceptionFilter}
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<RequestWithId>();

    // 确定HTTP状态码
    const status = this.getHttpStatus(exception);
    
    // 确定错误消息
    const message = this.getErrorMessage(exception);
    
    // 确定错误详情
    const errorDetails = this.getErrorDetails(exception);

    // 生成请求ID (如果不存在)
    const requestId = request.requestId || this.generateRequestId();

    // 构建错误响应
    const errorResponse = {
      success: false,
      error: {
        code: status,
        message,
        details: errorDetails,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
      },
      requestId,
      timestamp: new Date().toISOString(),
    };

    // 记录错误日志
    this.logError(exception, request, errorResponse);

    // 返回错误响应
    response.status(status).json(errorResponse);
  }

  /**
   * 获取HTTP状态码
   */
  private getHttpStatus(exception: unknown): number {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }
    
    // 默认返回500内部服务器错误
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(exception: unknown): string {
    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      if (typeof response === 'string') {
        return response;
      }
      if (typeof response === 'object' && response !== null) {
        return (response as { message?: string }).message || exception.message;
      }
    }

    if (exception instanceof Error) {
      return exception.message;
    }

    return '未知错误';
  }

  /**
   * 获取错误详情
   */
  private getErrorDetails(exception: unknown): ErrorDetails | null {
    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      if (typeof response === 'object' && response !== null) {
        return response as ErrorDetails;
      }
    }

    if (exception instanceof Error) {
      return {
        name: exception.name,
        stack: process.env.NODE_ENV === 'development' ? exception.stack : undefined,
      };
    }

    return null;
  }

  /**
   * 记录错误日志
   */
  private logError(exception: unknown, request: Request, errorResponse: unknown): void {
    const { method, url, headers, body } = request;
    
    console.error('GlobalExceptionFilter - 捕获异常:', {
      timestamp: new Date().toISOString(),
      exception: {
        name: exception instanceof Error ? exception.name : 'Unknown',
        message: exception instanceof Error ? exception.message : String(exception),
        stack: exception instanceof Error ? exception.stack : undefined,
      },
      request: {
        method,
        url,
        headers: {
          'user-agent': headers['user-agent'],
          'content-type': headers['content-type'],
          'authorization': headers.authorization ? '[REDACTED]' : undefined,
        },
        body: this.sanitizeBody(body),
      },
      response: errorResponse,
    });
  }

  /**
   * 清理请求体中的敏感信息
   */
  private sanitizeBody(body: unknown): unknown {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    
    // 隐藏敏感字段
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
} 