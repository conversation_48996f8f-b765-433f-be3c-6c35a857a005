import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '../../database/services/database.service';
import { Express } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

// 上传文件数据库记录类型定义
interface UploadedFileRow {
  id: string;
  original_name: string;
  filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  extension: string;
  file_hash: string;
  uploaded_by: string;
  template_id: string;
  is_deleted: boolean | number;
  uploaded_at: Date | string;
  created_at: Date | string;
  updated_at: Date | string;
}

interface CountResult {
  total: number;
}

/**
 * 文件上传服务
 * 提供文件上传、存储和管理功能
 * 迁移自原有的uploadService.js
 */
@Injectable()
export class UploadService {
  private readonly uploadPath: string;
  private readonly maxFileSize: number;
  private readonly allowedExtensions: string[];

  constructor(
    private configService: ConfigService,
    private databaseService: DatabaseService,
  ) {
    this.uploadPath = this.configService.get<string>('UPLOAD_PATH') || './uploads';
    this.maxFileSize = this.configService.get<number>('MAX_FILE_SIZE') || 100 * 1024 * 1024; // 100MB
    this.allowedExtensions = this.configService.get<string>('ALLOWED_EXTENSIONS')?.split(',') || [
      '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt', '.rtf'
    ];

    // 确保上传目录存在
    this.ensureUploadDirectory();
  }

  /**
   * 上传文件
   */
  async uploadFile(file: Express.Multer.File, uploadedBy?: string, templateId?: string) {
    try {
      console.log(`[UploadService] 开始上传文件: ${file.originalname}`);

      // 验证文件
      this.validateFile(file);

      // 生成文件信息
      const fileInfo = await this.generateFileInfo(file);

      // 保存文件到磁盘
      const filePath = await this.saveFileToDisk(file, fileInfo.filename);

      // 保存文件信息到数据库
      const documentId = await this.saveFileInfoToDatabase({
        ...fileInfo,
        filePath,
        uploadedBy: uploadedBy || 'system',
        templateId: templateId || 'default-edit'
      });

      console.log(`[UploadService] 文件上传成功: ${documentId}`);

      return {
        documentId,
        originalName: file.originalname,
        filename: fileInfo.filename,
        size: file.size,
        mimeType: file.mimetype,
        extension: fileInfo.extension,
        fileHash: fileInfo.fileHash,
        uploadedAt: new Date().toISOString(),
        url: `/api/uploads/${fileInfo.filename}`,
        downloadUrl: `/api/uploads/${documentId}/download`
      };
    } catch (error) {
      console.error('[UploadService] 文件上传失败:', error.message);
      throw new HttpException(
        error.message || '文件上传失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 下载文件
   */
  async downloadFile(documentId: string): Promise<{ filePath: string; originalName: string; mimeType: string }> {
    try {
      console.log(`[UploadService] 下载文件: ${documentId}`);

      // 从数据库获取文件信息
      const sql = `
        SELECT file_path, original_name, mime_type, is_deleted
        FROM uploaded_files
        WHERE id = ? AND is_deleted = FALSE
      `;

      const [fileRecord] = await this.databaseService.query(sql, [documentId]) as unknown as UploadedFileRow[];

      if (!fileRecord) {
        throw new HttpException('文件不存在', HttpStatus.NOT_FOUND);
      }

      // 检查文件是否存在于磁盘
      if (!fs.existsSync(fileRecord.file_path)) {
        throw new HttpException('文件已损坏或丢失', HttpStatus.NOT_FOUND);
      }

      return {
        filePath: fileRecord.file_path,
        originalName: fileRecord.original_name,
        mimeType: fileRecord.mime_type
      };
    } catch (error) {
      console.error('[UploadService] 下载文件失败:', error.message);
      throw new HttpException(
        error.message || '下载文件失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(documentId: string) {
    try {
      console.log(`[UploadService] 获取文件信息: ${documentId}`);

      const sql = `
        SELECT id, original_name, filename, file_size, mime_type, extension, 
               file_hash, uploaded_by, template_id, uploaded_at, created_at, updated_at
        FROM uploaded_files
        WHERE id = ? AND is_deleted = FALSE
      `;

      const [fileRecord] = await this.databaseService.query(sql, [documentId]) as unknown as UploadedFileRow[];

      if (!fileRecord) {
        throw new HttpException('文件不存在', HttpStatus.NOT_FOUND);
      }

      return {
        ...fileRecord,
        url: `/api/uploads/${fileRecord.filename}`,
        downloadUrl: `/api/uploads/${documentId}/download`
      };
    } catch (error) {
      console.error('[UploadService] 获取文件信息失败:', error.message);
      throw new HttpException(
        error.message || '获取文件信息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(documentId: string) {
    try {
      console.log(`[UploadService] 删除文件: ${documentId}`);

      // 软删除文件记录
      const sql = `
        UPDATE uploaded_files
        SET is_deleted = TRUE, updated_at = NOW()
        WHERE id = ? AND is_deleted = FALSE
      `;

      const result = await this.databaseService.query(sql, [documentId]);

      if (!result || result.length === 0) {
        throw new HttpException('文件不存在', HttpStatus.NOT_FOUND);
      }

      console.log(`[UploadService] 文件删除成功: ${documentId}`);
      return true;
    } catch (error) {
      console.error('[UploadService] 删除文件失败:', error.message);
      throw new HttpException(
        error.message || '删除文件失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取文件列表
   */
  async getFileList(options: Record<string, unknown> = {}) {
    try {
      const { page = 1, limit = 20, search, extension, uploadedBy } = options;
      const pageNum = Number(page) || 1;
      const limitNum = Number(limit) || 20;
      const offset = (pageNum - 1) * limitNum;

      console.log(`[UploadService] 获取文件列表, 页码: ${page}, 每页: ${limit}`);

      // 构建查询条件
      let whereClause = 'WHERE is_deleted = FALSE';
      const params: unknown[] = [];

      if (search) {
        whereClause += ' AND original_name LIKE ?';
        params.push(`%${search}%`);
      }

      if (extension) {
        whereClause += ' AND extension = ?';
        params.push(extension);
      }

      if (uploadedBy) {
        whereClause += ' AND uploaded_by = ?';
        params.push(uploadedBy);
      }

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM uploaded_files ${whereClause}`;
      const [countResult] = await this.databaseService.query(countSql, params) as unknown as CountResult[];
      const total = countResult.total;

      // 获取文件列表
      const listSql = `
        SELECT id, original_name, filename, file_size, mime_type, extension,
               uploaded_by, template_id, uploaded_at, created_at
        FROM uploaded_files
        ${whereClause}
        ORDER BY uploaded_at DESC
        LIMIT ? OFFSET ?
      `;

      params.push(limitNum, offset);
      const files = await this.databaseService.query(listSql, params) as unknown as UploadedFileRow[];

      // 添加URL信息
      const filesWithUrls = files.map(file => ({
        ...file,
        url: `/api/uploads/${file.filename}`,
        downloadUrl: `/api/uploads/${file.id}/download`
      }));

      return {
        files: filesWithUrls,
        pagination: {
          total,
          page: pageNum,
          limit: limitNum,
          totalPages: Math.ceil(Number(total) / limitNum)
        }
      };
    } catch (error) {
      console.error('[UploadService] 获取文件列表失败:', error.message);
      throw new HttpException(
        error.message || '获取文件列表失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 验证文件
   */
  private validateFile(file: Express.Multer.File) {
    // 检查文件大小
    if (file.size > this.maxFileSize) {
      throw new HttpException(
        `文件大小超过限制，最大允许 ${this.maxFileSize / 1024 / 1024}MB`,
        HttpStatus.BAD_REQUEST
      );
    }

    // 检查文件扩展名
    const extension = path.extname(file.originalname).toLowerCase();
    if (!this.allowedExtensions.includes(extension)) {
      throw new HttpException(
        `不支持的文件类型，允许的类型: ${this.allowedExtensions.join(', ')}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 生成文件信息
   */
  private async generateFileInfo(file: Express.Multer.File) {
    const extension = path.extname(file.originalname).toLowerCase();
    const fileHash = crypto.createHash('md5').update(file.buffer).digest('hex');
    const timestamp = Date.now();
    const filename = `${timestamp}_${fileHash}${extension}`;

    return {
      filename,
      extension,
      fileHash,
      size: file.size,
      mimeType: file.mimetype
    };
  }

  /**
   * 保存文件到磁盘
   */
  private async saveFileToDisk(file: Express.Multer.File, filename: string): Promise<string> {
    const filePath = path.join(this.uploadPath, filename);
    
    try {
      await fs.promises.writeFile(filePath, file.buffer);
      console.log(`[UploadService] 文件保存到磁盘: ${filePath}`);
      return filePath;
    } catch (error) {
      console.error('[UploadService] 保存文件到磁盘失败:', error.message);
      throw new HttpException('保存文件失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 保存文件信息到数据库
   */
  private async saveFileInfoToDatabase(fileInfo: Record<string, unknown>): Promise<string> {
    const documentId = this.generateUUID();

    const sql = `
      INSERT INTO uploaded_files 
      (id, original_name, filename, file_path, file_size, mime_type, extension, 
       file_hash, uploaded_by, template_id, uploaded_at, created_at, updated_at, is_deleted)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW(), FALSE)
    `;

    try {
      await this.databaseService.query(sql, [
        documentId,
        fileInfo.originalName || fileInfo.filename,
        fileInfo.filename,
        fileInfo.filePath,
        fileInfo.size,
        fileInfo.mimeType,
        fileInfo.extension,
        fileInfo.fileHash,
        fileInfo.uploadedBy,
        fileInfo.templateId
      ]);

      return documentId;
    } catch (error) {
      console.error('[UploadService] 保存文件信息到数据库失败:', error.message);
      throw new HttpException('保存文件信息失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 确保上传目录存在
   */
  private ensureUploadDirectory() {
    try {
      if (!fs.existsSync(this.uploadPath)) {
        fs.mkdirSync(this.uploadPath, { recursive: true });
        console.log(`[UploadService] 创建上传目录: ${this.uploadPath}`);
      }
    } catch (error) {
      console.error('[UploadService] 创建上传目录失败:', error.message);
      throw new Error(`无法创建上传目录: ${error.message}`);
    }
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
} 