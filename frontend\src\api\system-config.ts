/**
 * 系统配置管理API
 */

import request from '@/utils/request'
import type {
  SystemConfig,
  ConfigUpdateRequest,
  ConfigHistoryItem,
  ConfigTestResult,
} from '@/types/system-config'

/**
 * 系统配置API接口
 */
export const systemConfigApi = {
  /**
   * 获取所有配置项
   */
  getAllConfigs(): Promise<{ data: SystemConfig[] }> {
    return request.get('/system-config/all')
  },

  /**
   * 根据分类获取配置项
   */
  getConfigsByCategory(category: string): Promise<{ data: SystemConfig[] }> {
    return request.get(`/system-config/category/${category}`)
  },

  /**
   * 获取单个配置项
   */
  getConfig(key: string): Promise<{ data: SystemConfig }> {
    return request.get(`/system-config/${key}`)
  },

  /**
   * 更新配置项
   */
  updateConfig(key: string, data: ConfigUpdateRequest): Promise<{ message: string }> {
    return request.put(`/system-config/${key}`, data)
  },

  /**
   * 批量更新配置项
   */
  batchUpdateConfigs(
    configs: Array<{
      setting_key: string
      setting_value: string
      description?: string
    }>
  ): Promise<{ message: string }> {
    return request.put('/system-config/batch', { configs })
  },

  /**
   * 重置为默认配置
   */
  resetToDefaults(): Promise<{ message: string }> {
    return request.post('/system-config/reset')
  },

  /**
   * 获取配置变更历史
   */
  getConfigHistory(limit = 50): Promise<{ data: ConfigHistoryItem[] }> {
    return request.get('/system-config/history', { params: { limit } })
  },

  /**
   * 测试配置连接
   */
  testConfig(key: string): Promise<ConfigTestResult> {
    return request.post(`/system-config/${key}/test`)
  },

  /**
   * 导出配置
   */
  exportConfigs(): Promise<Blob> {
    return request.get('/system-config/export', {
      responseType: 'blob',
    })
  },

  /**
   * 导入配置
   */
  importConfigs(file: File): Promise<{ message: string; imported: number }> {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/system-config/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  /**
   * 验证配置格式
   */
  validateConfig(
    key: string,
    value: string
  ): Promise<{
    valid: boolean
    message?: string
  }> {
    return request.post('/system-config/validate', { key, value })
  },
}
