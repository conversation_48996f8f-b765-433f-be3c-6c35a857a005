<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice 设计原型库</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 60px;
      color: white;
    }

    .title {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .subtitle {
      font-size: 20px;
      opacity: 0.9;
      margin-bottom: 32px;
    }

    .stats {
      display: flex;
      justify-content: center;
      gap: 40px;
      flex-wrap: wrap;
    }

    .stat-item {
      text-align: center;
      background: rgba(255, 255, 255, 0.2);
      padding: 20px 30px;
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }

    .section {
      margin-bottom: 60px;
    }

    .section-title {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 32px;
      color: white;
      text-align: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .prototypes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
      gap: 30px;
    }

    .prototype-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      position: relative;
    }

    .prototype-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    }

    .card-preview {
      height: 240px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 64px;
      border-bottom: 1px solid #e9ecef;
    }

    .card-content {
      padding: 24px;
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #2c3e50;
    }

    .card-description {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 16px;
    }

    .card-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 20px;
    }

    .tag {
      background: #e3f2fd;
      color: #1976d2;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }

    .tag.new {
      background: #e8f5e9;
      color: #2e7d32;
    }

    .tag.popular {
      background: #fff3e0;
      color: #f57c00;
    }

    .card-actions {
      display: flex;
      gap: 12px;
    }

    .btn {
      padding: 10px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.2s ease;
      border: none;
      cursor: pointer;
    }

    .btn-primary {
      background: #667eea;
      color: white;
      flex: 1;
      text-align: center;
    }

    .btn-primary:hover {
      background: #5a67d8;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: #f8f9fa;
      color: #666;
      border: 1px solid #e9ecef;
    }

    .btn-secondary:hover {
      background: #e9ecef;
    }

    .footer {
      text-align: center;
      margin-top: 80px;
      padding: 40px 0;
      color: white;
      opacity: 0.8;
    }

    @media (max-width: 768px) {
      .container {
        padding: 20px 16px;
      }

      .title {
        font-size: 36px;
      }

      .subtitle {
        font-size: 18px;
      }

      .stats {
        gap: 20px;
      }

      .stat-item {
        padding: 16px 20px;
      }

      .prototypes-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }

    .copy-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      z-index: 1000;
    }

    .copy-notification.show {
      transform: translateX(0);
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <h1 class="title">🎨 OnlyOffice 设计原型库</h1>
      <p class="subtitle">企业级文档管理系统界面设计原型展示</p>
      <div class="stats">
        <div class="stat-item">
          <div class="stat-number">12</div>
          <div class="stat-label">设计原型</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">7</div>
          <div class="stat-label">设计风格</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">100%</div>
          <div class="stat-label">响应式设计</div>
        </div>
      </div>
    </header>

    <!-- 首页设计原型 -->
    <section class="section">
      <h2 class="section-title">🏠 首页设计原型</h2>
      <div class="prototypes-grid">
        <!-- 明亮经典风格 -->
        <div class="prototype-card">
          <div class="card-preview">🏢</div>
          <div class="card-content">
            <h3 class="card-title">明亮经典风格首页</h3>
            <p class="card-description">采用经典企业级布局设计，明亮色调配合传统的卡片式排版，适合正式的企业环境。包含完整的左侧导航栏、系统状态监控和文档管理功能。</p>
            <div class="card-tags">
              <span class="tag new">新设计</span>
              <span class="tag">明亮风格</span>
              <span class="tag">经典布局</span>
              <span class="tag">企业级</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v5-bright-classic.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v5-bright-classic.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 明亮卡片风格 -->
        <div class="prototype-card">
          <div class="card-preview">🎨</div>
          <div class="card-content">
            <h3 class="card-title">明亮卡片风格首页</h3>
            <p class="card-description">现代化的卡片式设计，使用明亮的蓝色渐变主题。强调视觉层次和交互体验，适合追求现代感的企业。具有丰富的动画效果和交互反馈。</p>
            <div class="card-tags">
              <span class="tag new">新设计</span>
              <span class="tag">卡片设计</span>
              <span class="tag">现代感</span>
              <span class="tag">交互丰富</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v6-bright-cards.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v6-bright-cards.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 明亮简约风格 -->
        <div class="prototype-card">
          <div class="card-preview">🎯</div>
          <div class="card-content">
            <h3 class="card-title">明亮简约风格首页</h3>
            <p class="card-description">Google Material Design风格的简约设计，注重内容和功能性。使用干净的线条和充足的留白，提供清晰的信息架构和优秀的可用性。</p>
            <div class="card-tags">
              <span class="tag new">新设计</span>
              <span class="tag">Material Design</span>
              <span class="tag">简约风格</span>
              <span class="tag">高可用性</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v7-bright-minimal.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v7-bright-minimal.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 现代办公风格 -->
        <div class="prototype-card">
          <div class="card-preview">💼</div>
          <div class="card-content">
            <h3 class="card-title">现代办公风格首页</h3>
            <p class="card-description">专业的现代办公设计风格，结合了企业级功能和现代审美。使用渐变色彩和精致的细节处理，营造专业而不失活力的工作环境氛围。</p>
            <div class="card-tags">
              <span class="tag new">新设计</span>
              <span class="tag">办公风格</span>
              <span class="tag">专业感</span>
              <span class="tag">现代审美</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v8-bright-office.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v8-bright-office.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 现代商务风格 -->
        <div class="prototype-card">
          <div class="card-preview">🏢</div>
          <div class="card-content">
            <h3 class="card-title">现代商务风格首页</h3>
            <p class="card-description">专为商务环境设计的现代化首页，蓝色主题配合实时数据展示。包含用户信息面板、快捷操作区域和系统监控，适合日常办公使用。</p>
            <div class="card-tags">
              <span class="tag">商务风格</span>
              <span class="tag">数据展示</span>
              <span class="tag">实用性</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v2-modern-business.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v2-modern-business.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 数据密集型风格 -->
        <div class="prototype-card">
          <div class="card-preview">📊</div>
          <div class="card-content">
            <h3 class="card-title">数据密集型控制台</h3>
            <p class="card-description">重点突出数据展示和系统监控的设计风格。集成图表、KPI指标和实时监控功能，适合需要大量数据分析的管理场景。</p>
            <div class="card-tags">
              <span class="tag popular">热门</span>
              <span class="tag">数据驱动</span>
              <span class="tag">图表展示</span>
              <span class="tag">控制台</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v3-data-intensive.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v3-data-intensive.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 工作流风格 -->
        <div class="prototype-card">
          <div class="card-preview">📋</div>
          <div class="card-content">
            <h3 class="card-title">工作流管理风格</h3>
            <p class="card-description">专注于任务管理和工作流程的设计。采用看板布局和进度追踪，非常适合项目管理和团队协作场景。</p>
            <div class="card-tags">
              <span class="tag">工作流</span>
              <span class="tag">任务管理</span>
              <span class="tag">团队协作</span>
              <span class="tag">看板设计</span>
            </div>
            <div class="card-actions">
              <a href="homepage-v4-workflow.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('homepage-v4-workflow.html')">复制代码</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 文档页面设计原型 -->
    <section class="section">
      <h2 class="section-title">📄 文档页面设计原型</h2>
      <div class="prototypes-grid">
        <!-- 表格填报系统 -->
        <div class="prototype-card">
          <div class="card-preview preview-form">📋</div>
          <div class="card-content">
            <h3 class="card-title">表格填报系统</h3>
            <p class="card-description">专业的电子报价单填报界面，完整的表格交互功能，包含自动保存、实时计算器、数据验证等高级功能。适合复杂表格填写和企业报价流程。</p>
            <div class="card-tags">
              <span class="tag new">新设计</span>
              <span class="tag">表格填报</span>
              <span class="tag">自动保存</span>
              <span class="tag">数据验证</span>
            </div>
            <div class="card-actions">
              <a href="form-filling-demo.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('form-filling-demo.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 现代简约风格 -->
        <div class="prototype-card">
          <div class="card-preview">📋</div>
          <div class="card-content">
            <h3 class="card-title">现代简约文档页面</h3>
            <p class="card-description">简洁现代的文档管理界面，采用卡片式布局和清晰的信息层次。支持多种视图模式和高级搜索功能，提供优秀的用户体验。</p>
            <div class="card-tags">
              <span class="tag popular">热门</span>
              <span class="tag">简约设计</span>
              <span class="tag">卡片布局</span>
              <span class="tag">多视图</span>
            </div>
            <div class="card-actions">
              <a href="documents-page-v1-modern.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('documents-page-v1-modern.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 卡片展示风格 -->
        <div class="prototype-card">
          <div class="card-preview">🎴</div>
          <div class="card-content">
            <h3 class="card-title">卡片展示文档页面</h3>
            <p class="card-description">以大型卡片为主的展示风格，突出文档预览和视觉效果。适合需要强调文档内容展示的场景，提供丰富的交互动画。</p>
            <div class="card-tags">
              <span class="tag">卡片设计</span>
              <span class="tag">视觉突出</span>
              <span class="tag">预览模式</span>
              <span class="tag">动画效果</span>
            </div>
            <div class="card-actions">
              <a href="documents-page-v2-cards.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('documents-page-v2-cards.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 数据表格风格 -->
        <div class="prototype-card">
          <div class="card-preview">📊</div>
          <div class="card-content">
            <h3 class="card-title">数据表格风格</h3>
            <p class="card-description">传统的表格式布局，强调数据的完整性和快速浏览。支持排序、筛选、批量操作等高效的数据管理功能。</p>
            <div class="card-tags">
              <span class="tag">表格布局</span>
              <span class="tag">数据管理</span>
              <span class="tag">批量操作</span>
              <span class="tag">高效率</span>
            </div>
            <div class="card-actions">
              <a href="documents-page-v3-table.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('documents-page-v3-table.html')">复制代码</button>
            </div>
          </div>
        </div>

        <!-- 混合布局风格 -->
        <div class="prototype-card">
          <div class="card-preview">🎯</div>
          <div class="card-content">
            <h3 class="card-title">混合布局风格</h3>
            <p class="card-description">结合了列表、卡片和表格的混合布局。用户可以根据需要切换不同的显示模式，兼顾了美观性和实用性。</p>
            <div class="card-tags">
              <span class="tag">混合布局</span>
              <span class="tag">多模式</span>
              <span class="tag">用户友好</span>
              <span class="tag">灵活切换</span>
            </div>
            <div class="card-actions">
              <a href="documents-page-v4-hybrid.html" class="btn btn-primary" target="_blank">预览设计</a>
              <button class="btn btn-secondary" onclick="copyCode('documents-page-v4-hybrid.html')">复制代码</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <footer class="footer">
      <p>💡 所有设计原型都采用响应式设计，支持桌面端、平板和移动端设备</p>
      <p>🚀 使用现代CSS技术和最佳实践，确保在各种浏览器中的兼容性</p>
    </footer>
  </div>

  <div class="copy-notification" id="copyNotification">
    代码已复制到剪贴板！
  </div>

  <script>
    function copyCode(filename) {
      // 这里可以实现实际的代码复制功能
      // 现在只是显示一个通知
      const notification = document.getElementById('copyNotification');
      notification.classList.add('show');
      
      // 模拟复制操作
      const codeContent = `<!-- ${filename} 的代码内容 -->
<!-- 请在实际项目中查看完整代码 -->`;
      
      // 尝试使用现代的剪贴板API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(codeContent).then(() => {
          console.log('代码已复制到剪贴板');
        }).catch(err => {
          console.error('复制失败:', err);
        });
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = codeContent;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          document.execCommand('copy');
          console.log('代码已复制到剪贴板（降级方案）');
        } catch (err) {
          console.error('复制失败:', err);
        }
        document.body.removeChild(textArea);
      }
      
      setTimeout(() => {
        notification.classList.remove('show');
      }, 3000);
    }

    // 添加一些交互效果
    document.querySelectorAll('.prototype-card').forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-8px) scale(1.02)';
      });
      
      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0) scale(1)';
      });
    });

    // 平滑滚动效果
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  </script>
</body>
</html> 