import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

/**
 * JWT认证错误类型
 */
interface _JwtAuthInfo {
  name?: string;
  message?: string;
}

/**
 * JWT认证守卫
 * 
 * 继承自PassportJS的AuthGuard，专门用于JWT认证
 * 保护需要用户认证的路由端点
 * 
 * @class JwtAuthGuard
 * @extends AuthGuard
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  /**
   * 判断是否可以激活路由
   * @param context 执行上下文
   * @returns 是否允许访问
   */
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    
    console.log('🔍 [JwtAuthGuard] canActivate 开始:', {
      url: request.url,
      method: request.method,
      hasAuthHeader: !!request.headers?.authorization,
      authHeaderPrefix: request.headers?.authorization?.substring(0, 20) + '...',
      userAgent: request.headers?.['user-agent'],
      timestamp: new Date().toISOString(),
    });

    // 调用父类的canActivate方法进行JWT验证
    return super.canActivate(context);
  }

  /**
   * 处理请求，在认证失败时抛出异常
   * @param err 错误信息
   * @param user 用户信息
   * @param info 附加信息
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleRequest(err: any, user: any, info: any, context?: ExecutionContext) {
    const request = context?.switchToHttp()?.getRequest();
    
    console.log('🔍 [JwtAuthGuard] handleRequest:', {
      hasUser: !!user,
      hasError: !!err,
      hasInfo: !!info,
      userId: user?.sub,
      username: user?.username,
      errorMessage: err?.message,
      infoName: info?.name,
      infoMessage: info?.message,
      url: request?.url,
      timestamp: new Date().toISOString(),
    });

    // 如果有错误或者用户不存在，抛出未授权异常
    if (err || !user) {
      // 记录认证失败的详细信息
      const errorMessage = this.getAuthErrorMessage(err, info);
      
      console.error('❌ [JwtAuthGuard] 认证失败:', {
        url: request?.url,
        method: request?.method,
        hasAuthHeader: !!request?.headers?.authorization,
        authHeaderValue: request?.headers?.authorization || 'No auth token',
        errorMessage,
        err: err?.message,
        info: info?.message,
        timestamp: new Date().toISOString(),
      });
      
      throw new UnauthorizedException({
        message: '认证失败',
        error: errorMessage,
        statusCode: 401,
        timestamp: new Date().toISOString(),
      });
    }
    
    console.log('✅ [JwtAuthGuard] 认证成功:', {
      userId: user.sub,
      username: user.username,
      url: request?.url,
      timestamp: new Date().toISOString(),
    });
    
    return user;
  }

  /**
   * 获取认证错误信息
   * @param err 错误对象
   * @param info 信息对象
   * @returns 错误信息字符串
   */
  private getAuthErrorMessage(err: unknown, info: unknown): string {
    if (err && typeof err === 'object' && err !== null && 'message' in err) {
      return (err as Error).message || '认证过程中发生错误';
    }
    
    if (info && typeof info === 'object' && info !== null) {
      const infoObj = info as Record<string, unknown>;
      // 处理JWT相关错误
      switch (infoObj.name) {
        case 'TokenExpiredError':
          return '访问令牌已过期，请刷新令牌';
        case 'JsonWebTokenError':
          return '无效的访问令牌格式';
        case 'NotBeforeError':
          return '令牌尚未生效';
        default:
          return (typeof infoObj.message === 'string' ? infoObj.message : null) || '令牌验证失败';
      }
    }
    
    return '缺少有效的访问令牌';
  }
} 