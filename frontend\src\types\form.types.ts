/**
 * 表单相关类型定义
 * @description 定义表单验证和表单数据的TypeScript类型
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

// Ant Design Form验证失败错误信息
export interface ValidateErrorEntity {
  values: Record<string, unknown>
  errorFields: FieldError[]
  outOfDate: boolean
}

// 单个字段错误信息
export interface FieldError {
  name: string[]
  errors: string[]
  warnings?: string[]
}

// 表单验证规则
export interface FormValidationRule {
  required?: boolean
  message?: string
  type?:
    | 'string'
    | 'number'
    | 'boolean'
    | 'method'
    | 'regexp'
    | 'integer'
    | 'float'
    | 'array'
    | 'object'
    | 'enum'
    | 'date'
    | 'url'
    | 'hex'
    | 'email'
  pattern?: RegExp
  min?: number
  max?: number
  len?: number
  validator?: (rule: FormValidationRule, value: unknown) => Promise<void> | void
  trigger?: string | string[]
}

// 通用表单字段类型
export interface FormField {
  name: string
  label: string
  rules?: FormValidationRule[]
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

// 登录表单数据
export interface LoginFormData {
  username: string
  password: string
  remember?: boolean
}

// 用户注册表单数据
export interface RegisterFormData {
  username: string
  password: string
  confirmPassword: string
  email: string
  phone?: string
  fullName: string
}

// 文档上传表单数据
export interface DocumentUploadFormData {
  title: string
  description?: string
  category: string
  tags?: string[]
  file: File
  isPublic?: boolean
}

// 模板配置表单数据
export interface TemplateConfigFormData {
  name: string
  description?: string
  type: 'document' | 'spreadsheet' | 'presentation'
  category: string
  isDefault?: boolean
  config: Record<string, unknown>
}

// 用户信息表单数据
export interface UserInfoFormData {
  username: string
  email: string
  fullName: string
  phone?: string
  department?: string
  role: string
  isActive: boolean
}

// 系统配置表单数据
export interface SystemConfigFormData {
  siteName: string
  siteDescription?: string
  maxFileSize: number
  allowedFileTypes: string[]
  enableRegistration: boolean
  enableGuestAccess: boolean
  defaultLanguage: string
  timezone: string
}

// 表单提交状态
export interface FormSubmitState {
  loading: boolean
  error?: string
  success?: boolean
}
