{"name": "onlyoffice-backend-nestjs", "version": "1.0.0", "description": "OnlyOffice集成系统后端 - NestJS + TypeScript", "author": "OnlyOffice Integration Team", "private": true, "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:dev:hot": "nest start --watch --webpack", "start:debug": "nest start --debug --watch", "start:debug:hot": "nest start --debug --watch --webpack", "start:prod": "node dist/main", "dev": "nest start --watch", "dev:hot": "nest start --watch --webpack", "dev:debug": "nest start --debug --watch --webpack", "dev:enhanced": "node scripts/dev-hot.js", "hot": "node scripts/dev-hot.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\" --max-warnings 0", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "check-types": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "pre-commit": "npm run lint:check && npm run format:check && npm run check-types", "validate": "npm run pre-commit && npm run test", "prepare": "husky install", "init-db": "ts-node src/scripts/init-database.ts", "init-permissions": "ts-node src/scripts/init-permissions.ts", "init-users": "ts-node src/scripts/init-users.ts", "config:check": "ts-node src/scripts/config-migration.ts", "config:verify": "npm run config:check"}, "dependencies": {"@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^10.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/typeorm": "^10.0.0", "@types/bcrypt": "^5.0.2", "axios": "^1.7.8", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "fs-extra": "^11.2.0", "helmet": "^7.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "mysql2": "^3.14.1", "onlyoffice-backend-nestjs": "file:", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.17.0", "ws": "^8.18.0", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.0", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.0", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.10", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "chokidar": "^3.6.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^15.5.2", "prettier": "^3.0.0", "rimraf": "^5.0.5", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "webpack-node-externals": "^3.0.0"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run validate"}}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "keywords": ["onlyoffice", "<PERSON><PERSON><PERSON>", "typescript", "api", "enterprise", "document-management"]}