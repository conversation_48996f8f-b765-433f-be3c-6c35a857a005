---
description: 
globs: 
alwaysApply: true
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
# OnlyOffice集成系统 - 程序员约束规则 (Developer Rules)

> **版本**: v1.0  
> **更新时间**: 2024年12月19日  
> **适用范围**: 所有参与OnlyOffice集成系统开发的程序员  
> **强制执行**: 通过代码审查、自动化工具和CI/CD流程确保执行  

## 🎯 核心开发原则

### 1. 架构升级约束
- **✅ 必须遵循**: 从Node.js+Express.js平滑升级到Ant Design Pro + Vue 3 + TypeScript+nest.js架构
- **✅ 新代码位置**: 所有新代码必须放在`frontend/`和`backend/`目录下
- **⚠️ 老代码处理**: 老项目代码仅供参考，不得直接修改，需要重构到新架构中
- **🚫 禁止**: 在老项目目录中添加新功能

### 2. 技术栈强制要求
- **后端**: 必须使用TypeScript + nest.js + RESTful API
- **前端**: 必须使用Vue 3 + TypeScript + Ant Design Pro
- **数据管理**: 使用Node-cache + EventEmitter（保留Redis + RabbitMQ扩展性）
- **代码质量**: 强制使用ESLint、Prettier、Jest测试

## 📁 文件和目录命名强制规范

### 🗂️ 目录命名 (kebab-case)
```bash
✅ 正确示例:
backend/src/error-handlers/
frontend/src/config-templates/
doc/api-documentation/

❌ 错误示例:
backend/src/errorHandlers/
frontend/src/configTemplates/
doc/apiDocumentation/
```

### 📄 文件命名规范

#### 后端文件 (PascalCase + 后缀)
```typescript
✅ 控制器: DocumentController.ts, UserController.ts
✅ 服务类: DocumentService.ts, CacheService.ts
✅ 中间件: AuthMiddleware.ts, ValidationMiddleware.ts
✅ 类型定义: document.types.ts, user.types.ts (kebab-case)
✅ 配置文件: database.ts, app-config.ts (kebab-case)

❌ 禁止: documentController.ts, Document_Controller.ts
```

#### 前端文件 (PascalCase组件 + kebab-case其他)
```vue
✅ Vue组件: DocumentList.vue, UserManagement.vue
✅ 组合函数: useDocuments.ts, useAuth.ts
✅ API封装: document-api.ts, user-api.ts
✅ 类型定义: document.types.ts, api.types.ts

❌ 禁止: documentList.vue, use_documents.ts
```

## 🏷️ 代码命名强制规范

### 变量命名
```typescript
✅ 基础变量 (camelCase):
const documentId = "doc_123";
const uploadedFiles = [];
const isAuthenticated = false;

✅ 常量 (SCREAMING_SNAKE_CASE):
const MAX_UPLOAD_SIZE = 100 * 1024 * 1024;
const API_BASE_URL = "http://localhost:3000/api/v1";

✅ 私有变量 (下划线前缀):
private _cachedDocuments = new Map();
private _connectionPool = null;

✅ 布尔变量 (is/has/can/should前缀):
const isLoading = true;
const hasPermission = false;
const canEdit = true;

❌ 禁止: DocumentId, document_id, IsLoading
```

### 函数命名 (camelCase + 动词开头)
```typescript
✅ 控制器方法:
async createDocument()
async getDocumentById()
async updateDocument()
async deleteDocument()

✅ 服务方法:
async validateUser()
async processUpload()
async generateToken()

❌ 禁止: CreateDocument(), get_document_by_id(), DocumentCreate()
```

## 🔧 开发流程强制要求

### 1. 环境配置约束
- **✅ 必须使用**: 根目录的`.env`文件管理所有环境变量
- **🚫 禁止**: 在代码中硬编码配置信息

### 2. API开发约束
- **✅ 必须**: 使用RESTful API设计规范
- **✅ 必须**: 集成Swagger文档自动生成
- **✅ 必须**: 所有API使用TypeScript类型定义
- **🚫 禁止**: 使用非标准HTTP方法和状态码

### 3. 数据库操作约束
- **✅ 必须**: 使用连接池管理数据库连接
- **✅ 必须**: 所有数据库操作必须有错误处理
- **✅ 必须**: 使用参数化查询防止SQL注入
- **🚫 禁止**: 直接拼接SQL字符串

### 4. 缓存和事件管理约束
- **✅ 必须**: 使用Node-cache + EventEmitter总线管理方式
- **✅ 必须**: 保留Redis + RabbitMQ扩展接口
- **⚠️ 设计**: 充分考虑后续扩展性

## 🧪 测试和质量约束

### 1. 单元测试强制要求
- **✅ 必须**: 所有开发任务完成后进行单元测试
- **✅ 必须**: 使用Jest测试框架
- **✅ 必须**: 测试覆盖率不低于80%
- **🚫 禁止**: 提交未经测试的代码

### 2. 代码质量检查
- **✅ 必须**: 通过ESLint检查，无警告和错误
- **✅ 必须**: 使用Prettier格式化代码
- **✅ 必须**: 通过TypeScript类型检查
- **🚫 禁止**: 使用`any`类型（除非特殊情况并注释说明）

### 3. 代码审查要求
- **✅ 必须**: 所有代码提交前进行代码审查
- **✅ 必须**: 遵循Git提交信息规范
- **✅ 必须**: 功能分支开发，禁止直接在main分支提交

## 📚 文档和注释约束

### 1. 文档要求
- **✅ 必须**: 说明类文档放在根目录`doc/`下
- **✅ 必须**: API文档使用Swagger自动生成
- **✅ 必须**: 重要功能模块提供README说明
- **⚠️ 格式**: 所有文档使用Markdown格式

### 2. 代码注释要求
```typescript
✅ 类和接口注释:
/**
 * 文档管理服务类
 * @description 处理文档的CRUD操作和权限验证
 * <AUTHOR>
 * @since 2024-12-19
 */
class DocumentService {
  /**
   * 创建新文档
   * @param documentData 文档数据
   * @param userId 用户ID
   * @returns Promise<Document> 创建的文档对象
   * @throws {ValidationError} 当文档数据无效时
   */
  async createDocument(documentData: CreateDocumentDto, userId: string): Promise<Document> {
    // 实现逻辑
  }
}

✅ 复杂逻辑注释:
// TODO: 优化文件上传性能，考虑使用流式处理
// FIXME: 修复并发上传时的竞态条件问题
// NOTE: 此处使用缓存提高查询性能
```

## 🚨 安全和性能约束

### 1. 安全要求
- **✅ 必须**: 所有用户输入进行验证和清理
- **✅ 必须**: 使用JWT进行身份认证
- **✅ 必须**: 敏感信息使用环境变量管理
- **🚫 禁止**: 在代码中暴露密码、密钥等敏感信息

### 2. 性能要求
- **✅ 必须**: 数据库查询使用索引优化
- **✅ 必须**: 大文件上传使用流式处理
- **✅ 必须**: 合理使用缓存减少数据库压力
- **⚠️ 监控**: 关键接口响应时间不超过2秒

## 🔄 版本控制和部署约束

### 1. Git工作流
```bash
✅ 分支命名规范:
feature/document-upload-optimization
bugfix/database-connection-issue
hotfix/security-vulnerability-fix

✅ 提交信息格式:
feat: 添加文档批量上传功能
fix: 修复数据库连接池配置问题
docs: 更新API文档
test: 添加用户认证单元测试

❌ 禁止: 
update, fix bug, 修改
```

### 2. 部署要求
- **✅ 必须**: 使用环境变量区分开发、测试、生产环境
- **✅ 必须**: 生产部署前通过所有测试
- **✅ 必须**: 数据库迁移脚本版本化管理
- **🚫 禁止**: 直接在生产环境调试代码


## 📋 违规处理机制

### 1. 自动检查
- **ESLint**: 代码提交时自动检查，不通过则拒绝提交
- **Prettier**: 保存时自动格式化
- **TypeScript**: 编译时类型检查
- **Jest**: CI/CD流程中自动运行测试

### 2. 人工审查
- **代码审查**: 每个PR必须至少一人审查通过
- **架构审查**: 重大架构变更需要技术负责人审批
- **文档审查**: 文档更新需要相关人员确认

### 3. 违规后果
- **轻微违规**: 代码审查时要求修改
- **重复违规**: 强制培训和指导
- **严重违规**: 回滚代码并重新开发

---

## 📖 相关文档链接

- [代码规范和命名标准.md](mdc:代码规范和命名标准.md) - 详细的代码规范说明
- [环境配置说明.md](mdc:环境配置说明.md) - 环境变量配置指南
- [TODO.md](mdc:TODO.md) - 项目任务和进度跟踪
- [README.md](mdc:README.md) - 项目介绍和快速开始

---

**⚠️ 重要提醒**: 本规则文档具有强制约束力，所有开发人员必须严格遵守。如有疑问或建议，请及时与项目负责人沟通。 
