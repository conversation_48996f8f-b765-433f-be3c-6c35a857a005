<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 企业级版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2d3748;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 32px;
        }

        /* 头部区域 - 企业级设计 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 24px 0;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .app-logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            font-weight: bold;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .header-text h1 {
            font-size: 32px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 6px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-text p {
            color: #718096;
            font-size: 16px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .btn-outline {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        /* 主要内容布局 */
        .main-layout {
            display: grid;
            grid-template-columns: 420px 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        /* 左侧模板区域 - 企业级卡片 */
        .templates-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 36px;
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
            height: fit-content;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f7fafc;
        }

        .section-title {
            font-size: 22px;
            font-weight: 700;
            color: #1a202c;
        }

        .section-subtitle {
            font-size: 14px;
            color: #718096;
            margin-top: 6px;
        }

        .add-btn {
            width: 48px;
            height: 48px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }

        .add-btn:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .search-container {
            position: relative;
            margin-bottom: 28px;
        }

        .search-input {
            width: 100%;
            padding: 18px 24px 18px 56px;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            font-size: 15px;
            background: rgba(247, 250, 252, 0.8);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 16px;
        }

        .template-card {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(226, 232, 240, 0.5);
            border-radius: 20px;
            padding: 28px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .template-card:hover {
            border-color: rgba(102, 126, 234, 0.5);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
        }

        .template-card:hover::before {
            opacity: 1;
        }

        .template-card.active {
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 16px 48px rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }

        .template-card.active::before {
            opacity: 1;
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .template-name {
            font-size: 19px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .template-status {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 6px 14px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-default {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .status-active {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }

        .status-inactive {
            background: #e2e8f0;
            color: #718096;
        }

        .template-desc {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #a0aec0;
            padding-top: 16px;
            border-top: 1px solid #f1f5f9;
        }

        .template-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 10px;
            background: rgba(226, 232, 240, 0.7);
            color: #718096;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 13px;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        /* 右侧配置区域 - 企业级设计 */
        .config-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            height: fit-content;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .config-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .config-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: rotate(0deg) translate(-50%, -50%); }
            100% { transform: rotate(360deg) translate(-50%, -50%); }
        }

        .config-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .config-subtitle {
            opacity: 0.9;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .config-nav {
            background: rgba(247, 250, 252, 0.9);
            padding: 0 32px;
            display: flex;
            gap: 8px;
            overflow-x: auto;
            backdrop-filter: blur(10px);
        }

        .nav-tab {
            padding: 20px 28px;
            background: transparent;
            border: none;
            color: #718096;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 16px 16px 0 0;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
        }

        .nav-tab.active {
            background: rgba(255, 255, 255, 0.95);
            color: #667eea;
            backdrop-filter: blur(10px);
        }

        .nav-tab:hover {
            color: #667eea;
            background: rgba(255, 255, 255, 0.7);
        }

        .config-content {
            padding: 40px;
            max-height: 700px;
            overflow-y: auto;
        }

        .config-group {
            margin-bottom: 48px;
        }

        .group-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f7fafc;
        }

        .group-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .group-title {
            font-size: 22px;
            font-weight: 700;
            color: #1a202c;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
            gap: 28px;
        }

        .config-card {
            background: rgba(247, 250, 252, 0.8);
            border: 2px solid rgba(226, 232, 240, 0.5);
            border-radius: 20px;
            padding: 28px;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
        }

        .config-card:hover {
            border-color: rgba(102, 126, 234, 0.3);
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }

        .config-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .config-item-title {
            font-size: 17px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 6px;
        }

        .config-item-desc {
            font-size: 13px;
            color: #718096;
            line-height: 1.5;
        }

        .required-indicator {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .switch-controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .switch-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .switch-label {
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
        }

        .toggle-switch {
            position: relative;
            width: 56px;
            height: 32px;
            background: #e2e8f0;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .toggle-thumb {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 28px;
            height: 28px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-thumb {
            transform: translateX(24px);
        }

        .config-status {
            margin-top: 20px;
            padding: 16px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            border-left: 4px solid #667eea;
            backdrop-filter: blur(10px);
        }

        .status-text {
            font-size: 13px;
            color: #667eea;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 18px;
            height: 18px;
            border-radius: 50%;
        }

        .status-enabled {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
        }

        .status-disabled {
            background: #cbd5e0;
        }

        /* 底部操作栏 */
        .action-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 28px 40px;
            border-top: 1px solid rgba(226, 232, 240, 0.5);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .save-info {
            color: #718096;
            font-size: 14px;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
        }

        .btn-secondary {
            background: rgba(226, 232, 240, 0.8);
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 100px 40px;
            color: #718096;
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 32px;
            opacity: 0.4;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .empty-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            color: #4a5568;
        }

        .empty-desc {
            font-size: 16px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .main-layout {
                grid-template-columns: 380px 1fr;
                gap: 32px;
            }
        }

        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .templates-section {
                order: 2;
            }

            .config-section {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .header-content {
                flex-direction: column;
                gap: 24px;
                text-align: center;
            }

            .config-grid {
                grid-template-columns: 1fr;
            }

            .config-content,
            .templates-section {
                padding: 24px;
            }
        }

        /* 加载动画增强 */
        @keyframes slideInUpEnterprise {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .template-card,
        .config-card {
            animation: slideInUpEnterprise 0.4s ease;
        }

        /* 滚动条企业级样式 */
        .config-content::-webkit-scrollbar {
            width: 8px;
        }

        .config-content::-webkit-scrollbar-track {
            background: rgba(247, 250, 252, 0.5);
            border-radius: 4px;
        }

        .config-content::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        .config-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #5a67d8, #6b46c1);
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="header-left">
                    <div class="app-logo">OO</div>
                    <div class="header-text">
                        <h1>企业配置中心</h1>
                        <p>专业级文档编辑器配置管理平台</p>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-download"></i>
                        导入配置
                    </a>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i>
                        新建模板
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <div class="container">
        <div class="main-layout">
            <!-- 左侧模板列表 -->
            <aside class="templates-section">
                <div class="section-header">
                    <div>
                        <h2 class="section-title">配置模板库</h2>
                        <p class="section-subtitle">企业级配置模板管理</p>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索企业配置模板...">
                </div>

                <div class="templates-list">
                    <div class="template-card active">
                        <div class="template-header">
                            <div class="template-name">企业标准模板</div>
                            <div class="template-status">
                                <span class="status-badge status-default">默认</span>
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            企业级标准配置，包含完整的权限控制、安全设置和合规要求，适用于大型企业的文档管理需求。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-19</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">安全审计模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            高安全级别配置，启用全面的审计日志、访问控制和数据保护功能，满足金融和政府机构要求。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-18</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-name">部门协作模板</div>
                            <div class="template-status">
                                <span class="status-badge status-active">启用</span>
                            </div>
                        </div>
                        <div class="template-desc">
                            专为部门间协作优化，支持实时编辑、评论和工作流程，提升团队协作效率和项目管理能力。
                        </div>
                        <div class="template-meta">
                            <span>更新于 12-17</span>
                            <div class="template-actions">
                                <button class="action-btn" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧配置区域 -->
            <main class="config-section">
                <div class="config-header">
                    <h2 class="config-title">企业标准模板配置</h2>
                    <p class="config-subtitle">专业级配置管理，满足企业级安全和合规要求</p>
                </div>

                <nav class="config-nav">
                    <button class="nav-tab active">权限管理</button>
                    <button class="nav-tab">安全设置</button>
                    <button class="nav-tab">合规控制</button>
                    <button class="nav-tab">审计日志</button>
                    <button class="nav-tab">高级选项</button>
                </nav>

                <div class="config-content">
                    <div class="config-group">
                        <div class="group-header">
                            <div class="group-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="group-title">企业权限控制</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">管理员权限</div>
                                        <div class="config-item-desc">超级管理员的完整文档控制权限</div>
                                    </div>
                                    <span class="required-indicator">必需</span>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">启用管理员模式</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">界面显示权限</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        管理员权限已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">部门权限</div>
                                        <div class="config-item-desc">基于部门的细化权限控制</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">部门级权限</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">界面显示权限</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        部门权限已启用并可见
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">外部访客权限</div>
                                        <div class="config-item-desc">临时访客的受限访问权限</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">允许访客访问</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">界面显示选项</span>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-disabled"></div>
                                        访客权限已禁用且隐藏
                                    </div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="config-item-header">
                                    <div>
                                        <div class="config-item-title">审批流程</div>
                                        <div class="config-item-desc">文档编辑和发布的审批机制</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <span class="switch-label">启用审批流程</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <span class="switch-label">界面显示功能</span>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-text">
                                        <div class="status-icon status-enabled"></div>
                                        审批流程已启用并可见
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-bar">
                    <div class="save-info">
                        <i class="fas fa-clock"></i>
                        最后同步于 12月19日 15:45
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            重置配置
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存并部署
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模板卡片选择
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    templateCards.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 标签页切换
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    navTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 开关切换
            const toggleSwitches = document.querySelectorAll('.toggle-switch');
            toggleSwitches.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 更新状态显示
                    const card = this.closest('.config-card');
                    const statusText = card.querySelector('.status-text');
                    const switches = card.querySelectorAll('.toggle-switch');
                    
                    const enableSwitch = switches[0];
                    const visibilitySwitch = switches[1];
                    
                    if (enableSwitch.classList.contains('active') && visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>功能已启用并可见';
                    } else if (enableSwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-enabled"></div>功能已启用但隐藏';
                    } else if (visibilitySwitch.classList.contains('active')) {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>功能已禁用但可见';
                    } else {
                        statusText.innerHTML = '<div class="status-icon status-disabled"></div>功能已禁用且隐藏';
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                templateCards.forEach(card => {
                    const name = card.querySelector('.template-name').textContent.toLowerCase();
                    const desc = card.querySelector('.template-desc').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || desc.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html> 