/**
 * OnlyOffice配置管理路由
 */
const express = require('express');
const router = express.Router();
const configService = require('../services/configService');

/**
 * 获取当前OnlyOffice配置
 */
router.get('/api/onlyoffice-config', async (req, res) => {
    try {
        const config = await configService.getConfig();
        const descriptions = configService.getConfigDescriptions();

        res.json({
            success: true,
            config,
            descriptions
        });
    } catch (error) {
        console.error('获取OnlyOffice配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取配置失败: ' + error.message
        });
    }
});

/**
 * 更新OnlyOffice配置
 */
router.post('/api/onlyoffice-config', async (req, res) => {
    try {
        const { config } = req.body;

        if (!config) {
            return res.status(400).json({
                success: false,
                message: '配置数据不能为空'
            });
        }

        const result = await configService.saveConfig(config);
        res.json(result);
    } catch (error) {
        console.error('保存OnlyOffice配置失败:', error);
        res.status(500).json({
            success: false,
            message: '保存配置失败: ' + error.message
        });
    }
});

/**
 * 重置OnlyOffice配置为默认值
 */
router.post('/api/onlyoffice-config/reset', async (req, res) => {
    try {
        const result = await configService.resetConfig();
        res.json(result);
    } catch (error) {
        console.error('重置OnlyOffice配置失败:', error);
        res.status(500).json({
            success: false,
            message: '重置配置失败: ' + error.message
        });
    }
});

/**
 * 获取默认配置
 */
router.get('/api/onlyoffice-config/default', async (req, res) => {
    try {
        res.json({
            success: true,
            config: configService.DEFAULT_CONFIG
        });
    } catch (error) {
        console.error('获取默认配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取默认配置失败: ' + error.message
        });
    }
});

/**
 * 预览配置（用于测试编辑器效果）
 */
router.post('/api/onlyoffice-config/preview', async (req, res) => {
    try {
        const { config } = req.body;

        if (!config) {
            return res.status(400).json({
                success: false,
                message: '配置数据不能为空'
            });
        }

        // 这里可以添加配置验证逻辑
        const validationResult = validateConfig(config);

        res.json({
            success: true,
            valid: validationResult.valid,
            errors: validationResult.errors,
            config: config
        });
    } catch (error) {
        console.error('预览配置失败:', error);
        res.status(500).json({
            success: false,
            message: '预览配置失败: ' + error.message
        });
    }
});

/**
 * 配置验证函数
 */
function validateConfig(config) {
    const errors = [];

    // 验证必要的配置项
    if (!config.lang) {
        errors.push('语言配置不能为空');
    }

    if (!config.user || !config.user.id) {
        errors.push('用户配置不完整');
    }

    // 验证自定义配置
    if (config.customization) {
        // 验证缩放比例
        if (config.customization.zoom && (config.customization.zoom < 25 || config.customization.zoom > 500)) {
            errors.push('缩放比例必须在25%-500%之间');
        }

        // 验证主题
        if (config.customization.uiTheme && !['theme-light', 'theme-dark'].includes(config.customization.uiTheme)) {
            errors.push('无效的UI主题');
        }

        // 验证单位
        if (config.customization.unit && !['cm', 'pt', 'inch'].includes(config.customization.unit)) {
            errors.push('无效的测量单位');
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 获取当前生效的配置（用于测试）
 */
router.get('/api/onlyoffice-config/current', async (req, res) => {
    try {
        const config = await configService.getConfig();

        res.json({
            success: true,
            message: '当前生效的OnlyOffice配置',
            config: config,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('获取当前配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取当前配置失败: ' + error.message
        });
    }
});

module.exports = router; 