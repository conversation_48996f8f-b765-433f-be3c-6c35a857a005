{"name": "onlyoffice-integration", "version": "1.0.0", "description": "OnlyOffice文档服务器集成应用", "main": "app.js", "scripts": {"start": "node server.js", "dev2": "nodemon server.js", "dev": "nodemon server.js --watch ./ --ignore node_modules/ --ignore tmp/ --ignore uploads/", "init-db": "node scripts/init-database.js", "init-config-templates": "node scripts/init-config-templates.js", "cleanup": "node scripts/cleanup-duplicates.js", "migrate-uuid": "node scripts/migrate-to-uuid.js", "migrate-templates-to-uuid": "node scripts/migrate-templates-to-uuid.js"}, "keywords": ["onlyoffice", "document", "editor", "preview"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "ejs": "^3.1.9", "express": "^4.18.2", "express-fileupload": "^1.4.0", "form-data": "^4.0.2", "fs-extra": "^11.3.0", "jsonwebtoken": "^9.0.0", "multer": "^2.0.0", "mysql2": "^3.14.1", "onlyoffice-integration": "file:", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^2.0.22"}}