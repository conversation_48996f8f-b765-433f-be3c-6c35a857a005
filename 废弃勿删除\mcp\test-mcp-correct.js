// 使用正确的方式测试MCP服务器
const { spawn } = require('child_process');

console.log('使用正确的方式测试MCP MySQL服务器...');

// 设置环境变量
const env = {
  ...process.env,
  MYSQL_HOST: '*************',
  MYSQL_PORT: '3306',
  MYSQL_USER: 'onlyfile_user',
  MYSQL_PASS: '0nlyF!le$ecure#123',
  MYSQL_DB: 'onlyfile',
  MYSQL_ENABLE_LOGGING: 'true',
  ALLOW_INSERT_OPERATION: 'true',
  ALLOW_UPDATE_OPERATION: 'true',
  ALLOW_DELETE_OPERATION: 'false'
};

console.log('环境变量设置完成');

// 方法1: 使用npx（推荐方式）
console.log('\n=== 方法1: 使用npx ===');
const mcpProcess1 = spawn('npx', ['@benborla29/mcp-server-mysql'], {
  env: env,
  stdio: ['pipe', 'pipe', 'pipe'],
  shell: true
});

console.log('MCP进程1已启动，PID:', mcpProcess1.pid);

let hasOutput1 = false;
let hasError1 = false;

mcpProcess1.stdout.on('data', (data) => {
  hasOutput1 = true;
  console.log('方法1 STDOUT:', data.toString().trim());
});

mcpProcess1.stderr.on('data', (data) => {
  hasError1 = true;
  console.log('方法1 STDERR:', data.toString().trim());
});

mcpProcess1.on('exit', (code, signal) => {
  console.log(`方法1 进程退出: 代码=${code}, 信号=${signal}`);
});

mcpProcess1.on('error', (err) => {
  console.log('方法1 进程错误:', err.message);
});

// 发送MCP初始化消息
setTimeout(() => {
  console.log('方法1: 发送MCP初始化消息...');
  
  const initMessage = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {
        roots: {
          listChanged: true
        },
        sampling: {}
      },
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  };
  
  try {
    mcpProcess1.stdin.write(JSON.stringify(initMessage) + '\n');
    console.log('方法1: 初始化消息已发送');
  } catch (err) {
    console.log('方法1: 发送消息失败:', err.message);
  }
}, 2000);

// 5秒后测试方法2
setTimeout(() => {
  console.log('\n=== 方法2: 直接使用node ===');
  
  const mcpProcess2 = spawn('node', ['C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@benborla29\\mcp-server-mysql\\dist\\index.js'], {
    env: env,
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  console.log('MCP进程2已启动，PID:', mcpProcess2.pid);
  
  let hasOutput2 = false;
  let hasError2 = false;
  
  mcpProcess2.stdout.on('data', (data) => {
    hasOutput2 = true;
    console.log('方法2 STDOUT:', data.toString().trim());
  });
  
  mcpProcess2.stderr.on('data', (data) => {
    hasError2 = true;
    console.log('方法2 STDERR:', data.toString().trim());
  });
  
  mcpProcess2.on('exit', (code, signal) => {
    console.log(`方法2 进程退出: 代码=${code}, 信号=${signal}`);
  });
  
  mcpProcess2.on('error', (err) => {
    console.log('方法2 进程错误:', err.message);
  });
  
  // 发送初始化消息
  setTimeout(() => {
    console.log('方法2: 发送MCP初始化消息...');
    
    const initMessage = {
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {
          roots: {
            listChanged: true
          },
          sampling: {}
        },
        clientInfo: {
          name: "test-client",
          version: "1.0.0"
        }
      }
    };
    
    try {
      mcpProcess2.stdin.write(JSON.stringify(initMessage) + '\n');
      console.log('方法2: 初始化消息已发送');
    } catch (err) {
      console.log('方法2: 发送消息失败:', err.message);
    }
  }, 1000);
  
  // 3秒后停止方法2
  setTimeout(() => {
    mcpProcess2.kill();
    console.log('\n=== 方法2 测试结果 ===');
    console.log('有输出:', hasOutput2);
    console.log('有错误:', hasError2);
  }, 3000);
  
}, 5000);

// 10秒后停止所有测试
setTimeout(() => {
  console.log('\n=== 停止所有测试 ===');
  mcpProcess1.kill();
  
  console.log('\n=== 方法1 测试结果 ===');
  console.log('有输出:', hasOutput1);
  console.log('有错误:', hasError1);
  
  console.log('\n=== 总结 ===');
  if (!hasOutput1 && !hasError1 && !hasOutput2 && !hasError2) {
    console.log('两种方法都没有输出，可能的原因:');
    console.log('1. MCP服务器需要特定的启动参数');
    console.log('2. 服务器启动后等待特定的输入格式');
    console.log('3. 环境变量配置问题');
    console.log('4. 依赖包缺失');
  }
  
  process.exit(0);
}, 10000);
