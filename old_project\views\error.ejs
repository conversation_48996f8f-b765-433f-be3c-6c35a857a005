<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - OnlyOffice文档管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }

        .error-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            position: relative;
        }

        .error-icon {
            font-size: 72px;
            color: #e74c3c;
            margin-bottom: 30px;
        }

        .error-title {
            font-size: 36px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .error-details {
            background-color: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: left;
        }

        .error-details pre {
            background: #2c3e50;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
            margin-top: 10px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
            transform: translateY(-2px);
        }

        .help-info {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .help-info h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .help-info ul {
            text-align: left;
            color: #666;
        }

        .help-info li {
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .error-card {
                padding: 40px 20px;
            }

            .error-title {
                font-size: 28px;
            }

            .error-message {
                font-size: 16px;
            }
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">⚠️</div>

            <h1 class="error-title">
                <%= typeof status !=='undefined' ? status : '500' %> 错误
            </h1>

            <p class="error-message">
                <%= typeof message !=='undefined' ? message : '服务器内部错误，请稍后重试。' %>
            </p>

            <% if (typeof error !=='undefined' && error) { %>
                <div class="error-details">
                    <h4>错误详情:</h4>
                    <p><strong>错误:</strong>
                        <%= error.message %>
                    </p>
                    <% if (error.stack) { %>
                        <pre><%= error.stack %></pre>
                        <% } %>
                </div>
                <% } %>

                    <div class="btn-group">
                        <a href="/" class="btn btn-primary">返回首页</a>
                        <a href="javascript:history.back()" class="btn btn-secondary">返回上一页</a>
                        <a href="/navigation" class="btn btn-secondary">功能导航</a>
                    </div>

                    <div class="help-info">
                        <h4>常见解决方案:</h4>
                        <ul>
                            <li>检查网络连接是否正常</li>
                            <li>确认输入的URL地址是否正确</li>
                            <li>尝试刷新页面重新加载</li>
                            <li>如果问题持续存在，请联系系统管理员</li>
                        </ul>
                    </div>
        </div>
    </div>

    <script>
        // 自动刷新功能（可选）
        function autoRefresh() {
            if (confirm('是否要自动刷新页面尝试解决问题？')) {
                window.location.reload();
            }
        }

        // 10秒后提示用户是否刷新
        setTimeout(() => {
            if (confirm('页面加载出现问题，是否尝试重新加载？')) {
                window.location.reload();
            }
        }, 10000);
    </script>
</body>

</html>