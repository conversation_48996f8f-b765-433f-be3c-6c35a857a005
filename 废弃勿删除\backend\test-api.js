/**
 * API测试脚本
 * 测试编辑器配置API是否正确返回JWT token
 */

const http = require('http');

async function testEditorConfigAPI() {
  console.log('=== 编辑器配置API测试 ===');
  
  const fileId = '286bf7f3-1eca-4bc1-9c4f-f92767cd74f9';
  const url = `http://localhost:3000/api/editor/${fileId}/config`;
  
  console.log('📡 请求URL:', url);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📊 响应状态:', response.status, response.statusText);
    
    const contentType = response.headers.get('content-type');
    console.log('📋 内容类型:', contentType);
    
    if (response.ok) {
      const data = await response.json();
      
      console.log('✅ API响应成功!');
      console.log('📄 完整响应数据:', JSON.stringify(data, null, 2));
      console.log('📄 响应数据结构:');
      console.log('- 包含document:', !!data.data?.document);
      console.log('- 包含documentType:', !!data.data?.documentType);
      console.log('- 包含editorConfig:', !!data.data?.editorConfig);
      console.log('- 包含apiUrl:', !!data.data?.apiUrl);
      console.log('- 包含token:', !!data.data?.token);
      
      if (data.data?.token) {
        console.log('🔐 JWT Token信息:');
        console.log('- Token长度:', data.data.token.length);
        console.log('- Token开头:', data.data.token.substring(0, 50) + '...');
        
        // 解析JWT Header和Payload（不验证签名）
        try {
          const parts = data.data.token.split('.');
          if (parts.length === 3) {
            const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            
            console.log('- JWT算法:', header.alg);
            console.log('- JWT类型:', header.typ);
            console.log('- 签发者:', payload.iss);
            console.log('- 接收者:', payload.aud);
            console.log('- 过期时间:', new Date(payload.exp * 1000).toLocaleString());
            console.log('- 文档标题:', payload.document?.title);
          }
        } catch (parseError) {
          console.log('- JWT解析失败:', parseError.message);
        }
      } else {
        console.log('❌ 响应中缺少JWT token!');
      }
      
      if (data.data?.document) {
        console.log('📋 文档信息:');
        console.log('- 文件类型:', data.data.document.fileType);
        console.log('- 文档标题:', data.data.document.title);
        console.log('- 文档URL:', data.data.document.url);
        console.log('- 权限配置:', JSON.stringify(data.data.document.permissions, null, 2));
      }
      
    } else {
      console.log('❌ API请求失败!');
      const errorText = await response.text();
      console.log('错误内容:', errorText);
    }
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('提示: 请确保后端服务器正在运行 (npm run start:dev)');
    }
  }
  
  console.log('');
  console.log('🎉 测试完成!');
}

// 运行测试
testEditorConfigAPI(); 