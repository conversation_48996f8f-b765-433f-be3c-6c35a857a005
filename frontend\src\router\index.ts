import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import BasicLayout from '@/components/Layout/BasicLayout.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard',
  },
  {
    path: '/',
    component: BasicLayout,
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/pages/Dashboard/index.vue'),
        meta: {
          title: '系统首页',
          icon: 'dashboard',
          requiresAuth: true,
        },
      },
      {
        path: 'documents',
        name: 'Documents',
        component: () => import('@/pages/Documents/index.vue'),
        meta: {
          title: '文档管理',
          icon: 'file-text',
          requiresAuth: true,
        },
      },
      {
        path: 'documents/editor/:id',
        name: 'DocumentEdit',
        component: () => import('@/pages/editor/EditorPage.vue'),
        meta: {
          title: '文档编辑',
          icon: 'edit',
          requiresAuth: true,
          hideInMenu: true,
          fullscreen: true,
        },
      },
      {
        path: 'documents/editor/template-session/:sessionId',
        name: 'TemplateSessionEdit',
        component: () => import('@/pages/editor/EditorPage.vue'),
        meta: {
          title: '基于模板编辑',
          icon: 'edit',
          requiresAuth: true,
          hideInMenu: true,
          fullscreen: true,
        },
      },
      {
        path: 'templates',
        name: 'Templates',
        component: () => import('@/pages/Templates/index.vue'),
        meta: {
          title: '文档模板管理',
          icon: 'snippets',
          requiresAuth: true,
        },
      },
      {
        path: 'onlyoffice-config',
        name: 'OnlyOfficeConfig',
        component: () => import('@/pages/OnlyOfficeConfig/index.vue'),
        meta: {
          title: 'OnlyOffice配置模板管理',
          icon: 'setting',
          requiresAuth: true,
        },
      },
      {
        path: 'onlyoffice-config/edit/:id',
        name: 'OnlyOfficeConfigEdit',
        component: () => import('@/pages/OnlyOfficeConfig/ConfigDetail.vue'),
        meta: {
          title: 'OnlyOffice配置模板编辑',
          icon: 'setting',
          requiresAuth: true,
          hideInMenu: true,
        },
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/pages/Users/<USER>'),
        meta: {
          title: '用户管理',
          icon: 'user',
          requiresAuth: true,
        },
      },

      {
        path: 'permissions',
        name: 'Permissions',
        component: () => import('@/pages/Permissions/index.vue'),
        meta: {
          title: '权限管理',
          icon: 'safety-certificate',
          requiresAuth: true,
        },
      },
      {
        path: 'system-config',
        name: 'SystemConfig',
        component: () => import('@/pages/system/index.vue'),
        meta: {
          title: '系统配置管理',
          icon: 'setting',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/Auth/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
    },
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/pages/Error/404.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false,
    },
  },
  {
    path: '/editor/fullscreen/:id',
    name: 'FullscreenEditor',
    component: () => import('@/pages/editor/FullscreenEditor.vue'),
    meta: {
      title: '文档编辑',
      requiresAuth: true,
      standalone: true,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

const router = createRouter({
  history: createWebHistory('/'),
  routes,
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log('🚀 路由导航开始:')
  console.log('  从:', from.path, from.name)
  console.log('  到:', to.path, to.name)
  console.log('  需要认证:', to.meta?.requiresAuth)

  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - OnlyOffice集成系统`
  }

  // 获取认证状态
  const token = localStorage.getItem('token')
  const userInfo = localStorage.getItem('userInfo')
  console.log('  Token存在:', !!token)
  console.log('  用户信息存在:', !!userInfo)

  // 如果已登录用户访问登录页，跳转到首页
  if (to.path === '/login') {
    if (token && userInfo && userInfo !== 'undefined') {
      console.log('✅ 已登录用户访问登录页，重定向到首页')
      next('/')
      return
    } else {
      console.log('✅ 未登录用户访问登录页，允许访问')
      next()
      return
    }
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    if (!token || !userInfo || userInfo === 'undefined') {
      console.log('❌ 需要认证但未登录，重定向到登录页')
      next('/login')
      return
    } else {
      console.log('✅ 已认证，允许访问')
      next()
      return
    }
  }

  console.log('✅ 不需要认证，直接访问')
  next()
})

export default router
