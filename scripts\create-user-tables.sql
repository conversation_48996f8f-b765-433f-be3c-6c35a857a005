-- OnlyOffice 用户管理系统数据库表
-- 用于实现完整的用户认证、角色权限管理和审计功能

-- 用户主表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    full_name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '电话号码',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active' COMMENT '用户状态',
    role_id VARCHAR(36) COMMENT '角色ID',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '密码修改时间',
    failed_login_attempts INT DEFAULT 0 COMMENT '失败登录尝试次数',
    locked_until TIMESTAMP NULL COMMENT '账户锁定截止时间',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '电话是否已验证',
    two_factor_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因子认证',
    created_by VARCHAR(36) COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(36) COMMENT '更新者ID',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role_id (role_id),
    INDEX idx_last_login (last_login_at),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 角色表
CREATE TABLE IF NOT EXISTS user_roles (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    display_name VARCHAR(100) NOT NULL COMMENT '角色显示名称',
    description TEXT COMMENT '角色描述',
    permissions JSON COMMENT '权限列表JSON',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统预设角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    created_by VARCHAR(36) COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(36) COMMENT '更新者ID',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 权限表
CREATE TABLE IF NOT EXISTS user_permissions (
    id VARCHAR(36) PRIMARY KEY,
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    description TEXT COMMENT '权限描述',
    module VARCHAR(50) NOT NULL COMMENT '所属模块',
    resource VARCHAR(100) COMMENT '资源标识',
    action VARCHAR(50) COMMENT '操作类型 (create,read,update,delete,execute)',
    conditions JSON COMMENT '权限条件JSON',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_module (module),
    INDEX idx_resource (resource),
    INDEX idx_action (action),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    session_token VARCHAR(255) NOT NULL UNIQUE COMMENT '会话令牌',
    refresh_token VARCHAR(255) COMMENT '刷新令牌',
    device_type VARCHAR(50) COMMENT '设备类型',
    device_name VARCHAR(100) COMMENT '设备名称',
    user_agent TEXT COMMENT '用户代理字符串',
    ip_address VARCHAR(45) COMMENT '登录IP地址',
    location VARCHAR(200) COMMENT '登录地点',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id VARCHAR(36) COMMENT '资源ID',
    details JSON COMMENT '操作详情JSON',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    status ENUM('success', 'failed', 'error') DEFAULT 'success' COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    execution_time INT COMMENT '执行时间(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_id (resource_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 密码历史表
CREATE TABLE IF NOT EXISTS password_history (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    password_hash VARCHAR(255) NOT NULL COMMENT '历史密码哈希',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加外键约束
ALTER TABLE users ADD CONSTRAINT fk_users_role_id 
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL;

-- 插入系统预设角色
INSERT IGNORE INTO user_roles (id, name, display_name, description, permissions, is_system, is_active, sort_order) VALUES
('role-super-admin', 'super_admin', '超级管理员', '系统最高权限管理员，拥有所有权限', JSON_ARRAY('*'), TRUE, TRUE, 1),
('role-admin', 'admin', '管理员', '系统管理员，拥有大部分管理权限', JSON_ARRAY('users.*', 'documents.*', 'templates.*', 'config.*', 'system.read'), TRUE, TRUE, 2),
('role-editor', 'editor', '编辑者', '内容编辑者，可以编辑文档和模板', JSON_ARRAY('documents.*', 'templates.*', 'config.read'), TRUE, TRUE, 3),
('role-viewer', 'viewer', '查看者', '只读用户，只能查看内容', JSON_ARRAY('documents.read', 'templates.read', 'config.read'), TRUE, TRUE, 4),
('role-guest', 'guest', '访客', '访客用户，最小权限', JSON_ARRAY('documents.read'), TRUE, TRUE, 5);

-- 插入系统预设权限
INSERT IGNORE INTO user_permissions (id, code, name, description, module, resource, action, is_active, sort_order) VALUES
-- 用户管理权限
('perm-user-create', 'users.create', '创建用户', '创建新用户账户', 'users', 'user', 'create', TRUE, 1),
('perm-user-read', 'users.read', '查看用户', '查看用户信息和列表', 'users', 'user', 'read', TRUE, 2),
('perm-user-update', 'users.update', '更新用户', '修改用户信息', 'users', 'user', 'update', TRUE, 3),
('perm-user-delete', 'users.delete', '删除用户', '删除用户账户', 'users', 'user', 'delete', TRUE, 4),
('perm-user-all', 'users.*', '用户管理全权限', '用户管理模块所有权限', 'users', 'user', '*', TRUE, 5),

-- 文档管理权限
('perm-doc-create', 'documents.create', '创建文档', '上传和创建新文档', 'documents', 'document', 'create', TRUE, 11),
('perm-doc-read', 'documents.read', '查看文档', '查看和下载文档', 'documents', 'document', 'read', TRUE, 12),
('perm-doc-update', 'documents.update', '编辑文档', '编辑和修改文档', 'documents', 'document', 'update', TRUE, 13),
('perm-doc-delete', 'documents.delete', '删除文档', '删除文档', 'documents', 'document', 'delete', TRUE, 14),
('perm-doc-all', 'documents.*', '文档管理全权限', '文档管理模块所有权限', 'documents', 'document', '*', TRUE, 15),

-- 模板管理权限
('perm-tpl-create', 'templates.create', '创建模板', '创建文档模板', 'templates', 'template', 'create', TRUE, 21),
('perm-tpl-read', 'templates.read', '查看模板', '查看模板列表和详情', 'templates', 'template', 'read', TRUE, 22),
('perm-tpl-update', 'templates.update', '编辑模板', '编辑和修改模板', 'templates', 'template', 'update', TRUE, 23),
('perm-tpl-delete', 'templates.delete', '删除模板', '删除模板', 'templates', 'template', 'delete', TRUE, 24),
('perm-tpl-all', 'templates.*', '模板管理全权限', '模板管理模块所有权限', 'templates', 'template', '*', TRUE, 25),

-- 配置管理权限
('perm-cfg-read', 'config.read', '查看配置', '查看系统配置', 'config', 'config', 'read', TRUE, 31),
('perm-cfg-update', 'config.update', '修改配置', '修改系统配置', 'config', 'config', 'update', TRUE, 32),
('perm-cfg-all', 'config.*', '配置管理全权限', '配置管理模块所有权限', 'config', 'config', '*', TRUE, 33),

-- 系统管理权限
('perm-sys-read', 'system.read', '查看系统信息', '查看系统状态和信息', 'system', 'system', 'read', TRUE, 41),
('perm-sys-manage', 'system.manage', '系统管理', '系统管理和维护操作', 'system', 'system', 'manage', TRUE, 42),
('perm-sys-all', 'system.*', '系统管理全权限', '系统管理模块所有权限', 'system', 'system', '*', TRUE, 43),

-- 超级权限
('perm-all', '*', '所有权限', '系统所有权限', 'system', '*', '*', TRUE, 100);

-- 插入默认管理员用户 (密码: admin123)
INSERT IGNORE INTO users (id, username, email, password_hash, full_name, status, role_id, created_by) VALUES
('user-admin', 'admin', '<EMAIL>', '$2b$10$rOh4P1.q/nF7aGdl.N7gq.L5k3vZfU8.5/gqA.5WP0lBwHx2f8azO', '系统管理员', 'active', 'role-super-admin', 'system'),
('user-demo', 'demo', '<EMAIL>', '$2b$10$rOh4P1.q/nF7aGdl.N7gq.L5k3vZfU8.5/gqA.5WP0lBwHx2f8azO', '演示用户', 'active', 'role-editor', 'system');

-- 创建视图：用户详细信息视图
CREATE OR REPLACE VIEW v_user_details AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.full_name,
    u.phone,
    u.avatar_url,
    u.status,
    u.department,
    u.position,
    u.last_login_at,
    u.last_login_ip,
    u.email_verified,
    u.phone_verified,
    u.two_factor_enabled,
    u.created_at,
    u.updated_at,
    r.name as role_name,
    r.display_name as role_display_name,
    r.permissions as role_permissions
FROM users u
LEFT JOIN user_roles r ON u.role_id = r.id
WHERE u.deleted_at IS NULL;

-- 创建视图：活跃会话视图
CREATE OR REPLACE VIEW v_active_sessions AS
SELECT 
    s.id,
    s.user_id,
    u.username,
    u.full_name,
    s.device_type,
    s.device_name,
    s.ip_address,
    s.location,
    s.last_activity_at,
    s.expires_at,
    s.created_at
FROM user_sessions s
JOIN users u ON s.user_id = u.id
WHERE s.is_active = TRUE 
  AND s.expires_at > NOW()
  AND u.deleted_at IS NULL; 