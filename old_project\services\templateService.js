const db = require('./database');
const fileStorage = require('./fileStorage');
const documentService = require('./document'); // 假设 documentService 中有创建文档记录等辅助函数
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs-extra');
const config = require('../config');

/**
 * 获取模板列表
 * @param {object} options - 查询选项，例如 { limit, offset, categoryId, status }
 * @returns {Promise<Array>} 模板列表
 */
async function getTemplates(options = {}) {
    const { limit = 10, offset = 0, categoryId, status = 'enabled', sortBy = 'created_at', order = 'DESC' } = options;

    // Base part of the query, including join for category name
    const baseQueryParts = [
        'FROM templates t',
        'LEFT JOIN template_categories tc ON t.category_id = tc.id'
    ];

    const conditions = ['t.is_deleted = FALSE'];
    const queryParams = [];

    // Modified status handling
    if (status && status.toLowerCase() !== 'all') {
        conditions.push('t.status = ?');
        queryParams.push(status);
    } else if (status && status.toLowerCase() === 'all') {
        // If status is 'all', do not add any status condition, effectively fetching all statuses (respecting is_deleted = FALSE)
    }

    if (categoryId) {
        conditions.push('t.category_id = ?');
        queryParams.push(categoryId);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    const fromAndWhereClauses = `${baseQueryParts.join(' ')} ${whereClause}`;

    // Main query for fetching template data
    const validSortColumns = ['name', 'created_at', 'updated_at'];
    // Ensure the sort column is prefixed with the table alias if it's not ambiguous or handled by DB.
    // For clarity, prefixing 't.' to template columns.
    const sortColumn = validSortColumns.includes(sortBy) ? `t.${sortBy}` : 't.created_at';
    const sortOrder = (order.toUpperCase() === 'ASC' || order.toUpperCase() === 'DESC') ? order.toUpperCase() : 'DESC';

    const mainQueryString = `SELECT t.*, tc.name as category_name, fd.original_name as source_document_name 
        FROM templates t 
        LEFT JOIN template_categories tc ON t.category_id = tc.id 
        LEFT JOIN filenet_documents fd ON t.doc_id = fd.id 
        ${whereClause} 
        ORDER BY ${sortColumn} ${sortOrder} 
        LIMIT ? OFFSET ?`;
    const mainQueryParams = [...queryParams, limit, offset];

    // Count query for pagination
    const countQueryString = `
        SELECT COUNT(*) as total 
        FROM templates t
        LEFT JOIN template_categories tc ON t.category_id = tc.id
        ${whereClause}
    `;

    try {
        // Fixed: db.query 返回的是直接的行结果，而不是 [rows, fields]
        const templates = await db.query(mainQueryString, mainQueryParams);
        console.log(`查询到 ${templates.length} 条模板记录，查询条件: status=${status}, limit=${limit}, offset=${offset}`);

        // 对于总数查询也需要修正
        const countResult = await db.query(countQueryString, queryParams);

        let total = 0;
        // Adjust parsing based on actual db.query implementation
        if (countResult && countResult.length > 0 && typeof countResult[0].total === 'number') {
            total = countResult[0].total;
        } else {
            console.warn(`Template count query returned unexpected structure or value. Query: "${countQueryString}", Params: ${JSON.stringify(queryParams)}, Result: ${JSON.stringify(countResult)}`);
        }

        console.log(`模板总数: ${total}`);
        return { templates, total, limit, offset };
    } catch (error) {
        console.error('Error fetching templates:', error);
        // For more detailed debugging in case of an error, you might want to log the failing query and params
        console.error(`Failed query (main): "${mainQueryString}", Params: ${JSON.stringify(mainQueryParams)}`);
        console.error(`Failed query (count): "${countQueryString}", Params: ${JSON.stringify(queryParams)}`);
        throw new Error('获取模板列表失败');
    }
}

/**
 * 根据ID获取模板详情
 * @param {string} templateId - 模板ID
 * @returns {Promise<object|null>} 模板对象或null
 */
async function getTemplateById(templateId) {
    const query = 'SELECT t.*, tc.name as category_name, fd.original_name as source_document_name, fd.extension as source_document_extension FROM templates t LEFT JOIN template_categories tc ON t.category_id = tc.id LEFT JOIN filenet_documents fd ON t.doc_id = fd.id WHERE t.id = ? AND t.is_deleted = FALSE';
    try {
        // 修复: db.query返回的是直接的行结果，不是[rows, fields]
        const rows = await db.query(query, [templateId]);
        console.log(`查询单个模板(ID:${templateId})结果:`, rows && rows.length > 0 ? '找到记录' : '未找到记录');
        if (!rows || rows.length === 0) {
            return null;
        }
        return rows[0];
    } catch (error) {
        console.error(`Error fetching template by ID ${templateId}:`, error);
        throw new Error('获取模板详情失败');
    }
}

/**
 * 创建新模板
 * @param {object} templateData - 模板数据
 * @param {string} templateData.name - 模板名称
 * @param {string} templateData.doc_id - 源文档ID (来自 filenet_documents 表)
 * @param {string} [templateData.category_id] - 分类ID
 * @param {string} [templateData.description] - 描述
 * @param {string} [templateData.created_by] - 创建者
 * @returns {Promise<object>} 创建的模板对象
 */
async function createTemplate(templateData) {
    const { name, doc_id, category_id = null, description = '', created_by = 'system' } = templateData;
    if (!name || !doc_id) {
        throw new Error('模板名称和源文档ID不能为空');
    }

    // 检查源文档是否存在
    const sourceDoc = await documentService.getDocumentById(doc_id); // 假设 documentService 有 getDocumentById
    if (!sourceDoc) {
        throw new Error(`源文档ID ${doc_id} 不存在或无法访问`);
    }

    const templateId = uuidv4();
    const query = `
        INSERT INTO templates (id, name, doc_id, category_id, description, created_by, status)
        VALUES (?, ?, ?, ?, ?, ?, 'enabled')
    `;
    try {
        await db.query(query, [templateId, name, doc_id, category_id, description, created_by]);
        return await getTemplateById(templateId);
    } catch (error) {
        console.error('Error creating template:', error);
        if (error.code === 'ER_NO_REFERENCED_ROW_2' && error.message.includes('category_id')) {
            throw new Error('创建模板失败：指定的分类ID不存在');
        }
        if (error.code === 'ER_NO_REFERENCED_ROW_2' && error.message.includes('doc_id')) {
            throw new Error('创建模板失败：指定的源文档ID不存在 (外键约束)');
        }
        throw new Error('创建模板失败');
    }
}

/**
 * 更新模板
 * @param {string} templateId - 模板ID
 * @param {object} updateData - 要更新的数据
 * @returns {Promise<object|null>} 更新后的模板对象
 */
async function updateTemplate(templateId, updateData) {
    const allowedUpdates = ['name', 'category_id', 'description', 'status', 'updated_by'];
    const updates = {};
    let querySet = [];

    for (const key in updateData) {
        if (allowedUpdates.includes(key) && updateData[key] !== undefined) {
            updates[key] = updateData[key];
            querySet.push(`${key} = ?`);
        }
    }

    if (querySet.length === 0) {
        throw new Error('没有提供有效的更新字段');
    }

    const query = `UPDATE templates SET ${querySet.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_deleted = FALSE`;
    const params = [...Object.values(updates), templateId];

    try {
        // 修复: db.query 返回的是直接的结果对象
        const result = await db.query(query, params);
        console.log(`更新模板(ID:${templateId})结果:`, result);

        // 检查结果中的 affectedRows
        const affectedRows = result.affectedRows || (result[0] && result[0].affectedRows) || 0;
        if (affectedRows === 0) {
            return null; // 模板不存在或未更新
        }
        return await getTemplateById(templateId);
    } catch (error) {
        console.error(`Error updating template ${templateId}:`, error);
        if (error.code === 'ER_NO_REFERENCED_ROW_2' && error.message.includes('category_id')) {
            throw new Error('更新模板失败：指定的分类ID不存在');
        }
        throw new Error('更新模板失败');
    }
}

/**
 * 删除模板 (软删除)
 * @param {string} templateId - 模板ID
 * @returns {Promise<boolean>} 是否成功删除
 */
async function deleteTemplate(templateId) {
    const query = 'UPDATE templates SET is_deleted = TRUE, status = \'disabled\', updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_deleted = FALSE';
    try {
        // 修复: db.query 返回的是直接的结果对象
        const result = await db.query(query, [templateId]);
        console.log(`删除模板(ID:${templateId})结果:`, result);

        // 检查结果中的 affectedRows
        const affectedRows = result.affectedRows || (result[0] && result[0].affectedRows) || 0;
        return affectedRows > 0;
    } catch (error) {
        console.error(`Error deleting template ${templateId}:`, error);
        throw new Error('删除模板失败');
    }
}

/**
 * 基于模板创建新文档
 * @param {string} templateId - 模板ID
 * @param {string} newDocumentName - 新文档的名称
 * @param {string} [userId='system'] - 创建用户ID
 * @returns {Promise<object>} 新创建的文档对象 (来自 filenet_documents 表)
 */
async function createDocumentFromTemplate(templateId, newDocumentName, userId = 'system') {
    if (!templateId || !newDocumentName) {
        throw new Error('模板ID和新文档名称不能为空');
    }

    const template = await getTemplateById(templateId);
    if (!template || template.status !== 'enabled') {
        throw new Error('模板不存在、已被删除或已禁用');
    }

    console.log('获取到模板信息:', JSON.stringify(template));

    const sourceDocument = await documentService.getDocumentRawById(template.doc_id);
    if (!sourceDocument) {
        throw new Error('模板的源文档不存在或无法访问');
    }

    console.log('获取到源文档信息:', JSON.stringify(sourceDocument));

    // 检查源文档必要信息
    if (!sourceDocument.fn_doc_id) {
        throw new Error('源文档缺少FileNet文档ID(fn_doc_id)');
    }

    // 确保文件扩展名存在
    let fileExtension = sourceDocument.extension;
    if (!fileExtension && sourceDocument.original_name) {
        fileExtension = path.extname(sourceDocument.original_name);
    }
    if (!fileExtension) {
        console.warn('无法确定文件扩展名，将使用默认扩展名 .tmp');
        fileExtension = '.tmp';
    } else if (!fileExtension.startsWith('.')) {
        fileExtension = '.' + fileExtension;
    }

    // 获取FileNet文档以创建副本
    const fileNetService = require('./filenetService');
    console.log(`准备从FileNet复制文档: ${sourceDocument.fn_doc_id}`);

    try {
        // 调用FileNet服务复制文档
        const newFileNetDoc = await fileNetService.copyFileNetDocument(
            sourceDocument.fn_doc_id,
            sanitizeFileName(newDocumentName + (fileExtension ? fileExtension : '')),
            userId
        );

        if (!newFileNetDoc || !newFileNetDoc.fn_doc_id) {
            throw new Error('从FileNet复制文档失败');
        }

        console.log('成功从FileNet复制文档:', newFileNetDoc);

        // 检查是否是文档内容复用
        if (newFileNetDoc.contentWasReused) {
            console.log(`注意：新文档复用了现有FileNet文档的内容（ID: ${newFileNetDoc.fn_doc_id}）`);

            // 检查数据库中是否已存在使用相同fn_doc_id且具有相同template_id的记录
            const existingDoc = await db.queryOne(`
                SELECT id FROM filenet_documents 
                WHERE fn_doc_id = ? AND template_id = ? AND original_name = ? AND is_deleted = FALSE
            `, [newFileNetDoc.fn_doc_id, templateId, sanitizeFileName(newDocumentName + (fileExtension ? fileExtension : ''))]);

            if (existingDoc) {
                console.log(`找到数据库中已存在的相同文档记录 ID: ${existingDoc.id}，将直接返回`);
                // 如果已存在完全相同的记录，则直接返回该记录
                return await documentService.getDocumentById(existingDoc.id);
            }
        }

        // 2. 在 filenet_documents 表中创建新记录
        const newDocumentId = uuidv4();

        const documentData = {
            id: newDocumentId,
            original_name: sanitizeFileName(newDocumentName + (fileExtension ? fileExtension : '')),
            file_size: sourceDocument.file_size,
            mime_type: sourceDocument.mime_type,
            extension: fileExtension.startsWith('.') ? fileExtension.substring(1) : fileExtension,
            created_by: userId,
            last_modified_by: userId,
            template_id: templateId,
            fn_doc_id: newFileNetDoc.fn_doc_id
        };

        // 使用documentService创建记录
        return await documentService.createDocumentRecord(documentData);

    } catch (error) {
        console.error('从模板创建文档失败:', error);
        throw new Error(`从模板创建文档失败: ${error.message}`);
    }
}

// 辅助函数 - 清理文件名，防止路径遍历等问题
// (这个函数也可以放在一个公共的 utils.js 文件中)
function sanitizeFileName(fileName) {
    if (!fileName || typeof fileName !== 'string') {
        return `document-${uuidv4()}`;
    }
    // 移除非法字符，替换空格等
    let sanName = fileName.replace(/[\\/:\*\?"<>\|]/g, '_'); // 移除Windows和Linux非法字符
    sanName = sanName.replace(/\s+/g, '_'); // 将空格替换为下划线
    // 限制长度
    sanName = sanName.substring(0, 200);
    if (!path.extname(sanName) && fileName.includes('.')) { // 如果原始文件名有扩展名但处理后没了，尝试恢复
        const originalExt = path.extname(fileName);
        if (originalExt) sanName += originalExt;
    }
    return sanName;
}


module.exports = {
    getTemplates,
    getTemplateById,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    createDocumentFromTemplate,
    // (未来可以添加模板分类相关的服务函数)
    // getTemplateCategories,
    // createTemplateCategory,
    // updateTemplateCategory,
    // deleteTemplateCategory
}; 