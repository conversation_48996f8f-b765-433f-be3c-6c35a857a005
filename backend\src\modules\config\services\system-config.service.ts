import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';

/**
 * 系统配置项接口
 */
export interface SystemConfigItem {
  setting_key: string;
  setting_value: string;
  description?: string;
  updated_at?: Date | string;
}

/**
 * 配置历史记录接口
 */
export interface ConfigHistoryItem {
  id: string;
  setting_key: string;
  new_value: string;
  old_value: string;
  changed_by: string;
  changed_at: Date | string;
  operation: string;
  description?: string;
}

/**
 * 系统配置服务
 * @description 提供系统配置项的管理功能
 */
@Injectable()
export class SystemConfigService {
  private readonly logger = new Logger(SystemConfigService.name);

  constructor(
    private readonly databaseService: DatabaseService,
  ) {}

  /**
   * 获取所有配置项
   */
  async getAllConfigs(): Promise<SystemConfigItem[]> {
    try {
      const query = `
        SELECT setting_key, setting_value, description, updated_at
        FROM system_settings
        ORDER BY setting_key
      `;
      
      return await this.databaseService.query(query) as SystemConfigItem[];
    } catch (error) {
      this.logger.error('获取所有配置失败:', error);
      throw error;
    }
  }

  /**
   * 根据分类获取配置项
   */
  async getConfigsByCategory(category: string): Promise<SystemConfigItem[]> {
    try {
      const query = `
        SELECT setting_key, setting_value, description, updated_at
        FROM system_settings
        WHERE setting_key LIKE ?
        ORDER BY setting_key
      `;
      
      return await this.databaseService.query(query, [`${category}.%`]) as SystemConfigItem[];
    } catch (error) {
      this.logger.error(`获取${category}分类配置失败:`, error);
      throw error;
    }
  }

  /**
   * 获取单个配置项
   */
  async getConfig(key: string): Promise<SystemConfigItem> {
    try {
      const query = `
        SELECT setting_key, setting_value, description, updated_at
        FROM system_settings
        WHERE setting_key = ?
      `;
      
      const result = await this.databaseService.queryOne(query, [key]);
      
      if (!result) {
        throw new NotFoundException(`配置项 ${key} 不存在`);
      }
      
      return result as SystemConfigItem;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`获取配置项${key}失败:`, error);
      throw error;
    }
  }

  /**
   * 更新配置项
   * @param key 配置键
   * @param updateData 更新数据
   */
  async updateConfig(
    key: string, 
    updateData: { setting_value: string; description?: string }
  ): Promise<void> {
    try {
      // 检查配置项是否存在并获取旧值
      const oldConfig = await this.getConfig(key);
      const oldValue = oldConfig.setting_value;

      // 检查值是否真的发生了变化
      if (oldValue === updateData.setting_value) {
        this.logger.log(`配置项 ${key} 值未变化，跳过更新`);
        return;
      }

      const query = `
        UPDATE system_settings 
        SET setting_value = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE setting_key = ?
      `;
      
      await this.databaseService.query(query, [
        updateData.setting_value,
        updateData.description,
        key
      ]);

      // 只有在值真正变化时才记录审计日志
      await this.recordConfigChangeAudit(key, oldValue, updateData.setting_value, updateData.description);

      this.logger.log(`配置项 ${key} 更新成功`);
    } catch (error) {
      this.logger.error(`更新配置项${key}失败:`, error);
      throw error;
    }
  }

  /**
   * 记录配置变更的审计日志
   */
  private async recordConfigChangeAudit(
    settingKey: string,
    oldValue: string,
    newValue: string,
    description?: string
  ): Promise<void> {
    try {
      const auditRecord = {
        id: this.generateUUID(),
        user_id: null, // 当前没有用户上下文，使用null
        action: 'update_system_config',
        resource_type: 'system_config',
        resource_id: settingKey,
        details: JSON.stringify({
          setting_key: settingKey,
          old_value: oldValue,
          new_value: newValue,
          description: description || '',
          change_type: 'update',
          timestamp: new Date().toISOString()
        }),
        ip_address: null,
        user_agent: null,
        status: 'success',
        error_message: null,
        execution_time: null
      };

      const insertQuery = `
        INSERT INTO audit_logs (
          id, user_id, action, resource_type, resource_id, details, 
          ip_address, user_agent, status, error_message, execution_time, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `;

      await this.databaseService.query(insertQuery, [
        auditRecord.id,
        auditRecord.user_id,
        auditRecord.action,
        auditRecord.resource_type,
        auditRecord.resource_id,
        auditRecord.details,
        auditRecord.ip_address,
        auditRecord.user_agent,
        auditRecord.status,
        auditRecord.error_message,
        auditRecord.execution_time
      ]);

      this.logger.log(`配置变更审计日志已记录: ${settingKey} (${oldValue} → ${newValue})`);
    } catch (error) {
      this.logger.error('记录配置变更审计日志失败:', error);
      // 不抛出错误，因为这不应该影响配置更新的主要功能
    }
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'audit-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 批量更新配置项
   */
  async batchUpdateConfigs(configs: Array<{
    setting_key: string;
    setting_value: string;
    description?: string;
  }>): Promise<void> {
    try {
      for (const config of configs) {
        await this.updateConfig(config.setting_key, {
          setting_value: config.setting_value,
          description: config.description,
        });
      }

      this.logger.log(`批量更新 ${configs.length} 个配置项成功`);
    } catch (error) {
      this.logger.error('批量更新配置失败:', error);
      throw error;
    }
  }

  /**
   * 重置为默认配置（重新执行初始化脚本）
   */
  async resetToDefaults(): Promise<void> {
    try {
      // 删除现有配置
      await this.databaseService.query('DELETE FROM system_settings');

      // 重新插入默认配置
      await this.initializeDefaultConfigs();

      this.logger.log('配置已重置为默认值');
    } catch (error) {
      this.logger.error('重置配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取配置变更历史
   * 从 audit_logs 表获取系统配置相关的变更记录
   */
  async getConfigHistory(limit: number): Promise<ConfigHistoryItem[]> {
    try {
      this.logger.log(`🔍 [getConfigHistory] 开始执行，limit: ${limit}`);
      
      // 确保limit是数字类型
      const limitNum = parseInt(String(limit)) || 10;
      
      // 从审计日志表获取配置变更记录
      const query = `
        SELECT 
          id,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.setting_key')), resource_id) as setting_key,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.new_value')), '') as new_value,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.old_value')), '') as old_value,
          COALESCE(user_id, 'system') as changed_by,
          created_at as changed_at,
          action as operation,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.description')), '') as description
        FROM audit_logs
        WHERE resource_type = 'system_config' 
           OR action LIKE '%config%'
           OR action LIKE '%setting%'
        ORDER BY created_at DESC
        LIMIT ${limitNum}
      `;
      
      this.logger.log(`🔍 [getConfigHistory] 执行SQL查询...`);
      this.logger.log(`📋 [getConfigHistory] SQL: ${query.replace(/\s+/g, ' ').trim()}`);
      
      const results = await this.databaseService.query(query);
      
      this.logger.log(`✅ [getConfigHistory] 数据库查询完成`);
      this.logger.log(`📊 [getConfigHistory] 结果类型: ${typeof results}`);
      this.logger.log(`📊 [getConfigHistory] 结果长度: ${Array.isArray(results) ? results.length : 'not array'}`);
      this.logger.log(`📊 [getConfigHistory] 结果内容: ${JSON.stringify(results).substring(0, 500)}`);
      
      // 如果审计日志中没有配置变更记录，则返回一些模拟的历史记录
      if (!results || results.length === 0) {
        this.logger.log(`⚠️ [getConfigHistory] 审计日志为空，使用fallback查询`);
        
        // 获取最近更新的配置作为"历史记录"
        const fallbackQuery = `
          SELECT 
            CONCAT('sys_', setting_key, '_', UNIX_TIMESTAMP(updated_at)) as id,
            setting_key,
            setting_value as new_value,
            '' as old_value,
            'system' as changed_by,
            updated_at as changed_at,
            'initialize' as operation,
            description
          FROM system_settings
          WHERE updated_at IS NOT NULL
          ORDER BY updated_at DESC
          LIMIT ${Math.min(limitNum, 5)}
        `;
        
        const fallbackResults = await this.databaseService.query(fallbackQuery);
        this.logger.log(`🔄 [getConfigHistory] Fallback查询结果: ${fallbackResults.length} 条`);
        return fallbackResults as unknown as ConfigHistoryItem[] || [];
      }
      
      this.logger.log(`✅ [getConfigHistory] 返回 ${results.length} 条审计日志记录`);
      return results as unknown as ConfigHistoryItem[];
    } catch (error) {
      this.logger.error('❌ [getConfigHistory] 获取配置历史失败:', error);
      this.logger.error('❌ [getConfigHistory] 错误堆栈:', error.stack);
      
      // 如果出错，返回空数组
      return [];
    }
  }

  /**
   * 测试配置连接（模拟实现）
   */
  async testConfig(key: string): Promise<{
    success: boolean;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      const config = await this.getConfig(key);
      
      // 模拟不同类型配置的测试
      if (key.includes('database')) {
        return {
          success: true,
          message: '数据库连接测试成功',
          details: { responseTime: '15ms' }
        };
      } else if (key.includes('redis')) {
        return {
          success: true,
          message: 'Redis连接测试成功',
          details: { ping: 'PONG' }
        };
      } else if (key.includes('filenet')) {
        return {
          success: false,
          message: 'FileNet连接超时',
          details: { timeout: '30s' }
        };
      } else if (key.includes('onlyoffice')) {
        return {
          success: true,
          message: 'OnlyOffice服务器连接正常',
          details: { version: '7.5.1' }
        };
      } else {
        return {
          success: true,
          message: '配置项验证通过',
          details: { value: config.setting_value }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '配置测试失败',
        details: { error: error.message }
      };
    }
  }

  /**
   * 验证配置格式
   */
  async validateConfig(key: string, value: string): Promise<{
    valid: boolean;
    message?: string;
  }> {
    try {
      // 基本验证规则
      if (!value || value.trim() === '') {
        return {
          valid: false,
          message: '配置值不能为空'
        };
      }

      // 针对特定配置的验证
      if (key.includes('port')) {
        const port = parseInt(value);
        if (isNaN(port) || port < 1 || port > 65535) {
          return {
            valid: false,
            message: '端口号必须是1-65535之间的数字'
          };
        }
      }

      if (key.includes('url') || key.includes('host')) {
        try {
          new URL(value.startsWith('http') ? value : `http://${value}`);
        } catch {
          return {
            valid: false,
            message: '无效的URL或主机地址格式'
          };
        }
      }

      return {
        valid: true,
        message: '配置格式验证通过'
      };
    } catch (error) {
      return {
        valid: false,
        message: error.message || '配置验证失败'
      };
    }
  }

  /**
   * 导出配置
   */
  async exportConfigs(): Promise<SystemConfigItem[]> {
    try {
      return await this.getAllConfigs();
    } catch (error) {
      this.logger.error('导出配置失败:', error);
      throw error;
    }
  }

  /**
   * 初始化默认配置
   */
  private async initializeDefaultConfigs(): Promise<void> {
    const defaultConfigs = [
      // JWT配置
      { key: 'jwt.api.secret', value: 'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV', description: 'API JWT密钥' },
      { key: 'jwt.api.expires_in', value: '24h', description: 'API JWT过期时间' },
      { key: 'jwt.onlyoffice.secret', value: 'OnlyOffice-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV', description: 'OnlyOffice文档服务器JWT密钥' },
      { key: 'jwt.onlyoffice.header', value: 'Authorization', description: 'OnlyOffice JWT头部名称' },
      { key: 'jwt.onlyoffice.in_body', value: 'true', description: '是否在请求体中包含OnlyOffice JWT' },
      { key: 'jwt.onlyoffice.algorithm', value: 'HS256', description: 'OnlyOffice JWT算法' },

      // CORS配置
      { key: 'cors.origin', value: 'http://localhost:8080', description: 'CORS允许的源地址' },
      { key: 'cors.allowed_origins', value: 'http://localhost:3000,http://localhost:8080', description: 'CORS允许的所有源地址列表' },

      // OnlyOffice服务器配置
      { key: 'onlyoffice.server_url', value: 'http://*************/', description: 'OnlyOffice服务器地址' },
      { key: 'onlyoffice.document_server_url', value: 'http://*************/', description: 'OnlyOffice文档服务器地址' },
      { key: 'onlyoffice.document_port', value: '80', description: 'OnlyOffice文档服务器端口' },
      { key: 'onlyoffice.api_url_suffix', value: '/web-apps/apps/api/documents/api.js', description: 'OnlyOffice API JS路径' },

      // FileNet配置
      { key: 'filenet.host', value: '*************', description: 'FileNet服务器主机地址' },
      { key: 'filenet.port', value: '8090', description: 'FileNet服务器端口' },
      { key: 'filenet.username', value: 'your-filenet-username', description: 'FileNet用户名' },
      { key: 'filenet.password', value: 'YourFileNetPassword123!', description: 'FileNet密码（加密存储）' },
      { key: 'filenet.default_folder', value: '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}', description: 'FileNet默认文件夹ID' },
      { key: 'filenet.default_doc_class', value: 'SimpleDocument', description: 'FileNet默认文档类' },
      { key: 'filenet.default_source_type', value: 'MaxOffice', description: 'FileNet默认源类型' },
      { key: 'filenet.default_biz_tag', value: 'office_file', description: 'FileNet默认业务标签' },

      // 文件存储配置
      { key: 'storage.upload_path', value: './uploads', description: '文件上传路径' },
      { key: 'storage.tmp_path', value: './tmp', description: '临时文件路径' },
      { key: 'storage.max_file_size', value: '50MB', description: '最大文件大小' },
      { key: 'storage.allowed_file_types', value: '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt', description: '允许的文件类型' },

      // 缓存配置
      { key: 'cache.type', value: 'memory', description: '缓存类型' },
      { key: 'cache.ttl', value: '3600', description: '缓存TTL(秒)' },
      { key: 'cache.max_size', value: '100', description: '缓存最大条目数' },

      // Redis配置
      { key: 'redis.host', value: 'localhost', description: 'Redis主机地址' },
      { key: 'redis.port', value: '6379', description: 'Redis端口' },
      { key: 'redis.password', value: '', description: 'Redis密码' },
      { key: 'redis.db', value: '0', description: 'Redis数据库编号' },

      // 日志配置
      { key: 'logging.level', value: 'info', description: '日志级别' },
      { key: 'logging.dir', value: './logs', description: '日志目录' },
      { key: 'logging.max_size', value: '10m', description: '日志文件最大大小' },
      { key: 'logging.max_files', value: '7', description: '保留的日志文件数量' },

      // 性能监控配置
      { key: 'monitoring.enabled', value: 'true', description: '是否启用性能监控' },
      { key: 'monitoring.slow_query_threshold', value: '1000', description: '慢查询阈值(毫秒)' },
      { key: 'monitoring.request_timeout', value: '30000', description: '请求超时时间(毫秒)' },

      // 安全配置
      { key: 'security.rate_limit_window_ms', value: '900000', description: '速率限制时间窗口(毫秒)' },
      { key: 'security.rate_limit_max_requests', value: '100', description: '速率限制最大请求数' },
      { key: 'security.enable_security_headers', value: 'true', description: '是否启用安全头' },

      // 服务器配置
      { key: 'server.host', value: 'localhost', description: '服务器主机地址' },
      { key: 'server.callback_url', value: 'http://localhost:3000/api/editor/callback', description: '回调URL' },
    ];

    for (const config of defaultConfigs) {
      await this.databaseService.query(
        'INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)',
        [config.key, config.value, config.description]
      );
    }
  }
} 