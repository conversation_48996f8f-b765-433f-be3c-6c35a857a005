import { ApiService } from './api'
import type { Role, Permission } from '@/types/api.types'

// 权限列表API响应类型
export interface PermissionListResponse {
  data: Permission[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 角色列表API响应类型
export interface RoleListResponse {
  data: Role[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户权限响应类型
export interface UserPermissionResponse {
  id: string
  username: string
  email: string
  fullName: string
  roles: Array<{
    id: string
    name: string
    displayName: string
    color?: string
  }>
  permissions: string[]
  lastLoginAt: string
}

// 权限查询参数
export interface PermissionQueryParams {
  page?: number
  pageSize?: number
  search?: string
  module?: string
  action?: string
  status?: 'active' | 'inactive'
  sortBy?: 'name' | 'code' | 'module' | 'createdAt'
  sortOrder?: 'ASC' | 'DESC'
}

// 角色查询参数
export interface RoleQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive'
  sortBy?: 'name' | 'code' | 'createdAt'
  sortOrder?: 'ASC' | 'DESC'
}

// 创建权限DTO
export interface CreatePermissionDto {
  code: string
  name: string
  description?: string
  module: string
  resource: string
  action: string
  level?: 'basic' | 'medium' | 'high'
  status?: 'active' | 'inactive'
}

// 更新权限DTO
export interface UpdatePermissionDto {
  name?: string
  description?: string
  module?: string
  resource?: string
  action?: string
  level?: 'basic' | 'medium' | 'high'
  status?: 'active' | 'inactive'
}

// 创建角色DTO
export interface CreateRoleDto {
  code: string
  name: string
  displayName?: string
  description?: string
  permissions: string[]
  status?: 'active' | 'inactive'
  color?: string
  icon?: string
}

// 更新角色DTO
export interface UpdateRoleDto {
  name?: string
  displayName?: string
  description?: string
  permissions?: string[]
  status?: 'active' | 'inactive'
  color?: string
  icon?: string
}

// 权限统计信息
export interface PermissionStatsResponse {
  totalPermissions: number
  moduleCount: number
  byModule: Record<string, number>
  byLevel: {
    basic: number
    medium: number
    high: number
  }
}

// 角色统计信息
export interface RoleStatsResponse {
  totalRoles: number
  activeRoles: number
  inactiveRoles: number
  userCounts: Record<string, number>
}

/**
 * 权限管理API服务
 */
export class PermissionsApiService {
  // ================================
  // 权限管理相关API
  // ================================

  /**
   * 获取权限列表
   */
  static async getPermissions(params?: PermissionQueryParams): Promise<PermissionListResponse> {
    return ApiService.get<PermissionListResponse>('/permissions', { params })
  }

  /**
   * 获取权限详情
   */
  static async getPermissionById(id: string): Promise<Permission> {
    return ApiService.get<Permission>(`/permissions/${id}`)
  }

  /**
   * 根据代码获取权限
   */
  static async getPermissionByCode(code: string): Promise<Permission> {
    return ApiService.get<Permission>(`/permissions/by-code/${code}`)
  }

  /**
   * 创建权限
   */
  static async createPermission(data: CreatePermissionDto): Promise<Permission> {
    return ApiService.post<Permission>('/permissions', data)
  }

  /**
   * 更新权限
   */
  static async updatePermission(id: string, data: UpdatePermissionDto): Promise<Permission> {
    return ApiService.put<Permission>(`/permissions/${id}`, data)
  }

  /**
   * 删除权限
   */
  static async deletePermission(id: string): Promise<void> {
    return ApiService.delete<void>(`/permissions/${id}`)
  }

  /**
   * 批量操作权限
   */
  static async bulkOperationPermissions(
    operation: 'enable' | 'disable' | 'delete',
    ids: string[]
  ): Promise<{ successCount: number; failedCount: number }> {
    return ApiService.post<{ successCount: number; failedCount: number }>(
      '/permissions/bulk-operation',
      {
        operation,
        ids,
      }
    )
  }

  /**
   * 获取权限统计信息
   */
  static async getPermissionStats(): Promise<PermissionStatsResponse> {
    return ApiService.get<PermissionStatsResponse>('/permissions/stats')
  }

  /**
   * 获取权限树结构
   */
  static async getPermissionTree(): Promise<{
    [module: string]: Permission[]
  }> {
    return ApiService.get<{
      [module: string]: Permission[]
    }>('/permissions/tree')
  }

  // ================================
  // 角色管理相关API
  // ================================

  /**
   * 获取角色列表
   */
  static async getRoles(params?: RoleQueryParams): Promise<RoleListResponse> {
    return ApiService.get<RoleListResponse>('/roles', { params })
  }

  /**
   * 获取角色详情
   */
  static async getRoleById(id: string): Promise<Role> {
    return ApiService.get<Role>(`/roles/${id}`)
  }

  /**
   * 创建角色
   */
  static async createRole(data: CreateRoleDto): Promise<Role> {
    return ApiService.post<Role>('/roles', data)
  }

  /**
   * 更新角色
   */
  static async updateRole(id: string, data: UpdateRoleDto): Promise<Role> {
    return ApiService.put<Role>(`/roles/${id}`, data)
  }

  /**
   * 删除角色
   */
  static async deleteRole(id: string): Promise<void> {
    return ApiService.delete<void>(`/roles/${id}`)
  }

  /**
   * 获取角色权限
   */
  static async getRolePermissions(id: string): Promise<string[]> {
    return ApiService.get<string[]>(`/roles/${id}/permissions`)
  }

  /**
   * 设置角色权限
   */
  static async setRolePermissions(id: string, permissions: string[]): Promise<void> {
    return ApiService.put<void>(`/roles/${id}/permissions`, { permissions })
  }

  /**
   * 获取角色统计信息
   */
  static async getRoleStats(): Promise<RoleStatsResponse> {
    return ApiService.get<RoleStatsResponse>('/roles/stats')
  }

  // ================================
  // 用户权限相关API
  // ================================

  /**
   * 获取用户权限列表
   */
  static async getUserPermissions(params?: {
    page?: number
    pageSize?: number
    keyword?: string
    roleId?: string
  }): Promise<{
    data: UserPermissionResponse[]
    total: number
    page: number
    pageSize: number
  }> {
    return ApiService.get<{
      data: UserPermissionResponse[]
      total: number
      page: number
      pageSize: number
    }>('/users/permissions', { params })
  }

  /**
   * 获取指定用户的权限
   */
  static async getUserPermissionsById(userId: string): Promise<{
    user: UserPermissionResponse
    permissions: string[]
    roles: Role[]
  }> {
    return ApiService.get<{
      user: UserPermissionResponse
      permissions: string[]
      roles: Role[]
    }>(`/users/${userId}/permissions`)
  }

  /**
   * 设置用户权限
   */
  static async setUserPermissions(userId: string, permissions: string[]): Promise<void> {
    return ApiService.put<void>(`/users/${userId}/permissions`, { permissions })
  }

  /**
   * 检查用户权限
   */
  static async checkUserPermission(
    userId: string,
    permission: string
  ): Promise<{ hasPermission: boolean }> {
    return ApiService.get<{ hasPermission: boolean }>(`/users/${userId}/permissions/check`, {
      params: { permission },
    })
  }

  /**
   * 获取当前用户权限
   */
  static async getCurrentUserPermissions(): Promise<{
    user: UserPermissionResponse
    permissions: string[]
    roles: Role[]
  }> {
    return ApiService.get<{
      user: UserPermissionResponse
      permissions: string[]
      roles: Role[]
    }>('/users/permissions/me')
  }
}

export default PermissionsApiService
