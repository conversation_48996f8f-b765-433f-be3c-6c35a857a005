@echo off
chcp 65001 >nul
echo Starting MySQL MCP Server...
set MYSQL_HOST=*************
set MYSQL_PORT=3306
set MYSQL_USER=onlyfile_user
set MYSQL_PASS=0nlyF!le$ecure#123
set MY<PERSON><PERSON>_DB=onlyfile
set ALLOW_INSERT_OPERATION=true
set ALLOW_UPDATE_OPERATION=true
set ALLOW_DELETE_OPERATION=false
set MYSQL_ENABLE_LOGGING=true
set MYSQL_POOL_SIZE=10
set MYSQL_QUERY_TIMEOUT=30000

echo Environment variables set
echo Starting MCP server...
echo.

npx -y -p @benborla29/mcp-server-mysql -p dotenv mcp-server-mysql

echo.
echo Process finished. Press any key to exit...
pause >nul