import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { DatabaseService } from '../../database/services/database.service';

/**
 * JWT配置管理服务
 * @description 管理API JWT和OnlyOffice JWT的分离配置
 */
@Injectable()
export class JwtConfigService {
  private readonly logger = new Logger(JwtConfigService.name);
  private jwtCache = new Map<string, string>();
  private cacheUpdatedAt = new Date(0);
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  constructor(
    private readonly nestConfigService: NestConfigService,
    private readonly databaseService: DatabaseService,
  ) {
    // 延迟初始化，等待数据库连接完成
    setTimeout(() => this.initializeJwtSettings(), 1000);
  }

  /**
   * 初始化JWT设置
   */
  private async initializeJwtSettings(): Promise<void> {
    try {
      // 检查数据库连接是否可用
      if (!this.databaseService) {
        this.logger.warn('数据库服务未准备就绪，跳过JWT配置初始化');
        return;
      }

      await this.ensureJwtSettingsExist();
      await this.refreshCache();
      this.logger.log('JWT配置初始化完成');
    } catch (error) {
      this.logger.error('JWT配置初始化失败:', error);
      // 不抛出错误，允许服务正常启动
    }
  }

  /**
   * 确保JWT设置存在
   */
  private async ensureJwtSettingsExist(): Promise<void> {
    const jwtSettings = [
      {
        key: 'jwt.onlyoffice.secret',
        value: 'OnlyOffice-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV',
        description: 'OnlyOffice文档服务器JWT密钥',
      },
      {
        key: 'jwt.onlyoffice.header',
        value: 'Authorization',
        description: 'OnlyOffice JWT头部名称',
      },
      {
        key: 'jwt.onlyoffice.in_body',
        value: 'true',
        description: '是否在请求体中包含OnlyOffice JWT',
      },
    ];

    for (const setting of jwtSettings) {
      const query = `
        INSERT IGNORE INTO system_settings (setting_key, setting_value, description)
        VALUES (?, ?, ?)
      `;
      await this.databaseService.query(query, [setting.key, setting.value, setting.description]);
    }
  }

  /**
   * 刷新JWT缓存
   */
  private async refreshCache(): Promise<void> {
    try {
      const query = `
        SELECT setting_key, setting_value
        FROM system_settings
        WHERE setting_key LIKE 'jwt.%'
      `;
      
      const jwtConfigs = await this.databaseService.query(query);
      
      this.jwtCache.clear();
      jwtConfigs.forEach((config: { setting_key: string; setting_value: string }) => {
        this.jwtCache.set(config.setting_key, config.setting_value);
      });
      
      this.cacheUpdatedAt = new Date();
      this.logger.log(`JWT配置缓存已更新，共 ${this.jwtCache.size} 项`);
    } catch (error) {
      this.logger.error('刷新JWT配置缓存失败:', error);
    }
  }

  /**
   * 检查缓存是否需要刷新
   */
  private async checkCacheValidity(): Promise<void> {
    const now = new Date();
    if (now.getTime() - this.cacheUpdatedAt.getTime() > this.CACHE_TTL) {
      await this.refreshCache();
    }
  }

  /**
   * 获取API JWT配置 - 用于前后端认证
   */
  getApiJwtConfig(): {
    secret: string;
    expiresIn: string;
    algorithm: string;
    issuer: string;
    audience: string;
  } {
    return {
      secret: this.nestConfigService.get<string>('JWT_SECRET', 'default-api-secret'),
      expiresIn: this.nestConfigService.get<string>('JWT_EXPIRES_IN', '24h'),
      algorithm: 'HS256',
      issuer: 'onlyoffice-api',
      audience: 'onlyoffice-client',
    };
  }

  /**
   * 获取OnlyOffice JWT配置 - 用于文档服务器认证
   */
  async getOnlyOfficeJwtConfig(): Promise<{
    secret: string;
    header: string;
    inBody: boolean;
    algorithm: string;
  }> {
    await this.checkCacheValidity();
    
    return {
      secret: this.jwtCache.get('jwt.onlyoffice.secret') || 'default-onlyoffice-secret',
      header: this.jwtCache.get('jwt.onlyoffice.header') || 'Authorization',
      inBody: (this.jwtCache.get('jwt.onlyoffice.in_body') || 'true') === 'true',
      algorithm: 'HS256',
    };
  }

  /**
   * 更新OnlyOffice JWT密钥
   */
  async updateOnlyOfficeJwtSecret(newSecret: string): Promise<void> {
    const query = `
      UPDATE system_settings 
      SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
      WHERE setting_key = 'jwt.onlyoffice.secret'
    `;
    
    await this.databaseService.query(query, [newSecret]);
    await this.refreshCache();
    
    this.logger.log('OnlyOffice JWT密钥已更新');
  }

  /**
   * 更新OnlyOffice JWT配置
   */
  async updateOnlyOfficeJwtConfig(config: {
    secret?: string;
    header?: string;
    inBody?: boolean;
  }): Promise<void> {
    const updates: Array<{ key: string; value: string }> = [];

    if (config.secret !== undefined) {
      updates.push({ key: 'jwt.onlyoffice.secret', value: config.secret });
    }
    if (config.header !== undefined) {
      updates.push({ key: 'jwt.onlyoffice.header', value: config.header });
    }
    if (config.inBody !== undefined) {
      updates.push({ key: 'jwt.onlyoffice.in_body', value: config.inBody.toString() });
    }

    for (const update of updates) {
      const query = `
        UPDATE system_settings 
        SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
        WHERE setting_key = ?
      `;
      await this.databaseService.query(query, [update.value, update.key]);
    }

    await this.refreshCache();
    this.logger.log('OnlyOffice JWT配置已更新');
  }

  /**
   * 获取JWT配置状态
   */
  async getJwtConfigStatus(): Promise<{
    apiJwt: {
      configured: boolean;
      source: 'env' | 'default';
    };
    onlyofficeJwt: {
      configured: boolean;
      source: 'database' | 'default';
    };
  }> {
    await this.checkCacheValidity();

    const apiSecret = this.nestConfigService.get<string>('JWT_SECRET');
    const onlyofficeSecret = this.jwtCache.get('jwt.onlyoffice.secret');

    return {
      apiJwt: {
        configured: !!apiSecret && apiSecret !== 'default-api-secret',
        source: apiSecret ? 'env' : 'default',
      },
      onlyofficeJwt: {
        configured: !!onlyofficeSecret && onlyofficeSecret !== 'default-onlyoffice-secret',
        source: onlyofficeSecret ? 'database' : 'default',
      },
    };
  }

  /**
   * 验证JWT配置完整性
   */
  async validateJwtConfig(): Promise<{
    valid: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];
    
    try {
      const apiConfig = this.getApiJwtConfig();
      const onlyofficeConfig = await this.getOnlyOfficeJwtConfig();

      // 检查API JWT配置
      if (!apiConfig.secret || apiConfig.secret === 'default-api-secret') {
        issues.push('API JWT密钥未正确配置');
      }
      if (apiConfig.secret.length < 32) {
        issues.push('API JWT密钥长度不足（建议至少32字符）');
      }

      // 检查OnlyOffice JWT配置
      if (!onlyofficeConfig.secret || onlyofficeConfig.secret === 'default-onlyoffice-secret') {
        issues.push('OnlyOffice JWT密钥未正确配置');
      }
      if (onlyofficeConfig.secret.length < 32) {
        issues.push('OnlyOffice JWT密钥长度不足（建议至少32字符）');
      }

      // 检查JWT密钥是否相同（应该不同）
      if (apiConfig.secret === onlyofficeConfig.secret) {
        issues.push('API JWT和OnlyOffice JWT使用了相同的密钥（建议使用不同的密钥）');
      }

      return {
        valid: issues.length === 0,
        issues,
      };
    } catch (error) {
      issues.push(`JWT配置验证失败: ${error.message}`);
      return {
        valid: false,
        issues,
      };
    }
  }

  /**
   * 生成推荐的JWT密钥
   */
  generateRecommendedJwtSecret(prefix = 'OO'): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = prefix + '-';
    for (let i = 0; i < 48; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
} 