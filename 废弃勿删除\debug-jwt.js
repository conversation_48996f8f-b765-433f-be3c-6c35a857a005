const jwt = require('jsonwebtoken');

// 从浏览器localStorage获取token进行调试
// 你需要将实际的token粘贴在这里
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NDkxODAzMTAsImV4cCI6MTc0OTI2NjcxMCwiaXNzIjoib25seW9mZmljZS1uZXN0anMiLCJhdWQiOiJvbmx5b2ZmaWNlLWNsaWVudCJ9.Rk3vd99oqmLQo94dqMDxpYZqVL5HZlZiFlVRzBdI0fw';

// 实际的JWT密钥
const JWT_SECRET = "R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV";

console.log('=== JWT Token 调试信息 ===');
console.log('Token 长度:', token.length);

try {
  // 解码token(不验证签名)
  const decoded = jwt.decode(token, { complete: true });
  console.log('\n🔍 Token Header:');
  console.log(JSON.stringify(decoded.header, null, 2));
  
  console.log('\n🔍 Token Payload:');
  console.log(JSON.stringify(decoded.payload, null, 2));
  
  // 检查是否过期
  const now = Math.floor(Date.now() / 1000);
  console.log('\n⏰ 时间检查:');
  console.log('当前时间戳:', now);
  console.log('Token签发时间:', decoded.payload.iat);
  console.log('Token过期时间:', decoded.payload.exp);
  console.log('是否过期:', decoded.payload.exp < now);
  
  // 测试验证（使用实际的密钥）
  console.log('\n🔐 验证测试:');
  console.log('使用密钥:', JWT_SECRET);
  
  try {
    const verified = jwt.verify(token, JWT_SECRET, {
      issuer: 'onlyoffice-nestjs',
      audience: 'onlyoffice-client'
    });
    console.log('✅ Token验证成功!');
    console.log('验证后的payload:', JSON.stringify(verified, null, 2));
  } catch (verifyError) {
    console.log('❌ Token验证失败:', verifyError.message);
    
    // 尝试不验证issuer和audience
    console.log('\n🔄 尝试不验证issuer/audience:');
    try {
      const verified2 = jwt.verify(token, JWT_SECRET);
      console.log('✅ 基础Token验证成功!');
      console.log('验证后的payload:', JSON.stringify(verified2, null, 2));
    } catch (verifyError2) {
      console.log('❌ 基础Token验证也失败:', verifyError2.message);
    }
  }
  
} catch (error) {
  console.error('❌ Token解析失败:', error.message);
} 