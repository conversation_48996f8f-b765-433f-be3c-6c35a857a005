// 统一导出所有API服务
export { ApiService } from './api'
export { AuthApiService } from './auth.api'
export { UsersApiService } from './users.api'
export { DocumentsApiService } from './documents.api'
export { ConfigTemplateApiService } from './config.api'
export { TemplatesApiService } from './templates.api'
export { UploadsApiService } from './uploads.api'
export { FileNetApiService } from './filenet.api'

// 导出类型定义
export type { LoginCredentials, RefreshTokenResponse } from './auth.api'
export type {
  CreateUserDto,
  UpdateUserDto,
  ChangePasswordDto,
  ResetPasswordDto,
  UserListQueryParams,
  UserStatsResponse,
} from './users.api'
export type {
  CreateDocumentDto,
  UpdateDocumentDto,
  DocumentListQueryParams,
  OnlyOfficeCallbackData,
} from './documents.api'

// 导出配置管理相关类型
export type {
  ConfigTemplate,
  CreateConfigTemplateDto,
  UpdateConfigTemplateDto,
  ConfigTemplateResponse,
  SingleConfigTemplateResponse,
} from './config.api'

// 导出模板管理相关类型
export type {
  CreateDocumentTemplateDto,
  UpdateDocumentTemplateDto,
  DocumentTemplateListQueryParams,
  CreateTemplateCategoryDto,
  TemplateCategory,
} from './templates.api'

// 导出文件上传相关类型
export type { UploadFileResponse, FileListQueryParams, FileUploadOptions } from './uploads.api'

// 导出FileNet相关类型
export type {
  FileNetDocument,
  UploadToFileNetDto,
  FileNetSearchParams,
  FileNetVersionInfo,
} from './filenet.api'
