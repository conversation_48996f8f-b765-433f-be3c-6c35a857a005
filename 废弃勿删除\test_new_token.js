const http = require('http');

// 先登录获取新token
const loginAndTestPermissions = async () => {
  // 登录请求
  const loginData = JSON.stringify({
    username: 'admin',
    password: 'password123'
  });

  const loginOptions = {
    hostname: '*************',
    port: 3000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(loginData)
    }
  };

  return new Promise((resolve, reject) => {
    const loginReq = http.request(loginOptions, (res) => {
      console.log(`登录状态: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const loginResponse = JSON.parse(data);
          console.log('登录响应:', JSON.stringify(loginResponse, null, 2));
          
          if (loginResponse.success && loginResponse.data.accessToken) {
            const token = loginResponse.data.accessToken;
            console.log('获取到新token:', token.substring(0, 50) + '...');
            
            // 测试权限API
            testPermissionsWithToken(token);
          } else {
            console.error('登录失败:', loginResponse);
          }
        } catch (error) {
          console.error('解析登录响应失败:', error);
          console.log('原始响应:', data);
        }
      });
    });

    loginReq.on('error', (error) => {
      console.error('登录请求失败:', error);
    });

    loginReq.write(loginData);
    loginReq.end();
  });
};

const testPermissionsWithToken = (token) => {
  const options = {
    hostname: '*************',
    port: 3000,
    path: '/api/permissions/user/my',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`权限API状态: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('权限API响应:');
      try {
        const jsonData = JSON.parse(data);
        console.log(JSON.stringify(jsonData, null, 2));
      } catch (error) {
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('权限API请求失败:', error);
  });

  req.end();
};

loginAndTestPermissions(); 