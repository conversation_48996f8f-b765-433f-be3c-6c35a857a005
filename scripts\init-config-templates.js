/**
 * 初始化配置模板数据库脚本
 */
const fs = require('fs').promises;
const path = require('path');
const db = require('../services/database');

async function initConfigTemplates() {
    try {
        console.log('开始初始化配置模板数据库...');

        // 读取并执行SQL脚本
        const sqlPath = path.join(__dirname, 'create-config-templates.sql');
        const sql = await fs.readFile(sqlPath, 'utf8');

        // 将SQL分割为单独的语句
        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

        console.log(`执行 ${statements.length} 个SQL语句...`);

        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i].trim();
            if (statement) {
                try {
                    console.log(`执行第 ${i + 1} 个语句...`);
                    await db.query(statement);
                } catch (error) {
                    // 忽略表已存在的错误
                    if (error.code !== 'ER_TABLE_EXISTS_ERROR' && !error.message.includes('already exists')) {
                        console.error(`执行第 ${i + 1} 个语句时出错:`, error.message);
                        throw error;
                    } else {
                        console.log(`跳过第 ${i + 1} 个语句（表已存在或其他非致命错误）`);
                    }
                }
            }
        }

        // 验证初始化结果
        const templatesCount = await db.queryOne('SELECT COUNT(*) as count FROM config_templates');
        const itemsCount = await db.queryOne('SELECT COUNT(*) as count FROM config_template_items');

        console.log('配置模板初始化完成！');
        console.log(`- 配置模板数量: ${templatesCount.count}`);
        console.log(`- 配置项数量: ${itemsCount.count}`);

        // 显示已创建的模板
        const templates = await db.query('SELECT id, name, description FROM config_templates WHERE is_active = TRUE ORDER BY created_at');
        console.log('\n已创建的配置模板:');
        templates.forEach(template => {
            console.log(`- ${template.name}: ${template.description} (ID: ${template.id})`);
        });

    } catch (error) {
        console.error('初始化配置模板失败:', error);
        process.exit(1);
    } finally {
        // 数据库连接池会自动管理连接，不需要手动关闭
        console.log('初始化脚本执行完成');
    }
}

// 如果是直接运行此脚本
if (require.main === module) {
    initConfigTemplates();
}

module.exports = initConfigTemplates; 