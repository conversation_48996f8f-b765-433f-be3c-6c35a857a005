import{j as u,k as te,d as J,r as y,z as j,Z as se,q as m,h as f,c as C,e as i,t as b,x as T,s as g,i as L,b as w,n as F,v as U,O as ue,X as de,_ as W,m as S,o as fe,R as ge,F as H,g as G,a7 as pe,$ as ve,a0 as _e}from"./index-5218909a.js";import{r as $}from"./request-228c3d43.js";import{S as me}from"./SaveOutlined-064bb5bd.js";var ye={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"};const he=ye;function K(o){for(var n=1;n<arguments.length;n++){var e=arguments[n]!=null?Object(arguments[n]):{},l=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(e).filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),l.forEach(function(r){Ce(o,r,e[r])})}return o}function Ce(o,n,e){return n in o?Object.defineProperty(o,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):o[n]=e,o}var Z=function(n,e){var l=K({},n,e.attrs);return u(te,K({},l,{icon:he}),null)};Z.displayName="ApiOutlined";Z.inheritAttrs=!1;const ke=Z;var be={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 472a40 40 0 1080 0 40 40 0 10-80 0zm367 352.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 01-121.3 43.9c-32.7 0-64.1-8.3-91.8-23.7l118.8-307.5zM210.5 844l41.7-107.8c35.7 18.1 75.4 27.8 116.6 27.8 61.2 0 119.2-21.5 165.3-60 33.9-28.2 76.3-43.9 121.3-43.9 35 0 68.4 9.5 97.6 27.1L813.5 844h-603z"}}]},name:"experiment",theme:"outlined"};const we=be;function ee(o){for(var n=1;n<arguments.length;n++){var e=arguments[n]!=null?Object(arguments[n]):{},l=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(e).filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),l.forEach(function(r){Oe(o,r,e[r])})}return o}function Oe(o,n,e){return n in o?Object.defineProperty(o,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):o[n]=e,o}var Y=function(n,e){var l=ee({},n,e.attrs);return u(te,ee({},l,{icon:we}),null)};Y.displayName="ExperimentOutlined";Y.inheritAttrs=!1;const $e=Y,q={getAllConfigs(){return $.get("/system-config/all")},getConfigsByCategory(o){return $.get(`/system-config/category/${o}`)},getConfig(o){return $.get(`/system-config/${o}`)},updateConfig(o,n){return $.put(`/system-config/${o}`,n)},batchUpdateConfigs(o){return $.put("/system-config/batch",{configs:o})},resetToDefaults(){return $.post("/system-config/reset")},getConfigHistory(o=50){return $.get("/system-config/history",{params:{limit:o}})},testConfig(o){return $.post(`/system-config/${o}/test`)},exportConfigs(){return $.get("/system-config/export",{responseType:"blob"})},importConfigs(o){const n=new FormData;return n.append("file",o),$.post("/system-config/import",n,{headers:{"Content-Type":"multipart/form-data"}})},validateConfig(o,n){return $.post("/system-config/validate",{key:o,value:n})}},Se={class:"config-info"},Te={class:"config-key"},Le={class:"key-text"},Me={key:0,class:"config-description"},xe={class:"config-control"},De={class:"config-input"},Pe={class:"config-actions"},ze=J({__name:"ConfigItem",props:{config:{type:Object,required:!0},compact:{type:Boolean,required:!1,default:!1}},emits:["update","test"],setup(o,{emit:n}){const e=o,l=n,r=y(""),p=y(!1),A=j(()=>e.config.setting_key.includes("advanced")||e.config.setting_key.includes("debug")||e.config.setting_key.includes("dev")),M=j(()=>{const c=e.config.setting_key.toLowerCase();return c.includes("password")||c.includes("secret")||c.includes("key")||c.includes("token")}),B=j(()=>{const c=e.config.setting_key.toLowerCase();return c.includes("url")||c.includes("host")||c.includes("endpoint")||c.includes("server")}),x=c=>c?"*".repeat(Math.min(c.length,8)):"",O=()=>{r.value!==e.config.setting_value&&l("update",e.config.setting_key,r.value)},I=()=>{l("test",e.config)},N=()=>{p.value=!p.value};return se(()=>e.config.setting_value,c=>{r.value=c},{immediate:!0}),(c,k)=>{const s=m("a-tag"),v=m("a-input"),z=m("a-input-password"),D=m("a-button"),E=m("a-tooltip");return f(),C("div",{class:F(["config-item",{compact:c.compact}])},[i("div",Se,[i("div",Te,[i("span",Le,b(c.config.setting_key),1),A.value?(f(),T(s,{key:0,size:"small",color:"orange"},{default:g(()=>k[2]||(k[2]=[L("高级")])),_:1,__:[2]})):w("v-if",!0),M.value?(f(),T(s,{key:1,size:"small",color:"red"},{default:g(()=>k[3]||(k[3]=[L("敏感")])),_:1,__:[3]})):w("v-if",!0)]),c.config.description?(f(),C("div",Me,b(c.config.description),1)):w("v-if",!0)]),i("div",xe,[i("div",De,[!M.value||p.value?(f(),T(v,{key:0,value:r.value,"onUpdate:value":k[0]||(k[0]=P=>r.value=P),placeholder:c.config.setting_value,onBlur:O,onPressEnter:O},null,8,["value","placeholder"])):(f(),T(z,{key:1,value:r.value,"onUpdate:value":k[1]||(k[1]=P=>r.value=P),placeholder:x(c.config.setting_value),onBlur:O,onPressEnter:O},null,8,["value","placeholder"]))]),i("div",Pe,[u(E,{title:"测试连接"},{default:g(()=>[u(D,{type:"text",size:"small",onClick:I,disabled:!B.value,class:F({compact:c.compact})},{icon:g(()=>[u(U(ke))]),_:1},8,["disabled","class"])]),_:1}),M.value?(f(),T(E,{key:0,title:p.value?"隐藏":"显示"},{default:g(()=>[u(D,{type:"text",size:"small",onClick:N,class:F({compact:c.compact})},{icon:g(()=>[p.value?(f(),T(U(de),{key:1})):(f(),T(U(ue),{key:0}))]),_:1},8,["class"])]),_:1},8,["title"])):w("v-if",!0)])])],2)}}});const Ae=W(ze,[["__scopeId","data-v-b7223bf6"],["__file","D:/Code/OnlyOffice/frontend/src/pages/system/components/ConfigItem.vue"]]),Ee={key:0},je={class:"config-key"},Ue={class:"config-value"},Be={class:"test-section"},Ie={key:0,class:"test-result"},Ne={key:0,class:"test-details"},Ve=J({__name:"ConfigTestModal",props:{visible:{type:Boolean,required:!0},config:{type:[Object,null],required:!0}},emits:["update:visible","close"],setup(o,{emit:n}){const e=o,l=n,r=y(!1),p=y(null);se(()=>e.visible,s=>{s&&(p.value=null)});const A=s=>{if(!e.config)return s;const v=e.config.setting_key.toLowerCase();return v.includes("password")||v.includes("secret")?"*".repeat(s.length):s},M=async()=>{if(e.config){r.value=!0,p.value=null;try{let s;e.config.setting_key.includes("database")?s=await B():e.config.setting_key.includes("redis")?s=await x():e.config.setting_key.includes("filenet")?s=await O():e.config.setting_key.includes("onlyoffice")?s=await I():s=await N(),p.value=s,s.success?S.success("测试成功"):S.error("测试失败")}catch(s){const v=s instanceof Error?s.message:"测试过程中发生错误";p.value={success:!1,message:v,details:{error:String(s)}},S.error("测试失败")}finally{r.value=!1}}},B=async()=>(await new Promise(s=>setTimeout(s,1e3)),{success:!0,message:"数据库连接正常",details:{host:"*************",port:3306,database:"onlyfile",responseTime:"15ms"}}),x=async()=>(await new Promise(s=>setTimeout(s,800)),{success:!0,message:"Redis连接正常",details:{host:"localhost",port:6379,pingResponse:"PONG"}}),O=async()=>(await new Promise(s=>setTimeout(s,1500)),{success:!1,message:"FileNet服务器连接超时",details:{host:"*************",port:8090,timeout:"30s"}}),I=async()=>(await new Promise(s=>setTimeout(s,1200)),{success:!0,message:"OnlyOffice文档服务器连接正常",details:{serverUrl:"http://*************/",version:"7.5.1",status:"healthy"}}),N=async()=>{var s;return await new Promise(v=>setTimeout(v,500)),{success:!0,message:"配置项验证通过",details:{value:(s=e.config)==null?void 0:s.setting_value,validation:"passed"}}},c=()=>{l("update:visible",!1),l("close")},k=()=>{l("update:visible",!1),l("close")};return(s,v)=>{const z=m("a-descriptions-item"),D=m("a-descriptions"),E=m("a-button"),P=m("a-alert"),Q=m("a-modal");return f(),T(Q,{open:s.visible,title:"配置连接测试",width:600,onOk:c,onCancel:k,"onUpdate:open":v[0]||(v[0]=X=>l("update:visible",X))},{footer:g(()=>[u(E,{onClick:k},{default:g(()=>v[3]||(v[3]=[L("关闭")])),_:1,__:[3]})]),default:g(()=>[s.config?(f(),C("div",Ee,[u(D,{column:1,bordered:""},{default:g(()=>[u(z,{label:"配置项"},{default:g(()=>[i("span",je,b(s.config.setting_key),1)]),_:1}),u(z,{label:"当前值"},{default:g(()=>[i("span",Ue,b(A(s.config.setting_value)),1)]),_:1}),u(z,{label:"描述"},{default:g(()=>[L(b(s.config.description),1)]),_:1})]),_:1}),i("div",Be,[u(E,{type:"primary",onClick:M,loading:r.value,disabled:!s.config.setting_value},{default:g(()=>[u(U($e)),v[1]||(v[1]=L(" 开始测试 "))]),_:1,__:[1]},8,["loading","disabled"])]),p.value?(f(),C("div",Ie,[u(P,{type:p.value.success?"success":"error",message:p.value.success?"连接测试成功":"连接测试失败",description:p.value.message,"show-icon":""},null,8,["type","message","description"]),p.value.details?(f(),C("div",Ne,[v[2]||(v[2]=i("h4",null,"详细信息:",-1)),i("pre",null,b(JSON.stringify(p.value.details,null,2)),1)])):w("v-if",!0)])):w("v-if",!0)])):w("v-if",!0)]),_:1},8,["open"])}}});const Re=W(Ve,[["__scopeId","data-v-d57af3f1"],["__file","D:/Code/OnlyOffice/frontend/src/pages/system/components/ConfigTestModal.vue"]]),qe={class:"system-config-container"},Fe={class:"config-header"},He={class:"header-actions"},Ge={class:"config-filters"},Je={class:"filter-row"},We={class:"config-content-compact"},Qe={class:"config-main"},Xe=["onClick"],Ze={class:"category-name"},Ye={class:"category-content-compact"},Ke={class:"config-grid"},et={class:"config-sidebar"},tt={class:"sidebar-section"},st={class:"history-list-compact"},nt={key:1,class:"history-items"},ot={class:"history-config"},it={class:"history-time"},at={class:"sidebar-section"},lt={class:"stats-grid"},ct={class:"stat-item"},rt={class:"stat-number"},ut={class:"stat-item"},dt={class:"stat-number"},ft=J({__name:"SystemConfig",setup(o){const n=y([]),e=y([]),l=y(""),r=y(""),p=y(!1),A=y(!1),M=y(!1),B=y(!0),x=y({jwt:!0,onlyoffice:!0,filenet:!0,storage:!1}),O=y(!1),I=y(null),N=y([{key:"jwt",name:"JWT 配置",icon:"lock"},{key:"onlyoffice",name:"OnlyOffice 配置",icon:"file"},{key:"filenet",name:"FileNet 配置",icon:"database"},{key:"storage",name:"存储配置",icon:"folder"},{key:"redis",name:"Redis/缓存配置",icon:"thunderbolt"},{key:"monitoring",name:"监控配置",icon:"monitor"},{key:"security",name:"安全配置",icon:"safety"},{key:"other",name:"其他配置",icon:"setting"}]),c=j(()=>n.value.length),k=j(()=>N.value.map(t=>({...t,configs:n.value.filter(_=>_.setting_key.toLowerCase().startsWith(t.key.toLowerCase()))}))),s=j(()=>k.value.filter(a=>{var _,h;return r.value&&a.key!==r.value?!1:((_=a.configs)==null?void 0:_.some(V=>V.setting_key.toLowerCase().includes(l.value.toLowerCase())||V.description&&V.description.toLowerCase().includes(l.value.toLowerCase())))&&(((h=a.configs)==null?void 0:h.length)||0)>0}).map(a=>{var t;return{...a,configs:(t=a.configs)==null?void 0:t.filter(_=>_.setting_key.toLowerCase().includes(l.value.toLowerCase())||_.description&&_.description.toLowerCase().includes(l.value.toLowerCase()))}})),v=j(()=>s.value.flatMap(a=>a.configs||[])),z=async()=>{try{B.value=!0;const a=await q.getAllConfigs();n.value=a.data||[],S.success(`加载了 ${n.value.length} 个配置项`)}catch(a){console.error("加载配置失败:",a),S.error("加载配置失败")}finally{B.value=!1}},D=async()=>{try{const a=await q.getConfigHistory(10);e.value=a.data||[],e.value.length>0?console.log(`✅ 加载了 ${e.value.length} 条配置历史记录`):console.log("ℹ️ 暂无配置历史记录")}catch(a){console.error("加载配置历史失败:",a),e.value=[{id:"demo-1",setting_key:"cache.ttl",old_value:"3600",new_value:"test-value-"+Date.now(),changed_by:"system",changed_at:new Date().toISOString(),description:"配置更新示例"}],console.log("⚠️ 使用模拟历史记录")}},E=a=>{x.value[a]=!x.value[a]},P=()=>{},Q=async(a,t)=>{try{await q.updateConfig(a,{setting_value:t});const _=n.value.find(h=>h.setting_key===a);_&&(_.setting_value=t,_.updated_at=new Date().toISOString()),S.success("配置更新成功"),await D()}catch(_){console.error("更新配置失败:",_),S.error("更新配置失败")}},X=async()=>{try{A.value=!0;const a=n.value.map(t=>({setting_key:t.setting_key,setting_value:t.setting_value,description:t.description}));await q.batchUpdateConfigs(a),S.success("所有配置保存成功"),await D()}catch(a){console.error("保存配置失败:",a),S.error("保存配置失败")}finally{A.value=!1}},ne=async()=>{try{M.value=!0,await q.resetToDefaults(),S.success("配置已重置为默认值"),await z(),await D()}catch(a){console.error("重置配置失败:",a),S.error("重置配置失败")}finally{M.value=!1}},oe=a=>{I.value=a,O.value=!0},ie=a=>{const t=new Date(a),h=Math.floor((new Date().getTime()-t.getTime())/1e3);return h<60?"刚刚":h<3600?`${Math.floor(h/60)}分钟前`:h<86400?`${Math.floor(h/3600)}小时前`:`${Math.floor(h/86400)}天前`};return fe(async()=>{await z(),await D()}),(a,t)=>{const _=m("a-button"),h=m("a-input-search"),V=m("a-select-option"),ae=m("a-select"),le=m("a-switch"),ce=m("a-tag"),re=m("a-empty");return f(),C("div",qe,[w(" 页面头部 "),i("div",Fe,[t[7]||(t[7]=i("div",{class:"header-title"},[i("h2",null,"系统配置管理")],-1)),i("div",He,[u(_,{type:"primary",onClick:X,loading:A.value},{icon:g(()=>[u(U(me))]),default:g(()=>[t[5]||(t[5]=L(" 保存所有配置 "))]),_:1,__:[5]},8,["loading"]),u(_,{onClick:ne,loading:M.value},{icon:g(()=>[u(U(ge))]),default:g(()=>[t[6]||(t[6]=L(" 重置为默认值 "))]),_:1,__:[6]},8,["loading"])])]),w(" 搜索和过滤区域 "),i("div",Ge,[i("div",Je,[u(h,{value:l.value,"onUpdate:value":t[0]||(t[0]=d=>l.value=d),placeholder:"搜索配置项...",style:{width:"300px"},onSearch:P},null,8,["value"]),u(ae,{value:r.value,"onUpdate:value":t[1]||(t[1]=d=>r.value=d),style:{width:"200px"},onChange:P},{default:g(()=>[u(V,{value:""},{default:g(()=>t[8]||(t[8]=[L("全部配置")])),_:1,__:[8]}),(f(!0),C(H,null,G(N.value,d=>(f(),T(V,{key:d.key,value:d.key},{default:g(()=>[L(b(d.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"]),u(le,{checked:p.value,"onUpdate:checked":t[2]||(t[2]=d=>p.value=d),"checked-children":"显示高级","un-checked-children":"隐藏高级",onChange:P},null,8,["checked"])])]),w(" 紧凑型配置内容区域 "),i("div",We,[w(" 左侧配置项 "),i("div",Qe,[(f(!0),C(H,null,G(s.value,d=>(f(),C("div",{key:d.key,class:"config-category-compact"},[i("div",{class:F(["category-header-compact",{expanded:x.value[d.key]}]),onClick:R=>E(d.key)},[u(U(pe),{class:F({rotated:x.value[d.key]})},null,8,["class"]),i("span",Ze,b(d.name),1),u(ce,{size:"small"},{default:g(()=>{var R;return[L(b(((R=d.configs)==null?void 0:R.length)||0)+" 项",1)]}),_:2},1024)],10,Xe),ve(i("div",Ye,[i("div",Ke,[(f(!0),C(H,null,G(d.configs,R=>(f(),C("div",{key:R.setting_key,class:"config-item-compact"},[u(Ae,{config:R,compact:!0,onUpdate:Q,onTest:oe},null,8,["config"])]))),128))])],512),[[_e,x.value[d.key]]])]))),128))]),w(" 右侧信息面板 "),i("div",et,[i("div",tt,[t[9]||(t[9]=i("h4",null,"最近变更",-1)),i("div",st,[e.value.length===0?(f(),T(re,{key:0,image:!1,description:"暂无变更记录"})):(f(),C("div",nt,[(f(!0),C(H,null,G(e.value.slice(0,5),d=>(f(),C("div",{key:d.id,class:"history-item-compact"},[i("div",ot,b(d.setting_key),1),i("div",it,b(ie(d.changed_at)),1)]))),128))]))])]),i("div",at,[t[12]||(t[12]=i("h4",null,"快速统计",-1)),i("div",lt,[i("div",ct,[i("div",rt,b(c.value),1),t[10]||(t[10]=i("div",{class:"stat-label"},"配置项总数",-1))]),i("div",ut,[i("div",dt,b(v.value.length),1),t[11]||(t[11]=i("div",{class:"stat-label"},"当前显示",-1))])])])])]),w(" 测试模态框 "),u(Re,{visible:O.value,config:I.value,"onUpdate:visible":t[3]||(t[3]=d=>O.value=d),onClose:t[4]||(t[4]=d=>O.value=!1)},null,8,["visible","config"])])}}});const gt=W(ft,[["__scopeId","data-v-e7e76d77"],["__file","D:/Code/OnlyOffice/frontend/src/pages/system/components/SystemConfig.vue"]]),pt=J({__name:"index",setup(o){return(n,e)=>(f(),T(gt))}}),yt=W(pt,[["__file","D:/Code/OnlyOffice/frontend/src/pages/system/index.vue"]]);export{yt as default};
