import { config } from 'dotenv';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * 统一环境变量配置管理
 * 
 * 功能特点：
 * 1. 自动加载根目录的 .env 文件
 * 2. 提供类型安全的配置访问
 * 3. 支持默认值和验证
 * 4. 统一的配置管理接口
 * 
 * @class EnvConfig
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0
 */

// 计算项目根目录路径 (backend的上一级目录)
const PROJECT_ROOT = join(__dirname, '../../../');  // 编译后的代码在 dist 目录中，需要额外一层
const ENV_FILE_PATH = join(PROJECT_ROOT, '.env');

// 加载根目录的 .env 文件
if (existsSync(ENV_FILE_PATH)) {
  const result = config({ path: ENV_FILE_PATH });
  if (result.error) {
    console.error(`[EnvConfig] 加载环境变量文件失败: ${ENV_FILE_PATH}`, result.error);
  } else {
    console.log(`[EnvConfig] ✅ 成功加载环境变量: ${ENV_FILE_PATH}`);
  }
} else {
  console.warn(`[EnvConfig] ⚠️ 环境变量文件不存在: ${ENV_FILE_PATH}`);
}

/**
 * 环境变量配置接口定义
 */
export interface AppConfig {
  // 应用基础配置
  node_env: string;
  port: number;
  frontend_port: number;
  
  // 数据库配置
  database: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };
  
  // JWT认证配置
  jwt: {
    secret: string;
    expiresIn: string;
  };
  
  // CORS配置
  cors: {
    origin: string;
    allowedOrigins: string[];
  };
  
  // OnlyOffice配置
  onlyoffice: {
    serverUrl: string;
    documentServerUrl: string;
    documentPort: number;
    secretKey: string;
  };
  
  // FileNet配置
  filenet: {
    host: string;
    port: number;
    username: string;
    password: string;
    defaultFolder: string;
    defaultDocClass: string;
    defaultSourceType: string;
    defaultBizTag: string;
  };
  
  // 文件存储配置
  storage: {
    uploadPath: string;
    tmpPath: string;
    maxFileSize: string;
    allowedFileTypes: string[];
  };
  
  // 缓存配置
  cache: {
    type: string;
    ttl: number;
    maxSize: number;
  };
  
  // Redis配置
  redis: {
    host: string;
    port: number;
    password: string;
    db: number;
  };
  
  // 日志配置
  logging: {
    level: string;
    dir: string;
    maxSize: string;
    maxFiles: number;
  };
  
  // 性能监控配置
  monitoring: {
    enabled: boolean;
    slowQueryThreshold: number;
    requestTimeout: number;
  };
  
  // 安全配置
  security: {
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
    enableSecurityHeaders: boolean;
  };
  
  // 开发配置
  development: {
    hotReload: boolean;
    debugMode: boolean;
    apiDocsEnabled: boolean;
    swaggerEndpoint: string;
  };
  
  // 服务器网络配置
  server: {
    host: string;
    callbackUrl: string;
  };
}

/**
 * 获取环境变量值，支持默认值
 */
function getEnvValue(key: string, defaultValue?: string): string {
  return process.env[key] || defaultValue || '';
}

/**
 * 获取数字类型的环境变量
 */
function getEnvNumber(key: string, defaultValue: number): number {
  const value = process.env[key];
  return value ? parseInt(value, 10) : defaultValue;
}

/**
 * 获取布尔类型的环境变量
 */
function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

/**
 * 解析数组类型的环境变量 (逗号分隔)
 */
function getEnvArray(key: string, defaultValue: string[] = []): string[] {
  const value = process.env[key];
  if (!value) return defaultValue;
  return value.split(',').map(item => item.trim().replace(/^["']|["']$/g, ''));
}

/**
 * 清理密码字符串的引号
 */
function cleanPassword(value: string): string {
  return value.replace(/^["']|["']$/g, '');
}

/**
 * 统一配置对象
 */
export const envConfig: AppConfig = {
  // 应用基础配置
  node_env: getEnvValue('NODE_ENV', 'development'),
  port: getEnvNumber('PORT', 3000),
  frontend_port: getEnvNumber('FRONTEND_PORT', 8080),
  
  // 数据库配置
  database: {
    host: getEnvValue('DB_HOST', 'localhost'),
    port: getEnvNumber('DB_PORT', 3306),
    name: getEnvValue('DB_NAME', 'onlyfile'),
    user: getEnvValue('DB_USER', 'root'),
    password: cleanPassword(getEnvValue('DB_PASSWORD', '')),
  },
  
  // JWT认证配置 - API JWT从环境变量读取（安全考虑）
  jwt: {
    secret: cleanPassword(getEnvValue('JWT_SECRET', 'API-Auth-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV')), // API JWT密钥，与OnlyOffice JWT分离
    expiresIn: getEnvValue('JWT_EXPIRES_IN', '24h'),
  },

  // CORS配置 - 基本回退配置
  cors: {
    origin: getEnvValue('CORS_ORIGIN', 'http://localhost:8080'),
    allowedOrigins: getEnvArray('ALLOWED_ORIGINS', ['http://localhost:3000', 'http://localhost:8080']),
  },

  // OnlyOffice配置 - 基本回退配置，实际配置从数据库读取
  onlyoffice: {
    serverUrl: getEnvValue('ONLYOFFICE_SERVER_URL', 'http://localhost/'),
    documentServerUrl: getEnvValue('ONLYOFFICE_DOCUMENT_SERVER_URL', 'http://localhost/'),
    documentPort: getEnvNumber('ONLYOFFICE_DOCUMENT_PORT', 80),
    secretKey: cleanPassword(getEnvValue('ONLYOFFICE_SECRET_KEY', '')),
  },

  // FileNet配置 - 基本回退配置，实际配置从数据库读取
  filenet: {
    host: getEnvValue('FILENET_HOST', 'localhost'),
    port: getEnvNumber('FILENET_PORT', 8090),
    username: getEnvValue('FILENET_USERNAME', ''),
    password: getEnvValue('FILENET_PASSWORD', ''),
    defaultFolder: getEnvValue('FILENET_DEFAULT_FOLDER', ''),
    defaultDocClass: getEnvValue('FILENET_DEFAULT_DOC_CLASS', 'SimpleDocument'),
    defaultSourceType: getEnvValue('FILENET_DEFAULT_SOURCE_TYPE', 'MaxOffice'),
    defaultBizTag: getEnvValue('FILENET_DEFAULT_BIZ_TAG', 'office_file'),
  },
  
  // 文件存储配置 - 基本回退配置，实际配置从数据库读取
  storage: {
    uploadPath: getEnvValue('UPLOAD_PATH', './uploads'),
    tmpPath: getEnvValue('TMP_PATH', './tmp'),
    maxFileSize: getEnvValue('MAX_FILE_SIZE', '50MB'),
    allowedFileTypes: getEnvArray('ALLOWED_FILE_TYPES', ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt']),
  },

  // 缓存配置 - 基本回退配置，实际配置从数据库读取
  cache: {
    type: getEnvValue('CACHE_TYPE', 'memory'),
    ttl: getEnvNumber('CACHE_TTL', 3600),
    maxSize: getEnvNumber('CACHE_MAX_SIZE', 100),
  },

  // Redis配置 - 基本回退配置，实际配置从数据库读取
  redis: {
    host: getEnvValue('REDIS_HOST', 'localhost'),
    port: getEnvNumber('REDIS_PORT', 6379),
    password: getEnvValue('REDIS_PASSWORD', ''),
    db: getEnvNumber('REDIS_DB', 0),
  },

  // 日志配置 - 基本回退配置，实际配置从数据库读取
  logging: {
    level: getEnvValue('LOG_LEVEL', 'info'),
    dir: getEnvValue('LOG_DIR', './logs'),
    maxSize: getEnvValue('LOG_MAX_SIZE', '10m'),
    maxFiles: getEnvNumber('LOG_MAX_FILES', 7),
  },

  // 性能监控配置 - 基本回退配置，实际配置从数据库读取
  monitoring: {
    enabled: getEnvBoolean('ENABLE_PERFORMANCE_MONITORING', true),
    slowQueryThreshold: getEnvNumber('SLOW_QUERY_THRESHOLD', 1000),
    requestTimeout: getEnvNumber('REQUEST_TIMEOUT', 30000),
  },

  // 安全配置 - 基本回退配置，实际配置从数据库读取
  security: {
    rateLimitWindowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 900000),
    rateLimitMaxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
    enableSecurityHeaders: getEnvBoolean('ENABLE_SECURITY_HEADERS', true),
  },
  
  // 开发配置 - 保持从环境变量读取（开发相关配置）
  development: {
    hotReload: getEnvBoolean('HOT_RELOAD', true),
    debugMode: getEnvBoolean('DEBUG_MODE', true),
    apiDocsEnabled: getEnvBoolean('API_DOCS_ENABLED', true),
    swaggerEndpoint: getEnvValue('SWAGGER_ENDPOINT', '/api-docs'),
  },

  // 服务器网络配置 - 基本回退配置，实际配置从数据库读取
  server: {
    host: getEnvValue('SERVER_HOST', 'localhost'),
    callbackUrl: cleanPassword(getEnvValue('CALLBACK_URL', 'http://localhost:3000/api/editor/callback')),
  },
};

/**
 * 配置验证函数
 */
export function validateConfig(): void {
  const requiredFields = [
    'database.host',
    'database.name',
    'database.user',
    'jwt.secret',
  ];

  const missingFields: string[] = [];

  requiredFields.forEach(field => {
    const value = getNestedValue(envConfig, field);
    if (!value || value === '') {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new Error(`[EnvConfig] 缺少必需的配置项: ${missingFields.join(', ')}`);
  }

  console.log('[EnvConfig] ✅ 配置验证通过');
}

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: unknown, path: string): unknown {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 打印配置信息 (隐藏敏感信息)
 */
export function printConfig(): void {
  const configCopy = JSON.parse(JSON.stringify(envConfig));
  
  // 隐藏敏感信息
  if (configCopy.database?.password) configCopy.database.password = '***隐藏***';
  if (configCopy.jwt?.secret) configCopy.jwt.secret = '***隐藏***';
  if (configCopy.onlyoffice?.secretKey) configCopy.onlyoffice.secretKey = '***隐藏***';
  if (configCopy.filenet?.password) configCopy.filenet.password = '***隐藏***';
  if (configCopy.redis?.password) configCopy.redis.password = '***隐藏***';

  console.log('[EnvConfig] 📋 当前配置:', JSON.stringify(configCopy, null, 2));
}

/**
 * 导出配置验证函数，在应用启动时调用
 */
export function initializeConfig(): void {
  try {
    validateConfig();
    if (envConfig.development.debugMode) {
      printConfig();
    }
    console.log(`[EnvConfig] 🚀 配置初始化完成 - 环境: ${envConfig.node_env}`);
  } catch (error) {
    console.error('[EnvConfig] ❌ 配置初始化失败:', error);
    throw error;
  }
}

// 默认导出配置对象
export default envConfig; 