const mysql = require('mysql2/promise');

async function checkRolePermissions() {
  const connection = await mysql.createConnection({
    host: '*************',
    user: 'onlyfile_user',
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
  });

  const [results] = await connection.execute('SELECT id, name, permissions FROM user_roles');
  console.log('角色权限数据:');
  results.forEach(role => {
    console.log(`ID: ${role.id}, Name: ${role.name}, Permissions: '${role.permissions}'`);
  });

  // 检查admin用户的详细信息
  const [adminUser] = await connection.execute(`
    SELECT u.*, ur.name as role_name, ur.permissions as role_permissions
    FROM users u
    LEFT JOIN user_roles ur ON u.role_id = ur.id
    WHERE u.username = 'admin'
  `);
  
  console.log('\nAdmin用户信息:');
  if (adminUser.length > 0) {
    const user = adminUser[0];
    console.log(`用户ID: ${user.id}`);
    console.log(`用户名: ${user.username}`);
    console.log(`角色: ${user.role_name}`);
    console.log(`角色权限原始值: '${user.role_permissions}'`);
    console.log(`权限类型: ${typeof user.role_permissions}`);
  }

  await connection.end();
}

checkRolePermissions().catch(console.error); 