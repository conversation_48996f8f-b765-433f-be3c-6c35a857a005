/**
 * 迁移脚本：将filenet_documents和filenet_document_versions表的ID从自增整数改为UUID格式
 * 该脚本会执行以下操作：
 * 1. 备份原始表
 * 2. 创建新的表结构（使用UUID作为主键）
 * 3. 迁移数据到新表
 * 4. 重命名表
 */
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const db = require('../services/database');
const config = require('../config');

async function migrateToUUID() {
    console.log('开始迁移filenet_documents和filenet_document_versions表到UUID主键...');

    try {
        // 测试数据库连接
        const connected = await db.testConnection();
        if (!connected) {
            console.error('无法连接到数据库，迁移操作取消');
            process.exit(1);
        }

        // 使用事务包装所有数据库操作
        await db.transaction(async (connection) => {
            // 备份原表
            await backupTables(connection);

            // 重建filenet_documents表
            const idMapping = await migrateFilenetDocuments(connection);

            // 重建filenet_document_versions表
            await migrateFilenetDocumentVersions(connection, idMapping);

            console.log('迁移成功完成！');
        });
    } catch (error) {
        console.error('迁移失败:', error);
        process.exit(1);
    }
}

/**
 * 备份原表
 * @param {Object} connection 数据库连接
 */
async function backupTables(connection) {
    console.log('备份原表...');

    // 检查并备份filenet_documents表
    const docsTableExists = await connection.query("SHOW TABLES LIKE 'filenet_documents'");
    if (docsTableExists[0].length > 0) {
        console.log('备份filenet_documents表...');
        await connection.query('CREATE TABLE IF NOT EXISTS filenet_documents_backup LIKE filenet_documents');
        await connection.query('INSERT INTO filenet_documents_backup SELECT * FROM filenet_documents');
        console.log('filenet_documents表备份完成。');
    } else {
        console.log('filenet_documents表不存在，跳过备份。');
    }

    // 检查并备份filenet_document_versions表
    const versionsTableExists = await connection.query("SHOW TABLES LIKE 'filenet_document_versions'");
    if (versionsTableExists[0].length > 0) {
        console.log('备份filenet_document_versions表...');
        await connection.query('CREATE TABLE IF NOT EXISTS filenet_document_versions_backup LIKE filenet_document_versions');
        await connection.query('INSERT INTO filenet_document_versions_backup SELECT * FROM filenet_document_versions');
        console.log('filenet_document_versions表备份完成。');
    } else {
        console.log('filenet_document_versions表不存在，跳过备份。');
    }
}

/**
 * 迁移filenet_documents表
 * @param {Object} connection 数据库连接
 * @returns {Object} ID映射
 */
async function migrateFilenetDocuments(connection) {
    console.log('迁移filenet_documents表...');

    // 创建新表结构
    await connection.query(`
        CREATE TABLE IF NOT EXISTS filenet_documents_new (
            id VARCHAR(36) PRIMARY KEY,
            fn_doc_id VARCHAR(255) NOT NULL COMMENT 'FileNet Document ID',
            original_name VARCHAR(255) NOT NULL COMMENT 'Original file name',
            file_size BIGINT COMMENT 'File size in bytes',
            mime_type VARCHAR(100) COMMENT 'MIME type of the file',
            extension VARCHAR(20) COMMENT 'File extension',
            version INT DEFAULT 1 COMMENT 'Current document version',
            file_hash VARCHAR(64) COMMENT 'SHA-256 hash of file content',
            created_by VARCHAR(100) COMMENT 'User who created the document',
            last_modified_by VARCHAR(100) COMMENT 'User who last modified the document',
            template_id INT COMMENT 'ID of template if document is based on template',
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of upload to FileNet',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
            is_deleted BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag',
            UNIQUE KEY unique_fn_doc_id (fn_doc_id),
            INDEX idx_original_name_filenet (original_name),
            INDEX idx_file_hash (file_hash),
            INDEX idx_template_id (template_id),
            INDEX idx_is_deleted (is_deleted)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 获取旧表数据
    const [oldRecords] = await connection.query('SELECT * FROM filenet_documents');
    console.log(`从filenet_documents读取了${oldRecords.length}条记录`);

    // 创建旧ID到新UUID的映射
    const idMapping = {};

    // 迁移数据
    for (const record of oldRecords) {
        const newUuid = uuidv4();
        idMapping[record.id] = newUuid;

        // 构建插入字段和值
        const fields = ['id'];
        const placeholders = ['?'];
        const values = [newUuid];

        // 动态添加存在的字段
        for (const [key, value] of Object.entries(record)) {
            if (key !== 'id') {  // 跳过旧的ID字段
                fields.push(key);
                placeholders.push('?');
                values.push(value);
            }
        }

        // 构建SQL语句
        const sql = `
            INSERT INTO filenet_documents_new 
            (${fields.join(', ')}) 
            VALUES (${placeholders.join(', ')})
        `;

        await connection.query(sql, values);
    }

    // 保存ID映射到文件
    fs.writeFileSync(
        path.join(process.cwd(), 'filenet_documents_id_mapping.json'),
        JSON.stringify(idMapping, null, 2)
    );

    console.log('filenet_documents表迁移完成，ID映射已保存到文件。');

    // 重命名表
    await connection.query('DROP TABLE IF EXISTS filenet_documents_old');
    await connection.query('RENAME TABLE filenet_documents TO filenet_documents_old');
    await connection.query('RENAME TABLE filenet_documents_new TO filenet_documents');

    console.log('filenet_documents表重命名完成。');

    return idMapping;
}

/**
 * 迁移filenet_document_versions表
 * @param {Object} connection 数据库连接
 * @param {Object} idMapping ID映射
 */
async function migrateFilenetDocumentVersions(connection, idMapping) {
    console.log('迁移filenet_document_versions表...');

    // 检查表是否存在
    const [versionsTableExists] = await connection.query("SHOW TABLES LIKE 'filenet_document_versions'");
    if (versionsTableExists.length === 0) {
        console.log('filenet_document_versions表不存在，跳过迁移。');
        return;
    }

    // 创建新表结构
    await connection.query(`
        CREATE TABLE IF NOT EXISTS filenet_document_versions_new (
            id VARCHAR(36) PRIMARY KEY,
            doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id',
            fn_doc_id VARCHAR(255) NOT NULL COMMENT 'FileNet Document ID for this version',
            version INT NOT NULL COMMENT 'Version number',
            file_hash VARCHAR(64) COMMENT 'SHA-256 hash of this version',
            modified_by VARCHAR(100) COMMENT 'User who created this version',
            modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When this version was created',
            file_size BIGINT COMMENT 'File size of this version in bytes',
            comment TEXT COMMENT 'Version comment/description',
            UNIQUE KEY unique_doc_version (doc_id, version),
            INDEX idx_version_fn_doc_id (fn_doc_id),
            FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 获取旧表数据
    const [oldRecords] = await connection.query('SELECT * FROM filenet_document_versions');
    console.log(`从filenet_document_versions读取了${oldRecords.length}条记录`);

    // 迁移数据
    for (const record of oldRecords) {
        const newUuid = uuidv4();
        const newDocId = idMapping[record.doc_id];

        if (!newDocId) {
            console.warn(`警告：找不到文档ID ${record.doc_id} 的映射，跳过此版本记录`);
            continue;
        }

        await connection.query(`
            INSERT INTO filenet_document_versions_new
            (id, doc_id, fn_doc_id, version, file_hash, modified_by, modified_at, file_size, comment)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            newUuid,
            newDocId,
            record.fn_doc_id,
            record.version,
            record.file_hash,
            record.modified_by,
            record.modified_at,
            record.file_size,
            record.comment
        ]);
    }

    console.log('filenet_document_versions表数据迁移完成。');

    // 重命名表
    await connection.query('DROP TABLE IF EXISTS filenet_document_versions_old');
    await connection.query('RENAME TABLE filenet_document_versions TO filenet_document_versions_old');
    await connection.query('RENAME TABLE filenet_document_versions_new TO filenet_document_versions');

    console.log('filenet_document_versions表重命名完成。');
}

// 执行迁移
migrateToUUID()
    .then(() => {
        console.log('迁移脚本执行完毕！');
        process.exit(0);
    })
    .catch((error) => {
        console.error('迁移脚本执行失败:', error);
        process.exit(1);
    }); 