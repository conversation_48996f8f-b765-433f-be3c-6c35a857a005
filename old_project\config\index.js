/**
 * 配置加载器
 * 根据环境变量加载不同的配置文件
 */
const defaultConfig = require('./default');
const dbConfig = require('./database');

// 根据环境变量获取当前环境
const env = process.env.NODE_ENV || 'development';

let envConfig = {};

// 尝试加载对应环境的配置文件
try {
    // 只有当环境不是development时才尝试加载特定环境配置
    if (env !== 'development') {
        envConfig = require(`./${env}`);
        console.log(`已加载 ${env} 环境配置`);
    }
} catch (error) {
    console.log(`未找到 ${env} 环境配置文件，使用默认配置`);
}

// 合并配置，环境特定配置优先级更高
const config = {
    ...defaultConfig,
    ...envConfig,
    // 添加数据库配置
    database: dbConfig,
    jwtSecret: process.env.JWT_SECRET || 'your_very_secret_jwt_key', // TODO: 从环境变量读取
};

// FileNet Configuration
config.filenet = {
    host: process.env.FILENET_HOST || '*************', // 正式环境 Host
    port: process.env.FILENET_PORT || 8090,            // 确保这里是 8090
    defaultFolder: process.env.FILENET_DEFAULT_FOLDER || '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}', // 更新为 Postman 中成功的 Folder ID
    defaultDocClass: process.env.FILENET_DEFAULT_DOC_CLASS || 'SimpleDocument',
    defaultSourceType: process.env.FILENET_DEFAULT_SOURCE_TYPE || 'MaxOffice',      // 更新为 Postman 中成功的值
    defaultBizTag: process.env.FILENET_DEFAULT_BIZ_TAG || 'office_file',          // 更新为 Postman 中成功的值
};

// 为了便于调试，输出当前配置信息
if (env === 'development') {
    console.log('当前使用配置:');
    console.log(`- 服务器端口: ${config.server.port}`);
    console.log(`- 服务器地址: ${config.server.host}`);
    console.log(`- 文档服务器地址: ${config.documentServer.url}`);
    console.log(`- 回调URL: ${config.editor.callbackUrl}`);
    console.log(`- 数据库: ${config.database.host}:${config.database.port}/${config.database.database}`);
}

module.exports = config; 