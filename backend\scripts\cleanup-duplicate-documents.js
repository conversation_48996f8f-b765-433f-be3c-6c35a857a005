const mysql = require('mysql2/promise');
const fs = require('fs');

/**
 * 清理重复文档数据脚本
 * 修复由于OnlyOffice回调bug导致的重复文档记录
 * 
 * 清理策略：
 * 1. 对于每个重复的文件名，保留最早创建的记录作为主记录
 * 2. 将重复记录的版本信息合并到版本表中
 * 3. 删除重复的主表记录
 * 4. 更新主记录的版本号为最新版本
 */
async function cleanupDuplicateDocuments(dryRun = true) {
  let connection;
  const backupFile = `backup-before-cleanup-${Date.now()}.sql`;
  
  try {
    // 数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'onlyfile'
    });

    console.log('🧹 开始清理重复文档数据...');
    console.log(`模式: ${dryRun ? '预览模式 (不实际修改数据)' : '实际执行模式'}\n`);

    if (!dryRun) {
      console.log('⚠️  即将进行实际数据修改，建议先备份数据库！');
      console.log('可以使用以下命令备份：');
      console.log('mysqldump -u root -p onlyfile > backup.sql\n');
    }

    // 1. 查找所有重复的文档
    console.log('=== 1. 查找重复文档 ===');
    const [duplicatesByName] = await connection.execute(`
      SELECT 
        original_name,
        COUNT(*) as count,
        GROUP_CONCAT(id ORDER BY created_at) as all_ids,
        GROUP_CONCAT(version ORDER BY created_at) as all_versions,
        GROUP_CONCAT(file_hash ORDER BY created_at) as all_hashes,
        GROUP_CONCAT(created_at ORDER BY created_at) as all_created_times,
        GROUP_CONCAT(fn_doc_id ORDER BY created_at) as all_fn_doc_ids
      FROM filenet_documents 
      WHERE is_deleted = 0
      GROUP BY original_name 
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `);

    console.log(`发现 ${duplicatesByName.length} 个重复的文件名\n`);

    const cleanupOperations = [];
    let totalOperations = 0;

    // 2. 为每个重复文件生成清理计划
    for (const duplicate of duplicatesByName) {
      const ids = duplicate.all_ids.split(',');
      const versions = duplicate.all_versions.split(',').map(v => parseInt(v) || 1);
      const hashes = duplicate.all_hashes.split(',');
      const createdTimes = duplicate.all_created_times.split(',');
      const fnDocIds = duplicate.all_fn_doc_ids.split(',');

      const keepId = ids[0]; // 保留最早的记录
      const removeIds = ids.slice(1); // 删除其余记录
      const maxVersion = Math.max(...versions);

      console.log(`📄 文件: "${duplicate.original_name}"`);
      console.log(`   重复数量: ${duplicate.count}`);
      console.log(`   保留记录: ${keepId} (${createdTimes[0]})`);
      console.log(`   删除记录: ${removeIds.length} 个`);
      console.log(`   最高版本: ${maxVersion}`);

      const operation = {
        fileName: duplicate.original_name,
        keepRecord: {
          id: keepId,
          createdAt: createdTimes[0]
        },
        removeRecords: removeIds.map((id, i) => ({
          id: id,
          version: versions[i + 1],
          hash: hashes[i + 1],
          createdAt: createdTimes[i + 1],
          fnDocId: fnDocIds[i + 1]
        })),
        updateVersion: maxVersion
      };

      cleanupOperations.push(operation);
      totalOperations += removeIds.length;

      // 显示将要删除的记录详情
      removeIds.forEach((removeId, i) => {
        console.log(`     - 删除: ${removeId} (版本${versions[i + 1]}, ${createdTimes[i + 1]})`);
      });
      console.log('');
    }

    console.log(`📊 清理统计:`);
    console.log(`- 需要处理的文件: ${duplicatesByName.length} 个`);
    console.log(`- 需要删除的记录: ${totalOperations} 个`);
    console.log(`- 需要更新的主记录: ${duplicatesByName.length} 个\n`);

    if (dryRun) {
      console.log('🔍 预览模式 - 以上是将要执行的操作');
      console.log('如需实际执行，请使用: node scripts/cleanup-duplicate-documents.js --execute\n');
      
      // 保存清理计划
      fs.writeFileSync(
        'cleanup-plan.json',
        JSON.stringify(cleanupOperations, null, 2),
        'utf8'
      );
      console.log('📄 清理计划已保存到: cleanup-plan.json');
      return { operations: cleanupOperations, executed: false };
    }

    // 3. 实际执行清理操作
    console.log('🚀 开始执行清理操作...\n');
    
    await connection.beginTransaction();
    
    try {
      let processedFiles = 0;
      let deletedRecords = 0;

      for (const operation of cleanupOperations) {
        console.log(`处理文件: "${operation.fileName}"`);

        // 3.1 为要删除的记录创建版本记录（如果不存在）
        for (const removeRecord of operation.removeRecords) {
          // 检查是否已存在版本记录
          const [existingVersion] = await connection.execute(`
            SELECT id FROM filenet_document_versions 
            WHERE doc_id = ? AND version = ?
          `, [operation.keepRecord.id, removeRecord.version]);

          if (existingVersion.length === 0 && removeRecord.hash) {
            // 创建版本记录
            const versionId = require('crypto').randomUUID();
            await connection.execute(`
              INSERT INTO filenet_document_versions 
              (id, doc_id, fn_doc_id, version, file_hash, modified_by, modified_at, file_size, comment)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              versionId,
              operation.keepRecord.id,
              removeRecord.fnDocId,
              removeRecord.version,
              removeRecord.hash,
              'cleanup-system',
              removeRecord.createdAt,
              0, // file_size - 可以后续补充
              `合并自重复记录 ${removeRecord.id}`
            ]);
            console.log(`   ✅ 创建版本记录: 版本${removeRecord.version}`);
          }
        }

        // 3.2 更新主记录的版本号
        await connection.execute(`
          UPDATE filenet_documents 
          SET version = ?, updated_at = NOW()
          WHERE id = ?
        `, [operation.updateVersion, operation.keepRecord.id]);
        console.log(`   ✅ 更新主记录版本号为: ${operation.updateVersion}`);

        // 3.3 删除重复记录
        for (const removeRecord of operation.removeRecords) {
          await connection.execute(`
            UPDATE filenet_documents 
            SET is_deleted = 1, updated_at = NOW()
            WHERE id = ?
          `, [removeRecord.id]);
          console.log(`   🗑️  标记删除记录: ${removeRecord.id}`);
          deletedRecords++;
        }

        processedFiles++;
        console.log(`   ✅ 完成处理 (${processedFiles}/${cleanupOperations.length})\n`);
      }

      // 提交事务
      await connection.commit();
      
      console.log('🎉 清理操作完成！');
      console.log(`📊 执行结果:`);
      console.log(`- 处理文件数: ${processedFiles}`);
      console.log(`- 删除记录数: ${deletedRecords}`);
      console.log(`- 更新主记录数: ${processedFiles}`);

      // 保存执行结果
      const result = {
        timestamp: new Date().toISOString(),
        executed: true,
        processedFiles,
        deletedRecords,
        operations: cleanupOperations
      };

      fs.writeFileSync(
        `cleanup-result-${Date.now()}.json`,
        JSON.stringify(result, null, 2),
        'utf8'
      );

      return result;

    } catch (error) {
      // 回滚事务
      await connection.rollback();
      console.error('❌ 清理过程中发生错误，已回滚事务:', error);
      throw error;
    }

  } catch (error) {
    console.error('❌ 清理失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行清理脚本
if (require.main === module) {
  const isDryRun = !process.argv.includes('--execute');
  
  if (!isDryRun) {
    console.log('⚠️  您选择了实际执行模式！');
    console.log('这将实际修改数据库数据，请确保已经备份！');
    console.log('按 Ctrl+C 取消，或等待5秒后继续...\n');
    
    setTimeout(() => {
      cleanupDuplicateDocuments(false)
        .then((result) => {
          console.log('\n✅ 重复文档数据清理完成');
          if (result.executed) {
            console.log('数据已实际修改，请验证结果');
          }
        })
        .catch((error) => {
          console.error('❌ 清理失败:', error);
          process.exit(1);
        });
    }, 5000);
  } else {
    cleanupDuplicateDocuments(true)
      .then((result) => {
        console.log('\n✅ 重复文档数据分析完成');
        console.log('如需实际执行，请添加 --execute 参数');
      })
      .catch((error) => {
        console.error('❌ 分析失败:', error);
        process.exit(1);
      });
  }
}

module.exports = { cleanupDuplicateDocuments }; 