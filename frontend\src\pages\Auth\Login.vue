<template>
  <div class="login-container">
    <div class="login-bg">
      <!-- 背景动画元素 -->
      <div class="bg-animation">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>

      <!-- 登录表单区域 -->
      <div class="login-form-wrapper">
        <div class="login-form">
          <div class="login-header">
            <div class="logo">🏢</div>
            <h2>用户登录</h2>
            <p>欢迎使用OnlyOffice集成系统</p>
          </div>

          <a-form
            :model="loginForm"
            :rules="rules"
            layout="vertical"
            size="large"
            @finish="handleLogin"
            @finish-failed="handleLoginFailed"
          >
            <a-form-item name="username" label="用户名">
              <a-input
                v-model:value="loginForm.username"
                placeholder="请输入用户名"
                :prefix="h(UserOutlined)"
              />
            </a-form-item>

            <a-form-item name="password" label="密码">
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="请输入密码"
                :prefix="h(LockOutlined)"
              />
            </a-form-item>

            <a-form-item>
              <div class="login-options">
                <a-checkbox v-model:checked="loginForm.remember"> 记住我 </a-checkbox>
                <a href="#" class="forgot-password">忘记密码？</a>
              </div>
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                :loading="authStore.loading"
                block
                size="large"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>

          <div class="login-footer">
            <p>© {{ currentYear }} OnlyOffice集成系统. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { ValidateErrorEntity } from '@/types/form.types'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loginForm = ref({
  username: 'admin',
  password: 'password123',
  remember: false,
})

// 当前年份
const currentYear = new Date().getFullYear()

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' },
  ],
}

// 登录处理
const handleLogin = async () => {
  try {
    await authStore.login({
      username: loginForm.value.username,
      password: loginForm.value.password,
    })

    // 登录成功后跳转到首页
    router.push('/')
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 登录失败处理
const handleLoginFailed = (errorInfo: ValidateErrorEntity) => {
  console.log('表单验证失败:', errorInfo)
}

// 检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-bg {
  position: relative;
  width: 100%;
  max-width: 400px;
  z-index: 1;
}

.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-form-wrapper {
  position: relative;
  z-index: 2;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  font-size: 48px;
  margin-bottom: 16px;
}

.login-header h2 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  color: #999;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-form {
    padding: 24px;
  }

  .logo {
    font-size: 36px;
  }
}
</style>
