<template>
  <div class="editor-container">
    <!-- 加载状态 -->
    <div v-if="!isReady" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载编辑器...</p>
      </div>
    </div>

    <!-- OnlyOffice编辑器容器 -->
    <div
      id="editor-container"
      class="editor-iframe-container"
      :class="{ 'editor-ready': isReady }"
    ></div>

    <!-- 错误状态 -->
    <div v-if="hasError" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3 class="error-title">编辑器加载失败</h3>
        <p class="error-message">{{ errorMessage }}</p>
        <button class="retry-button" @click="handleRetry">重试</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import type { OnlyOfficeConfig } from '@/types/onlyoffice.types'

/**
 * 编辑器容器组件
 *
 * @description 提供OnlyOffice编辑器的容器和状态管理
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

interface Props {
  /** 编辑器是否就绪 */
  isReady: boolean
  /** 编辑器配置 */
  config: OnlyOfficeConfig | null
}

interface Emits {
  /** 编辑器就绪事件 */
  (e: 'editor-ready'): void
  /** 文档状态变更事件 */
  (e: 'document-state-change', event: unknown): void
  /** 保存事件 */
  (e: 'save', event: unknown): void
  /** 错误事件 */
  (e: 'error', event: unknown): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 错误状态
const hasError = ref(false)
const errorMessage = ref('')

/**
 * 处理重试
 */
const handleRetry = (): void => {
  hasError.value = false
  errorMessage.value = ''
  emit('editor-ready')
}

/**
 * 设置错误状态
 */
const setError = (message: string): void => {
  hasError.value = true
  errorMessage.value = message
}

/**
 * 处理窗口大小变化
 */
const handleResize = (): void => {
  if (props.isReady) {
    nextTick(() => {
      // 强制OnlyOffice编辑器重新计算大小
      const editorContainer = document.getElementById('editor-container')
      if (editorContainer) {
        const iframe = editorContainer.querySelector('iframe')
        if (iframe) {
          // 触发编辑器重新调整大小
          iframe.style.width = '100%'
          iframe.style.height = '100%'
        }
      }
    })
  }
}

// 监听配置变化
watch(
  () => props.config,
  newConfig => {
    if (newConfig) {
      hasError.value = false
      errorMessage.value = ''
    }
  }
)

// 监听就绪状态变化
watch(
  () => props.isReady,
  newReady => {
    if (newReady) {
      hasError.value = false
      // 编辑器就绪后立即调整大小
      nextTick(() => {
        handleResize()
      })
    }
  }
)

onMounted(() => {
  console.log('编辑器容器已挂载')

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  console.log('编辑器容器已卸载')

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})

// 暴露方法给父组件
defineExpose({
  setError,
  handleResize,
})
</script>

<style scoped>
.editor-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 0; /* 确保flex容器能正确缩放 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.editor-iframe-container {
  width: 100%;
  height: 100%;
  flex: 1;
  min-height: 0; /* 确保能缩放到合适大小 */
  opacity: 0;
  transition: opacity 0.3s ease;
  overflow: hidden;
}

.editor-iframe-container.editor-ready {
  opacity: 1;
}

/* 确保OnlyOffice iframe不产生滚动条 */
.editor-iframe-container :deep(iframe) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  overflow: hidden !important;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-content {
  text-align: center;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.error-content {
  text-align: center;
  max-width: 400px;
  padding: 32px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  color: #dc3545;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px;
}

.error-message {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 24px;
}

.retry-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #0056b3;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
