"use strict";
exports.id = 0;
exports.ids = null;
exports.modules = {

/***/ 79:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EditorService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EditorService = void 0;
const common_1 = __webpack_require__(5);
const config_1 = __webpack_require__(8);
const onlyoffice_jwt_service_1 = __webpack_require__(64);
const hybrid_config_service_1 = __webpack_require__(69);
const document_service_1 = __webpack_require__(49);
const config_template_service_1 = __webpack_require__(62);
let EditorService = EditorService_1 = class EditorService {
    constructor(configService, onlyOfficeJwtService, hybridConfigService, documentService, configTemplateService) {
        this.configService = configService;
        this.onlyOfficeJwtService = onlyOfficeJwtService;
        this.hybridConfigService = hybridConfigService;
        this.documentService = documentService;
        this.configTemplateService = configTemplateService;
        this.logger = new common_1.Logger(EditorService_1.name);
    }
    async getDocumentById(fileId) {
        return await this.documentService.getDocumentById(fileId);
    }
    isValidUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }
    getDocumentType(fileExtension) {
        const ext = fileExtension.toLowerCase().replace('.', '');
        if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) {
            return 'word';
        }
        else if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) {
            return 'cell';
        }
        else if (['ppt', 'pptx', 'odp'].includes(ext)) {
            return 'slide';
        }
        else if (['pdf'].includes(ext)) {
            return 'pdf';
        }
        return 'word';
    }
    async getConfigFromTemplate(templateId) {
        try {
            console.log('🔄 [EditorService] getConfigFromTemplate 开始获取配置模板');
            console.log('🔧 [EditorService] 请求的模板ID:', templateId);
            if (templateId) {
                console.log('✅ [EditorService] 使用指定的配置模板:', templateId);
                try {
                    const template = await this.configTemplateService.getTemplateConfig(templateId);
                    console.log('📥 [EditorService] 成功获取到指定配置模板:');
                    console.log('  - 模板ID:', templateId);
                    console.log('  - 模板内容:', JSON.stringify(template, null, 2));
                    return template;
                }
                catch (error) {
                    console.error('❌ [EditorService] 获取指定配置模板失败:', templateId, error);
                    console.log('⚠️ [EditorService] 指定模板获取失败，尝试获取默认模板');
                }
            }
            else {
                console.log('🔍 [EditorService] 未指定模板ID，查找默认模板');
            }
            const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
            if (defaultTemplate) {
                console.log('✅ [EditorService] 找到默认模板:', defaultTemplate.id, defaultTemplate.name);
                const template = await this.configTemplateService.getTemplateConfig(defaultTemplate.id);
                console.log('📥 [EditorService] 成功获取到默认配置模板:');
                console.log('  - 默认模板ID:', defaultTemplate.id);
                console.log('  - 默认模板内容:', JSON.stringify(template, null, 2));
                return template;
            }
            else {
                console.log('⚠️ [EditorService] 未找到默认模板，使用系统默认配置');
            }
            const defaultConfig = await this.getDefaultConfig();
            console.log('📥 [EditorService] 使用系统默认配置:');
            console.log('  - 系统默认配置:', JSON.stringify(defaultConfig, null, 2));
            return defaultConfig;
        }
        catch (error) {
            console.error('❌ [EditorService] 从配置模板获取配置失败:', error);
            this.logger.error('从配置模板获取配置失败:', error);
            const defaultConfig = await this.getDefaultConfig();
            console.log('📥 [EditorService] 错误回退，使用系统默认配置:');
            console.log('  - 系统默认配置:', JSON.stringify(defaultConfig, null, 2));
            return defaultConfig;
        }
    }
    async getDefaultConfig() {
        try {
            console.log('🔄 [EditorService] getDefaultConfig 开始获取默认配置');
            const defaultTemplate = await this.configTemplateService.getDefaultTemplate();
            if (defaultTemplate) {
                console.log('✅ [EditorService] 找到默认模板:', defaultTemplate.id, defaultTemplate.name);
                const template = await this.configTemplateService.getTemplateConfig(defaultTemplate.id);
                console.log('📥 [EditorService] 成功获取到默认配置模板:');
                console.log('  - 默认模板ID:', defaultTemplate.id);
                console.log('  - 默认模板内容:', JSON.stringify(template, null, 2));
                return template;
            }
            else {
                console.error('❌ [EditorService] 数据库中未找到默认配置模板');
                throw new Error('数据库中未找到默认配置模板，请先在配置管理中创建默认模板');
            }
        }
        catch (error) {
            console.error('❌ [EditorService] 从数据库获取默认配置失败:', error);
            this.logger.error('从数据库获取默认配置失败:', error);
            throw new common_1.InternalServerErrorException('无法获取编辑器配置，请检查配置模板设置');
        }
    }
    async getEditorConfig(fileId, queryParams = {}) {
        console.log('🔄 [EditorService] getEditorConfig 开始生成编辑器配置');
        console.log('📋 [EditorService] 文档ID/会话ID:', fileId);
        console.log('🔧 [EditorService] 原始查询参数:', queryParams);
        const templateConfig = await this.getConfigFromTemplate(queryParams.template);
        console.log('📋 [EditorService] 使用的配置模板:', JSON.stringify(templateConfig, null, 2));
        try {
            if (!this.isValidUUID(fileId)) {
                throw new Error(`无效的文档ID/会话ID格式: ${fileId}`);
            }
            const isTemplateSession = queryParams.templateId;
            let document;
            if (isTemplateSession) {
                console.log('🎯 [EditorService] 检测到模板会话模式, sessionId:', fileId);
                console.log('🎯 [EditorService] 模板ID:', queryParams.templateId);
                console.log('🎯 [EditorService] 文档名称:', queryParams.documentName);
                document = {
                    id: fileId,
                    name: queryParams.documentName || '基于模板的新文档.docx',
                    extension: 'docx',
                    type: 'Word文档',
                    size: 0,
                    templateId: queryParams.templateId
                };
            }
            else {
                console.log('🔍 [EditorService] 普通文档模式，开始获取文档信息...');
                document = await this.getDocumentById(fileId);
            }
            console.log('📄 [EditorService] 文档信息:', {
                id: document.id,
                name: document.name,
                extension: document.extension,
                type: document.type
            });
            const fileKey = `${fileId}-${Date.now()}`;
            console.log('🔑 [EditorService] 生成文件键值:', fileKey);
            let fileUrl;
            if (isTemplateSession) {
                fileUrl = `${this.getServerBaseUrl()}/api/document-templates/${queryParams.templateId}/download`;
                console.log('🔗 [EditorService] 构建模板文件URL:', fileUrl);
            }
            else {
                fileUrl = `${this.getServerBaseUrl()}/api/documents/${fileId}`;
                console.log('🔗 [EditorService] 构建文档文件URL:', fileUrl);
            }
            const documentType = this.getDocumentType(document.extension || document.type);
            console.log('📑 [EditorService] 确定文档类型:', documentType);
            const templatePermissions = templateConfig.config?.permissions || {};
            const templateConfigStates = templateConfig.configStates?.permissions || {};
            console.log('🔒 [EditorService] 模板中的权限配置:', JSON.stringify(templatePermissions, null, 2));
            console.log('🔒 [EditorService] 模板中的权限状态:', JSON.stringify(templateConfigStates, null, 2));
            const permissions = {
                edit: (templateConfigStates.edit?.enabled !== false ? templatePermissions.edit !== false : false) && !queryParams.readonly && document.extension !== 'pdf',
                download: templateConfigStates.download?.enabled !== false ? templatePermissions.download !== false : false,
                review: templateConfigStates.review?.enabled !== false ? templatePermissions.review !== false : false,
                comment: (templateConfigStates.comment?.enabled !== false ? templatePermissions.comment !== false : false) && !queryParams.hideComments,
                fillForms: templateConfigStates.fillForms?.enabled !== false ? templatePermissions.fillForms !== false : false,
                modifyFilter: templateConfigStates.modifyFilter?.enabled !== false ? templatePermissions.modifyFilter !== false : false,
                modifyContentControl: templateConfigStates.modifyContentControl?.enabled !== false ? templatePermissions.modifyContentControl !== false : false,
            };
            console.log('🔒 [EditorService] 构建的权限配置:', JSON.stringify(permissions, null, 2));
            const templateCustomization = templateConfig.config?.customization || {};
            const templateCustomizationStates = templateConfig.configStates?.customization || {};
            console.log('🎨 [EditorService] 模板中的自定义配置:', JSON.stringify(templateCustomization, null, 2));
            console.log('🎨 [EditorService] 模板中的自定义配置状态:', JSON.stringify(templateCustomizationStates, null, 2));
            const customization = {
                chat: (templateConfigStates.chat?.enabled !== false ? templatePermissions.chat === true : false) && !queryParams.hideChat,
                comments: (templateConfigStates.comment?.enabled !== false ? templatePermissions.comment !== false : false) && !queryParams.hideComments,
                help: templateCustomizationStates.help?.enabled !== false ? templateCustomization.help !== false : false,
                about: templateCustomizationStates.about?.enabled !== false ? templateCustomization.about !== false : false,
                feedback: templateCustomizationStates.feedback?.enabled !== false ? templateCustomization.feedback === true : false,
                forcesave: templateCustomizationStates.forcesave?.enabled !== false ? templateCustomization.forcesave !== false : false,
                review: templateConfigStates.review?.enabled !== false ? templatePermissions.review !== false : false,
                toolbarNoTabs: templateCustomizationStates.toolbarNoTabs?.enabled !== false ? templateCustomization.toolbarNoTabs === true : false,
                toolbarHideFileName: templateCustomizationStates.toolbarHideFileName?.enabled !== false ? templateCustomization.toolbarHideFileName === true : false,
            };
            console.log('🎨 [EditorService] 构建的自定义配置:', JSON.stringify(customization, null, 2));
            const templateUser = templateConfig.config?.user || {};
            const user = {
                id: queryParams.userId || templateUser.id || 'user-1',
                name: queryParams.userName || templateUser.name || '默认用户',
            };
            console.log('👤 [EditorService] 构建的用户信息:', JSON.stringify(user, null, 2));
            const templateCoEditing = templateConfig.config?.coEditing || {};
            const coEditing = {
                mode: (templateCoEditing.mode === 'strict' ? 'strict' : 'fast'),
                change: templateCoEditing.change !== false,
            };
            console.log('👥 [EditorService] 构建的协同编辑配置:', JSON.stringify(coEditing, null, 2));
            const editorConfig = {
                document: {
                    fileType: (document.extension || document.type).replace('.', ''),
                    key: fileKey,
                    title: document.name || document.originalName,
                    url: fileUrl,
                    permissions,
                    dbId: fileId,
                },
                documentType,
                editorConfig: {
                    callbackUrl: `${this.getServerBaseUrl()}/api/editor/callback`,
                    lang: 'zh',
                    mode: permissions.edit ? 'edit' : 'view',
                    customization,
                    user,
                    coEditing,
                },
                apiUrl: `${this.getOnlyOfficeBaseUrl().replace(/\/$/, '')}/web-apps/apps/api/documents/api.js`,
            };
            console.log('📋 [EditorService] 构建完成的完整编辑器配置:');
            console.log('  📄 文档信息:', JSON.stringify(editorConfig.document, null, 2));
            console.log('  🔧 编辑器配置:', JSON.stringify(editorConfig.editorConfig, null, 2));
            console.log('  🌐 API URL:', editorConfig.apiUrl);
            console.log('  📑 文档类型:', editorConfig.documentType);
            const signedConfig = await this.onlyOfficeJwtService.signConfig(editorConfig);
            console.log('🔐 [EditorService] JWT签名完成:');
            console.log('  ✅ 签名成功');
            console.log('  🎯 最终发送给OnlyOffice的配置:', JSON.stringify(signedConfig, null, 2));
            this.logger.log('生成编辑器配置成功并签名JWT: ' + (document.name || document.originalName));
            return signedConfig;
        }
        catch (error) {
            this.logger.error(`获取编辑器配置失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('获取编辑器配置时发生内部错误');
        }
    }
    async handleCallback(callbackData, _fileId) {
        this.logger.log(`处理OnlyOffice回调: status=${callbackData.status}, key=${callbackData.key}`);
        const response = { error: 0, message: '回调处理成功' };
        setTimeout(async () => {
            try {
                const result = await this.documentService.handleCallback(callbackData);
                this.logger.log(`后台文档保存处理结果: ${result ? '成功' : '失败'}`);
            }
            catch (error) {
                this.logger.error(`后台处理回调失败: ${error.message}`, error.stack);
            }
        }, 10);
        return response;
    }
    async checkSaveStatus(fileId) {
        this.logger.log(`检查保存状态: ${fileId}`);
        try {
            const document = await this.getDocumentById(fileId);
            if (!document) {
                throw new common_1.NotFoundException('找不到对应的文档');
            }
            return {
                success: true,
                status: 'saved',
                message: '文档状态正常',
                fileName: document.name || document.originalName,
                version: document.version || 1,
                lastModifiedDb: document.lastModified || new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`检查保存状态失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('检查保存状态时发生内部错误');
        }
    }
    async forceSave(fileId, forceSaveData) {
        this.logger.log(`强制保存文档: ${fileId}`);
        console.log('🔄 [EditorService] 开始强制保存流程');
        console.log('📋 [EditorService] 文档ID:', fileId);
        console.log('🔧 [EditorService] 强制保存参数:', JSON.stringify(forceSaveData, null, 2));
        try {
            const document = await this.getDocumentById(fileId);
            if (!document) {
                throw new common_1.NotFoundException('找不到对应的文档');
            }
            console.log('📄 [EditorService] 找到文档:', document.name || document.originalName);
            if (!forceSaveData.documentKey) {
                throw new common_1.BadRequestException('缺少必需的文档密钥(documentKey)参数');
            }
            const documentKey = forceSaveData.documentKey;
            console.log('🔑 [EditorService] 使用的文档key:', documentKey);
            const commandData = {
                c: 'forcesave',
                key: documentKey,
                userdata: JSON.stringify({
                    fileId: fileId,
                    userId: 'user-1',
                    timestamp: new Date().toISOString(),
                    force: forceSaveData.force || true,
                    requestType: 'manual'
                })
            };
            console.log('📤 [EditorService] 准备发送OnlyOffice强制保存命令:', JSON.stringify(commandData, null, 2));
            const onlyOfficeBaseUrl = this.getOnlyOfficeBaseUrl().replace(/\/$/, '');
            const commandUrl = `${onlyOfficeBaseUrl}/coauthoring/CommandService.ashx`;
            console.log('🌐 [EditorService] OnlyOffice Command Service URL:', commandUrl);
            let requestData = commandData;
            if (this.onlyOfficeJwtService) {
                try {
                    const jwtToken = await this.onlyOfficeJwtService.generateToken(commandData);
                    requestData = {
                        ...commandData,
                        token: jwtToken
                    };
                    console.log('🔐 [EditorService] Command请求已签名');
                }
                catch (error) {
                    console.error('❌ [EditorService] Command请求签名失败:', error);
                }
            }
            const response = await fetch(commandUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify(requestData),
            });
            console.log('📥 [EditorService] OnlyOffice响应状态:', response.status);
            if (!response.ok) {
                const responseText = await response.text();
                console.error('❌ [EditorService] OnlyOffice Command Service错误响应:', responseText);
                throw new Error(`OnlyOffice Command Service 返回错误: ${response.status} ${response.statusText}, 响应: ${responseText}`);
            }
            const responseData = await response.json();
            console.log('📥 [EditorService] OnlyOffice响应数据:', JSON.stringify(responseData, null, 2));
            if (responseData.error === 0) {
                console.log('✅ [EditorService] 强制保存命令发送成功');
                return {
                    success: true,
                    message: '强制保存命令已成功发送至OnlyOffice服务器，文档将在处理完成后自动保存到FileNet',
                };
            }
            else {
                console.error('❌ [EditorService] OnlyOffice强制保存失败:', responseData);
                let errorMessage = '强制保存失败';
                let shouldReturnSuccess = false;
                switch (responseData.error) {
                    case 1:
                        errorMessage = '文档密钥错误或文档不存在';
                        break;
                    case 2:
                        errorMessage = '回调URL错误';
                        break;
                    case 3:
                        errorMessage = '内部服务器错误';
                        break;
                    case 4:
                        errorMessage = 'OnlyOffice认为文档自上次保存以来没有变更，将等待自动保存';
                        shouldReturnSuccess = true;
                        console.log('⚠️ [EditorService] 错误码4：文档可能已经是最新状态，或OnlyOffice缓存问题');
                        break;
                    case 5:
                        errorMessage = '强制保存命令格式错误';
                        break;
                    case 6:
                        errorMessage = 'JWT令牌无效';
                        break;
                    default:
                        errorMessage = `OnlyOffice服务器返回错误码: ${responseData.error}`;
                }
                if (shouldReturnSuccess) {
                    return {
                        success: true,
                        message: `${errorMessage}。如果您确实进行了修改，请稍等片刻让OnlyOffice检测到变更，或关闭编辑器触发自动保存。`,
                    };
                }
                else {
                    throw new Error(errorMessage);
                }
            }
        }
        catch (error) {
            console.error('❌ [EditorService] 强制保存失败:', error);
            this.logger.error(`强制保存失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            if (error instanceof Error && error.message.includes('OnlyOffice服务器返回错误码')) {
                throw new common_1.BadRequestException(error.message);
            }
            throw new common_1.InternalServerErrorException(`强制保存时发生内部错误: ${error.message}`);
        }
    }
    async getServerBaseUrl() {
        try {
            const appConfig = await this.hybridConfigService.getAppConfig();
            const host = appConfig.server.host;
            const port = appConfig.port;
            return `http://${host}:${port}`;
        }
        catch (error) {
            this.logger.warn('获取服务器配置失败，使用环境变量配置', error);
            const host = process.env.SERVER_HOST || 'localhost';
            const port = parseInt(process.env.PORT || '3000', 10);
            return `http://${host}:${port}`;
        }
    }
    async getOnlyOfficeBaseUrl() {
        try {
            const appConfig = await this.hybridConfigService.getAppConfig();
            return appConfig.onlyoffice.documentServerUrl;
        }
        catch (error) {
            this.logger.warn('获取OnlyOffice配置失败，使用环境变量配置', error);
            return process.env.ONLYOFFICE_DOCUMENT_SERVER_URL || 'http://localhost';
        }
    }
};
exports.EditorService = EditorService;
exports.EditorService = EditorService = EditorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof onlyoffice_jwt_service_1.OnlyOfficeJwtService !== "undefined" && onlyoffice_jwt_service_1.OnlyOfficeJwtService) === "function" ? _b : Object, typeof (_c = typeof hybrid_config_service_1.HybridConfigService !== "undefined" && hybrid_config_service_1.HybridConfigService) === "function" ? _c : Object, typeof (_d = typeof document_service_1.DocumentService !== "undefined" && document_service_1.DocumentService) === "function" ? _d : Object, typeof (_e = typeof config_template_service_1.ConfigTemplateService !== "undefined" && config_template_service_1.ConfigTemplateService) === "function" ? _e : Object])
], EditorService);


/***/ })

};
exports.runtime =
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("947e133c647efba3abea")
/******/ })();
/******/ 
/******/ }
;