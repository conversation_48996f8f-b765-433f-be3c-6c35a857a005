const jwt = require('jsonwebtoken');

// 使用和NestJS相同的JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_ISSUER = process.env.JWT_ISSUER || 'onlyoffice-api';
const JWT_AUDIENCE = process.env.JWT_AUDIENCE || 'onlyoffice-users';

// 创建payload（24小时有效期）
const payload = {
  sub: 'user-admin',      // 用户ID
  username: 'admin',      // 用户名
  role: 'super_admin',    // 角色
  type: 'access',         // token类型
  iat: Math.floor(Date.now() / 1000),           // 签发时间
  exp: Math.floor(Date.now() / 1000) + (24 * 3600), // 24小时后过期
  iss: JWT_ISSUER,        // 签发者
  aud: JWT_AUDIENCE       // 受众
};

// 生成token
const token = jwt.sign(payload, JWT_SECRET);

console.log('🎫 Swagger测试专用JWT令牌（24小时有效期）:');
console.log('');
console.log(token);
console.log('');
console.log('📋 令牌信息:');
console.log('- 用户ID:', payload.sub);
console.log('- 用户名:', payload.username); 
console.log('- 角色:', payload.role);
console.log('- 有效期:', new Date(payload.exp * 1000).toLocaleString());
console.log('');
console.log('📖 在Swagger中使用步骤:');
console.log('1. 打开浏览器访问: http://localhost:3000/api-docs');
console.log('2. 点击右上角的 "Authorize" 按钮');
console.log('3. 在弹出的对话框中粘贴上面的令牌（不要加Bearer前缀）');
console.log('4. 点击 "Authorize" 按钮完成认证');
console.log('5. 关闭对话框，现在可以测试需要认证的API了');
console.log('');
console.log('⚠️  注意事项:');
console.log('- 令牌有效期为24小时，过期后需要重新生成');
console.log('- 如果API返回401错误，请检查令牌是否正确粘贴');
console.log('- 用户管理API可能需要特定权限，请使用超级管理员令牌'); 