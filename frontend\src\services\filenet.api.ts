import { ApiService } from './api'

export interface FileNetDocument {
  id: string
  title: string
  filename: string
  size: number
  mimeType: string
  version: string
  createdBy: string
  createdAt: string
  updatedAt: string
  filenetId: string
  metadata?: Record<string, unknown>
}

export interface UploadToFileNetDto {
  title: string
  description?: string
  category?: string
  metadata?: Record<string, unknown>
  file: File
}

export interface FileNetSearchParams {
  keyword?: string
  category?: string
  createdBy?: string
  startDate?: string
  endDate?: string
  limit?: number
  offset?: number
}

export interface FileNetVersionInfo {
  version: string
  createdAt: string
  createdBy: string
  fileSize: number
  comment?: string
}

/**
 * FileNet集成API服务
 */
export class FileNetApiService {
  /**
   * 上传文件到FileNet
   */
  static async uploadToFileNet(data: UploadToFileNetDto): Promise<FileNetDocument> {
    const formData = new FormData()
    formData.append('title', data.title)
    formData.append('file', data.file)

    if (data.description) {
      formData.append('description', data.description)
    }

    if (data.category) {
      formData.append('category', data.category)
    }

    if (data.metadata) {
      formData.append('metadata', JSON.stringify(data.metadata))
    }

    return ApiService.post<FileNetDocument>('/filenet/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 从FileNet下载文件
   */
  static async downloadFromFileNet(id: string, filename?: string): Promise<void> {
    return ApiService.download(`/filenet/${id}/download`, filename)
  }

  /**
   * 获取FileNet文档信息
   */
  static async getFileNetDocumentInfo(id: string): Promise<FileNetDocument> {
    return ApiService.get<FileNetDocument>(`/filenet/${id}/info`)
  }

  /**
   * 删除FileNet文档
   */
  static async deleteFileNetDocument(id: string): Promise<void> {
    return ApiService.delete<void>(`/filenet/${id}`)
  }

  /**
   * 更新FileNet文档版本
   */
  static async updateFileNetDocumentVersion(
    id: string,
    file: File,
    comment?: string
  ): Promise<FileNetDocument> {
    const formData = new FormData()
    formData.append('file', file)

    if (comment) {
      formData.append('comment', comment)
    }

    return ApiService.put<FileNetDocument>(`/filenet/${id}/version`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 获取FileNet文档版本列表
   */
  static async getFileNetDocumentVersions(id: string): Promise<FileNetVersionInfo[]> {
    return ApiService.get<FileNetVersionInfo[]>(`/filenet/${id}/versions`)
  }

  /**
   * 搜索FileNet文档
   */
  static async searchFileNetDocuments(params: FileNetSearchParams): Promise<{
    documents: FileNetDocument[]
    total: number
    hasMore: boolean
  }> {
    return ApiService.get('/filenet/search', { params })
  }

  /**
   * 检查FileNet连接状态
   */
  static async checkFileNetHealth(): Promise<{
    status: 'healthy' | 'unhealthy'
    message: string
    serverInfo?: {
      version: string
      url: string
      authenticated: boolean
    }
  }> {
    return ApiService.get('/filenet/health')
  }
}

export default FileNetApiService
