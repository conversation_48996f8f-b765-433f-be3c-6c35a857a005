#!/usr/bin/env ts-node

/**
 * 配置迁移验证脚本
 * 
 * 功能：
 * 1. 验证数据库中的配置项是否完整
 * 2. 检查配置值是否正确
 * 3. 测试HybridConfigService是否正常工作
 * 4. 验证所有服务是否能正确从数据库读取配置
 * 
 * 使用方法：
 * cd backend && npm run ts-node src/scripts/verify-config-migration.ts
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { HybridConfigService } from '../modules/config/services/hybrid-config.service';
import { SystemConfigService } from '../modules/config/services/system-config.service';
import { OnlyOfficeJwtService } from '../modules/config/services/onlyoffice-jwt.service';
import { EditorService } from '../modules/editor/services/editor.service';

async function verifyConfigMigration() {
  console.log('🔍 开始验证配置迁移...\n');

  try {
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取服务实例
    const hybridConfigService = app.get(HybridConfigService);
    const systemConfigService = app.get(SystemConfigService);
    const onlyOfficeJwtService = app.get(OnlyOfficeJwtService);
    const editorService = app.get(EditorService);

    console.log('✅ 应用上下文创建成功\n');

    // 1. 验证数据库配置完整性
    console.log('📋 1. 验证数据库配置完整性...');
    const allConfigs = await systemConfigService.getAllConfigs();
    console.log(`   数据库中共有 ${allConfigs.length} 个配置项`);

    // 检查关键配置项
    const requiredConfigs = [
      'onlyoffice.server_url',
      'onlyoffice.document_server_url',
      'onlyoffice.document_port',
      'jwt.onlyoffice.secret',
      'jwt.onlyoffice.header',
      'jwt.onlyoffice.in_body',
      'filenet.host',
      'filenet.port',
      'filenet.username',
      'filenet.password',
      'storage.upload_path',
      'storage.tmp_path',
      'storage.max_file_size',
      'storage.allowed_file_types',
      'cache.type',
      'cache.ttl',
      'cache.max_size',
      'redis.host',
      'redis.port',
      'redis.db',
      'logging.level',
      'logging.dir',
      'monitoring.enabled',
      'security.rate_limit_max_requests',
      'server.host',
      'server.callback_url'
    ];

    const missingConfigs: string[] = [];
    const configMap = new Map(allConfigs.map(c => [c.setting_key, c.setting_value]));

    for (const configKey of requiredConfigs) {
      if (!configMap.has(configKey)) {
        missingConfigs.push(configKey);
      } else {
        console.log(`   ✅ ${configKey}: ${configMap.get(configKey)?.substring(0, 30)}...`);
      }
    }

    if (missingConfigs.length > 0) {
      console.log(`   ❌ 缺少配置项: ${missingConfigs.join(', ')}`);
    } else {
      console.log('   ✅ 所有必需配置项都存在');
    }

    // 2. 测试HybridConfigService
    console.log('\n🔧 2. 测试HybridConfigService...');
    const appConfig = await hybridConfigService.getAppConfig();
    console.log(`   OnlyOffice服务器: ${appConfig.onlyoffice.documentServerUrl}`);
    console.log(`   服务器主机: ${appConfig.server.host}`);
    console.log(`   服务器端口: ${appConfig.port}`);
    console.log(`   FileNet主机: ${appConfig.filenet.host}:${appConfig.filenet.port}`);
    console.log(`   存储路径: ${appConfig.storage.uploadPath}`);
    console.log('   ✅ HybridConfigService 工作正常');

    // 3. 测试OnlyOfficeJwtService
    console.log('\n🔐 3. 测试OnlyOfficeJwtService...');
    try {
      const testPayload = { test: 'data', timestamp: Date.now() };
      const token = await onlyOfficeJwtService.generateToken(testPayload);
      console.log(`   生成的JWT Token长度: ${token.length}`);
      console.log('   ✅ OnlyOfficeJwtService 工作正常');
    } catch (error) {
      console.log(`   ❌ OnlyOfficeJwtService 测试失败: ${error.message}`);
    }

    // 4. 测试配置来源
    console.log('\n📊 4. 检查配置来源...');
    const configSources = await hybridConfigService.getConfigSources();
    const dbConfigs = Object.keys(configSources).filter(key => configSources[key] === 'database');
    const envConfigs = Object.keys(configSources).filter(key => configSources[key] === 'env');
    
    console.log(`   从数据库读取的配置 (${dbConfigs.length}项):`);
    dbConfigs.slice(0, 10).forEach(key => console.log(`     - ${key}`));
    if (dbConfigs.length > 10) {
      console.log(`     ... 还有 ${dbConfigs.length - 10} 项`);
    }

    console.log(`   从环境变量读取的配置 (${envConfigs.length}项):`);
    envConfigs.forEach(key => console.log(`     - ${key}`));

    // 5. 验证关键配置值
    console.log('\n🎯 5. 验证关键配置值...');
    const keyConfigs = [
      { key: 'onlyoffice.server_url', expected: 'http://*************/' },
      { key: 'onlyoffice.document_server_url', expected: 'http://*************/' },
      { key: 'filenet.host', expected: '*************' },
      { key: 'filenet.port', expected: '8090' },
      { key: 'storage.max_file_size', expected: '52428800' },
      { key: 'cache.type', expected: 'memory' },
      { key: 'redis.host', expected: 'localhost' },
      { key: 'redis.port', expected: '6379' }
    ];

    for (const { key, expected } of keyConfigs) {
      const actual = configMap.get(key);
      if (actual === expected) {
        console.log(`   ✅ ${key}: ${actual}`);
      } else {
        console.log(`   ⚠️ ${key}: 期望 "${expected}", 实际 "${actual}"`);
      }
    }

    console.log('\n🎉 配置迁移验证完成！');
    
    // 关闭应用
    await app.close();
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  verifyConfigMigration()
    .then(() => {
      console.log('\n✅ 验证脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 验证脚本执行失败:', error);
      process.exit(1);
    });
}

export { verifyConfigMigration };
