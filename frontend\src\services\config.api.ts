import { ApiService } from './api'

/**
 * OnlyOffice配置对象类型定义
 */
export interface OnlyOfficeConfig {
  document?: {
    permissions?: {
      edit?: boolean
      download?: boolean
      review?: boolean
      comment?: boolean
    }
    info?: {
      author?: string
      created?: string
      folder?: string
    }
  }
  editorConfig?: {
    mode?: 'edit' | 'view' | 'review'
    lang?: string
    callbackUrl?: string
    user?: {
      id?: string
      name?: string
    }
    customization?: {
      chat?: boolean
      comments?: boolean
      help?: boolean
      hideRightMenu?: boolean
      hideRulers?: boolean
      toolbar?: boolean
    }
  }
  type?: 'desktop' | 'mobile' | 'embedded'
  [key: string]: unknown
}

/**
 * 原始数据库字段类型定义
 */
interface RawConfigTemplate {
  id: string
  name: string
  description: string
  is_default?: number | boolean
  isDefault?: boolean
  is_active?: number | boolean
  isActive?: boolean
  created_at?: string
  createdAt?: string
  updated_at?: string
  updatedAt?: string
  config?: OnlyOfficeConfig
}

/**
 * 配置模板接口定义
 */
export interface ConfigTemplate {
  id: string
  name: string
  description: string
  isDefault: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  config?: OnlyOfficeConfig
}

/**
 * 创建配置模板DTO
 */
export interface CreateConfigTemplateDto {
  name: string
  description: string
  isDefault?: boolean
  isActive?: boolean
  config?: OnlyOfficeConfig
}

/**
 * 更新配置模板DTO
 */
export interface UpdateConfigTemplateDto {
  name?: string
  description?: string
  isDefault?: boolean
  isActive?: boolean
  config?: OnlyOfficeConfig
}

/**
 * 配置模板API响应格式
 */
export interface ConfigTemplateResponse {
  success: boolean
  data: ConfigTemplate[]
  message: string
}

/**
 * 单个配置模板API响应格式
 */
export interface SingleConfigTemplateResponse {
  success: boolean
  data: ConfigTemplate
  message: string
}

/**
 * 配置模板API服务
 * @description 处理配置模板相关的API调用
 */
export class ConfigTemplateApiService {
  /**
   * 数据字段转换：将snake_case转换为camelCase
   * @param rawData 原始数据
   * @returns 转换后的数据
   */
  private static transformTemplate(rawData: RawConfigTemplate): ConfigTemplate {
    return {
      id: rawData.id,
      name: rawData.name,
      description: rawData.description,
      isDefault: Boolean(rawData.is_default || rawData.isDefault),
      isActive: Boolean(rawData.is_active || rawData.isActive),
      createdAt: rawData.created_at || rawData.createdAt || '',
      updatedAt: rawData.updated_at || rawData.updatedAt || '',
      config: rawData.config,
    }
  }

  /**
   * 获取所有配置模板
   * @returns Promise<ConfigTemplate[]> 配置模板列表
   */
  static async getAllTemplates(): Promise<ConfigTemplate[]> {
    try {
      console.log('🚀 获取所有配置模板')

      const response = await ApiService.get<RawConfigTemplate[] | { data: RawConfigTemplate[] }>(
        '/config-templates'
      )

      console.log('✅ 获取配置模板响应:', response)
      console.log('📝 响应类型:', typeof response, Array.isArray(response))
      console.log('📝 响应结构:', Object.keys(response || {}))

      // 处理不同的响应格式
      let rawData: RawConfigTemplate[] = []

      if (Array.isArray(response)) {
        // 如果直接返回数组
        rawData = response
        console.log('🔍 响应是直接数组格式')
      } else if (response?.data && Array.isArray(response.data)) {
        // 如果返回包含data字段的对象
        rawData = response.data
        console.log('🔍 响应是对象格式，包含data字段')
      } else {
        console.warn('⚠️ API响应格式不正确:', response)
        return []
      }

      console.log('📝 原始数据样例:', rawData[0])

      if (rawData.length === 0) {
        console.log('🔍 没有找到配置模板数据')
        return []
      }

      // 转换数据格式
      const transformedData = rawData.map((item: RawConfigTemplate) => this.transformTemplate(item))
      console.log('🔄 转换后数据样例:', transformedData[0])
      console.log('🔄 转换后数据总数:', transformedData.length)

      return transformedData
    } catch (error) {
      console.error('❌ 获取配置模板失败:', error)
      throw error
    }
  }

  /**
   * 获取配置模板详情
   * @param id 配置模板ID
   * @returns Promise<ConfigTemplate> 配置模板详情
   */
  static async getTemplateById(id: string): Promise<ConfigTemplate> {
    try {
      console.log(`🚀 获取配置模板详情: ${id}`)

      const response = await ApiService.get<SingleConfigTemplateResponse>(`/config-templates/${id}`)

      console.log('✅ 获取配置模板详情响应:', response)

      return response.data
    } catch (error) {
      console.error('❌ 获取配置模板详情失败:', error)
      throw error
    }
  }

  /**
   * 获取默认配置模板
   * @returns Promise<ConfigTemplate> 默认配置模板
   */
  static async getDefaultTemplate(): Promise<ConfigTemplate> {
    try {
      console.log('🚀 获取默认配置模板')

      const response = await ApiService.get<SingleConfigTemplateResponse>(
        '/config-templates/default/template'
      )

      console.log('✅ 获取默认配置模板响应:', response)

      return response.data
    } catch (error) {
      console.error('❌ 获取默认配置模板失败:', error)
      throw error
    }
  }

  /**
   * 创建配置模板
   * @param data 配置模板数据
   * @returns Promise<ConfigTemplate> 创建的配置模板
   */
  static async createTemplate(data: CreateConfigTemplateDto): Promise<ConfigTemplate> {
    try {
      console.log('🚀 创建配置模板:', data)

      const response = await ApiService.post<SingleConfigTemplateResponse>(
        '/config-templates',
        data
      )

      console.log('✅ 创建配置模板响应:', response)

      return response.data
    } catch (error) {
      console.error('❌ 创建配置模板失败:', error)
      throw error
    }
  }

  /**
   * 更新配置模板
   * @param id 配置模板ID
   * @param data 更新数据
   * @returns Promise<ConfigTemplate> 更新后的配置模板
   */
  static async updateTemplate(id: string, data: UpdateConfigTemplateDto): Promise<ConfigTemplate> {
    try {
      console.log(`🚀 更新配置模板: ${id}`, data)

      const response = await ApiService.put<SingleConfigTemplateResponse>(
        `/config-templates/${id}`,
        data
      )

      console.log('✅ 更新配置模板响应:', response)

      return response.data
    } catch (error) {
      console.error('❌ 更新配置模板失败:', error)
      throw error
    }
  }

  /**
   * 删除配置模板
   * @param id 配置模板ID
   * @returns Promise<void>
   */
  static async deleteTemplate(id: string): Promise<void> {
    try {
      console.log(`🚀 删除配置模板: ${id}`)

      await ApiService.delete(`/config-templates/${id}`)

      console.log('✅ 删除配置模板成功')
    } catch (error) {
      console.error('❌ 删除配置模板失败:', error)
      throw error
    }
  }

  /**
   * 设置默认配置模板
   * @param id 配置模板ID
   * @returns Promise<ConfigTemplate> 设置为默认的配置模板
   */
  static async setDefaultTemplate(id: string): Promise<ConfigTemplate> {
    try {
      console.log(`🚀 设置默认配置模板: ${id}`)

      const response = await ApiService.put<SingleConfigTemplateResponse>(
        `/config-templates/${id}/set-default`
      )

      console.log('✅ 设置默认配置模板响应:', response)

      return response.data
    } catch (error) {
      console.error('❌ 设置默认配置模板失败:', error)
      throw error
    }
  }
}

export default ConfigTemplateApiService
