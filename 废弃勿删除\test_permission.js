const https = require('https');
const http = require('http');

const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NTEzNDMwNjEsImV4cCI6MTc1MTQyOTQ2MSwiYXVkIjoib25seW9mZmljZS1jbGllbnQiLCJpc3MiOiJvbmx5b2ZmaWNlLW5lc3RqcyJ9.fypHQeRRPybb0DX21oq9br4ktziXn_9whw2349zakPI";

const options = {
  hostname: '*************',
  port: 3000,
  path: '/api/permissions/user/my',
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
};

console.log('正在调用权限API...');
console.log('URL:', `http://${options.hostname}:${options.port}${options.path}`);

const req = http.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头: ${JSON.stringify(res.headers, null, 2)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('API响应:');
    try {
      const response = JSON.parse(data);
      console.log(JSON.stringify(response, null, 2));
    } catch (e) {
      console.log('原始响应:', data);
    }
  });
});

req.on('error', (e) => {
  console.error(`请求出错: ${e.message}`);
});

req.end(); 