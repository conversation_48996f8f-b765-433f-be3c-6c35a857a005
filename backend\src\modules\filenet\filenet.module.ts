import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { MulterModule } from '@nestjs/platform-express';
import { FilenetController } from './controllers/filenet.controller';
import { FilenetService } from './services/filenet.service';
import { DatabaseModule } from '../database/database.module';
import * as multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// 确保上传目录存在
const uploadDir = path.resolve(process.cwd(), 'uploads/tmp');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

/**
 * FileNet集成模块
 * 
 * 提供企业级文档存储和版本控制功能
 * 集成IBM FileNet Content Manager
 * 
 * @class FilenetModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    MulterModule.register({
      storage: multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, uploadDir); // 保存到临时目录
        },
        filename: function (req, file, cb) {
          // 使用UUID生成唯一文件名，避免使用原始文件名可能带来的编码问题
          const uniqueId = uuidv4();
          const extension = path.extname(file.originalname);
          cb(null, `${Date.now()}-${uniqueId}${extension}`);
        }
      }),
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB 文件大小限制
      },
    }),
    DatabaseModule,
  ],
  controllers: [FilenetController],
  providers: [FilenetService],
  exports: [FilenetService],
})
export class FilenetModule {} 