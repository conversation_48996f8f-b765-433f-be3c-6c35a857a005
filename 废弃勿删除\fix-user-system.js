const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

// 数据库配置
const dbConfig = {
    host: '*************',
    port: 3306,
    user: 'onlyfile_user', 
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
};

async function fixUserSystem() {
    let connection;
    
    try {
        console.log('🔄 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 1. 检查当前SQL模式
        console.log('🔄 检查SQL模式...');
        const [sqlModeResult] = await connection.execute('SELECT @@sql_mode as sql_mode');
        console.log('📋 当前SQL模式:', sqlModeResult[0].sql_mode);
        
        // 2. 临时设置SQL模式（去除only_full_group_by，兼容MySQL 8.0）
        console.log('🔄 临时修改SQL模式...');
        await connection.execute(`
            SET SESSION sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'
        `);
        console.log('✅ SQL模式已临时修改');
        
        // 3. 检查用户表数据
        console.log('🔄 检查用户数据...');
        const [users] = await connection.execute('SELECT username, password_hash FROM users WHERE username = "admin"');
        
        if (users.length === 0) {
            console.log('❌ 没有找到admin用户');
            
            // 创建admin用户
            console.log('🔄 创建admin用户...');
            const hashedPassword = await bcrypt.hash('admin123', 10);
            await connection.execute(`
                INSERT INTO users (id, username, email, password_hash, full_name, status, role_id, created_by) 
                VALUES ('user-admin', 'admin', '<EMAIL>', ?, '系统管理员', 'active', 'role-super-admin', 'system')
            `, [hashedPassword]);
            console.log('✅ admin用户创建成功');
        } else {
            console.log('✅ 找到admin用户');
            console.log('🔄 验证密码哈希...');
            
            const currentHash = users[0].password_hash;
            console.log('📋 当前密码哈希:', currentHash.substring(0, 20) + '...');
            
            // 验证哈希是否正确
            const isValidHash = await bcrypt.compare('admin123', currentHash);
            if (!isValidHash) {
                console.log('❌ 密码哈希验证失败，重新生成...');
                const newHash = await bcrypt.hash('admin123', 10);
                await connection.execute('UPDATE users SET password_hash = ? WHERE username = "admin"', [newHash]);
                console.log('✅ 密码哈希已更新');
            } else {
                console.log('✅ 密码哈希验证通过');
            }
        }
        
        // 4. 创建缺失的视图
        console.log('🔄 创建用户详细信息视图...');
        try {
            await connection.execute(`
                CREATE OR REPLACE VIEW v_user_details AS
                SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.full_name,
                    u.phone,
                    u.avatar_url,
                    u.status,
                    u.department,
                    u.position,
                    u.last_login_at,
                    u.last_login_ip,
                    u.email_verified,
                    u.phone_verified,
                    u.two_factor_enabled,
                    u.created_at,
                    u.updated_at,
                    r.name as role_name,
                    r.display_name as role_display_name,
                    r.permissions as role_permissions
                FROM users u
                LEFT JOIN user_roles r ON u.role_id = r.id
                WHERE u.deleted_at IS NULL
            `);
            console.log('✅ 用户详细信息视图创建成功');
        } catch (viewError) {
            console.warn('⚠️ 视图创建失败:', viewError.message);
        }
        
        // 5. 创建活跃会话视图
        console.log('🔄 创建活跃会话视图...');
        try {
            await connection.execute(`
                CREATE OR REPLACE VIEW v_active_sessions AS
                SELECT 
                    s.id,
                    s.user_id,
                    u.username,
                    u.full_name,
                    s.device_type,
                    s.device_name,
                    s.ip_address,
                    s.location,
                    s.last_activity_at,
                    s.expires_at,
                    s.created_at
                FROM user_sessions s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE s.is_active = TRUE 
                AND s.expires_at > NOW()
                AND u.deleted_at IS NULL
            `);
            console.log('✅ 活跃会话视图创建成功');
        } catch (viewError) {
            console.warn('⚠️ 活跃会话视图创建失败:', viewError.message);
        }
        
        // 6. 验证修复结果
        console.log('🔄 验证修复结果...');
        const [finalUsers] = await connection.execute(`
            SELECT u.username, u.full_name, r.display_name as role_name, u.status 
            FROM users u 
            LEFT JOIN user_roles r ON u.role_id = r.id 
            WHERE u.username = 'admin'
        `);
        
        if (finalUsers.length > 0) {
            const user = finalUsers[0];
            console.log('👤 用户验证结果:');
            console.log(`  - 用户名: ${user.username}`);
            console.log(`  - 姓名: ${user.full_name}`);
            console.log(`  - 角色: ${user.role_name}`);
            console.log(`  - 状态: ${user.status}`);
        }
        
        // 7. 检查视图是否创建成功
        console.log('🔄 检查视图创建情况...');
        const [views] = await connection.execute(`
            SHOW TABLES LIKE 'v_%'
        `);
        
        console.log('📋 已创建的视图:');
        views.forEach(view => {
            console.log(`  - ${Object.values(view)[0]}`);
        });
        
        console.log('🎉 用户系统修复完成！');
        console.log('📝 请重新测试登录: admin / admin123');
        
    } catch (error) {
        console.error('❌ 修复失败:', error.message);
        console.error('🔧 完整错误:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 执行修复
fixUserSystem(); 