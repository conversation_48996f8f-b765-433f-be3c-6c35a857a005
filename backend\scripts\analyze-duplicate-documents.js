const mysql = require('mysql2/promise');
const fs = require('fs');

/**
 * 分析重复文档数据脚本
 * 用于分析由于OnlyOffice回调bug导致的重复文档记录
 */
async function analyzeDuplicateDocuments() {
  let connection;
  
  try {
    // 数据库连接配置 - 从环境变量或默认值获取
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'onlyfile'
    });

    console.log('🔍 开始分析重复文档数据...\n');

    // 1. 查找重复的文档名称
    console.log('=== 1. 按文件名分组统计重复记录 ===');
    const [duplicatesByName] = await connection.execute(`
      SELECT 
        original_name,
        COUNT(*) as count,
        MIN(created_at) as first_created,
        MAX(created_at) as last_created,
        GROUP_CONCAT(id ORDER BY created_at) as all_ids,
        GROUP_CONCAT(version ORDER BY created_at) as all_versions,
        GROUP_CONCAT(created_by ORDER BY created_at) as all_creators
      FROM filenet_documents 
      WHERE is_deleted = 0
      GROUP BY original_name 
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC, original_name
    `);

    console.log(`发现 ${duplicatesByName.length} 个重复的文件名：\n`);
    
    let totalDuplicates = 0;
    const duplicateAnalysis = [];

    duplicatesByName.forEach((dup, i) => {
      console.log(`${i + 1}. "${dup.original_name}"`);
      console.log(`   重复数量: ${dup.count} 个记录`);
      console.log(`   首次创建: ${dup.first_created}`);
      console.log(`   最后创建: ${dup.last_created}`);
      console.log(`   版本号: ${dup.all_versions}`);
      console.log(`   创建者: ${dup.all_creators}`);
      console.log(`   所有ID: ${dup.all_ids.substring(0, 100)}${dup.all_ids.length > 100 ? '...' : ''}`);
      console.log('---');
      
      totalDuplicates += dup.count - 1; // 减1是因为每组应该只保留1个
      
      duplicateAnalysis.push({
        fileName: dup.original_name,
        duplicateCount: dup.count,
        keepRecord: dup.all_ids.split(',')[0], // 保留最早的记录
        removeRecords: dup.all_ids.split(',').slice(1), // 删除其余记录
        firstCreated: dup.first_created,
        lastCreated: dup.last_created
      });
    });

    console.log(`\n📊 统计结果:`);
    console.log(`- 重复文件数量: ${duplicatesByName.length}`);
    console.log(`- 需要清理的记录数: ${totalDuplicates}`);

    // 2. 分析OnlyOffice系统创建的重复记录
    console.log('\n=== 2. OnlyOffice系统创建的重复记录分析 ===');
    const [onlyofficeRecords] = await connection.execute(`
      SELECT 
        original_name,
        COUNT(*) as count,
        MIN(created_at) as first_created,
        MAX(created_at) as last_created
      FROM filenet_documents 
      WHERE is_deleted = 0 
        AND (created_by = 'onlyoffice-system' OR last_modified_by = 'onlyoffice-system')
      GROUP BY original_name 
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `);

    console.log(`OnlyOffice系统创建的重复文件: ${onlyofficeRecords.length} 个`);
    onlyofficeRecords.forEach((record, i) => {
      console.log(`${i + 1}. "${record.original_name}" - ${record.count} 个重复`);
    });

    // 3. 检查版本表中的对应记录
    console.log('\n=== 3. 检查版本表中的相关记录 ===');
    for (const analysis of duplicateAnalysis.slice(0, 3)) { // 只检查前3个作为示例
      console.log(`\n检查文件: "${analysis.fileName}"`);
      
      const [versionRecords] = await connection.execute(`
        SELECT 
          fv.id as version_id,
          fv.doc_id,
          fv.fn_doc_id,
          fv.version,
          fv.modified_at,
          fv.comment,
          fd.original_name,
          fd.created_at as doc_created
        FROM filenet_document_versions fv
        LEFT JOIN filenet_documents fd ON fv.doc_id = fd.id
        WHERE fd.original_name = ?
        ORDER BY fv.modified_at
      `, [analysis.fileName]);

      console.log(`  版本表记录数: ${versionRecords.length}`);
      versionRecords.forEach((ver, i) => {
        console.log(`    ${i + 1}. 版本${ver.version} - 文档ID: ${ver.doc_id} - 修改时间: ${ver.modified_at}`);
      });
    }

    // 4. 生成清理建议
    console.log('\n=== 4. 数据清理建议 ===');
    console.log('基于分析结果，建议的清理策略：');
    console.log('1. 对于每个重复的文件名，保留最早创建的记录作为主记录');
    console.log('2. 将其他重复记录中的版本信息合并到版本表中');
    console.log('3. 删除重复的主表记录');
    console.log('4. 更新主记录的版本号为最新版本');

    // 5. 保存分析结果到文件
    const analysisReport = {
      timestamp: new Date().toISOString(),
      summary: {
        totalDuplicateFiles: duplicatesByName.length,
        totalRecordsToRemove: totalDuplicates,
        onlyofficeDuplicates: onlyofficeRecords.length
      },
      duplicateFiles: duplicateAnalysis,
      onlyofficeRecords: onlyofficeRecords
    };

    fs.writeFileSync(
      'duplicate-analysis-report.json', 
      JSON.stringify(analysisReport, null, 2),
      'utf8'
    );

    console.log('\n📄 分析报告已保存到: duplicate-analysis-report.json');
    
    return analysisReport;

  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行分析
if (require.main === module) {
  analyzeDuplicateDocuments()
    .then((report) => {
      console.log('\n✅ 重复文档数据分析完成');
      console.log('下一步可以运行清理脚本进行数据修复');
    })
    .catch((error) => {
      console.error('❌ 分析失败:', error);
      process.exit(1);
    });
}

module.exports = { analyzeDuplicateDocuments }; 