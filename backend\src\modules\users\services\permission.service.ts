import { Injectable, Logger, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';

/**
 * 数据库权限行数据接口
 */
interface PermissionRowData {
  id: string;
  code: string;
  name: string;
  description: string;
  module: string;
  resource: string;
  action: string;
  conditions: string | null;
  is_active: number;
  sort_order: number;
  created_by: string;
  created_at: Date;
  updated_by: string;
  updated_at: Date;
  role_count?: number;
  user_count?: number;
}

// 其他查询结果类型定义
interface CountResult {
  total: number;
}

interface StatsResult {
  total: number;
  active: number;
  inactive: number;
}

interface ModuleStatsResult {
  module: string;
  count: number;
}

interface ActionStatsResult {
  action: string;
  count: number;
}

import { v4 as uuidv4 } from 'uuid';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionQueryDto,
  BatchPermissionDto,
  PermissionResponseDto,
  PermissionListResponseDto,
  PermissionStatsResponseDto,
  ModulePermissionTreeDto,
  CheckPermissionDto,
  PermissionCheckResultDto,
} from '../dto/permission.dto';

/**
 * 权限管理服务类
 * 
 * @description 提供完整的权限管理功能，包括权限的CRUD操作、权限检查、统计分析等
 * <AUTHOR> Team
 * @since 2024-12-19
 */
@Injectable()
export class PermissionService {
  private readonly logger = new Logger(PermissionService.name);

  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 创建权限
   * @param createPermissionDto 创建权限的数据
   * @param currentUserId 当前用户ID
   * @returns 创建的权限信息
   */
  async createPermission(createPermissionDto: CreatePermissionDto, currentUserId: string): Promise<PermissionResponseDto> {
    const { code, name, description, module, resource, action, conditions, is_active = true, sort_order = 0 } = createPermissionDto;

    // 检查权限代码是否已存在
    const existingPermission = await this.findByCode(code);
    if (existingPermission) {
      throw new ConflictException(`权限代码 "${code}" 已存在`);
    }

    const permissionId = uuidv4();
    const now = new Date();

    const sql = `
      INSERT INTO user_permissions (
        id, code, name, description, module, resource, action, conditions, 
        is_active, sort_order, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      permissionId,
      code,
      name,
      description || null,
      module,
      resource || null,
      action || null,
      conditions ? JSON.stringify(conditions) : null,
      is_active ? 1 : 0,
      sort_order,
      currentUserId,
      now,
      now,
    ];

    try {
      await this.databaseService.query(sql, values);
      this.logger.log(`权限创建成功: ${code} (ID: ${permissionId})`);
      
      return await this.findById(permissionId);
    } catch (error) {
      this.logger.error(`创建权限失败: ${error.message}`, error.stack);
      throw new BadRequestException(`创建权限失败: ${error.message}`);
    }
  }

  /**
   * 根据ID查找权限
   * @param id 权限ID
   * @returns 权限信息或null
   */
  async findById(id: string): Promise<PermissionResponseDto | null> {
    const sql = `
      SELECT 
        p.*,
        (SELECT COUNT(*) FROM role_permissions rp WHERE rp.permission_id = p.id) as role_count,
        (SELECT COUNT(DISTINCT u.id) FROM users u 
         INNER JOIN user_roles ur ON u.role_id = ur.id 
         INNER JOIN role_permissions rp ON ur.id = rp.role_id 
         WHERE rp.permission_id = p.id) as user_count
      FROM user_permissions p 
      WHERE p.id = ?
    `;

    const results = await this.databaseService.query(sql, [id]) as unknown as PermissionRowData[];
    if (results.length === 0) {
      return null;
    }

    return this.formatPermissionResponse(results[0]);
  }

  /**
   * 根据权限代码查找权限
   * @param code 权限代码
   * @returns 权限信息或null
   */
  async findByCode(code: string): Promise<PermissionResponseDto | null> {
    const sql = `
      SELECT 
        p.*,
        (SELECT COUNT(*) FROM role_permissions rp WHERE rp.permission_id = p.id) as role_count,
        (SELECT COUNT(DISTINCT u.id) FROM users u 
         INNER JOIN user_roles ur ON u.role_id = ur.id 
         INNER JOIN role_permissions rp ON ur.id = rp.role_id 
         WHERE rp.permission_id = p.id) as user_count
      FROM user_permissions p 
      WHERE p.code = ?
    `;

    const results = await this.databaseService.query(sql, [code]) as unknown as PermissionRowData[];
    if (results.length === 0) {
      return null;
    }

    return this.formatPermissionResponse(results[0]);
  }

  /**
   * 查询权限列表
   * @param query 查询参数
   * @returns 权限列表和分页信息
   */
  async findMany(query: PermissionQueryDto): Promise<PermissionListResponseDto> {
    const {
      page = 1,
      pageSize = 10,
      search,
      module,
      action,
      status,
      sortBy = 'sort_order',
      sortOrder = 'ASC',
    } = query;

    const offset = (page - 1) * pageSize;
    const conditions: string[] = [];
    const values: (string | number)[] = [];

    // 构建查询条件
    if (search) {
      conditions.push('(p.name LIKE ? OR p.code LIKE ? OR p.description LIKE ?)');
      const searchPattern = `%${search}%`;
      values.push(searchPattern, searchPattern, searchPattern);
    }

    if (module) {
      conditions.push('p.module = ?');
      values.push(module);
    }

    if (action) {
      conditions.push('p.action = ?');
      values.push(action);
    }

    if (status) {
      conditions.push('p.is_active = ?');
      values.push(status === 'active' ? 1 : 0);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM user_permissions p 
      ${whereClause}
    `;
    const countResults = await this.databaseService.query(countSql, values) as unknown as CountResult[];
    const total = countResults[0].total;

    // 查询数据
    const dataSql = `
      SELECT 
        p.*,
        (SELECT COUNT(*) FROM user_roles ur 
         WHERE JSON_CONTAINS(ur.permissions, JSON_QUOTE(p.code))) as role_count,
        (SELECT COUNT(DISTINCT u.id) FROM users u 
         INNER JOIN user_roles ur ON u.role_id = ur.id 
         WHERE JSON_CONTAINS(ur.permissions, JSON_QUOTE(p.code))) as user_count
      FROM user_permissions p 
      ${whereClause}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    const dataResults = await this.databaseService.query(dataSql, [...values, pageSize, offset]) as unknown as PermissionRowData[];

    return {
      data: dataResults.map(row => this.formatPermissionResponse(row)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 更新权限
   * @param id 权限ID
   * @param updatePermissionDto 更新的数据
   * @param currentUserId 当前用户ID
   * @returns 更新后的权限信息
   */
  async updatePermission(id: string, updatePermissionDto: UpdatePermissionDto, currentUserId: string): Promise<PermissionResponseDto> {
    const existingPermission = await this.findById(id);
    if (!existingPermission) {
      throw new NotFoundException('权限不存在');
    }

    // 如果更新权限代码，检查是否与其他权限冲突
    if (updatePermissionDto.code && updatePermissionDto.code !== existingPermission.code) {
      const conflictPermission = await this.findByCode(updatePermissionDto.code);
      if (conflictPermission) {
        throw new ConflictException(`权限代码 "${updatePermissionDto.code}" 已存在`);
      }
    }

    const updateFields: string[] = [];
    const values: (string | number | Date | null)[] = [];

    // 构建更新字段
    Object.entries(updatePermissionDto).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'conditions') {
          updateFields.push('conditions = ?');
          values.push(value ? JSON.stringify(value) : null);
        } else if (key === 'is_active') {
          updateFields.push('is_active = ?');
          values.push(value ? 1 : 0);
        } else {
          updateFields.push(`${key} = ?`);
          values.push(value);
        }
      }
    });

    if (updateFields.length === 0) {
      return existingPermission;
    }

    updateFields.push('updated_by = ?', 'updated_at = ?');
    values.push(currentUserId, new Date());

    const sql = `
      UPDATE user_permissions 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    try {
      await this.databaseService.query(sql, [...values, id]);
      this.logger.log(`权限更新成功: ${id}`);
      
      return await this.findById(id);
    } catch (error) {
      this.logger.error(`更新权限失败: ${error.message}`, error.stack);
      throw new BadRequestException(`更新权限失败: ${error.message}`);
    }
  }

  /**
   * 删除权限
   * @param id 权限ID
   * @param currentUserId 当前用户ID
   */
  async deletePermission(id: string, _currentUserId: string): Promise<void> {
    const permission = await this.findById(id);
    if (!permission) {
      throw new NotFoundException('权限不存在');
    }

    // 检查是否有角色正在使用此权限
    if (permission.role_count > 0) {
      throw new ConflictException(`权限 "${permission.code}" 正在被 ${permission.role_count} 个角色使用，无法删除`);
    }

    const sql = 'DELETE FROM user_permissions WHERE id = ?';

    try {
      await this.databaseService.query(sql, [id]);
      this.logger.log(`权限删除成功: ${permission.code} (ID: ${id})`);
    } catch (error) {
      this.logger.error(`删除权限失败: ${error.message}`, error.stack);
      throw new BadRequestException(`删除权限失败: ${error.message}`);
    }
  }

  /**
   * 批量操作权限
   * @param batchDto 批量操作数据
   * @param currentUserId 当前用户ID
   * @returns 操作结果
   */
  async batchOperation(batchDto: BatchPermissionDto, currentUserId: string): Promise<{ success: number; failed: number; errors: string[] }> {
    const { permissionIds, operation = 'activate' } = batchDto;
    const results = { success: 0, failed: 0, errors: [] };

    for (const permissionId of permissionIds) {
      try {
        switch (operation) {
          case 'activate':
            await this.updatePermission(permissionId, { is_active: true }, currentUserId);
            break;
          case 'deactivate':
            await this.updatePermission(permissionId, { is_active: false }, currentUserId);
            break;
          case 'delete':
            await this.deletePermission(permissionId, currentUserId);
            break;
        }
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`权限 ${permissionId}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * 获取权限统计信息
   * @returns 权限统计数据
   */
  async getPermissionStats(): Promise<PermissionStatsResponseDto> {
    // 基础统计
    const basicStatsSql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
      FROM user_permissions
    `;
    const basicStats = await this.databaseService.query(basicStatsSql) as unknown as StatsResult[];

    // 按模块统计
    const moduleStatsSql = `
      SELECT module, COUNT(*) as count
      FROM user_permissions 
      WHERE is_active = 1
      GROUP BY module
      ORDER BY count DESC
    `;
    const moduleStats = await this.databaseService.query(moduleStatsSql) as unknown as ModuleStatsResult[];

    // 按操作类型统计
    const actionStatsSql = `
      SELECT action, COUNT(*) as count
      FROM user_permissions 
      WHERE is_active = 1 AND action IS NOT NULL
      GROUP BY action
      ORDER BY count DESC
    `;
    const actionStats = await this.databaseService.query(actionStatsSql) as unknown as ActionStatsResult[];

    return {
      total: basicStats[0].total,
      active: basicStats[0].active,
      inactive: basicStats[0].inactive,
      by_module: moduleStats.reduce((acc, item) => {
        acc[item.module] = item.count;
        return acc;
      }, {} as Record<string, number>),
      by_action: actionStats.reduce((acc, item) => {
        acc[item.action] = item.count;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  /**
   * 获取模块权限树
   * @returns 按模块分组的权限树
   */
  async getModulePermissionTree(): Promise<ModulePermissionTreeDto[]> {
    const sql = `
      SELECT 
        p.*,
        (SELECT COUNT(*) FROM user_roles ur WHERE JSON_CONTAINS(ur.permissions, JSON_QUOTE(p.code))) as role_count,
        (SELECT COUNT(DISTINCT u.id) FROM users u 
         INNER JOIN user_roles ur ON u.role_id = ur.id 
         WHERE JSON_CONTAINS(ur.permissions, JSON_QUOTE(p.code))) as user_count
      FROM user_permissions p 
      WHERE p.is_active = 1
      ORDER BY p.module, p.sort_order, p.name
    `;

    const permissions = await this.databaseService.query(sql) as unknown as PermissionRowData[];
    const permissionsByModule = new Map<string, PermissionResponseDto[]>();

    // 按模块分组
    permissions.forEach(permission => {
      const module = permission.module;
      if (!permissionsByModule.has(module)) {
        permissionsByModule.set(module, []);
      }
      permissionsByModule.get(module)?.push(this.formatPermissionResponse(permission));
    });

    // 模块名称映射
    const moduleNameMap: Record<string, string> = {
      documents: '文档管理',
      users: '用户管理',
      templates: '模板管理',
      config: '系统配置',
      auth: '认证管理',
      upload: '上传管理',
      filenet: 'FileNet管理',
      editor: '编辑器管理',
      health: '健康检查',
    };

    // 构建结果
    const result: ModulePermissionTreeDto[] = [];
    for (const [module, modulePermissions] of permissionsByModule) {
      result.push({
        module,
        module_name: moduleNameMap[module] || module,
        permissions: modulePermissions,
        permission_count: modulePermissions.length,
      });
    }

    return result;
  }

  /**
   * 检查用户权限
   * @param checkDto 权限检查参数
   * @returns 权限检查结果
   */
  async checkPermissions(checkDto: CheckPermissionDto): Promise<PermissionCheckResultDto> {
    const { userId, permissions, context } = checkDto;

    // 获取用户的所有权限
    const userPermissions = await this.getUserPermissions(userId);
    
    const results: Record<string, boolean> = {};
    const missingPermissions: string[] = [];

    // 检查每个权限
    for (const permission of permissions) {
      const hasPermission = this.checkSinglePermission(userPermissions, permission, context);
      results[permission] = hasPermission;
      
      if (!hasPermission) {
        missingPermissions.push(permission);
      }
    }

    return {
      results,
      has_all: missingPermissions.length === 0,
      has_any: Object.values(results).some(result => result),
      missing_permissions: missingPermissions,
    };
  }

  /**
   * 获取用户权限列表
   * @param userId 用户ID
   * @returns 权限代码数组
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    console.log('🚀 [PermissionService] getUserPermissions 开始执行:', {
      userId,
      userIdType: typeof userId,
      timestamp: new Date().toISOString(),
    });

    try {
      // 首先检查用户是否存在
      const userCheckSql = 'SELECT u.id, u.username, u.role_id, u.deleted_at FROM users u WHERE u.id = ?';
      const userResults = await this.databaseService.query(userCheckSql, [userId]);
      
      console.log('🔍 [PermissionService] 用户检查结果:', {
        userId,
        userExists: userResults.length > 0,
        userResults,
        timestamp: new Date().toISOString(),
      });

      if (userResults.length === 0) {
        console.log('⚠️ [PermissionService] 用户不存在，返回空权限');
        return [];
      }

      // 先检查role_permissions表数据
      const rolePermSql = `
        SELECT ur.id as role_id, ur.name as role_name, rp.permission_id
        FROM users u
        INNER JOIN user_roles ur ON u.role_id = ur.id AND ur.is_active = 1
        INNER JOIN role_permissions rp ON ur.id = rp.role_id
        WHERE u.id = ? AND u.deleted_at IS NULL
      `;
      
      const rolePermResults = await this.databaseService.query(rolePermSql, [userId]);
      console.log('🔍 [PermissionService] role_permissions 查询结果:', {
        userId,
        rolePermResults,
        timestamp: new Date().toISOString(),
      });

      // 通过role_permissions表查询用户权限
      const sql = `
        SELECT DISTINCT up.code, up.name, up.id as permission_id, rp.role_id, up.sort_order
        FROM users u
        INNER JOIN user_roles ur ON u.role_id = ur.id AND ur.is_active = 1
        INNER JOIN role_permissions rp ON ur.id = rp.role_id
        INNER JOIN user_permissions up ON rp.permission_id = up.id AND up.is_active = 1
        WHERE u.id = ? AND u.deleted_at IS NULL
        ORDER BY up.sort_order
      `;

      console.log('🔍 [PermissionService] 执行权限查询SQL:', {
        userId,
        sql: sql.replace(/\s+/g, ' ').trim(),
        timestamp: new Date().toISOString(),
      });

      const results = await this.databaseService.query(sql, [userId]);
      
      console.log('📊 [PermissionService] SQL查询原始结果:', {
        userId,
        resultCount: results.length,
        results,
        timestamp: new Date().toISOString(),
      });

      const permissions = results.map((row: { code: string }) => row.code);

      console.log('✅ [PermissionService] getUserPermissions 查询成功:', {
        userId,
        permissionCount: permissions.length,
        permissions,
        timestamp: new Date().toISOString(),
      });

      return permissions;
    } catch (error) {
      console.error('❌ [PermissionService] getUserPermissions 查询失败:', {
        userId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
      
      // 发生错误时返回空权限数组，不返回默认权限
      return [];
    }
  }

  /**
   * 检查单个权限
   * @param userPermissions 用户权限列表
   * @param requiredPermission 需要检查的权限
   * @param context 上下文信息
   * @returns 是否拥有权限
   */
  private checkSinglePermission(userPermissions: string[], requiredPermission: string, _context?: Record<string, unknown>): boolean {
    // 超级管理员权限
    if (userPermissions.includes('*')) {
      return true;
    }

    // 直接匹配
    if (userPermissions.includes(requiredPermission)) {
      return true;
    }

    // 通配符匹配
    const [module, _action] = requiredPermission.split('.');
    const wildcardPermission = `${module}.*`;
    if (userPermissions.includes(wildcardPermission)) {
      return true;
    }

    // TODO: 实现基于条件的权限检查
    // 如果有context，可以进行更复杂的权限判断

    return false;
  }

  /**
   * 复制权限
   * @param id 源权限ID
   * @param currentUserId 当前用户ID
   * @returns 新创建的权限
   */
  async copyPermission(id: string, currentUserId: string): Promise<PermissionResponseDto> {
    const sourcePermission = await this.findById(id);
    if (!sourcePermission) {
      throw new NotFoundException('源权限不存在');
    }

    // 生成新的权限代码
    const baseCode = sourcePermission.code;
    let newCode = `${baseCode}_copy`;
    let counter = 1;

    // 确保新代码不重复
    while (await this.findByCode(newCode)) {
      newCode = `${baseCode}_copy_${counter}`;
      counter++;
    }

    const createDto: CreatePermissionDto = {
      code: newCode,
      name: `${sourcePermission.name} (副本)`,
      description: sourcePermission.description,
      module: sourcePermission.module,
      resource: sourcePermission.resource,
      action: sourcePermission.action,
      conditions: sourcePermission.conditions,
      is_active: false, // 默认设置为禁用状态
      sort_order: sourcePermission.sort_order,
    };

    return await this.createPermission(createDto, currentUserId);
  }

  /**
   * 切换权限状态
   * @param id 权限ID
   * @param currentUserId 当前用户ID
   * @returns 更新后的权限信息
   */
  async togglePermissionStatus(id: string, currentUserId: string): Promise<PermissionResponseDto> {
    const permission = await this.findById(id);
    if (!permission) {
      throw new NotFoundException('权限不存在');
    }

    return await this.updatePermission(id, { is_active: !permission.is_active }, currentUserId);
  }

  /**
   * 格式化权限响应数据
   * @param row 数据库行数据
   * @returns 格式化后的权限响应
   */
  private formatPermissionResponse(row: PermissionRowData): PermissionResponseDto {
    return {
      id: row.id,
      code: row.code,
      name: row.name,
      description: row.description,
      module: row.module,
      resource: row.resource,
      action: row.action,
      conditions: row.conditions ? JSON.parse(row.conditions) : null,
      is_active: Boolean(row.is_active),
      sort_order: row.sort_order,
      created_by: row.created_by,
      created_at: row.created_at,
      updated_by: row.updated_by,
      updated_at: row.updated_at,
      role_count: row.role_count || 0,
      user_count: row.user_count || 0,
    };
  }
} 