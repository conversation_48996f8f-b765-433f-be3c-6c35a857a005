import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import router from './router'
import App from './App.vue'
import { setupPermissionGuard } from './router/permission'
import { permissionDirective } from './composables/usePermissions'

// 导入样式
import 'ant-design-vue/dist/reset.css'
import './styles/index.css'

const app = createApp(App)

// 使用Pinia状态管理
app.use(createPinia())

// 使用Vue Router
app.use(router)

// 使用Ant Design Vue
app.use(Antd)

// 注册权限指令
app.directive('permission', permissionDirective)

// 设置路由权限守卫
setupPermissionGuard(router)

app.mount('#app')
