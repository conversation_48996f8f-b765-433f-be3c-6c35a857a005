<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档管理 - 紧凑型设计 (版本D)</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: #fafbfc;
      color: #1a1a1a;
      line-height: 1.4;
    }

    .container {
      max-width: 1600px;
      margin: 0 auto;
      padding: 16px;
    }

    /* 紧凑型页面头部 */
    .page-header {
      background: white;
      border-radius: 10px;
      padding: 20px 24px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid #e8eaed;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .title-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .page-title {
      font-size: 22px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0;
    }

    .doc-count {
      background: #f0f1f3;
      color: #5f6368;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      text-decoration: none;
    }

    .btn-primary {
      background: #1a73e8;
      color: white;
    }

    .btn-primary:hover {
      background: #1557b0;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: #f8f9fa;
      color: #5f6368;
      border: 1px solid #dadce0;
    }

    .btn-secondary:hover {
      background: #f1f3f4;
      border-color: #c5c7ca;
    }

    /* 工具栏 */
    .toolbar {
      background: white;
      border-radius: 10px;
      padding: 16px 20px;
      margin-bottom: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid #e8eaed;
    }

    .toolbar-content {
      display: grid;
      grid-template-columns: 1fr auto auto;
      gap: 16px;
      align-items: center;
    }

    .search-section {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .search-box {
      position: relative;
      flex: 1;
      max-width: 320px;
    }

    .search-input {
      width: 100%;
      padding: 8px 12px 8px 36px;
      border: 1px solid #dadce0;
      border-radius: 20px;
      font-size: 14px;
      background: #f8f9fa;
      transition: all 0.2s ease;
    }

    .search-input:focus {
      outline: none;
      border-color: #1a73e8;
      background: white;
      box-shadow: 0 1px 6px rgba(26, 115, 232, 0.2);
    }

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #5f6368;
      font-size: 14px;
    }

    .filter-group {
      display: flex;
      gap: 6px;
    }

    .filter-select {
      padding: 6px 12px;
      border: 1px solid #dadce0;
      border-radius: 6px;
      font-size: 13px;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .filter-select:hover {
      border-color: #1a73e8;
    }

    .view-controls {
      display: flex;
      gap: 4px;
    }

    .view-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #dadce0;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .view-btn.active {
      background: #1a73e8;
      color: white;
      border-color: #1a73e8;
    }

    .view-btn:hover:not(.active) {
      background: #f8f9fa;
    }

    /* 紧凑型表格容器 */
    .table-container {
      background: white;
      border-radius: 10px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid #e8eaed;
      overflow: hidden;
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #e8eaed;
      font-size: 13px;
    }

    .selection-info {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #5f6368;
    }

    .selection-count {
      color: #1a73e8;
      font-weight: 500;
    }

    .bulk-actions {
      display: flex;
      gap: 6px;
    }

    .bulk-btn {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .bulk-btn.download {
      background: #e8f0fe;
      color: #1a73e8;
    }

    .bulk-btn.delete {
      background: #fce8e6;
      color: #d93025;
    }

    /* 数据表格 */
    .data-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 13px;
    }

    .data-table th {
      background: #f8f9fa;
      padding: 10px 16px;
      text-align: left;
      font-weight: 600;
      color: #3c4043;
      border-bottom: 1px solid #e8eaed;
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .data-table th:first-child {
      width: 40px;
      padding-left: 20px;
    }

    .data-table td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      vertical-align: middle;
    }

    .data-table td:first-child {
      padding-left: 20px;
    }

    .data-table tbody tr:hover {
      background: #f8f9fa;
    }

    .data-table tbody tr.selected {
      background: rgba(26, 115, 232, 0.04);
    }

    /* 列宽优化 */
    .col-checkbox { width: 40px; }
    .col-name { min-width: 280px; }
    .col-type { width: 80px; }
    .col-version { width: 60px; }
    .col-status { width: 90px; }
    .col-size { width: 80px; }
    .col-creator { width: 100px; }
    .col-modified { width: 120px; }
    .col-actions { width: 160px; }

    /* 文档信息优化 */
    .document-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .doc-icon {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: white;
      font-weight: 600;
      flex-shrink: 0;
    }

    .doc-icon.word { background: #1a73e8; }
    .doc-icon.excel { background: #0f9d58; }
    .doc-icon.ppt { background: #ff6d01; }
    .doc-icon.pdf { background: #d93025; }

    .doc-details {
      min-width: 0;
    }

    .doc-name {
      font-weight: 500;
      color: #1a1a1a;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .doc-path {
      font-size: 11px;
      color: #5f6368;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    /* 状态标签 */
    .status-badge {
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-badge.published {
      background: #e6f4ea;
      color: #137333;
    }

    .status-badge.draft {
      background: #fef7e0;
      color: #b06000;
    }

    .status-badge.archived {
      background: #f3f4f6;
      color: #5f6368;
    }

    /* 类型标签 */
    .type-badge {
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .type-badge.word {
      background: #e8f0fe;
      color: #1a73e8;
    }

    .type-badge.excel {
      background: #e6f4ea;
      color: #137333;
    }

    .type-badge.ppt {
      background: #fef7e0;
      color: #b06000;
    }

    /* 版本信息 */
    .version-info {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .version-badge {
      background: #f8f9fa;
      color: #5f6368;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
    }

    .version-btn {
      width: 20px;
      height: 20px;
      border: none;
      border-radius: 4px;
      background: #f8f9fa;
      color: #5f6368;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      transition: all 0.2s ease;
    }

    .version-btn:hover {
      background: #e8eaed;
    }

    /* 紧凑型操作按钮 */
    .action-buttons {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .action-btn {
      padding: 6px 10px;
      border: none;
      border-radius: 5px;
      font-size: 11px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .action-btn.primary {
      background: #1a73e8;
      color: white;
    }

    .action-btn.primary:hover {
      background: #1557b0;
      transform: translateY(-1px);
    }

    .action-btn.secondary {
      background: #f8f9fa;
      color: #5f6368;
      border: 1px solid #dadce0;
    }

    .action-btn.secondary:hover {
      background: #f1f3f4;
    }

    .config-select {
      padding: 4px 6px;
      border: 1px solid #dadce0;
      border-radius: 4px;
      font-size: 10px;
      background: white;
      cursor: pointer;
      min-width: 60px;
    }

    /* 时间显示 */
    .time-info {
      font-size: 12px;
      color: #5f6368;
    }

    /* 分页区域 */
    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      background: #f8f9fa;
      border-top: 1px solid #e8eaed;
      font-size: 13px;
    }

    .pagination-info {
      color: #5f6368;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .pagination-btn {
      padding: 6px 10px;
      border: 1px solid #dadce0;
      border-radius: 4px;
      background: white;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
    }

    .pagination-btn:hover {
      background: #f8f9fa;
    }

    .pagination-btn.active {
      background: #1a73e8;
      color: white;
      border-color: #1a73e8;
    }

    /* 复选框 */
    .checkbox {
      width: 16px;
      height: 16px;
      border: 1.5px solid #dadce0;
      border-radius: 3px;
      cursor: pointer;
      position: relative;
      transition: all 0.2s ease;
    }

    .checkbox:checked {
      background: #1a73e8;
      border-color: #1a73e8;
    }

    .checkbox:checked::after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 1px;
      color: white;
      font-size: 11px;
      font-weight: bold;
    }

    /* 响应式 */
    @media (max-width: 1200px) {
      .col-creator { display: none; }
      .col-actions { width: 140px; }
    }

    @media (max-width: 768px) {
      .toolbar-content {
        grid-template-columns: 1fr;
        gap: 12px;
      }
      
      .header-actions {
        flex-direction: column;
        gap: 8px;
      }
      
      .col-size,
      .col-version { display: none; }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">文档管理</h1>
          <span class="doc-count">126 个文档</span>
        </div>
        <div class="header-actions">
          <a href="#" class="btn btn-secondary">
            <span>📋</span>
            从模板创建
          </a>
          <a href="#" class="btn btn-primary">
            <span>⬆️</span>
            上传文档
          </a>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-content">
        <div class="search-section">
          <div class="search-box">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" placeholder="搜索文档名称、内容...">
          </div>
          <div class="filter-group">
            <select class="filter-select">
              <option>全部类型</option>
              <option>Word文档</option>
              <option>Excel表格</option>
              <option>PowerPoint</option>
            </select>
            <select class="filter-select">
              <option>全部状态</option>
              <option>已发布</option>
              <option>草稿</option>
              <option>已归档</option>
            </select>
          </div>
        </div>

        <div>
          <button class="btn btn-secondary">
            <span>🔄</span>
            刷新
          </button>
        </div>

        <div class="view-controls">
          <button class="view-btn active" title="表格视图">📊</button>
          <button class="view-btn" title="卡片视图">🔳</button>
          <button class="view-btn" title="列表视图">📋</button>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="table-header">
        <div class="selection-info">
          <span>已选择 <span class="selection-count">0</span> 个文档</span>
        </div>
        <div class="bulk-actions">
          <button class="bulk-btn download">📥 批量下载</button>
          <button class="bulk-btn delete">🗑️ 删除</button>
        </div>
      </div>

      <table class="data-table">
        <thead>
          <tr>
            <th class="col-checkbox">
              <input type="checkbox" class="checkbox">
            </th>
            <th class="col-name">文档名称</th>
            <th class="col-type">类型</th>
            <th class="col-version">版本</th>
            <th class="col-status">状态</th>
            <th class="col-size">大小</th>
            <th class="col-creator">创建者</th>
            <th class="col-modified">更新时间</th>
            <th class="col-actions">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <input type="checkbox" class="checkbox">
            </td>
            <td>
              <div class="document-info">
                <div class="doc-icon word">W</div>
                <div class="doc-details">
                  <div class="doc-name">张艺璐数学错题集(四年级下).docx</div>
                  <div class="doc-path">/文档/教育/数学</div>
                </div>
              </div>
            </td>
            <td>
              <span class="type-badge word">Word</span>
            </td>
            <td>
              <div class="version-info">
                <span class="version-badge">v1</span>
                <button class="version-btn">⏰</button>
              </div>
            </td>
            <td>
              <span class="status-badge published">已发布</span>
            </td>
            <td>
              <span class="time-info">1.69 MB</span>
            </td>
            <td>
              <span class="time-info">onlyoffice-system</span>
            </td>
            <td>
              <span class="time-info">1天前</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="action-btn primary">内嵌</button>
                <button class="action-btn secondary">新窗</button>
                <select class="config-select">
                  <option>默认</option>
                  <option>只读</option>
                  <option>协作</option>
                </select>
              </div>
            </td>
          </tr>

          <tr>
            <td>
              <input type="checkbox" class="checkbox">
            </td>
            <td>
              <div class="document-info">
                <div class="doc-icon word">W</div>
                <div class="doc-details">
                  <div class="doc-name">poems_answer.docx</div>
                  <div class="doc-path">/文档/文学</div>
                </div>
              </div>
            </td>
            <td>
              <span class="type-badge word">Word</span>
            </td>
            <td>
              <div class="version-info">
                <span class="version-badge">v1</span>
                <button class="version-btn">⏰</button>
              </div>
            </td>
            <td>
              <span class="status-badge published">已发布</span>
            </td>
            <td>
              <span class="time-info">46.18 KB</span>
            </td>
            <td>
              <span class="time-info">onlyoffice-system</span>
            </td>
            <td>
              <span class="time-info">2天前</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="action-btn primary">内嵌</button>
                <button class="action-btn secondary">新窗</button>
                <select class="config-select">
                  <option>默认</option>
                  <option>只读</option>
                  <option>协作</option>
                </select>
              </div>
            </td>
          </tr>

          <tr>
            <td>
              <input type="checkbox" class="checkbox">
            </td>
            <td>
              <div class="document-info">
                <div class="doc-icon excel">E</div>
                <div class="doc-details">
                  <div class="doc-name">财务报表_2024Q4.xlsx</div>
                  <div class="doc-path">/财务/季度报表</div>
                </div>
              </div>
            </td>
            <td>
              <span class="type-badge excel">Excel</span>
            </td>
            <td>
              <div class="version-info">
                <span class="version-badge">v3</span>
                <button class="version-btn">⏰</button>
              </div>
            </td>
            <td>
              <span class="status-badge draft">草稿</span>
            </td>
            <td>
              <span class="time-info">2.3 MB</span>
            </td>
            <td>
              <span class="time-info">张三</span>
            </td>
            <td>
              <span class="time-info">3小时前</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="action-btn primary">内嵌</button>
                <button class="action-btn secondary">新窗</button>
                <select class="config-select">
                  <option>默认</option>
                  <option>只读</option>
                  <option>协作</option>
                </select>
              </div>
            </td>
          </tr>

          <tr>
            <td>
              <input type="checkbox" class="checkbox">
            </td>
            <td>
              <div class="document-info">
                <div class="doc-icon ppt">P</div>
                <div class="doc-details">
                  <div class="doc-name">产品发布会演示.pptx</div>
                  <div class="doc-path">/营销/演示文稿</div>
                </div>
              </div>
            </td>
            <td>
              <span class="type-badge ppt">PPT</span>
            </td>
            <td>
              <div class="version-info">
                <span class="version-badge">v2</span>
                <button class="version-btn">⏰</button>
              </div>
            </td>
            <td>
              <span class="status-badge published">已发布</span>
            </td>
            <td>
              <span class="time-info">8.7 MB</span>
            </td>
            <td>
              <span class="time-info">李四</span>
            </td>
            <td>
              <span class="time-info">1周前</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="action-btn primary">内嵌</button>
                <button class="action-btn secondary">新窗</button>
                <select class="config-select">
                  <option>默认</option>
                  <option>只读</option>
                  <option>协作</option>
                </select>
              </div>
            </td>
          </tr>

          <tr>
            <td>
              <input type="checkbox" class="checkbox">
            </td>
            <td>
              <div class="document-info">
                <div class="doc-icon pdf">P</div>
                <div class="doc-details">
                  <div class="doc-name">合同模板_标准版.pdf</div>
                  <div class="doc-path">/法务/合同模板</div>
                </div>
              </div>
            </td>
            <td>
              <span class="type-badge pdf">PDF</span>
            </td>
            <td>
              <div class="version-info">
                <span class="version-badge">v1</span>
                <button class="version-btn">⏰</button>
              </div>
            </td>
            <td>
              <span class="status-badge archived">已归档</span>
            </td>
            <td>
              <span class="time-info">1.2 MB</span>
            </td>
            <td>
              <span class="time-info">王五</span>
            </td>
            <td>
              <span class="time-info">2周前</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="action-btn secondary">查看</button>
                <button class="action-btn secondary">下载</button>
                <select class="config-select">
                  <option>只读</option>
                </select>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="pagination">
        <div class="pagination-info">
          显示 1-20 条，共 126 个文档
        </div>
        <div class="pagination-controls">
          <button class="pagination-btn" disabled>上一页</button>
          <button class="pagination-btn active">1</button>
          <button class="pagination-btn">2</button>
          <button class="pagination-btn">3</button>
          <button class="pagination-btn">...</button>
          <button class="pagination-btn">7</button>
          <button class="pagination-btn">下一页</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 简单的交互功能
    document.addEventListener('DOMContentLoaded', function() {
      // 全选功能
      const masterCheckbox = document.querySelector('thead .checkbox');
      const rowCheckboxes = document.querySelectorAll('tbody .checkbox');
      const selectionCount = document.querySelector('.selection-count');

      masterCheckbox.addEventListener('change', function() {
        rowCheckboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
        });
        updateSelectionCount();
      });

      rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectionCount);
      });

      function updateSelectionCount() {
        const checkedCount = Array.from(rowCheckboxes).filter(cb => cb.checked).length;
        selectionCount.textContent = checkedCount;
        masterCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
        masterCheckbox.checked = checkedCount === rowCheckboxes.length;
      }

      // 视图切换
      const viewButtons = document.querySelectorAll('.view-btn');
      viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
          viewButtons.forEach(b => b.classList.remove('active'));
          this.classList.add('active');
        });
      });
    });
  </script>
</body>
</html> 