import {
  Controller,
  Get,
  Put,
  Body,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtConfigService } from '../services/jwt-config.service';
import { IsString, IsOptional, IsBoolean } from 'class-validator';

/**
 * JWT配置更新DTO
 */
export class UpdateOnlyOfficeJwtDto {
  @IsOptional()
  @IsString()
  secret?: string;

  @IsOptional()
  @IsString()
  header?: string;

  @IsOptional()
  @IsBoolean()
  inBody?: boolean;
}

/**
 * JWT配置管理控制器
 * @description 提供JWT配置的查看和管理功能
 */
@ApiTags('JWT配置管理')
@Controller('jwt-config')
// @UseGuards(JwtAuthGuard)  // 暂时关闭身份验证以便测试
// @ApiBearerAuth()
export class JwtConfigController {
  constructor(
    private readonly jwtConfigService: JwtConfigService,
  ) {}

  /**
   * 获取JWT配置状态
   */
  @Get('status')
  @ApiOperation({ summary: '获取JWT配置状态' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取JWT配置状态成功',
  })
  async getJwtConfigStatus() {
    const status = await this.jwtConfigService.getJwtConfigStatus();
    return {
      success: true,
      data: status,
    };
  }

  /**
   * 获取API JWT配置（不包含密钥）
   */
  @Get('api-jwt')
  @ApiOperation({ summary: '获取API JWT配置信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取API JWT配置成功',
  })
  async getApiJwtConfig() {
    const config = this.jwtConfigService.getApiJwtConfig();
    
    // 不返回实际密钥，只返回配置信息
    return {
      success: true,
      data: {
        expiresIn: config.expiresIn,
        algorithm: config.algorithm,
        issuer: config.issuer,
        audience: config.audience,
        secretConfigured: !!config.secret && config.secret !== 'default-api-secret',
        secretSource: 'environment',
      },
    };
  }

  /**
   * 获取OnlyOffice JWT配置（不包含密钥）
   */
  @Get('onlyoffice-jwt')
  @ApiOperation({ summary: '获取OnlyOffice JWT配置信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取OnlyOffice JWT配置成功',
  })
  async getOnlyOfficeJwtConfig() {
    const config = await this.jwtConfigService.getOnlyOfficeJwtConfig();
    
    // 不返回实际密钥，只返回配置信息
    return {
      success: true,
      data: {
        header: config.header,
        inBody: config.inBody,
        algorithm: config.algorithm,
        secretConfigured: !!config.secret && config.secret !== 'default-onlyoffice-secret',
        secretSource: 'database',
      },
    };
  }

  /**
   * 验证JWT配置
   */
  @Get('validate')
  @ApiOperation({ summary: '验证JWT配置完整性' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'JWT配置验证完成',
  })
  async validateJwtConfig() {
    const validation = await this.jwtConfigService.validateJwtConfig();
    return {
      success: true,
      data: validation,
    };
  }

  /**
   * 生成推荐的JWT密钥
   */
  @Get('generate-secret')
  @ApiOperation({ summary: '生成推荐的JWT密钥' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '生成JWT密钥成功',
  })
  async generateJwtSecret() {
    const apiSecret = this.jwtConfigService.generateRecommendedJwtSecret('API');
    const onlyofficeSecret = this.jwtConfigService.generateRecommendedJwtSecret('OOD');
    
    return {
      success: true,
      data: {
        apiSecret,
        onlyofficeSecret,
        note: '这些是推荐的密钥，请妥善保管。API密钥需要在环境变量中配置，OnlyOffice密钥可以通过API更新。',
      },
    };
  }

  /**
   * 更新OnlyOffice JWT密钥
   */
  @Put('onlyoffice-secret')
  @ApiOperation({ summary: '更新OnlyOffice JWT密钥' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OnlyOffice JWT密钥更新成功',
  })
  async updateOnlyOfficeJwtSecret(@Body('secret') secret: string) {
    if (!secret || secret.length < 32) {
      return {
        success: false,
        message: 'JWT密钥长度不足，建议至少32字符',
      };
    }

    await this.jwtConfigService.updateOnlyOfficeJwtSecret(secret);
    
    return {
      success: true,
      message: 'OnlyOffice JWT密钥更新成功',
    };
  }

  /**
   * 更新OnlyOffice JWT配置
   */
  @Put('onlyoffice-config')
  @ApiOperation({ summary: '更新OnlyOffice JWT配置' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OnlyOffice JWT配置更新成功',
  })
  async updateOnlyOfficeJwtConfig(@Body() updateDto: UpdateOnlyOfficeJwtDto) {
    // 验证密钥长度
    if (updateDto.secret && updateDto.secret.length < 32) {
      return {
        success: false,
        message: 'JWT密钥长度不足，建议至少32字符',
      };
    }

    await this.jwtConfigService.updateOnlyOfficeJwtConfig(updateDto);
    
    return {
      success: true,
      message: 'OnlyOffice JWT配置更新成功',
    };
  }

  /**
   * 获取JWT配置概览
   */
  @Get('overview')
  @ApiOperation({ summary: '获取JWT配置概览' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取JWT配置概览成功',
  })
  async getJwtConfigOverview() {
    const [status, validation] = await Promise.all([
      this.jwtConfigService.getJwtConfigStatus(),
      this.jwtConfigService.validateJwtConfig(),
    ]);

    const apiConfig = this.jwtConfigService.getApiJwtConfig();
    const onlyofficeConfig = await this.jwtConfigService.getOnlyOfficeJwtConfig();

    return {
      success: true,
      data: {
        status,
        validation,
        summary: {
          apiJwt: {
            configured: status.apiJwt.configured,
            source: status.apiJwt.source,
            expiresIn: apiConfig.expiresIn,
          },
          onlyofficeJwt: {
            configured: status.onlyofficeJwt.configured,
            source: status.onlyofficeJwt.source,
            header: onlyofficeConfig.header,
            inBody: onlyofficeConfig.inBody,
          },
        },
        recommendations: validation.valid ? [] : [
          '建议定期更换JWT密钥',
          '确保API JWT和OnlyOffice JWT使用不同的密钥',
          '生产环境建议使用至少64字符的强密钥',
        ],
      },
    };
  }
} 