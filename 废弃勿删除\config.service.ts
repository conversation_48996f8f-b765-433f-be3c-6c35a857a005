import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { JwtConfigService } from './jwt-config.service';

/**
 * 配置接口定义
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  type: 'mysql' | 'postgresql';
  synchronize: boolean;
  logging: boolean;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

export interface OnlyOfficeConfig {
  documentServerUrl: string;
  callbackUrl: string;
  jwt: {
    secret: string;
    header: string;
  };
}

export interface AppConfig {
  port: number;
  environment: string;
  apiPrefix: string;
  corsOrigins: string[];
}

/**
 * 应用程序配置服务
 * 
 * 提供类型安全的配置访问接口
 * 封装所有环境变量的读取和默认值处理
 * 
 * @class ConfigService
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Injectable()
export class ConfigService {
  constructor(
    private readonly configService: NestConfigService,
    private readonly jwtConfigService: JwtConfigService,
  ) {}

  /**
   * 获取应用配置
   */
  get app(): AppConfig {
    return {
      port: this.configService.get<number>('PORT', 3000),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      apiPrefix: this.configService.get<string>('API_PREFIX', 'api'),
      corsOrigins: this.getCorsOrigins(),
    };
  }

  /**
   * 获取数据库配置
   */
  get database(): DatabaseConfig {
    return {
      host: this.configService.get<string>('DB_HOST', 'localhost'),
      port: this.configService.get<number>('DB_PORT', 3306),
      username: this.configService.get<string>('DB_USER', 'root'),
      password: this.configService.get<string>('DB_PASSWORD', ''),
      database: this.configService.get<string>('DB_NAME', 'onlyoffice'),
      type: this.configService.get<'mysql' | 'postgresql'>('DB_TYPE', 'mysql'),
      synchronize: this.configService.get<boolean>('DB_SYNC', false),
      logging: this.configService.get<boolean>('DB_LOGGING', false),
    };
  }

  /**
   * 获取API JWT配置 - 用于前后端认证
   */
  get jwt(): JwtConfig {
    const apiConfig = this.jwtConfigService.getApiJwtConfig();
    return {
      secret: apiConfig.secret,
      expiresIn: apiConfig.expiresIn,
      refreshExpiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
    };
  }

  /**
   * 获取OnlyOffice配置 - 现在从配置模板和数据库读取
   */
  async getOnlyOfficeConfig(): Promise<OnlyOfficeConfig> {
    const onlyofficeJwt = await this.jwtConfigService.getOnlyOfficeJwtConfig();
    
    return {
      documentServerUrl: this.configService.get<string>('ONLYOFFICE_DOCS_URL', 'http://localhost:80'),
      callbackUrl: this.configService.get<string>('ONLYOFFICE_CALLBACK_URL', 'http://localhost:3000/api/documents/callback'),
      jwt: {
        secret: onlyofficeJwt.secret,
        header: onlyofficeJwt.header,
      },
    };
  }

  /**
   * 检查是否为生产环境
   */
  get isProduction(): boolean {
    return this.app.environment === 'production';
  }

  /**
   * 检查是否为开发环境
   */
  get isDevelopment(): boolean {
    return this.app.environment === 'development';
  }

  /**
   * 获取原始配置值
   */
  get<T = any>(key: string, defaultValue?: T): T {
    return this.configService.get<T>(key, defaultValue);
  }

  /**
   * 获取CORS允许的源地址
   */
  private getCorsOrigins(): string[] {
    const origins = this.configService.get<string>('CORS_ORIGINS', '*');
    
    if (origins === '*') {
      return ['*'];
    }
    
    return origins.split(',').map(origin => origin.trim());
  }

  /**
   * 验证必需的配置项
   */
  validateRequiredConfig(): void {
    const requiredKeys = [
      'DB_HOST',
      'DB_USER', 
      'DB_PASSWORD',
      'DB_NAME',
      'JWT_SECRET',
    ];

    const missingKeys = requiredKeys.filter(key => !this.configService.get(key));
    
    if (missingKeys.length > 0) {
      throw new Error(`缺少必需的环境变量: ${missingKeys.join(', ')}`);
    }
  }
} 