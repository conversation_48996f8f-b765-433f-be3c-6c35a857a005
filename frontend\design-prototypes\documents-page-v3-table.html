<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档管理 - 密集表格风格 (版本C)</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f5f5f7;
      color: #1d1d1f;
      line-height: 1.5;
    }

    .container {
      max-width: 1800px;
      margin: 0 auto;
      padding: 20px;
    }

    /* 页面头部 */
    .page-header {
      background: white;
      border-radius: 8px;
      padding: 24px 32px;
      margin-bottom: 24px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e5e7;
    }

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #1d1d1f;
      margin-bottom: 4px;
    }

    .page-description {
      color: #86868b;
      font-size: 15px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;
      display: inline-flex;
      align-items: center;
      gap: 6px;
    }

    .btn-primary {
      background: #007aff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056cc;
    }

    .btn-secondary {
      background: #f2f2f7;
      color: #007aff;
      border: 1px solid #d1d1d6;
    }

    .btn-secondary:hover {
      background: #e5e5ea;
    }

    /* 工具栏 */
    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
    }

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
    }

    .search-box {
      position: relative;
      min-width: 300px;
    }

    .search-input {
      width: 100%;
      padding: 10px 16px 10px 40px;
      border: 1px solid #d1d1d6;
      border-radius: 6px;
      font-size: 14px;
      background: white;
      transition: border-color 0.2s ease;
    }

    .search-input:focus {
      outline: none;
      border-color: #007aff;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }

    .search-icon {
      position: absolute;
      left: 14px;
      top: 50%;
      transform: translateY(-50%);
      color: #86868b;
      font-size: 16px;
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .filter-select {
      padding: 8px 12px;
      border: 1px solid #d1d1d6;
      border-radius: 6px;
      background: white;
      font-size: 14px;
      cursor: pointer;
      min-width: 120px;
    }

    .filter-select:focus {
      outline: none;
      border-color: #007aff;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .view-options {
      display: flex;
      background: #f2f2f7;
      border-radius: 6px;
      padding: 2px;
    }

    .view-btn {
      padding: 6px 12px;
      border: none;
      background: transparent;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.2s ease;
    }

    .view-btn.active {
      background: white;
      color: #007aff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .bulk-actions {
      display: flex;
      gap: 8px;
    }

    /* 表格容器 */
    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e5e7;
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #e5e5e7;
      background: #fafafa;
    }

    .table-info {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;
      color: #86868b;
    }

    .selected-count {
      color: #007aff;
      font-weight: 500;
    }

    /* 数据表格 */
    .data-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .data-table th {
      background: #fafafa;
      padding: 12px 16px;
      text-align: left;
      font-weight: 600;
      color: #1d1d1f;
      border-bottom: 1px solid #e5e5e7;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .data-table th:first-child {
      width: 40px;
      padding-left: 24px;
    }

    .data-table td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      vertical-align: middle;
    }

    .data-table td:first-child {
      padding-left: 24px;
    }

    .data-table tr:hover {
      background: #f9f9f9;
    }

    .data-table tr.selected {
      background: rgba(0, 122, 255, 0.05);
    }

    /* 表格列样式 */
    .col-checkbox {
      width: 40px;
    }

    .col-name {
      min-width: 300px;
    }

    .col-type {
      width: 100px;
    }

    .col-size {
      width: 100px;
    }

    .col-modified {
      width: 150px;
    }

    .col-status {
      width: 120px;
    }

    .col-owner {
      width: 150px;
    }

    .col-actions {
      width: 120px;
    }

    /* 文档信息 */
    .doc-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .doc-icon {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      flex-shrink: 0;
    }

    .icon-word { background: #2b5797; color: white; }
    .icon-excel { background: #217346; color: white; }
    .icon-ppt { background: #d24726; color: white; }
    .icon-pdf { background: #dc3545; color: white; }

    .doc-details h4 {
      font-size: 14px;
      font-weight: 500;
      color: #1d1d1f;
      margin-bottom: 2px;
      line-height: 1.3;
    }

    .doc-path {
      font-size: 12px;
      color: #86868b;
      line-height: 1.3;
    }

    /* 类型标签 */
    .type-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .type-word { background: #e3f2fd; color: #1976d2; }
    .type-excel { background: #e8f5e8; color: #2e7d32; }
    .type-ppt { background: #fff3e0; color: #f57c00; }
    .type-pdf { background: #ffebee; color: #d32f2f; }

    /* 状态标签 */
    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      text-transform: capitalize;
    }

    .status-published { background: #d4edda; color: #155724; }
    .status-draft { background: #fff3cd; color: #856404; }
    .status-archived { background: #f8d7da; color: #721c24; }
    .status-review { background: #d1ecf1; color: #0c5460; }

    /* 用户信息 */
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .user-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: linear-gradient(135deg, #007aff, #34c759);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .user-name {
      font-size: 13px;
      color: #1d1d1f;
    }

    /* 操作按钮 */
    .actions {
      display: flex;
      gap: 4px;
    }

    .action-btn {
      width: 28px;
      height: 28px;
      border: none;
      border-radius: 4px;
      background: #f2f2f7;
      color: #86868b;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }

    .action-btn:hover {
      background: #007aff;
      color: white;
    }

    /* 分页 */
    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: #fafafa;
      border-top: 1px solid #e5e5e7;
      font-size: 14px;
    }

    .pagination-info {
      color: #86868b;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .pagination-btn {
      padding: 6px 12px;
      border: 1px solid #d1d1d6;
      border-radius: 4px;
      background: white;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.2s ease;
    }

    .pagination-btn:hover {
      background: #f2f2f7;
    }

    .pagination-btn.active {
      background: #007aff;
      color: white;
      border-color: #007aff;
    }

    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* 复选框样式 */
    .checkbox {
      width: 16px;
      height: 16px;
      border: 1.5px solid #d1d1d6;
      border-radius: 3px;
      cursor: pointer;
      position: relative;
      transition: all 0.2s ease;
    }

    .checkbox:checked {
      background: #007aff;
      border-color: #007aff;
    }

    .checkbox:checked::after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 1px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    /* 排序图标 */
    .sort-icon {
      display: inline-block;
      margin-left: 4px;
      opacity: 0.5;
      font-size: 10px;
    }

    .sortable:hover .sort-icon {
      opacity: 1;
    }

    .sorted-asc .sort-icon::after {
      content: '▲';
      color: #007aff;
    }

    .sorted-desc .sort-icon::after {
      content: '▼';
      color: #007aff;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .col-path { display: none; }
      .col-size { display: none; }
    }

    @media (max-width: 768px) {
      .container {
        padding: 10px;
      }
      
      .toolbar {
        flex-direction: column;
        align-items: stretch;
      }
      
      .search-box {
        min-width: auto;
      }
      
      .col-owner { display: none; }
      .col-modified { display: none; }
      
      .data-table th,
      .data-table td {
        padding: 8px 12px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-top">
        <div>
          <h1 class="page-title">📁 文档管理</h1>
          <p class="page-description">高效管理您的文档资源，支持多种格式和协作编辑</p>
        </div>
        <div class="header-actions">
          <button class="btn btn-secondary">📊 报表导出</button>
          <button class="btn btn-secondary">📤 批量操作</button>
          <button class="btn btn-primary">➕ 新建文档</button>
        </div>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <div class="search-box">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" placeholder="搜索文档名称、内容、标签...">
          </div>
          <div class="filter-section">
            <select class="filter-select">
              <option>全部类型</option>
              <option>Word文档</option>
              <option>Excel表格</option>
              <option>PowerPoint</option>
              <option>PDF文件</option>
            </select>
            <select class="filter-select">
              <option>全部状态</option>
              <option>已发布</option>
              <option>草稿</option>
              <option>审核中</option>
              <option>已归档</option>
            </select>
            <select class="filter-select">
              <option>全部时间</option>
              <option>今天</option>
              <option>本周</option>
              <option>本月</option>
              <option>本年</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <div class="view-options">
            <button class="view-btn active">📋 表格</button>
            <button class="view-btn">🔲 卡片</button>
            <button class="view-btn">📊 看板</button>
          </div>
          <div class="bulk-actions">
            <button class="btn btn-secondary" disabled>批量删除</button>
            <button class="btn btn-secondary" disabled>批量移动</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-info">
          <span>共 <strong>156</strong> 个文档</span>
          <span class="selected-count">已选择 0 项</span>
        </div>
        <div>
          <button class="btn btn-secondary">🔄 刷新</button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div style="overflow-x: auto;">
        <table class="data-table">
          <thead>
            <tr>
              <th class="col-checkbox">
                <input type="checkbox" class="checkbox" id="select-all">
              </th>
              <th class="col-name sortable">
                文档名称
                <span class="sort-icon"></span>
              </th>
              <th class="col-type sortable">
                类型
                <span class="sort-icon"></span>
              </th>
              <th class="col-size sortable">
                大小
                <span class="sort-icon"></span>
              </th>
              <th class="col-modified sortable">
                修改时间
                <span class="sort-icon"></span>
              </th>
              <th class="col-status sortable">
                状态
                <span class="sort-icon"></span>
              </th>
              <th class="col-owner sortable">
                所有者
                <span class="sort-icon"></span>
              </th>
              <th class="col-actions">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-word">📄</div>
                  <div class="doc-details">
                    <h4>项目需求分析文档v2.0</h4>
                    <div class="doc-path">/projects/requirements/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-word">Word</span></td>
              <td>2.4 MB</td>
              <td>2小时前</td>
              <td><span class="status-badge status-published">已发布</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">张</div>
                  <span class="user-name">张三</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-excel">📊</div>
                  <div class="doc-details">
                    <h4>Q4财务报表汇总分析</h4>
                    <div class="doc-path">/finance/reports/2024/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-excel">Excel</span></td>
              <td>1.8 MB</td>
              <td>1天前</td>
              <td><span class="status-badge status-review">审核中</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">李</div>
                  <span class="user-name">李四</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-ppt">🎨</div>
                  <div class="doc-details">
                    <h4>产品发布会演示PPT</h4>
                    <div class="doc-path">/marketing/presentations/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-ppt">PPT</span></td>
              <td>15.2 MB</td>
              <td>3天前</td>
              <td><span class="status-badge status-published">已发布</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">王</div>
                  <span class="user-name">王五</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-pdf">📋</div>
                  <div class="doc-details">
                    <h4>用户操作手册v2.0</h4>
                    <div class="doc-path">/docs/manuals/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-pdf">PDF</span></td>
              <td>8.9 MB</td>
              <td>1周前</td>
              <td><span class="status-badge status-archived">已归档</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">赵</div>
                  <span class="user-name">赵六</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="下载">📥</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-word">📄</div>
                  <div class="doc-details">
                    <h4>会议纪要模板标准版</h4>
                    <div class="doc-path">/templates/meeting/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-word">Word</span></td>
              <td>456 KB</td>
              <td>2周前</td>
              <td><span class="status-badge status-published">已发布</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">孙</div>
                  <span class="user-name">孙七</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-excel">📊</div>
                  <div class="doc-details">
                    <h4>员工考勤统计月报</h4>
                    <div class="doc-path">/hr/attendance/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-excel">Excel</span></td>
              <td>3.2 MB</td>
              <td>3周前</td>
              <td><span class="status-badge status-draft">草稿</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">周</div>
                  <span class="user-name">周八</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-word">📄</div>
                  <div class="doc-details">
                    <h4>合同模板-劳动合同</h4>
                    <div class="doc-path">/legal/contracts/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-word">Word</span></td>
              <td>1.1 MB</td>
              <td>1个月前</td>
              <td><span class="status-badge status-published">已发布</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">吴</div>
                  <span class="user-name">吴九</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
            <tr>
              <td><input type="checkbox" class="checkbox"></td>
              <td class="col-name">
                <div class="doc-info">
                  <div class="doc-icon icon-ppt">🎨</div>
                  <div class="doc-details">
                    <h4>培训课件-新员工入职</h4>
                    <div class="doc-path">/training/onboarding/</div>
                  </div>
                </div>
              </td>
              <td><span class="type-badge type-ppt">PPT</span></td>
              <td>22.5 MB</td>
              <td>1个月前</td>
              <td><span class="status-badge status-review">审核中</span></td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">郑</div>
                  <span class="user-name">郑十</span>
                </div>
              </td>
              <td>
                <div class="actions">
                  <button class="action-btn" title="预览">👁️</button>
                  <button class="action-btn" title="编辑">✏️</button>
                  <button class="action-btn" title="分享">📤</button>
                  <button class="action-btn" title="更多">⋯</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <div class="pagination-info">
          显示第 1-10 项，共 156 项
        </div>
        <div class="pagination-controls">
          <button class="pagination-btn" disabled>← 上一页</button>
          <button class="pagination-btn active">1</button>
          <button class="pagination-btn">2</button>
          <button class="pagination-btn">3</button>
          <button class="pagination-btn">4</button>
          <button class="pagination-btn">5</button>
          <span>...</span>
          <button class="pagination-btn">16</button>
          <button class="pagination-btn">下一页 →</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全选功能
    document.getElementById('select-all').addEventListener('change', function() {
      const checkboxes = document.querySelectorAll('tbody .checkbox');
      checkboxes.forEach(cb => cb.checked = this.checked);
      updateSelectedCount();
    });

    // 单选功能
    document.querySelectorAll('tbody .checkbox').forEach(cb => {
      cb.addEventListener('change', function() {
        updateSelectedCount();
        updateSelectAll();
      });
    });

    // 更新选中数量
    function updateSelectedCount() {
      const selected = document.querySelectorAll('tbody .checkbox:checked').length;
      document.querySelector('.selected-count').textContent = `已选择 ${selected} 项`;
      
      // 更新批量操作按钮状态
      const bulkBtns = document.querySelectorAll('.bulk-actions .btn');
      bulkBtns.forEach(btn => {
        btn.disabled = selected === 0;
      });
    }

    // 更新全选状态
    function updateSelectAll() {
      const total = document.querySelectorAll('tbody .checkbox').length;
      const selected = document.querySelectorAll('tbody .checkbox:checked').length;
      const selectAll = document.getElementById('select-all');
      
      selectAll.checked = selected === total && total > 0;
      selectAll.indeterminate = selected > 0 && selected < total;
    }

    // 排序功能
    document.querySelectorAll('.sortable').forEach(th => {
      th.addEventListener('click', function() {
        const column = this.textContent.trim();
        console.log('排序列:', column);
        
        // 清除其他列的排序状态
        document.querySelectorAll('.sortable').forEach(t => {
          t.classList.remove('sorted-asc', 'sorted-desc');
        });
        
        // 切换当前列的排序状态
        if (this.classList.contains('sorted-asc')) {
          this.classList.remove('sorted-asc');
          this.classList.add('sorted-desc');
        } else {
          this.classList.add('sorted-asc');
        }
      });
    });

    // 视图切换
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        console.log('切换视图:', this.textContent);
      });
    });

    // 搜索功能
    document.querySelector('.search-input').addEventListener('input', function() {
      console.log('搜索:', this.value);
    });

    // 筛选功能
    document.querySelectorAll('.filter-select').forEach(select => {
      select.addEventListener('change', function() {
        console.log('筛选:', this.value);
      });
    });

    // 操作按钮
    document.querySelectorAll('.action-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        console.log('操作:', this.title);
      });
    });

    // 分页功能
    document.querySelectorAll('.pagination-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        if (!this.disabled) {
          document.querySelectorAll('.pagination-btn').forEach(b => b.classList.remove('active'));
          if (!isNaN(this.textContent)) {
            this.classList.add('active');
          }
          console.log('跳转页面:', this.textContent);
        }
      });
    });
  </script>
</body>
</html> 