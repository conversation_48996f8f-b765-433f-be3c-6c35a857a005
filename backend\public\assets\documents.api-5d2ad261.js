import{A as e}from"./index-5218909a.js";class a{static async getDocuments(t){return e.get("/documents",{params:t})}static async getDocumentById(t){return e.get(`/documents/${t}`)}static async createDocument(t){return e.post("/documents",t)}static async updateDocument(t,c){return e.put(`/documents/${t}`,c)}static async deleteDocument(t){return e.delete(`/documents/${t}`)}static async getDocumentConfig(t){return e.get(`/documents/${t}/config`)}static async downloadDocument(t,c){return e.download(`/documents/${t}/download`,c)}static async handleCallback(t){return e.post("/documents/callback",t)}static async searchDocuments(t){return e.get("/documents/search",{params:t})}static async getDocumentStats(){return e.get("/documents/stats")}static async batchDeleteDocuments(t){return e.post("/documents/batch-delete",{ids:t})}static async shareDocument(t,c){return e.post(`/documents/${t}/share`,c)}}export{a as D};
