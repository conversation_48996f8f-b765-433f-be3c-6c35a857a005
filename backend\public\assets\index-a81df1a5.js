import{j as n,k as me,d as Je,r as m,U as le,z as He,o as Qe,q as v,c as T,b,s,e as o,F as ie,g as re,v as k,T as ce,t as $,m as f,h,x as B,i as D,P as We,V as Ge,n as X,w as Xe,Q as Ye,B as Ze,C as Ke,W as ue,_ as et}from"./index-5218909a.js";import tt from"./ConfigDetail-944d09ef.js";import{S as nt}from"./SaveOutlined-064bb5bd.js";import{S as at}from"./StarOutlined-b7b083e4.js";import{M as ot}from"./MoreOutlined-59e0773e.js";var st={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 240H144c-17.7 0-32 14.3-32 32v608c0 17.7 14.3 32 32 32h608c17.7 0 32-14.3 32-32V272c0-17.7-14.3-32-32-32zm-40 600H184V312h528v528zm168-728H264c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h576v576c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V144c0-17.7-14.3-32-32-32zM300 550h296v64H300z"}}]},name:"switcher",theme:"outlined"};const lt=st;function de(p){for(var r=1;r<arguments.length;r++){var i=arguments[r]!=null?Object(arguments[r]):{},_=Object.keys(i);typeof Object.getOwnPropertySymbols=="function"&&(_=_.concat(Object.getOwnPropertySymbols(i).filter(function(w){return Object.getOwnPropertyDescriptor(i,w).enumerable}))),_.forEach(function(w){it(p,w,i[w])})}return p}function it(p,r,i){return r in p?Object.defineProperty(p,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):p[r]=i,p}var Y=function(r,i){var _=de({},r,i.attrs);return n(me,de({},_,{icon:lt}),null)};Y.displayName="SwitcherOutlined";Y.inheritAttrs=!1;const rt=Y;var ct={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"}}]},name:"undo",theme:"outlined"};const ut=ct;function fe(p){for(var r=1;r<arguments.length;r++){var i=arguments[r]!=null?Object(arguments[r]):{},_=Object.keys(i);typeof Object.getOwnPropertySymbols=="function"&&(_=_.concat(Object.getOwnPropertySymbols(i).filter(function(w){return Object.getOwnPropertyDescriptor(i,w).enumerable}))),_.forEach(function(w){dt(p,w,i[w])})}return p}function dt(p,r,i){return r in p?Object.defineProperty(p,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):p[r]=i,p}var Z=function(r,i){var _=fe({},r,i.attrs);return n(me,fe({},_,{icon:ut}),null)};Z.displayName="UndoOutlined";Z.inheritAttrs=!1;const ft=Z,mt={class:"onlyoffice-config-page"},vt={class:"content-wrapper"},pt={class:"main-layout"},gt={class:"templates-section"},_t={class:"section-header"},yt={class:"header-controls"},bt={class:"search-container"},ht={class:"templates-list"},wt=["onClick"],kt={class:"template-header"},Ot={class:"template-name"},Ct={key:0,class:"disabled-label"},St={class:"template-status"},Tt={key:0,class:"status-badge status-default"},xt={class:"template-desc"},$t={class:"template-meta"},Dt={class:"action-btn",title:"更多操作"},Ut={key:0,class:"empty-state"},At={class:"empty-icon"},Pt={class:"config-section"},jt={key:0,class:"empty-config"},Nt={class:"empty-config-content"},It={key:1,class:"config-panel"},Lt={class:"config-header"},Mt={class:"config-title"},Rt={class:"config-nav"},Et=["onClick"],Ft={class:"import-section"},zt={class:"ant-upload-drag-icon"},Vt={class:"duplicate-template-form"},qt=Je({__name:"index",setup(p){const r=m(!1),i=m(!1),_=m(""),w=m(!1),U=m([]),I=m(""),O=m(null),z=m(void 0),L=m(void 0),M=m(!1),V=m(null),J=m(),H=m(!1),ve=m(!1),K=m([]),pe=m(null),R=m(!1),Q=m(),A=m(null),q=m("permissions"),ge=m([{key:"permissions",label:"权限"},{key:"customization",label:"界面"},{key:"features",label:"功能"},{key:"coEditing",label:"协作"},{key:"user",label:"用户"},{key:"layout",label:"布局"},{key:"review",label:"审阅"},{key:"server",label:"服务器"},{key:"mobile",label:"移动端"},{key:"customer",label:"客户"}]),y=le({name:"",description:"",isDefault:!1,isActive:!0}),C=le({name:"",description:""}),_e={name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],description:[{required:!0,message:"请输入模板描述",trigger:"blur"}]},ye={name:[{required:!0,message:"请输入模板名称",trigger:"blur"},{validator:(t,e)=>!e||!e.trim()?Promise.reject("模板名称不能为空"):U.value.map(u=>u.name).includes(e.trim())?Promise.reject("模板名称已存在，请选择其他名称"):Promise.resolve(),trigger:"blur"}],description:[{required:!1}]},ee=He(()=>{let t=U.value;return w.value||(t=t.filter(e=>e.isActive)),_.value&&(t=t.filter(e=>e.name.toLowerCase().includes(_.value.toLowerCase())||e.description.toLowerCase().includes(_.value.toLowerCase()))),t}),x=async(t,e)=>{try{const l=await fetch(`http://*************:3000/api${t}`,{headers:{"Content-Type":"application/json",...e==null?void 0:e.headers},...e});if(!l.ok)throw new Error(`HTTP error! status: ${l.status}`);const u=await l.json();if(!u.success)throw new Error(u.message||"API请求失败");return u}catch(l){throw console.error("API调用失败:",l),l}},P=async()=>{try{r.value=!0;const t=await x("/config-templates");U.value=t.data||[],U.value.length>0&&!I.value&&await W(U.value[0])}catch(t){console.error("加载模板列表失败:",t),f.error("加载模板列表失败")}finally{r.value=!1}},E=async t=>{try{return(await x(`/config-templates/${t}`)).data}catch(e){return console.error("加载模板配置失败:",e),f.error("加载模板配置失败"),null}},W=async t=>{I.value=t.id,O.value=t;const e=await E(t.id);e&&(z.value=e.config,L.value=e.configStates)},be=()=>{V.value=null,Object.assign(y,{name:"",description:"",isDefault:!1,isActive:!0}),M.value=!0},he=async()=>{var t;try{await((t=J.value)==null?void 0:t.validate()),i.value=!0;const e={name:y.name,description:y.description,isDefault:y.isDefault,isActive:y.isActive,configItems:[]};V.value?(await x(`/config-templates/${V.value.id}`,{method:"PUT",body:JSON.stringify(e)}),f.success("模板更新成功")):(await x("/config-templates",{method:"POST",body:JSON.stringify(e)}),f.success("模板创建成功")),M.value=!1,await P()}catch(e){console.error("保存模板失败:",e),f.error("保存模板失败")}finally{i.value=!1}},we=()=>{var t;M.value=!1,(t=J.value)==null||t.resetFields()},ke=async t=>{try{await x(`/config-templates/${t.id}/set-default`,{method:"PUT"}),f.success(`已将 "${t.name}" 设为默认模板`),await P()}catch(e){console.error("设置默认模板失败:",e),f.error("设置默认模板失败")}},Oe=t=>{const e=[];return Object.keys(t).forEach(l=>{const u=t[l];Object.keys(u).forEach(c=>{const d=u[c];let g="string",S=String(d.value);typeof d.value=="boolean"?(g="boolean",S=String(d.value)):typeof d.value=="number"?(g="number",S=String(d.value)):typeof d.value=="object"&&(g="object",S=JSON.stringify(d.value)),e.push({config_group:l,config_key:c,config_value:S,value_type:g,is_enabled:d.enabled!==!1,is_required:d.required||!1,description:`${l}.${c}配置项`})})}),e},Ce=t=>{const e=U.value.map(c=>c.name);let l=`${t} - 副本`,u=1;for(;e.includes(l);)u++,l=`${t} - 副本${u}`;return l},Se=t=>{A.value=t;const e=Ce(t.name);C.name=e,C.description=`${t.description} (副本)`,R.value=!0},Te=async()=>{var t;if(A.value)try{await((t=Q.value)==null?void 0:t.validate());const e=await E(A.value.id);if(!e)return;const l=Oe(e.configStates||{}),u={name:C.name.trim(),description:C.description.trim()||C.name.trim(),configItems:l};if(console.log("🔄 复制模板数据:",{originalName:A.value.name,newName:u.name,configItemsCount:l.length,configStates:e.configStates,convertedItems:l,newTemplate:u}),l.length===0)f.warning("原模板没有配置项，将创建空模板");else{const c=l[0];console.log("📝 配置项样例:",c);const g=["config_group","config_key","config_value","value_type"].filter(S=>!(S in c));if(g.length>0){console.error("❌ 配置项缺少必要字段:",g),f.error(`配置项数据格式错误，缺少字段: ${g.join(", ")}`);return}}try{await x("/config-templates",{method:"POST",body:JSON.stringify(u)}),f.success(`模板"${u.name}"复制成功，包含 ${l.length} 个配置参数`),R.value=!1,await P(),setTimeout(async()=>{const c=U.value.find(d=>d.name===u.name);c&&(await W(c),f.info("已自动选择复制的模板，您可以查看复制的配置参数"))},500)}catch(c){console.error("❌ API调用详细错误:",c);const d=c,g=(d==null?void 0:d.message)||"未知错误";g.includes("不能为空")?f.error("数据验证失败：必要字段不能为空"):g.includes("数据库")?f.error("数据库操作失败，请检查数据库连接"):f.error(`复制模板失败: ${g}`)}}catch(e){console.error("复制模板失败:",e),f.error("复制模板失败")}},xe=()=>{var t;R.value=!1,A.value=null,(t=Q.value)==null||t.resetFields()},$e=async t=>{try{const e=await E(t.id);if(!e)return;const l={template:t,config:e.config,configStates:e.configStates,exportTime:new Date().toISOString()},u=JSON.stringify(l,null,2),c=new Blob([u],{type:"application/json"}),d=URL.createObjectURL(c),g=document.createElement("a");g.href=d,g.download=`${t.name}-配置模板.json`,g.click(),URL.revokeObjectURL(d),f.success("模板导出成功")}catch(e){console.error("导出模板失败:",e),f.error("导出模板失败")}},De=async t=>{try{await x(`/config-templates/${t.id}`,{method:"PUT",body:JSON.stringify({isActive:!t.isActive})}),f.success(`模板已${t.isActive?"禁用":"启用"}`),await P()}catch(e){console.error("切换模板状态失败:",e),f.error("切换模板状态失败")}},Ue=t=>{ue.confirm({title:"确认禁用",content:`确定要永久禁用模板 "${t.name}" 吗？禁用后模板将不再显示在列表中（除非开启"显示全部"）。`,okText:"确定",cancelText:"取消",okType:"danger",onOk:async()=>{try{await x(`/config-templates/${t.id}`,{method:"DELETE"}),f.success("模板已禁用"),I.value===t.id&&(I.value="",O.value=null,z.value=void 0),await P()}catch(e){console.error("禁用模板失败:",e),f.error("禁用模板失败")}}})},te=async t=>{if(O.value)try{i.value=!0,await x(`/config-templates/${O.value.id}`,{method:"PUT",body:JSON.stringify({configStates:t})}),f.success("配置保存成功");const e=await E(O.value.id);e&&(z.value=e.config,L.value=e.configStates)}catch(e){console.error("保存配置失败:",e),f.error("保存配置失败")}finally{i.value=!1}},Ae=()=>{L.value&&te(L.value)},Pe=()=>{ue.confirm({title:"确认重置",content:"确定要重置当前模板的配置吗？这将恢复到默认设置。",okText:"确定",cancelText:"取消",onOk:()=>{ne()}})},ne=()=>{O.value&&(E(O.value.id),f.success("配置已重置"))},je=()=>{},Ne=t=>{if(!t)return"未知";try{const e=new Date(t);if(isNaN(e.getTime()))return"未知";const u=new Date().getTime()-e.getTime(),c=Math.floor(u/(1e3*60*60*24));return c===0?"今天":c===1?"昨天":c<7?`${c}天前`:c<30?`${Math.floor(c/7)}周前`:e.toLocaleDateString("zh-CN")}catch{return"未知"}};Qe(()=>{P()});const Ie=()=>{},Le=()=>!1;return(t,e)=>{const l=v("a-button"),u=v("a-space"),c=v("a-page-header"),d=v("a-switch"),g=v("a-tooltip"),S=v("a-input"),F=v("a-menu-item"),Me=v("a-menu-divider"),Re=v("a-menu"),Ee=v("a-dropdown"),j=v("a-form-item"),ae=v("a-col"),Fe=v("a-row"),oe=v("a-textarea"),se=v("a-form"),G=v("a-modal"),ze=v("inbox-outlined"),Ve=v("a-upload-dragger"),qe=v("a-alert"),Be=v("a-divider");return h(),T("div",mt,[b(" 使用标准的a-page-header组件保持一致性 "),n(c,{title:"OnlyOffice配置模板管理","sub-title":"管理文档编辑器的权限和功能配置"},{extra:s(()=>[O.value?(h(),B(u,{key:0},{default:s(()=>[n(l,{onClick:Pe},{icon:s(()=>[n(k(ft))]),default:s(()=>[e[15]||(e[15]=D(" 重置 "))]),_:1,__:[15]}),n(l,{type:"primary",onClick:Ae,loading:i.value},{icon:s(()=>[n(k(nt))]),default:s(()=>[e[16]||(e[16]=D(" 保存 "))]),_:1,__:[16]},8,["loading"])]),_:1})):b("v-if",!0)]),_:1}),b(" 主要内容 "),o("div",vt,[o("div",pt,[b(" 左侧模板列表 "),o("aside",gt,[o("div",_t,[e[17]||(e[17]=o("div",null,[o("h2",{class:"section-title"},"配置模板"),o("p",{class:"section-subtitle"},"快速选择和编辑")],-1)),o("div",yt,[n(g,{title:"显示全部模板（包括已禁用的）"},{default:s(()=>[n(d,{checked:w.value,"onUpdate:checked":e[0]||(e[0]=a=>w.value=a),size:"small","checked-children":"全部","un-checked-children":"启用"},null,8,["checked"])]),_:1}),n(l,{onClick:be,type:"primary",size:"small",class:"add-btn"},{icon:s(()=>[n(k(We))]),_:1})])]),o("div",bt,[n(S,{value:_.value,"onUpdate:value":e[1]||(e[1]=a=>_.value=a),placeholder:"搜索模板...",size:"small",class:"search-input",onInput:je},{prefix:s(()=>[n(k(Ge),{class:"search-icon"})]),_:1},8,["value"])]),o("div",ht,[(h(!0),T(ie,null,re(ee.value,a=>(h(),T("div",{key:a.id,class:X(["template-card",{active:I.value===a.id,disabled:!a.isActive}]),onClick:N=>W(a)},[o("div",kt,[o("div",Ot,[D($(a.name)+" ",1),a.isActive?b("v-if",!0):(h(),T("span",Ct,"（已禁用）"))]),o("div",St,[a.isDefault?(h(),T("span",Tt,"默认")):b("v-if",!0),o("span",{class:X(["status-badge",a.isActive?"status-active":"status-inactive"])},$(a.isActive?"启用":"禁用"),3)])]),o("div",xt,$(a.description),1),o("div",$t,[o("span",null,$(Ne(a.updatedAt)),1),o("div",{class:"template-actions",onClick:e[2]||(e[2]=Xe(()=>{},["stop"]))},[n(Ee,null,{overlay:s(()=>[n(Re,null,{default:s(()=>[a.isDefault?b("v-if",!0):(h(),B(F,{key:"setDefault",onClick:N=>ke(a)},{default:s(()=>[n(k(at)),e[18]||(e[18]=D(" 设为默认 "))]),_:2,__:[18]},1032,["onClick"])),n(F,{key:"duplicate",onClick:N=>Se(a)},{default:s(()=>[n(k(Ye)),e[19]||(e[19]=D(" 复制模板 "))]),_:2,__:[19]},1032,["onClick"]),n(F,{key:"export",onClick:N=>$e(a)},{default:s(()=>[n(k(Ze)),e[20]||(e[20]=D(" 导出模板 "))]),_:2,__:[20]},1032,["onClick"]),n(F,{key:"toggle",onClick:N=>De(a)},{default:s(()=>[n(k(rt)),D(" "+$(a.isActive?"禁用模板":"启用模板"),1)]),_:2},1032,["onClick"]),n(Me),a.isActive?(h(),B(F,{key:"disable",onClick:N=>Ue(a),class:"danger-item"},{default:s(()=>[n(k(Ke)),e[21]||(e[21]=D(" 永久禁用 "))]),_:2,__:[21]},1032,["onClick"])):b("v-if",!0)]),_:2},1024)]),default:s(()=>[o("button",Dt,[n(k(ot))])]),_:2},1024)])])],10,wt))),128)),ee.value.length===0&&!r.value?(h(),T("div",Ut,[o("div",At,[n(k(ce))]),e[22]||(e[22]=o("div",{class:"empty-title"},"暂无模板",-1)),e[23]||(e[23]=o("div",{class:"empty-desc"},"点击右上角按钮创建新的配置模板",-1))])):b("v-if",!0)])]),b(" 右侧配置区域 "),o("main",Pt,[O.value?(h(),T("div",It,[o("div",Lt,[o("h2",Mt,$(O.value.name),1),e[26]||(e[26]=o("p",{class:"config-subtitle"},"快速配置文档编辑器的权限和功能",-1))]),o("nav",Rt,[(h(!0),T(ie,null,re(ge.value,a=>(h(),T("button",{key:a.key,class:X(["nav-tab",{active:q.value===a.key}]),onClick:N=>q.value=a.key},$(a.label),11,Et))),128))]),o("div",{class:"config-content",ref_key:"configContentRef",ref:pe},[n(tt,{"template-data":O.value,"initial-config":z.value,"initial-config-states":L.value,"active-tab":q.value,onSave:te,onReset:ne,onTabChange:e[3]||(e[3]=a=>q.value=a)},null,8,["template-data","initial-config","initial-config-states","active-tab"])],512)])):(h(),T("div",jt,[o("div",Nt,[n(k(ce),{class:"empty-config-icon"}),e[24]||(e[24]=o("h3",{class:"empty-config-title"},"请选择配置模板",-1)),e[25]||(e[25]=o("p",{class:"empty-config-desc"},"从左侧列表中选择一个模板来查看和编辑配置",-1))])]))])]),b(" 创建/编辑模板模态框 "),n(G,{open:M.value,"onUpdate:open":e[8]||(e[8]=a=>M.value=a),title:V.value?"编辑模板":"创建新模板",width:"600px",onOk:he,onCancel:we,"confirm-loading":i.value},{default:s(()=>[n(se,{ref_key:"formRef",ref:J,model:y,rules:_e,layout:"vertical"},{default:s(()=>[n(Fe,{gutter:16},{default:s(()=>[n(ae,{span:12},{default:s(()=>[n(j,{label:"模板名称",name:"name"},{default:s(()=>[n(S,{value:y.name,"onUpdate:value":e[4]||(e[4]=a=>y.name=a),placeholder:"请输入模板名称"},null,8,["value"])]),_:1})]),_:1}),n(ae,{span:12},{default:s(()=>[n(j,{label:"模板状态"},{default:s(()=>[n(u,null,{default:s(()=>[n(d,{checked:y.isActive,"onUpdate:checked":e[5]||(e[5]=a=>y.isActive=a)},null,8,["checked"]),o("span",null,$(y.isActive?"启用":"禁用"),1)]),_:1})]),_:1})]),_:1})]),_:1}),n(j,{label:"模板描述",name:"description"},{default:s(()=>[n(oe,{value:y.description,"onUpdate:value":e[6]||(e[6]=a=>y.description=a),placeholder:"请输入模板描述",rows:3},null,8,["value"])]),_:1}),n(j,{label:"设为默认模板"},{default:s(()=>[n(d,{checked:y.isDefault,"onUpdate:checked":e[7]||(e[7]=a=>y.isDefault=a)},null,8,["checked"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","title","confirm-loading"]),b(" 导入配置模态框 "),n(G,{open:H.value,"onUpdate:open":e[10]||(e[10]=a=>H.value=a),title:"导入配置模板",width:"600px",onOk:Ie,onCancel:e[11]||(e[11]=a=>H.value=!1),"confirm-loading":ve.value},{default:s(()=>[o("div",Ft,[n(Ve,{fileList:K.value,"onUpdate:fileList":e[9]||(e[9]=a=>K.value=a),"before-upload":Le,accept:".json",multiple:!1},{default:s(()=>[o("p",zt,[n(ze)]),e[27]||(e[27]=o("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[28]||(e[28]=o("p",{class:"ant-upload-hint"},"支持JSON格式的配置文件",-1))]),_:1,__:[27,28]},8,["fileList"])])]),_:1},8,["open","confirm-loading"]),b(" 复制模板重命名模态框 "),n(G,{open:R.value,"onUpdate:open":e[14]||(e[14]=a=>R.value=a),title:"复制配置模板",width:"600px",onOk:Te,onCancel:xe,"confirm-loading":i.value,"ok-text":"确认复制","cancel-text":"取消"},{default:s(()=>[o("div",Vt,[n(se,{ref_key:"duplicateFormRef",ref:Q,model:C,rules:ye,layout:"vertical"},{default:s(()=>[A.value?(h(),B(qe,{key:0,message:`正在复制模板: ${A.value.name}`,description:"将复制所有配置参数和设置，您可以自定义新模板的名称和描述",type:"info","show-icon":"",style:{"margin-bottom":"20px"}},null,8,["message"])):b("v-if",!0),n(j,{label:"新模板名称",name:"name"},{default:s(()=>[n(S,{value:C.name,"onUpdate:value":e[12]||(e[12]=a=>C.name=a),placeholder:"请输入新模板的名称","allow-clear":""},null,8,["value"]),e[29]||(e[29]=o("div",{class:"form-help-text"},[o("small",null,"系统已自动生成唯一名称，您可以根据需要修改")],-1))]),_:1,__:[29]}),n(j,{label:"模板描述",name:"description"},{default:s(()=>[n(oe,{value:C.description,"onUpdate:value":e[13]||(e[13]=a=>C.description=a),placeholder:"请输入新模板的描述（可选）",rows:3,"allow-clear":""},null,8,["value"])]),_:1}),n(Be,null,{default:s(()=>e[30]||(e[30]=[o("span",{class:"divider-text"},"复制说明",-1)])),_:1,__:[30]}),e[31]||(e[31]=o("div",{class:"copy-info"},[o("ul",{class:"copy-info-list"},[o("li",null,"✅ 所有配置参数和设置将被完整复制"),o("li",null,"✅ 配置项的启用状态和必需属性将被保留"),o("li",null,"✅ 复制完成后将自动选择新模板供您编辑"),o("li",null,'ℹ️ 新模板的状态将默认为"启用"')])],-1))]),_:1,__:[31]},8,["model"])])]),_:1},8,["open","confirm-loading"])])])}}});const Gt=et(qt,[["__scopeId","data-v-adae0860"],["__file","D:/Code/OnlyOffice/frontend/src/pages/OnlyOfficeConfig/index.vue"]]);export{Gt as default};
