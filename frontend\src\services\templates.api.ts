import { ApiService } from './api'
import type { TemplateInfo, PaginationParams, PaginationResponse } from '@/types/api.types'
import type {
  BackendDocumentTemplate,
  BackendConfigTemplate,
  BackendTemplateCategory,
  BackendPaginationResponse,
  BackendApiResponse,
} from '@/types/template.types'

export interface CreateDocumentTemplateDto {
  name: string
  description?: string
  type: 'document' | 'spreadsheet' | 'presentation'
  category: string
  tags?: string[]
  file?: File
}

export interface UpdateDocumentTemplateDto {
  name?: string
  description?: string
  category?: string
  tags?: string[]
}

export interface DocumentTemplateListQueryParams extends PaginationParams {
  search?: string
  type?: string
  category?: string
  tags?: string[]
}

export interface CreateTemplateCategoryDto {
  name: string
  description?: string
  parentId?: string | null
}

export interface TemplateCategory {
  id: string
  name: string
  description?: string
  parentId?: string | null
  children?: TemplateCategory[]
  templateCount: number
  createdAt: string
}

/**
 * 文档模板管理API服务
 */
export class TemplatesApiService {
  /**
   * 获取文档模板列表
   */
  static async getDocumentTemplates(
    params?: DocumentTemplateListQueryParams
  ): Promise<PaginationResponse<TemplateInfo>> {
    // 转换前端参数为后端期望的格式
    const backendParams = {
      limit: params?.pageSize || 10,
      offset: ((params?.current || 1) - 1) * (params?.pageSize || 10),
      categoryId: params?.category || '',
      status: '',
      sortBy: 'updatedAt',
      order: 'desc',
      keyword: params?.search || '',
    }

    // 调用后端API获取原始数据
    const backendResponse = await ApiService.get<
      BackendPaginationResponse<BackendDocumentTemplate>
    >('/document-templates', { params: backendParams })

    // 转换后端数据为前端TemplateInfo格式
    const transformedTemplates: TemplateInfo[] = (backendResponse.templates || []).map(
      (item: BackendDocumentTemplate) => ({
        id: item.id,
        name: item.name,
        description: item.description || '',
        type: this.getTemplateTypeFromExtension(item.extension) as
          | 'document'
          | 'spreadsheet'
          | 'presentation',
        category: item.category_name || item.category_id || '未分类',
        status: item.status === 'enabled' ? 'active' : 'inactive',
        isDefault: false, // 后端没有此字段，设为默认值
        previewUrl: undefined, // 后端没有此字段
        downloadUrl: `/api/document-templates/${item.id}/download`,
        size: item.file_size || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        createdBy: item.created_by,
        usageCount: 0, // 后端没有此字段，设为默认值
        tags: [], // 后端没有此字段，设为默认值
      })
    )

    // 转换为前端期望的格式
    return {
      list: transformedTemplates,
      total: backendResponse.total || 0,
      current: params?.current || 1,
      pageSize: params?.pageSize || 10,
    }
  }

  /**
   * 根据文件扩展名判断模板类型
   */
  private static getTemplateTypeFromExtension(extension: string): string {
    const ext = extension?.toLowerCase() || ''

    // 文档类型
    if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) {
      return 'document'
    }

    // 表格类型
    if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) {
      return 'spreadsheet'
    }

    // 演示文档类型
    if (['ppt', 'pptx', 'odp'].includes(ext)) {
      return 'presentation'
    }

    // 默认为文档类型
    return 'document'
  }

  /**
   * 获取文档模板详情
   */
  static async getDocumentTemplateById(id: string): Promise<TemplateInfo> {
    return ApiService.get<TemplateInfo>(`/document-templates/${id}`)
  }

  /**
   * 创建文档模板
   */
  static async createDocumentTemplate(data: CreateDocumentTemplateDto): Promise<TemplateInfo> {
    const formData = new FormData()
    formData.append('name', data.name)
    formData.append('type', data.type)
    formData.append('category', data.category)

    if (data.description) {
      formData.append('description', data.description)
    }

    if (data.tags) {
      formData.append('tags', JSON.stringify(data.tags))
    }

    if (data.file) {
      formData.append('file', data.file)
    }

    return ApiService.post<TemplateInfo>('/document-templates', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 更新文档模板
   */
  static async updateDocumentTemplate(
    id: string,
    data: UpdateDocumentTemplateDto
  ): Promise<TemplateInfo> {
    return ApiService.put<TemplateInfo>(`/document-templates/${id}`, data)
  }

  /**
   * 删除文档模板
   */
  static async deleteDocumentTemplate(id: string): Promise<void> {
    return ApiService.delete<void>(`/document-templates/${id}`)
  }

  /**
   * 基于模板创建编辑会话
   */
  static async createDocumentFromTemplate(
    id: string,
    data: {
      title: string
      description?: string
    }
  ): Promise<{
    sessionId: string
    templateId: string
    templateName: string
    templateFnDocId: string
    documentName: string
    userId: string
    status: string
    editUrl: string
  }> {
    return ApiService.post<{
      sessionId: string
      templateId: string
      templateName: string
      templateFnDocId: string
      documentName: string
      userId: string
      status: string
      editUrl: string
    }>(`/document-templates/${id}/create-document`, data)
  }

  /**
   * 创建模板分类
   */
  static async createTemplateCategory(data: CreateTemplateCategoryDto): Promise<TemplateCategory> {
    const requestData = {
      name: data.name,
      description: data.description || '',
      parentId: data.parentId || null,
      sortOrder: 0,
    }

    console.log('🚀 创建分类请求数据:', requestData)

    const response = await ApiService.post<BackendApiResponse<BackendTemplateCategory>>(
      '/document-templates/categories',
      requestData
    )

    console.log('✅ 创建分类响应:', response)

    // 检查响应格式并提取数据
    let categoryData: BackendTemplateCategory
    if (response && response.data && response.data.id) {
      // 包装格式：{ success: true, data: {...} }
      categoryData = response.data
    } else if (response && (response as unknown as BackendTemplateCategory).id) {
      // 直接格式：{id: ..., name: ...}
      categoryData = response as unknown as BackendTemplateCategory
    } else {
      throw new Error('Invalid response format from create category API')
    }

    // 转换响应数据为前端格式
    return {
      id: categoryData.id,
      name: categoryData.name,
      description: categoryData.description || '',
      parentId: categoryData.parent_id,
      children: [],
      templateCount: 0,
      createdAt: categoryData.created_at || new Date().toISOString(),
    }
  }

  /**
   * 获取模板分类列表
   */
  static async getTemplateCategories(): Promise<TemplateCategory[]> {
    try {
      const response = await ApiService.get<BackendApiResponse<BackendTemplateCategory[]>>(
        '/document-templates/categories/list'
      )

      console.log('🔍 原始分类API响应:', response)

      // 检查响应格式，适配不同的响应结构
      let categoryData: BackendTemplateCategory[] = []

      if (response && Array.isArray(response.data)) {
        // 标准响应格式：{ success: true, data: [...] }
        categoryData = response.data
      } else if (response && Array.isArray(response as unknown as BackendTemplateCategory[])) {
        // 直接数组响应
        categoryData = response as unknown as BackendTemplateCategory[]
      } else if (response && response.success === false) {
        console.warn('⚠️ API返回失败响应:', response)
        return []
      }

      console.log('🔍 提取的分类数据:', categoryData)

      // 转换后端数据格式为前端期望的格式
      return categoryData.map((item: BackendTemplateCategory) => ({
        id: item.id,
        name: item.name,
        description: item.description || '',
        parentId: item.parent_id,
        children: [],
        templateCount: 0, // 后端暂时没有返回，设为默认值
        createdAt: item.created_at,
      }))
    } catch (error) {
      console.error('❌ 获取分类列表失败:', error)
      return []
    }
  }

  /**
   * 获取所有模板概览
   */
  static async getAllTemplatesOverview(): Promise<{
    documentTemplates: TemplateInfo[]
    configTemplates: BackendConfigTemplate[]
    categories: TemplateCategory[]
    stats: {
      totalDocumentTemplates: number
      totalConfigTemplates: number
      totalCategories: number
      mostUsedTemplate: TemplateInfo | null
    }
  }> {
    return ApiService.get('/document-templates/overview/all')
  }

  /**
   * 搜索模板
   */
  static async searchTemplates(params: {
    keyword: string
    type?: string
    category?: string
    limit?: number
  }): Promise<{
    documentTemplates: TemplateInfo[]
    configTemplates: BackendConfigTemplate[]
    total: number
  }> {
    return ApiService.get('/document-templates/search/all', { params })
  }

  /**
   * 下载模板文件
   */
  static async downloadTemplate(id: string, filename?: string): Promise<void> {
    return ApiService.download(`/document-templates/${id}/download`, filename)
  }

  /**
   * 预览模板
   */
  static async previewTemplate(id: string): Promise<{ previewUrl: string }> {
    return ApiService.get<{ previewUrl: string }>(`/document-templates/${id}/preview`)
  }
}

export default TemplatesApiService
