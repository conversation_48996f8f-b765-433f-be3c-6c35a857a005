<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 现代办公风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #f6f8fc 0%, #eef2ff 100%);
      color: #1e293b;
      line-height: 1.6;
    }

    .workspace-container {
      display: flex;
      min-height: 100vh;
    }

    /* 左侧导航栏 */
    .sidebar-nav {
      width: 260px;
      background: #ffffff;
      border-right: 1px solid #e2e8f0;
      box-shadow: 4px 0 6px -1px rgba(0, 0, 0, 0.1);
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 1000;
    }

    .company-header {
      padding: 24px 20px;
      border-bottom: 1px solid #e2e8f0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .company-name {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .company-subtitle {
      font-size: 12px;
      opacity: 0.9;
    }

    .menu-navigation {
      padding: 20px 0;
    }

    .menu-group {
      margin-bottom: 28px;
    }

    .group-title {
      padding: 8px 20px;
      font-size: 10px;
      color: #64748b;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .menu-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: #475569;
      text-decoration: none;
      transition: all 0.2s ease;
      border-left: 3px solid transparent;
    }

    .menu-item:hover {
      background: #f1f5f9;
      color: #334155;
    }

    .menu-item.current {
      background: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%);
      color: #3730a3;
      border-left-color: #6366f1;
    }

    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .menu-text {
      font-size: 14px;
      font-weight: 500;
    }

    .menu-counter {
      margin-left: auto;
      background: #ef4444;
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 8px;
      min-width: 16px;
      text-align: center;
      font-weight: 600;
    }

    /* 主要工作区域 */
    .main-workspace {
      margin-left: 260px;
      flex: 1;
    }

    /* 工作区顶栏 */
    .workspace-header {
      background: #ffffff;
      height: 68px;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32px;
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .workspace-title {
      font-size: 24px;
      font-weight: 600;
      color: #1e293b;
    }

    .title-subtitle {
      font-size: 14px;
      color: #64748b;
      margin-top: 2px;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .notification-center {
      position: relative;
      width: 40px;
      height: 40px;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .notification-center:hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
    }

    .notification-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      width: 16px;
      height: 16px;
      background: #ef4444;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      color: white;
      font-weight: 600;
    }

    .user-profile-menu {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 12px;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .user-profile-menu:hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
    }

    .profile-avatar {
      width: 36px;
      height: 36px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    .profile-info {
      display: flex;
      flex-direction: column;
    }

    .profile-name {
      font-size: 14px;
      font-weight: 600;
      color: #1e293b;
    }

    .profile-status {
      font-size: 12px;
      color: #64748b;
    }

    /* 工作区内容 */
    .workspace-content {
      padding: 32px;
    }

    /* 数据仪表板 */
    .dashboard-metrics {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-bottom: 32px;
    }

    .metric-card {
      background: #ffffff;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 24px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }

    .metric-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
    }

    .metric-card.documents::before { background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%); }
    .metric-card.users::before { background: linear-gradient(90deg, #10b981 0%, #059669 100%); }
    .metric-card.storage::before { background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%); }
    .metric-card.health::before { background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%); }

    .metric-card:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transform: translateY(-2px);
    }

    .metric-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .metric-icon.documents { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
    .metric-icon.users { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
    .metric-icon.storage { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
    .metric-icon.health { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }

    .metric-change {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      padding: 4px 8px;
      border-radius: 8px;
    }

    .change-positive {
      background: #dcfce7;
      color: #166534;
    }

    .change-negative {
      background: #fef2f2;
      color: #dc2626;
    }

    .metric-value {
      font-size: 28px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 6px;
    }

    .metric-label {
      color: #64748b;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .metric-details {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #94a3b8;
    }

    /* 工作区主网格 */
    .workspace-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 32px;
    }

    .primary-section {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .workspace-section {
      background: #ffffff;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .section-header {
      padding: 20px 24px;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f8fafc;
    }

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    .section-action {
      color: #6366f1;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 6px;
      transition: all 0.2s ease;
    }

    .section-action:hover {
      background: #e0e7ff;
      color: #4338ca;
    }

    /* 工作台操作 */
    .operations-workspace {
      padding: 24px;
    }

    .operations-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
    }

    .operation-tile {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }

    .operation-tile::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .operation-tile:hover {
      background: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%);
      border-color: #c7d2fe;
      transform: translateY(-2px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .operation-tile:hover::before {
      transform: scaleX(1);
    }

    .operation-icon {
      width: 56px;
      height: 56px;
      margin: 0 auto 16px;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: white;
      transition: all 0.3s ease;
    }

    .operation-tile:hover .operation-icon {
      transform: scale(1.1);
    }

    .operation-title {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 8px;
    }

    .operation-description {
      font-size: 13px;
      color: #64748b;
      line-height: 1.4;
    }

    /* 文档工作区 */
    .documents-workspace {
      padding: 0 24px 24px;
    }

    .document-entry {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #f1f5f9;
      transition: all 0.2s ease;
    }

    .document-entry:hover {
      background: #f8fafc;
      margin: 0 -24px;
      padding: 16px 24px;
      border-radius: 8px;
    }

    .document-entry:last-child {
      border-bottom: none;
    }

    .document-type-badge {
      width: 48px;
      height: 48px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: white;
      font-weight: 600;
    }

    .doc-word { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); }
    .doc-excel { background: linear-gradient(135deg, #059669 0%, #047857 100%); }
    .doc-ppt { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
    .doc-pdf { background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%); }

    .document-info {
      flex: 1;
    }

    .document-title {
      font-size: 15px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .document-metadata {
      font-size: 13px;
      color: #64748b;
    }

    .document-status-badge {
      padding: 6px 12px;
      border-radius: 8px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-active {
      background: #fef3c7;
      color: #92400e;
    }

    .status-shared {
      background: #dbeafe;
      color: #1e40af;
    }

    .status-completed {
      background: #d1fae5;
      color: #065f46;
    }

    .document-actions {
      color: #64748b;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: all 0.2s ease;
    }

    .document-actions:hover {
      background: #f1f5f9;
      color: #374151;
    }

    /* 侧边区域 */
    .sidebar-area {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    /* 个人资料区域 */
    .profile-workspace {
      padding: 24px;
      text-align: center;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .profile-avatar-large {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-weight: 600;
      color: white;
      box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    }

    .profile-full-name {
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 6px;
    }

    .profile-position {
      font-size: 14px;
      color: #64748b;
      margin-bottom: 20px;
    }

    .profile-statistics {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      padding-top: 20px;
      border-top: 1px solid #e2e8f0;
    }

    .profile-stat {
      text-align: center;
    }

    .stat-number {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    .stat-description {
      font-size: 12px;
      color: #64748b;
    }

    /* 系统监控区域 */
    .monitoring-workspace {
      padding: 20px;
    }

    .monitor-entry {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px 0;
      border-bottom: 1px solid #f1f5f9;
    }

    .monitor-entry:last-child {
      border-bottom: none;
    }

    .monitor-service {
      font-size: 14px;
      color: #64748b;
      font-weight: 500;
    }

    .monitor-status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
    }

    .status-light {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .status-online { background: #10b981; }
    .status-warning { background: #f59e0b; }
    .status-offline { background: #ef4444; }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .sidebar-nav {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar-nav.active {
        transform: translateX(0);
      }
      
      .main-workspace {
        margin-left: 0;
      }
      
      .workspace-grid {
        grid-template-columns: 1fr;
      }
      
      .dashboard-metrics {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .workspace-content {
        padding: 20px;
      }
      
      .dashboard-metrics {
        grid-template-columns: 1fr;
      }
      
      .operations-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .profile-statistics {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .operations-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="workspace-container">
    <!-- 左侧导航栏 -->
    <nav class="sidebar-nav">
      <div class="company-header">
        <div class="company-name">OnlyOffice</div>
        <div class="company-subtitle">企业级文档管理平台</div>
      </div>
      
      <div class="menu-navigation">
        <div class="menu-group">
          <div class="group-title">核心功能</div>
          <a href="#" class="menu-item current">
            <span class="menu-icon">🏠</span>
            <span class="menu-text">工作台</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">📄</span>
            <span class="menu-text">文档中心</span>
            <span class="menu-counter">5</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">📋</span>
            <span class="menu-text">模板库</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">👥</span>
            <span class="menu-text">团队管理</span>
          </a>
        </div>
        
        <div class="menu-group">
          <div class="group-title">系统管理</div>
          <a href="#" class="menu-item">
            <span class="menu-icon">⚙️</span>
            <span class="menu-text">系统设置</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">🔐</span>
            <span class="menu-text">权限中心</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">🔔</span>
            <span class="menu-text">OnlyOffice配置</span>
          </a>
        </div>
        
        <div class="menu-group">
          <div class="group-title">数据洞察</div>
          <a href="#" class="menu-item">
            <span class="menu-icon">📊</span>
            <span class="menu-text">数据报表</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">📈</span>
            <span class="menu-text">使用分析</span>
          </a>
          <a href="#" class="menu-item">
            <span class="menu-icon">📋</span>
            <span class="menu-text">操作日志</span>
          </a>
        </div>
      </div>
    </nav>

    <!-- 主要工作区域 -->
    <main class="main-workspace">
      <!-- 工作区顶栏 -->
      <header class="workspace-header">
        <div class="header-left">
          <div>
            <h1 class="workspace-title">工作台</h1>
            <div class="title-subtitle">欢迎回到您的数字办公空间</div>
          </div>
        </div>
        <div class="header-right">
          <div class="notification-center">
            <span style="font-size: 18px;">🔔</span>
            <div class="notification-badge">3</div>
          </div>
          <div class="user-profile-menu">
            <div class="profile-avatar">管</div>
            <div class="profile-info">
              <div class="profile-name">系统管理员</div>
              <div class="profile-status">在线</div>
            </div>
          </div>
        </div>
      </header>

      <!-- 工作区内容 -->
      <div class="workspace-content">
        <!-- 数据仪表板 -->
        <div class="dashboard-metrics">
          <div class="metric-card documents">
            <div class="metric-header">
              <div class="metric-icon documents">📄</div>
              <div class="metric-change change-positive">↗ +16%</div>
            </div>
            <div class="metric-value">1,567</div>
            <div class="metric-label">文档总量</div>
            <div class="metric-details">
              <span>本月: +198</span>
              <span>活跃: 1,123</span>
            </div>
          </div>

          <div class="metric-card users">
            <div class="metric-header">
              <div class="metric-icon users">👥</div>
              <div class="metric-change change-positive">↗ +9%</div>
            </div>
            <div class="metric-value">94</div>
            <div class="metric-label">活跃用户</div>
            <div class="metric-details">
              <span>在线: 31</span>
              <span>本周: 78</span>
            </div>
          </div>

          <div class="metric-card storage">
            <div class="metric-header">
              <div class="metric-icon storage">💾</div>
              <div class="metric-change change-positive">↗ +11%</div>
            </div>
            <div class="metric-value">1.2TB</div>
            <div class="metric-label">存储用量</div>
            <div class="metric-details">
              <span>可用: 300GB</span>
              <span>使用率: 80%</span>
            </div>
          </div>

          <div class="metric-card health">
            <div class="metric-header">
              <div class="metric-icon health">❤️</div>
              <div class="metric-change change-positive">↗ +1%</div>
            </div>
            <div class="metric-value">99.8%</div>
            <div class="metric-label">系统健康</div>
            <div class="metric-details">
              <span>响应: 145ms</span>
              <span>稳定运行</span>
            </div>
          </div>
        </div>

        <!-- 工作区主网格 -->
        <div class="workspace-grid">
          <!-- 主要区域 -->
          <div class="primary-section">
            <!-- 快速操作工作台 -->
            <div class="workspace-section">
              <div class="section-header">
                <h2 class="section-title">快速操作工作台</h2>
                <span class="section-action">自定义工作区</span>
              </div>
              <div class="operations-workspace">
                <div class="operations-grid">
                  <div class="operation-tile">
                    <div class="operation-icon">📝</div>
                    <div class="operation-title">创建新文档</div>
                    <div class="operation-description">快速创建Word、Excel、PPT等文档</div>
                  </div>
                  <div class="operation-tile">
                    <div class="operation-icon">📤</div>
                    <div class="operation-title">批量文件上传</div>
                    <div class="operation-description">支持拖拽方式批量上传文档</div>
                  </div>
                  <div class="operation-tile">
                    <div class="operation-icon">📋</div>
                    <div class="operation-title">模板中心</div>
                    <div class="operation-description">使用企业模板快速创建文档</div>
                  </div>
                  <div class="operation-tile">
                    <div class="operation-icon">👥</div>
                    <div class="operation-title">协作空间</div>
                    <div class="operation-description">邀请团队成员协同编辑文档</div>
                  </div>
                  <div class="operation-tile">
                    <div class="operation-icon">📊</div>
                    <div class="operation-title">数据分析中心</div>
                    <div class="operation-description">查看详细的文档使用统计数据</div>
                  </div>
                  <div class="operation-tile">
                    <div class="operation-icon">⚙️</div>
                    <div class="operation-title">系统管理</div>
                    <div class="operation-description">配置系统参数和用户权限</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 最近处理的文档 -->
            <div class="workspace-section">
              <div class="section-header">
                <h2 class="section-title">最近处理的文档</h2>
                <span class="section-action">查看所有文档</span>
              </div>
              <div class="documents-workspace">
                <div class="document-entry">
                  <div class="document-type-badge doc-word">W</div>
                  <div class="document-info">
                    <div class="document-title">企业数字化转型战略规划文档.docx</div>
                    <div class="document-metadata">张三 · 1小时前最后编辑 · 4.2MB</div>
                  </div>
                  <div class="document-status-badge status-active">编辑中</div>
                  <div class="document-actions">⋮</div>
                </div>
                <div class="document-entry">
                  <div class="document-type-badge doc-excel">E</div>
                  <div class="document-info">
                    <div class="document-title">2024年第四季度财务预算表.xlsx</div>
                    <div class="document-metadata">李四 · 昨天下午2:15 · 2.8MB</div>
                  </div>
                  <div class="document-status-badge status-shared">已共享</div>
                  <div class="document-actions">⋮</div>
                </div>
                <div class="document-entry">
                  <div class="document-type-badge doc-ppt">P</div>
                  <div class="document-info">
                    <div class="document-title">新产品发布会演示方案.pptx</div>
                    <div class="document-metadata">王五 · 3天前 · 24.1MB</div>
                  </div>
                  <div class="document-status-badge status-completed">已完成</div>
                  <div class="document-actions">⋮</div>
                </div>
                <div class="document-entry">
                  <div class="document-type-badge doc-pdf">P</div>
                  <div class="document-info">
                    <div class="document-title">员工入职培训手册第三版.pdf</div>
                    <div class="document-metadata">赵六 · 1周前 · 15.6MB</div>
                  </div>
                  <div class="document-status-badge status-completed">已完成</div>
                  <div class="document-actions">⋮</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 侧边区域 -->
          <div class="sidebar-area">
            <!-- 个人工作区 -->
            <div class="workspace-section">
              <div class="profile-workspace">
                <div class="profile-avatar-large">管</div>
                <div class="profile-full-name">系统管理员</div>
                <div class="profile-position">Administrator</div>
                <div class="profile-statistics">
                  <div class="profile-stat">
                    <div class="stat-number">198</div>
                    <div class="stat-description">我的文档</div>
                  </div>
                  <div class="profile-stat">
                    <div class="stat-number">37</div>
                    <div class="stat-description">协作项目</div>
                  </div>
                  <div class="profile-stat">
                    <div class="stat-number">94</div>
                    <div class="stat-description">团队成员</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统运行监控 -->
            <div class="workspace-section">
              <div class="section-header">
                <h3 class="section-title">系统运行监控</h3>
                <span class="section-action">详细报告</span>
              </div>
              <div class="monitoring-workspace">
                <div class="monitor-entry">
                  <span class="monitor-service">数据库服务</span>
                  <div class="monitor-status">
                    <span class="status-light status-online"></span>
                    <span style="color: #10b981;">运行正常</span>
                  </div>
                </div>
                <div class="monitor-entry">
                  <span class="monitor-service">OnlyOffice服务</span>
                  <div class="monitor-status">
                    <span class="status-light status-online"></span>
                    <span style="color: #10b981;">服务可用</span>
                  </div>
                </div>
                <div class="monitor-entry">
                  <span class="monitor-service">FileNet连接</span>
                  <div class="monitor-status">
                    <span class="status-light status-warning"></span>
                    <span style="color: #f59e0b;">连接缓慢</span>
                  </div>
                </div>
                <div class="monitor-entry">
                  <span class="monitor-service">CPU使用率</span>
                  <div class="monitor-status">
                    <span style="color: #1e293b;">32%</span>
                  </div>
                </div>
                <div class="monitor-entry">
                  <span class="monitor-service">内存使用率</span>
                  <div class="monitor-status">
                    <span style="color: #1e293b;">71%</span>
                  </div>
                </div>
                <div class="monitor-entry">
                  <span class="monitor-service">存储空间</span>
                  <div class="monitor-status">
                    <span style="color: #f59e0b;">80%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // 菜单导航交互
    document.querySelectorAll('.menu-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        document.querySelectorAll('.menu-item').forEach(menu => menu.classList.remove('current'));
        item.classList.add('current');
      });
    });

    // 操作工作台交互
    document.querySelectorAll('.operation-tile').forEach(tile => {
      tile.addEventListener('click', () => {
        const title = tile.querySelector('.operation-title').textContent;
        console.log('执行操作:', title);
      });
    });

    // 文档项交互
    document.querySelectorAll('.document-entry').forEach(entry => {
      entry.addEventListener('click', () => {
        const docTitle = entry.querySelector('.document-title').textContent;
        console.log('打开文档:', docTitle);
      });
    });

    // 通知中心交互
    document.querySelector('.notification-center').addEventListener('click', () => {
      console.log('打开通知中心');
    });

    // 用户资料菜单交互
    document.querySelector('.user-profile-menu').addEventListener('click', () => {
      console.log('打开用户菜单');
    });

    // 移动端侧边栏切换
    function toggleNavbar() {
      const sidebar = document.querySelector('.sidebar-nav');
      sidebar.classList.toggle('active');
    }

    // 响应式处理
    window.addEventListener('resize', () => {
      if (window.innerWidth > 1200) {
        document.querySelector('.sidebar-nav').classList.remove('active');
      }
    });
  </script>
</body>
</html> 