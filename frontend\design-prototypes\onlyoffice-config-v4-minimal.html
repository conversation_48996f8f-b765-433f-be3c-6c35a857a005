<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 极简版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fafafa;
            color: #333;
            line-height: 1.7;
            font-size: 14px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* 顶部导航 */
        .top-nav {
            background: white;
            border-bottom: 1px solid #e5e5e5;
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .brand-text h1 {
            font-size: 18px;
            font-weight: 600;
            color: #000;
        }

        .nav-actions {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            background: white;
            color: #333;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .btn:hover {
            border-color: #ccc;
            background: #f8f8f8;
        }

        .btn-primary {
            background: #000;
            color: white;
            border-color: #000;
        }

        .btn-primary:hover {
            background: #333;
        }

        /* 主要布局 */
        .main-layout {
            padding: 40px 0;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 60px;
            min-height: calc(100vh - 200px);
        }

        /* 左侧导航 */
        .side-nav {
            position: sticky;
            top: 120px;
            height: fit-content;
        }

        .nav-title {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            margin-bottom: 24px;
        }

        .nav-search {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            font-size: 13px;
            background: white;
            margin-bottom: 32px;
            transition: border-color 0.2s ease;
        }

        .nav-search:focus {
            outline: none;
            border-color: #999;
        }

        .nav-search::placeholder {
            color: #999;
        }

        .template-list {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .template-item {
            padding: 12px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .template-item:hover {
            background: #f8f8f8;
        }

        .template-item.active {
            background: #f0f0f0;
            border-color: #e5e5e5;
        }

        .template-name {
            font-size: 14px;
            font-weight: 500;
            color: #000;
            margin-bottom: 4px;
        }

        .template-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .template-status {
            display: flex;
            gap: 6px;
            margin-top: 8px;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-top: 4px;
        }

        .status-active {
            background: #22c55e;
        }

        .status-inactive {
            background: #e5e5e5;
        }

        .status-default {
            background: #f59e0b;
        }

        .status-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 右侧主内容 */
        .main-content {
            padding-left: 20px;
        }

        .content-header {
            margin-bottom: 48px;
            padding-bottom: 24px;
            border-bottom: 1px solid #e5e5e5;
        }

        .content-title {
            font-size: 28px;
            font-weight: 600;
            color: #000;
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 24px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        /* 配置区域 */
        .config-tabs {
            display: flex;
            gap: 32px;
            margin-bottom: 40px;
            border-bottom: 1px solid #e5e5e5;
        }

        .tab-link {
            padding: 12px 0;
            font-size: 15px;
            font-weight: 500;
            color: #666;
            text-decoration: none;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab-link:hover {
            color: #000;
        }

        .tab-link.active {
            color: #000;
            border-bottom-color: #000;
        }

        .config-section {
            margin-bottom: 64px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            margin-bottom: 32px;
        }

        .config-items {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .config-item {
            padding: 32px;
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .config-item:hover {
            border-color: #ccc;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .item-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            margin-bottom: 4px;
        }

        .item-info p {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .required-badge {
            padding: 4px 8px;
            background: #fee2e2;
            color: #dc2626;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-radius: 4px;
        }

        .controls-section {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #fafafa;
            border-radius: 6px;
            border: 1px solid #f0f0f0;
        }

        .control-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .switch {
            width: 40px;
            height: 22px;
            background: #e5e5e5;
            border-radius: 11px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .switch.active {
            background: #000;
        }

        .switch-handle {
            width: 18px;
            height: 18px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .switch.active .switch-handle {
            transform: translateX(18px);
        }

        .status-summary {
            margin-top: 20px;
            padding: 16px 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #22c55e;
        }

        .status-summary.disabled {
            border-left-color: #e5e5e5;
        }

        .summary-text {
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .summary-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .summary-enabled {
            background: #22c55e;
        }

        .summary-disabled {
            background: #e5e5e5;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: #999;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #666;
        }

        .empty-desc {
            font-size: 14px;
            line-height: 1.5;
        }

        /* 底部操作 */
        .bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e5e5e5;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(20px);
        }

        .save-status {
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .save-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #22c55e;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .side-nav {
                position: static;
                order: 2;
            }

            .main-content {
                order: 1;
                padding-left: 0;
            }

            .config-tabs {
                flex-wrap: wrap;
                gap: 16px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .main-layout {
                padding: 24px 0;
            }

            .content-title {
                font-size: 24px;
            }

            .config-item {
                padding: 24px;
            }

            .controls-section {
                gap: 16px;
            }

            .control-row {
                padding: 12px 16px;
            }
        }

        /* 加载动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .config-item {
            animation: fadeIn 0.3s ease;
        }

        .template-item {
            animation: fadeIn 0.2s ease;
        }

        /* 微交互 */
        .btn:active {
            transform: translateY(1px);
        }

        .switch:active .switch-handle {
            transform: scale(0.95);
        }

        .switch.active:active .switch-handle {
            transform: translateX(18px) scale(0.95);
        }

        .template-item:active {
            transform: scale(0.98);
        }

        /* 焦点样式 */
        .btn:focus,
        .nav-search:focus,
        .switch:focus {
            outline: 2px solid #000;
            outline-offset: 2px;
        }

        /* 打印样式 */
        @media print {
            .nav-actions,
            .bottom-actions {
                display: none;
            }

            .main-layout {
                grid-template-columns: 1fr;
            }

            .side-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="top-nav">
        <div class="container">
            <div class="nav-content">
                <div class="logo-section">
                    <div class="logo">OO</div>
                    <div class="brand-text">
                        <h1>OnlyOffice Configuration</h1>
                    </div>
                </div>
                <div class="nav-actions">
                    <a href="#" class="btn">
                        <i class="fas fa-upload"></i>
                        Import
                    </a>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        New
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <div class="container">
        <div class="main-layout">
            <!-- 左侧导航 -->
            <aside class="side-nav">
                <h2 class="nav-title">Templates</h2>
                <input type="text" class="nav-search" placeholder="Search...">
                
                <div class="template-list">
                    <div class="template-item active">
                        <div class="template-name">Default Editor</div>
                        <div class="template-desc">Standard configuration for document editing</div>
                        <div class="template-status">
                            <div class="status-dot status-default"></div>
                            <span class="status-label">Default</span>
                            <div class="status-dot status-active"></div>
                            <span class="status-label">Active</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-name">Read Only</div>
                        <div class="template-desc">View-only access with basic controls</div>
                        <div class="template-status">
                            <div class="status-dot status-active"></div>
                            <span class="status-label">Active</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-name">Collaboration</div>
                        <div class="template-desc">Real-time editing with full collaboration</div>
                        <div class="template-status">
                            <div class="status-dot status-active"></div>
                            <span class="status-label">Active</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-name">Restricted</div>
                        <div class="template-desc">Limited functionality for external users</div>
                        <div class="template-status">
                            <div class="status-dot status-inactive"></div>
                            <span class="status-label">Inactive</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧主内容 -->
            <main class="main-content">
                <div class="content-header">
                    <h1 class="content-title">Default Editor</h1>
                    <p class="content-subtitle">Configure permissions, interface elements and functionality for the standard editing experience.</p>
                    <div class="content-actions">
                        <button class="btn">
                            <i class="fas fa-undo"></i>
                            Reset
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Save Changes
                        </button>
                    </div>
                </div>

                <nav class="config-tabs">
                    <a href="#" class="tab-link active">Permissions</a>
                    <a href="#" class="tab-link">Interface</a>
                    <a href="#" class="tab-link">Features</a>
                    <a href="#" class="tab-link">Collaboration</a>
                </nav>

                <div class="config-section">
                    <h2 class="section-title">Document Permissions</h2>
                    
                    <div class="config-items">
                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Edit Permission</h3>
                                    <p>Controls whether users can modify document content. This is a core permission that affects most editing functionality.</p>
                                </div>
                                <span class="required-badge">Required</span>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Allow editing</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Permission enabled and visible to users
                                </div>
                            </div>
                        </div>

                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Download Permission</h3>
                                    <p>Allows users to download a copy of the document to their local device.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Allow download</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Permission enabled and visible to users
                                </div>
                            </div>
                        </div>

                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Print Permission</h3>
                                    <p>Controls access to document printing functionality.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Allow printing</span>
                                    <div class="switch">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary disabled">
                                <div class="summary-text">
                                    <div class="summary-icon summary-disabled"></div>
                                    Permission disabled and hidden from users
                                </div>
                            </div>
                        </div>

                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Comment Permission</h3>
                                    <p>Enables users to add, edit, and view comments on the document.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Allow comments</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Permission enabled and visible to users
                                </div>
                            </div>
                        </div>

                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Review Permission</h3>
                                    <p>Controls access to review mode and change tracking functionality.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Enable review mode</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Permission enabled and visible to users
                                </div>
                            </div>
                        </div>

                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Form Filling</h3>
                                    <p>Allows users to fill out forms and interactive elements within documents.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Enable form filling</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Permission enabled and visible to users
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="config-section">
                    <h2 class="section-title">Interface Customization</h2>
                    
                    <div class="config-items">
                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Toolbar Display</h3>
                                    <p>Controls the visibility and behavior of the main editing toolbar.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Show toolbar</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Interface element enabled and visible
                                </div>
                            </div>
                        </div>

                        <div class="config-item">
                            <div class="item-header">
                                <div class="item-info">
                                    <h3>Status Bar</h3>
                                    <p>Shows document information and status at the bottom of the editor.</p>
                                </div>
                            </div>

                            <div class="controls-section">
                                <div class="control-row">
                                    <span class="control-label">Show status bar</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">Show in interface</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="status-summary">
                                <div class="summary-text">
                                    <div class="summary-icon summary-enabled"></div>
                                    Interface element enabled and visible
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
        <div class="save-status">
            <div class="save-indicator"></div>
            All changes saved automatically
        </div>
        <div class="action-buttons">
            <button class="btn">Preview</button>
            <button class="btn">Export</button>
            <button class="btn btn-primary">Publish Changes</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模板选择
            const templateItems = document.querySelectorAll('.template-item');
            templateItems.forEach(item => {
                item.addEventListener('click', function() {
                    templateItems.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 标签页切换
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    tabLinks.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 开关切换
            const switches = document.querySelectorAll('.switch');
            switches.forEach(switchEl => {
                switchEl.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 更新状态摘要
                    const configItem = this.closest('.config-item');
                    const statusSummary = configItem.querySelector('.status-summary');
                    const summaryText = statusSummary.querySelector('.summary-text');
                    const summaryIcon = statusSummary.querySelector('.summary-icon');
                    const switches = configItem.querySelectorAll('.switch');
                    
                    const enableSwitch = switches[0];
                    const visibilitySwitch = switches[1];
                    
                    if (enableSwitch.classList.contains('active') && visibilitySwitch.classList.contains('active')) {
                        statusSummary.classList.remove('disabled');
                        summaryIcon.className = 'summary-icon summary-enabled';
                        summaryText.innerHTML = '<div class="summary-icon summary-enabled"></div>Permission enabled and visible to users';
                    } else if (enableSwitch.classList.contains('active')) {
                        statusSummary.classList.remove('disabled');
                        summaryIcon.className = 'summary-icon summary-enabled';
                        summaryText.innerHTML = '<div class="summary-icon summary-enabled"></div>Permission enabled but hidden from users';
                    } else if (visibilitySwitch.classList.contains('active')) {
                        statusSummary.classList.add('disabled');
                        summaryIcon.className = 'summary-icon summary-disabled';
                        summaryText.innerHTML = '<div class="summary-icon summary-disabled"></div>Permission disabled but visible to users';
                    } else {
                        statusSummary.classList.add('disabled');
                        summaryIcon.className = 'summary-icon summary-disabled';
                        summaryText.innerHTML = '<div class="summary-icon summary-disabled"></div>Permission disabled and hidden from users';
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.nav-search');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                templateItems.forEach(item => {
                    const name = item.querySelector('.template-name').textContent.toLowerCase();
                    const desc = item.querySelector('.template-desc').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || desc.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });

            // 键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    // 清除搜索
                    searchInput.value = '';
                    searchInput.dispatchEvent(new Event('input'));
                }
                
                if (e.key === '/' && !e.target.matches('input, textarea')) {
                    e.preventDefault();
                    searchInput.focus();
                }
            });

            // 自动保存提示
            let saveTimeout;
            switches.forEach(switchEl => {
                switchEl.addEventListener('click', function() {
                    const saveStatus = document.querySelector('.save-status');
                    const saveIndicator = document.querySelector('.save-indicator');
                    
                    saveStatus.innerHTML = '<div class="save-indicator" style="background: #f59e0b;"></div>Saving changes...';
                    
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        saveStatus.innerHTML = '<div class="save-indicator"></div>All changes saved automatically';
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html> 