import { IsOptional, IsString, IsNumber, Is<PERSON><PERSON>y, IsBoolean, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * OnlyOffice回调请求DTO
 * 
 * @description 处理OnlyOffice编辑器回调请求的数据结构
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */
export class CallbackDto {
  [key: string]: unknown;

  @ApiProperty({
    description: '文档唯一标识',
    example: 'doc-123-1640123456789',
  })
  @IsString()
  key: string;

  @ApiProperty({
    description: '文档状态',
    example: 2,
  })
  @IsNumber()
  status: number;

  @ApiPropertyOptional({
    description: '文档下载URL（当status为2或6时提供）',
    example: 'https://onlyoffice-server/cache/files/doc_123.docx',
  })
  @IsOptional()
  @IsUrl()
  url?: string;

  @ApiPropertyOptional({
    description: '变更历史下载URL',
    example: 'https://onlyoffice-server/cache/files/changes.zip',
  })
  @IsOptional()
  @IsUrl()
  changesurl?: string;

  @ApiPropertyOptional({
    description: '文档修改历史',
    example: {
      serverVersion: '8.3.3',
      changes: [{}]
    },
  })
  @IsOptional()
  history?: Record<string, unknown>;

  @ApiPropertyOptional({
    description: '编辑用户列表',
    example: ['user-1', 'user-2'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  users?: string[];

  @ApiPropertyOptional({
    description: '用户操作列表',
    example: [{ type: 0, userid: 'user-1' }],
  })
  @IsOptional()
  @IsArray()
  actions?: Array<{
    type: number;
    userid: string;
  }>;

  @ApiPropertyOptional({
    description: '最后保存时间',
    example: '2024-12-19T10:30:00.000Z',
  })
  @IsOptional()
  @IsString()
  lastsave?: string;

  @ApiPropertyOptional({
    description: '文档是否未被修改',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  notmodified?: boolean;

  @ApiPropertyOptional({
    description: '文件类型',
    example: 'docx',
  })
  @IsOptional()
  @IsString()
  filetype?: string;

  @ApiPropertyOptional({
    description: '强制保存类型（当触发强制保存时）',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  forcesavetype?: number;

  @ApiPropertyOptional({
    description: '回调中提供的文件名',
    example: 'document.docx',
  })
  @IsOptional()
  @IsString()
  filename?: string;

  @ApiPropertyOptional({
    description: 'OnlyOffice文档服务器提供的JWT token（用于验证回调来源）',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsOptional()
  @IsString()
  token?: string;

  @ApiPropertyOptional({
    description: '用户自定义数据（强制保存时包含的元数据）',
    example: '{"fileId":"doc-123","userId":"user-1","timestamp":"2024-12-19T10:30:00.000Z"}',
  })
  @IsOptional()
  @IsString()
  userdata?: string;
}

/**
 * 回调响应DTO
 */
export class CallbackResponseDto {
  @ApiProperty({
    description: '错误码，0表示成功',
    example: 0,
  })
  error: number;

  @ApiPropertyOptional({
    description: '错误消息',
    example: '处理成功',
  })
  message?: string;
}

/**
 * 保存状态检查响应DTO
 */
export class SaveStatusResponseDto {
  @ApiProperty({
    description: '请求是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '当前状态',
    example: 'saved',
  })
  status: string;

  @ApiProperty({
    description: '状态描述',
    example: '文档已保存',
  })
  message: string;

  @ApiPropertyOptional({
    description: '文件名',
    example: 'document.docx',
  })
  fileName?: string;

  @ApiPropertyOptional({
    description: '文档版本号',
    example: 3,
  })
  version?: number;

  @ApiPropertyOptional({
    description: '最后修改时间',
    example: '2024-12-19T10:30:00.000Z',
  })
  lastModifiedDb?: string;

  @ApiPropertyOptional({
    description: '当前FileNet文档ID',
    example: 'fn_doc_123',
  })
  currentFnDocId?: string;

  @ApiPropertyOptional({
    description: '距离上次保存的时间',
    example: '2.5分钟前 (数据库记录)',
  })
  timeSinceLastSaveDb?: string;
}

/**
 * 强制保存请求DTO
 */
export class ForceSaveDto {
  @ApiProperty({
    description: '文档密钥（编辑器会话标识）',
    example: 'doc-123-1640123456789',
  })
  @IsString()
  documentKey: string;

  @ApiPropertyOptional({
    description: '是否强制保存',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  force?: boolean;

  @ApiPropertyOptional({
    description: '请求时间戳',
    example: '2024-12-19T10:30:00.000Z',
  })
  @IsOptional()
  @IsString()
  timestamp?: string;
}

/**
 * 强制保存响应DTO
 */
export class ForceSaveResponseDto {
  @ApiProperty({
    description: '请求是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '响应消息',
    example: '强制保存命令已成功发送至 OnlyOffice',
  })
  message: string;
} 