/**
 * 文档路由
 */
const express = require('express');
const router = express.Router();
const path = require('path');
const documentService = require('../services/document');
const jwtService = require('../services/jwt');
const fileStorage = require('../services/fileStorage');
const { uploadMiddleware, sanitizeFileName } = require('../middleware/upload');
const { apiError } = require('../middleware/error');
const config = require('../config');
const fs = require('fs-extra');
const templateService = require('../services/templateService');

// 获取文档列表
router.get('/', async (req, res, next) => {
    try {
        // 获取分页参数
        const limit = parseInt(req.query.limit) || 100;
        const offset = parseInt(req.query.offset) || 0;

        const documentList = await documentService.getDocumentList({ limit, offset });
        res.json({
            success: true,
            documents: documentList
        });
    } catch (error) {
        next(error);
    }
});

// 显示数据库文件统计（调试用）
router.get('/debug/stats', async (req, res, next) => {
    try {
        const db = require('../services/database');

        // 获取文件统计
        const fileStats = await db.query(`
            SELECT 
                COUNT(*) as total_files,
                SUM(file_size) as total_size,
                MAX(file_size) as max_size,
                MIN(file_size) as min_size,
                AVG(file_size) as avg_size
            FROM files
            WHERE is_deleted = FALSE
        `);

        // 获取版本统计
        const versionStats = await db.query(`
            SELECT 
                COUNT(*) as total_versions,
                AVG(version) as avg_versions_per_file,
                MAX(version) as max_version
            FROM files
            WHERE is_deleted = FALSE
        `);

        // 获取扩展名统计
        const extensionStats = await db.query(`
            SELECT 
                extension,
                COUNT(*) as count
            FROM files
            WHERE is_deleted = FALSE
            GROUP BY extension
            ORDER BY count DESC
        `);

        res.json({
            success: true,
            fileStats: fileStats[0],
            versionStats: versionStats[0],
            extensionStats
        });
    } catch (error) {
        next(error);
    }
});

// 上传文档
router.post('/upload', uploadMiddleware, async (req, res, next) => {
    try {
        if (!req.files || Object.keys(req.files).length === 0) {
            return next(apiError('没有上传文件', 400));
        }

        const file = req.files.file;
        const fileName = sanitizeFileName(file.name);
        console.log('上传文件名:', fileName);

        // 保存到临时目录
        const tempPath = path.join(config.storage.tmpDir, `upload_${Date.now()}_${fileName}`);
        await file.mv(tempPath);

        // 保存文件到存储系统并记录到数据库
        const result = await documentService.uploadDocument({
            originalName: fileName,
            tempPath: tempPath,
            size: file.size,
            userId: req.body.userId || 'anonymous'
        });

        // 获取文档配置
        const docConfig = await documentService.getDocumentConfig(result.fileId);

        // 生成JWT令牌
        const token = jwtService.generateJWT(docConfig);

        // 返回响应
        const response = {
            success: true,
            file: {
                id: result.fileId,
                name: result.fileName,
                type: docConfig.document.fileType,
                url: docConfig.document.url
            },
            token: token,
            config: docConfig
        };

        console.log('上传响应:', {
            success: response.success,
            fileId: response.file.id,
            fileName: response.file.name
        });

        res.json(response);
    } catch (error) {
        next(apiError('上传文件失败', 500, error));
    }
});

// 删除文档
router.delete('/:id', async (req, res, next) => {
    try {
        const fileId = req.params.id;
        const success = await documentService.deleteDocument(fileId);

        if (!success) {
            return next(apiError('文件不存在或删除失败', 404));
        }

        res.json({
            success: true,
            message: '文件已成功删除'
        });
    } catch (error) {
        next(apiError('删除文件失败', 500, error));
    }
});

// 通过ID获取文档
router.get('/:fileId', async (req, res, next) => {
    try {
        const fileId = req.params.fileId;
        console.log('收到通过ID请求文件:', fileId);

        // 设置CORS头，允许OnlyOffice服务器访问
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        const db = require('../services/database');
        const filenetService = require('../services/filenetService');
        let docInfo = null;
        let fnDocId = null;

        // 检查是否是UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const isUuid = uuidRegex.test(fileId);

        // 根据ID类型采用不同的查询策略
        if (isUuid) {
            // UUID格式ID - 直接查询filenet_documents表
            console.log(`检测到UUID格式的文件ID: ${fileId}，尝试从filenet_documents查询`);

            // 查询文件信息，不考虑is_deleted状态，以便调试
            docInfo = await db.queryOne(`
                SELECT id, fn_doc_id, original_name, file_size, mime_type, extension, version, is_deleted 
                FROM filenet_documents 
                WHERE id = ?
            `, [fileId]);

            if (docInfo) {
                console.log('找到文件信息:', docInfo);
                fnDocId = docInfo.fn_doc_id;

                // 检查文件是否被标记为删除
                if (docInfo.is_deleted) {
                    console.warn(`请求的文件 ${fileId} 已被标记为删除，但仍将尝试提供下载`);
                }
            } else {
                console.error('在filenet_documents表中找不到对应的UUID文件ID:', fileId);
            }
        } else if (!isNaN(fileId)) {
            // 数字ID - 查询是否是FileNet文档
            console.log(`检测到数字格式的文件ID: ${fileId}，尝试查询是否是FileNet文档`);

            // 尝试根据数字ID查找对应的FileNet文档
            const fileNetDoc = await db.queryOne(`
                SELECT fn_doc_id 
                FROM filenet_documents 
                WHERE id = ? AND is_deleted = FALSE
            `, [fileId]);

            if (fileNetDoc && fileNetDoc.fn_doc_id) {
                console.log(`检测到ID为 ${fileId} 的请求是针对FileNet文档，正在获取FileNet内容`);
                fnDocId = fileNetDoc.fn_doc_id;

                // 获取完整的文档信息
                docInfo = await db.queryOne(`
                    SELECT id, fn_doc_id, original_name, file_size, mime_type, extension, version 
                    FROM filenet_documents 
                    WHERE id = ? AND is_deleted = FALSE
                `, [fileId]);
            }
        }

        // 如果找到了FileNet文档信息，尝试下载和提供文件
        if (docInfo && fnDocId) {
            console.log('找到FileNet文档:', docInfo.original_name);

            // 处理文件名编码问题
            let fileName = docInfo.original_name;
            try {
                if (/%[0-9A-F]{2}/i.test(fileName)) {
                    const decodedName = decodeURIComponent(fileName);
                    if (decodedName !== fileName) {
                        console.log('original_name URL解码后:', decodedName);
                        fileName = decodedName;
                    }
                }
            } catch (e) {
                console.warn('文件名解码失败 (已忽略):', e.message);
            }

            // 从FileNet获取文档内容
            try {
                console.log(`尝试从FileNet下载文档，FileNet ID: ${fnDocId}`);
                const fileContent = await filenetService.downloadDocument(fnDocId);

                // 设置必要的HTTP头
                res.setHeader('Content-Type', docInfo.mime_type || 'application/octet-stream');
                if (fileContent) {
                    res.setHeader('Content-Length', fileContent.length);
                }
                res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`);
                res.setHeader('Cache-Control', 'no-cache');

                console.log(`提供FileNet文档 ${fileName} 服务，内容类型: ${docInfo.mime_type}`);

                // 发送文件内容
                return res.send(fileContent);
            } catch (error) {
                console.error('从FileNet下载文档失败:', error);
                return next(apiError('从FileNet下载文档失败', 500, error));
            }
        }

        // 如果不是FileNet文档或未找到，尝试从本地文件系统获取
        console.log('未找到FileNet文档或ID不是UUID格式，尝试从本地文件系统获取');

        // 从数据库获取文件信息
        const fileInfo = await fileStorage.getFileById(fileId);
        if (!fileInfo) {
            console.error('找不到对应的文件ID:', fileId);
            return next(apiError('文件不存在', 404));
        }

        console.log('通过ID请求文件:', fileId, '->', fileInfo.original_name);

        // 构建文件路径
        const filePath = path.join(config.storage.uploadDir, fileInfo.storage_name);
        console.log('读取文件路径:', filePath);

        if (!fs.existsSync(filePath)) {
            console.error('文件不存在:', filePath);
            return next(apiError('文件不存在', 404));
        }

        // 获取文件大小和最后修改时间，用于调试
        const stats = fs.statSync(filePath);
        console.log('文件大小:', stats.size, '字节');
        console.log('文件最后修改时间:', stats.mtime);

        // 设置正确的Content-Type
        const contentType = fileInfo.mime_type || 'application/octet-stream';

        // 设置必要的HTTP头
        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Length', stats.size);
        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(fileInfo.original_name)}`);
        res.setHeader('Last-Modified', stats.mtime.toUTCString());
        res.setHeader('Cache-Control', 'no-cache');

        console.log(`提供文件 ${fileInfo.original_name} 服务，内容类型: ${contentType}`);

        // 使用流式传输，可以处理大文件
        const fileStream = fs.createReadStream(filePath);
        fileStream.on('error', (error) => {
            console.error('文件流读取错误:', error);
            if (!res.headersSent) {
                return next(apiError('文件读取错误', 500, error));
            }
        });

        // 直接pipe到响应
        fileStream.pipe(res);
    } catch (error) {
        next(apiError('获取文件失败', 500, error));
    }
});

// 通过FileNet ID获取文档
router.get('/filenet/:fnDocId', async (req, res, next) => {
    try {
        const fnDocId = req.params.fnDocId;
        console.log('收到FileNet文档请求:', fnDocId);

        // 设置CORS头，允许OnlyOffice服务器访问
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // 获取FileNet文档服务
        const filenetService = require('../services/filenetService');
        const db = require('../services/database');

        // 从数据库获取文档信息
        const fileInfo = await db.queryOne(`
            SELECT id, fn_doc_id, original_name, file_size, mime_type, extension, version 
            FROM filenet_documents 
            WHERE fn_doc_id = ? AND is_deleted = FALSE
        `, [fnDocId]);

        if (!fileInfo) {
            console.error('找不到FileNet文档:', fnDocId);
            return next(apiError('文档不存在', 404));
        }

        console.log('找到FileNet文档:', fileInfo.original_name);

        // 从FileNet下载文档
        try {
            const fileContent = await filenetService.downloadDocument(fnDocId);

            // 修复文件名编码问题
            let fileName = fileInfo.original_name;
            try {
                // 尝试解码原始文件名（如果它是URL编码的）
                if (/%[0-9A-F]{2}/.test(fileName)) {
                    fileName = decodeURIComponent(fileName);
                }
            } catch (e) {
                console.warn('文件名解码失败:', e);
            }

            // 设置必要的HTTP头
            res.setHeader('Content-Type', fileInfo.mime_type || 'application/octet-stream');
            if (fileContent) {
                res.setHeader('Content-Length', fileContent.length);
            }
            res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`);
            res.setHeader('Cache-Control', 'no-cache');

            console.log(`提供FileNet文档 ${fileName} 服务，内容类型: ${fileInfo.mime_type}`);

            // 发送文件内容
            res.send(fileContent);
        } catch (error) {
            console.error('从FileNet下载文档失败:', error);
            return next(apiError('从FileNet下载文档失败', 500, error));
        }
    } catch (error) {
        next(apiError('获取FileNet文档失败', 500, error));
    }
});

/**
 * @swagger
 * /api/documents/from-template:
 *   post:
 *     summary: 基于模板创建新文档
 *     tags: [Documents]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - template_id
 *               - name
 *             properties:
 *               template_id:
 *                 type: string
 *                 format: uuid
 *                 description: 要使用的模板ID
 *               name:
 *                 type: string
 *                 description: 新文档的名称 (不含扩展名)
 *               user_id: # 可选，如果需要指定创建者
 *                 type: string
 *                 description: 创建文档的用户ID (如果未提供，则默认为 system)
 *     responses:
 *       201:
 *         description: 文档创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data: { $ref: '#/components/schemas/DocumentOutput' } # 假设 DocumentOutput schema 已定义
 *       400:
 *         description: 请求参数错误 (如模板ID无效或名称缺失)
 *       404:
 *         description: 模板未找到或源文档无法访问
 *       500:
 *         description: 服务器错误 (如文件复制失败或数据库错误)
 */
router.post('/from-template', /* isAuthenticated, */ async (req, res, next) => {
    const { template_id, name, user_id } = req.body;

    if (!template_id || !name) {
        return next(apiError('模板ID和新文档名称不能为空', 400));
    }

    try {
        const documentCreatorId = (req.user && req.user.id) || user_id || 'system'; // 从认证用户或请求体获取
        const newDocument = await templateService.createDocumentFromTemplate(template_id, name, documentCreatorId);
        res.status(201).json({ success: true, data: newDocument });
    } catch (error) {
        if (error.message.includes('不能为空') || error.message.includes('模板ID和新文档名称不能为空')) {
            return next(apiError(error.message, 400));
        }
        if (error.message.includes('模板不存在') || error.message.includes('源文档不存在')) {
            return next(apiError(error.message, 404));
        }
        // 其他错误（如文件复制、数据库）按500处理
        next(apiError(error.message || '从模板创建文档失败', 500, error));
    }
});

module.exports = router; 