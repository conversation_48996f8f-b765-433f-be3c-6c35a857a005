import { Module } from '@nestjs/common';
import { ConfigTemplateController } from './controllers/config-template.controller';
import { ConfigTemplateService } from './services/config-template.service';
import { JwtConfigService } from './services/jwt-config.service';
import { OnlyOfficeJwtService } from './services/onlyoffice-jwt.service';
import { JwtConfigController } from './controllers/jwt-config.controller';
import { SystemConfigController } from './controllers/system-config.controller';
import { SystemConfigService } from './services/system-config.service';
import { DatabaseModule } from '../database/database.module';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { HybridConfigService } from './services/hybrid-config.service';
import { HybridConfigController } from './controllers/hybrid-config.controller';

/**
 * OnlyOffice配置模块
 * 
 * 提供OnlyOffice配置模板的管理功能
 * 迁移自原有的配置模板系统
 * 增加混合配置管理功能
 * 
 * @class ConfigModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.1.0 (增加混合配置管理)
 */
@Module({
  imports: [DatabaseModule, NestConfigModule],
  controllers: [
    ConfigTemplateController, 
    JwtConfigController, 
    SystemConfigController,
    HybridConfigController
  ],
  providers: [
    ConfigTemplateService, 
    JwtConfigService, 
    OnlyOfficeJwtService, 
    SystemConfigService,
    HybridConfigService
  ],
  exports: [
    ConfigTemplateService, 
    JwtConfigService, 
    OnlyOfficeJwtService, 
    SystemConfigService,
    HybridConfigService
  ],
})
export class ConfigModule {} 