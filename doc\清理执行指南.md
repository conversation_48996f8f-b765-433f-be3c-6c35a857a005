# OnlyOffice 项目清理执行指南

## 🎯 快速开始

### 第一步：了解现状
1. 阅读 [`项目代码清理分析报告.md`](项目代码清理分析报告.md)
2. 查看 [`TODO_清理任务列表.md`](TODO_清理任务列表.md)
3. 理解项目当前存在的问题

### 第二步：选择任务
从以下优先级中选择任务：
- 🚨 **立即处理** - 修复404错误和安全问题
- 🟡 **中等优先级** - 验证冗余路由和功能
- 🟢 **低优先级** - 代码质量优化

## 🚨 立即需要处理的问题

### 1. 修复缺失的HTML文件（最高优先级）

```bash
# 检查这些文件是否存在
ls -la public/template-admin.html          # ❌ 不存在
ls -la public/onlyoffice-config.html       # ❌ 不存在  
ls -la public/test-config-templates.html   # ❌ 不存在
ls -la public/simple-editor-list.html      # ❌ 不存在
```

**解决方案选择：**
- 选项A：创建对应的HTML文件
- 选项B：修改导航链接指向现有页面
- 选项C：移除相关链接和路由

**建议操作：**
1. `test-config-templates.html` → 改为 `template-config-test.html`
2. `onlyoffice-config.html` → 改为 `weboffice-config.html`
3. `template-admin.html` → 改为 `template-manager.html`
4. `simple-editor-list.html` → 移除路由（似乎未使用）

### 2. 修复安全配置

```bash
# 检查JWT配置
grep -n "your_very_secret_jwt_key" config/index.js
```

**立即操作：**
```javascript
// config/index.js:29 修改为：
jwtSecret: process.env.JWT_SECRET || (() => {
    throw new Error('JWT_SECRET environment variable is required');
})()
```

## 🟡 验证和清理建议

### 1. 验证路由使用情况

```bash
# 启动开发服务器
npm run dev

# 使用浏览器开发者工具检查以下路由：
# /documents
# /edit/:id  
# /upload
# /simple-editor/:id
```

### 2. 检查templates.js是否还在使用

```bash
# 搜索对templates API的调用
grep -r "/api/templates" public/ views/
grep -r "templates.js" routes/
```

如果没有找到引用，可以考虑移除 `routes/templates.js`

### 3. 合并api.js到其他文件

```bash
# 查看api.js内容
cat routes/api.js

# 考虑将内容合并到filenetRoutes.js
```

## 🛠️ 清理操作模板

### 删除文件前的检查清单

```bash
# 1. 全局搜索文件引用
grep -r "filename" --exclude-dir=node_modules .

# 2. 检查路由注册
grep -r "require.*filename" .

# 3. 检查静态文件引用
grep -r "filename" public/ views/

# 4. 备份文件
cp filename filename.backup
```

### 安全删除流程

1. **创建清理分支**
   ```bash
   git checkout -b cleanup/remove-unused-files
   ```

2. **单个文件处理**
   ```bash
   # 每次只处理一个文件或功能
   # 提交前测试所有相关功能
   ```

3. **提交和验证**
   ```bash
   git add .
   git commit -m "清理: 移除未使用的XXX文件"
   npm run dev  # 测试功能
   ```

## 📝 清理记录模板

每次清理后，在相应的MD文件中更新：

```markdown
### 已完成的清理项目

- [x] **修复 /public/template-admin.html**
  - 日期：2024-XX-XX
  - 操作：重定向到 template-manager.html
  - 测试：✅ 导航链接正常工作
  - 影响：修复了404错误

- [x] **移除 routes/api.js**
  - 日期：2024-XX-XX
  - 操作：合并到 filenetRoutes.js
  - 测试：✅ API功能正常
  - 影响：减少1个路由文件
```

## ⚠️ 重要提醒

### 在删除任何代码前：

1. **确保备份** 
   ```bash
   git stash push -m "清理前备份"
   ```

2. **团队沟通**
   - 通知团队成员计划的清理内容
   - 确认是否有人正在使用相关功能

3. **测试覆盖**
   - 手动测试所有相关功能
   - 检查是否有自动化测试失败

4. **分步进行**
   - 不要一次性删除多个文件
   - 每个清理操作都单独提交

### 如果出现问题：

```bash
# 恢复上一次提交
git reset --hard HEAD~1

# 恢复特定文件
git checkout HEAD~1 -- filename

# 查看备份
git stash list
git stash apply stash@{0}
```

## 📞 需要帮助？

如果在清理过程中遇到问题：

1. 查看详细报告：[`项目代码清理分析报告.md`](项目代码清理分析报告.md)
2. 检查任务列表：[`TODO_清理任务列表.md`](TODO_清理任务列表.md)
3. 联系项目维护者
4. 创建Issue说明遇到的问题

---

**记住：代码清理的目标是让项目更易维护，而不是删除一切。当有疑问时，保留比删除更安全！** 