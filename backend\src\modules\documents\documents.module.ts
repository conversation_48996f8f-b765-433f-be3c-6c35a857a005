import { Module } from '@nestjs/common';
import { DocumentController } from './controllers/document.controller';
import { DocumentService } from './services/document.service';
import { DatabaseModule } from '../database/database.module';
import { FilenetModule } from '../filenet/filenet.module';

/**
 * 文档管理模块
 * 
 * 提供OnlyOffice文档管理的核心功能
 * 迁移自原有的文档管理系统
 * 
 * @class DocumentsModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Module({
  imports: [DatabaseModule, FilenetModule],
  controllers: [DocumentController],
  providers: [DocumentService],
  exports: [DocumentService],
})
export class DocumentsModule {} 