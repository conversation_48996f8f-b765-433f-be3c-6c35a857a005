/**
 * 权限管理API封装
 * @description 权限相关的API调用函数
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */

import request from '@/utils/request'

/**
 * 权限项接口定义
 */
export interface Permission {
  id: string
  name: string
  code: string
  module: string
  action: string
  description: string
  is_active: boolean
  created_at: string
  updated_at: string
}

/**
 * 角色接口定义
 */
export interface Role {
  id: string
  name: string
  displayName: string
  description: string
  permissions: string[]
  is_active: boolean
  created_at: string
  updated_at: string
}

/**
 * 用户接口定义
 */
export interface User {
  id: string
  username: string
  email: string
  full_name: string
  phone?: string
  avatar_url?: string
  status: string
  role_id: string
  role_name?: string
  role_display_name?: string
  department?: string
  position?: string
  is_active: boolean
  created_at: string
  updated_at: string
  last_login_at?: string
}

/**
 * 分配权限DTO
 */
export interface AssignPermissionsDto {
  permissions: string[]
}

/**
 * 更新用户DTO
 */
export interface UpdateUserDto {
  username?: string
  email?: string
  full_name?: string
  phone?: string
  avatar_url?: string
  status?: string
  role_id?: string
  department?: string
  position?: string
  email_verified?: boolean
  phone_verified?: boolean
  two_factor_enabled?: boolean
}

/**
 * 获取所有权限列表
 * @param params 查询参数
 */
export const getPermissions = (params?: { page?: number; pageSize?: number }) => {
  return request({
    method: 'GET',
    url: '/permissions',
    params: params,
  })
}

/**
 * 获取权限树结构
 */
export const getPermissionTree = () => {
  return request({
    method: 'GET',
    url: '/permissions/tree',
  })
}

/**
 * 获取角色详情
 * @param roleId 角色ID
 */
export const getRoleById = (roleId: string) => {
  return request({
    method: 'GET',
    url: `/roles/${roleId}`,
  })
}

/**
 * 为角色分配权限
 * @param roleId 角色ID
 * @param permissions 权限代码数组
 */
export const assignRolePermissions = (roleId: string, permissions: string[]) => {
  return request({
    method: 'PUT',
    url: `/roles/${roleId}/permissions`,
    data: { permissions },
  })
}

/**
 * 获取用户详情
 * @param userId 用户ID
 */
export const getUserById = (userId: string) => {
  return request({
    method: 'GET',
    url: `/users/${userId}`,
  })
}

/**
 * 更新用户信息
 * @param userId 用户ID
 * @param userData 用户数据
 */
export const updateUser = (userId: string, userData: UpdateUserDto) => {
  return request({
    method: 'PUT',
    url: `/users/${userId}`,
    data: userData,
  })
}

/**
 * 获取用户权限
 * @param userId 用户ID
 */
export const getUserPermissions = (userId: string) => {
  return request({
    method: 'GET',
    url: `/permissions/user/${userId}`,
  })
}

/**
 * 获取所有角色列表
 */
export const getRoles = () => {
  return request({
    method: 'GET',
    url: '/roles',
  })
}
