const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const db = require('./database'); // 引入数据库服务

/**
 * 计算文件的SHA-256哈希值
 * @param {string} filePath 文件路径
 * @returns {Promise<string>} 文件的SHA-256哈希值
 */
async function calculateFileHash(filePath) {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash('sha256');
        const stream = fs.createReadStream(filePath);

        stream.on('data', data => hash.update(data));
        stream.on('end', () => resolve(hash.digest('hex')));
        stream.on('error', error => reject(error));
    });
}

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 文件扩展名（不含点号）
 */
function getFileExtension(filename) {
    return path.extname(filename).toLowerCase().substring(1);
}

/**
 * 获取文件的MIME类型
 * @param {string} extension 文件扩展名
 * @returns {string} MIME类型
 */
function getMimeType(extension) {
    const mimeTypes = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt': 'application/vnd.ms-powerpoint',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'txt': 'text/plain',
        'xml': 'application/xml'
    };

    return mimeTypes[extension] || 'application/octet-stream';
}

/**
 * (新增辅助函数) 将内容上传到FileNet（如果不存在）并返回其ID和元数据
 * 此函数不直接操作 filenet_documents 表的写入。
 * @param {Object} fileData - 包含 { path: string, size: number }
 * @param {string} originalName 文件原始名称
 * @param {string} userId 用户ID (可选, 用于记录创建者等)
 * @returns {Promise<Object>} 包含 { fn_doc_id, file_hash, file_size, extension, mime_type }
 */
async function uploadContentToFileNetAndGetIds(fileData, originalName, userId = 'anonymous') {
    if (!config.filenet || !config.filenet.host) {
        throw new Error('FileNet 配置缺失，无法处理文件内容');
    }
    if (!fileData || !fileData.path || typeof fileData.size === 'undefined') {
        throw new Error('uploadContentToFileNetAndGetIds: fileData 参数无效 (需要 path 和 size)');
    }

    console.log('[uploadContentToFileNetAndGetIds] 开始处理:', originalName, '路径:', fileData.path);

    const fileHash = await calculateFileHash(fileData.path);
    const extension = getFileExtension(originalName);
    const mimeType = getMimeType(extension);

    // 检查此内容哈希是否已存在于任何 filenet_documents 记录中，以复用 fn_doc_id
    const existingDocWithSameHash = await db.queryOne(
        'SELECT fn_doc_id FROM filenet_documents WHERE file_hash = ? AND is_deleted = FALSE ORDER BY created_at DESC LIMIT 1',
        [fileHash]
    );

    let fn_doc_id_for_this_content;
    let contentWasReused = false; // 新增标志

    if (existingDocWithSameHash && existingDocWithSameHash.fn_doc_id) {
        console.log(`[uploadContentToFileNetAndGetIds] 内容哈希已存在于数据库记录中，复用FileNet文档ID: ${existingDocWithSameHash.fn_doc_id}`);
        fn_doc_id_for_this_content = existingDocWithSameHash.fn_doc_id;
        contentWasReused = true; // 设置标志
    } else {
        console.log('[uploadContentToFileNetAndGetIds] 内容哈希不存在于数据库记录或无有效fn_doc_id，将尝试上传到FileNet获取新ID');

        const uploadUrl = `http://${config.filenet.host}:${config.filenet.port}/common/uploadFiles`;
        const formData = new FormData();
        const folderValue = config.filenet.defaultFolder || '{6078817B-0000-C91A-BFF0-A3B7D84D1348}';
        const docClassValue = config.filenet.defaultDocClass || 'SimpleDocument';
        const sourceTypeValue = config.filenet.defaultSourceType || 'CUSTOMER';
        const bizTagValue = config.filenet.defaultBizTag || 'customer_certify';

        formData.append('folder', folderValue);
        formData.append('docClass', docClassValue);
        formData.append('docName', originalName); // 使用 originalName 作为 FileNet 中的 docName
        formData.append('source_type', sourceTypeValue);
        formData.append('biz_tag', bizTagValue);
        formData.append('file', fs.createReadStream(fileData.path), originalName);

        console.log(`[uploadContentToFileNetAndGetIds] 正在上传文件到 FileNet: ${originalName} 到 ${uploadUrl}`);

        try {
            const response = await axios.post(uploadUrl, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Connection': 'keep-alive'
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity
            });

            console.log('[uploadContentToFileNetAndGetIds] FileNet 上传响应状态:', response.status);
            console.log('[uploadContentToFileNetAndGetIds] FileNet 上传响应数据:', response.data);

            if (response.status === 200 && typeof response.data === 'string' && response.data.startsWith('{') && response.data.endsWith('}')) {
                fn_doc_id_for_this_content = response.data;
            } else if (response.data && response.data.data && typeof response.data.data === 'string' && response.data.data.startsWith('{') && response.data.data.endsWith('}')) {
                // 兼容 FileNet 可能将 ID 包裹在 data 字段中的情况
                fn_doc_id_for_this_content = response.data.data;
            } else if (response.data && response.data.code === '0' && response.data.data && typeof response.data.data === 'string' && response.data.data.startsWith('{') && response.data.data.endsWith('}')) {
                // 进一步兼容 FileNet 可能的成功响应格式
                fn_doc_id_for_this_content = response.data.data;
            }
            else {
                let failureMessage = 'FileNet 上传失败或响应格式不正确';
                if (response.data && typeof response.data === 'object' && response.data.message) {
                    failureMessage = `FileNet 操作消息: ${response.data.message}`;
                } else if (typeof response.data === 'string' && response.data.length < 200) { // Avoid logging huge HTML error pages
                    failureMessage = `FileNet 响应: ${response.data}`;
                }
                console.error('[uploadContentToFileNetAndGetIds] ' + failureMessage, response.data);
                throw new Error(failureMessage);
            }
            console.log('[uploadContentToFileNetAndGetIds] 新内容已上传到FileNet, 得到的 fn_doc_id:', fn_doc_id_for_this_content);
        } catch (error) {
            console.error('[uploadContentToFileNetAndGetIds] FileNet上传请求失败:', error.message);
            if (error.response) {
                console.error('[uploadContentToFileNetAndGetIds] FileNet响应状态:', error.response.status);
                console.error('[uploadContentToFileNetAndGetIds] FileNet响应数据:', error.response.data);
            }
            throw new Error(`FileNet上传失败: ${error.message}`);
        }
    }

    return {
        fn_doc_id: fn_doc_id_for_this_content,
        file_hash: fileHash,
        file_size: fileData.size,
        extension: extension,
        mime_type: mimeType,
        original_name: originalName, // 也返回原始文件名，方便后续使用
        contentWasReused: contentWasReused // 返回新标志
    };
}

/**
 * 上传文件到 FileNet (并在 filenet_documents 表创建记录)
 * @param {Object} fileData Express multer 文件对象 (req.file) 或包含 { path: string, size: number, originalname: string } 的对象
 * @param {string} originalName 文件原始名称 (如果 fileData 中没有 originalname)
 * @param {string} userId 用户ID，可选
 * @returns {Promise<Object>} FileNet 返回的数据，包含 docId (fn_doc_id) 和 dbId (filenet_documents 表中的 id)
 */
async function uploadToFileNet(fileData, originalNameToUse, userId = 'anonymous') {
    if (!config.filenet || !config.filenet.host) {
        throw new Error('FileNet 配置缺失，无法上传文件');
    }

    const filePath = fileData.path || fileData.tempPath;
    const fileSize = fileData.size;
    const originalFileName = fileData.originalname || fileData.originalName || originalNameToUse;

    if (!filePath || typeof fileSize === 'undefined' || !originalFileName) {
        console.error('uploadToFileNet: 无效的文件数据。需要 path/tempPath, size, 和 originalname/originalName。收到的 fileData:', fileData, 'originalNameToUse:', originalNameToUse);
        throw new Error('uploadToFileNet: 无效的文件数据，缺少路径、大小或原始文件名');
    }
    console.log('[uploadToFileNet] 准备上传新条目:', originalFileName, '源路径:', filePath);

    const contentInfo = await uploadContentToFileNetAndGetIds(
        { path: filePath, size: fileSize },
        originalFileName,
        userId
    );

    console.log('[uploadToFileNet] 从 uploadContentToFileNetAndGetIds 获取到的信息:', contentInfo);

    if (!contentInfo || !contentInfo.fn_doc_id) {
        console.error('[uploadToFileNet] 从 uploadContentToFileNetAndGetIds 未获取到有效的 fn_doc_id。ContentInfo:', contentInfo);
        throw new Error('未能从FileNet获取文档ID，上传处理中止。');
    }

    let documentIdForDbRecord;
    let existingDbRecord = null;

    // 如果内容在FileNet中被复用 (意味着 fn_doc_id 是一个已存在的 FileNet ID)
    // 我们需要检查我们的数据库中是否已经有这条 FileNet 文档的记录
    if (contentInfo.contentWasReused) {
        existingDbRecord = await db.queryOne(
            'SELECT id FROM filenet_documents WHERE fn_doc_id = ? AND file_hash = ? AND is_deleted = FALSE LIMIT 1',
            [contentInfo.fn_doc_id, contentInfo.file_hash]
        );

        if (existingDbRecord) {
            console.log(`[uploadToFileNet] 内容复用，且在本地数据库找到对应记录 (ID: ${existingDbRecord.id})，使用现有数据库记录。`);
            documentIdForDbRecord = existingDbRecord.id;
            // Optionally, update `updated_at` or other fields if needed, but for now, just reuse.
            // await db.query('UPDATE filenet_documents SET updated_at = CURRENT_TIMESTAMP, last_modified_by = ? WHERE id = ?', [userId, documentIdForDbRecord]);
        } else {
            // 内容在FileNet中复用，但在我们的数据库中没有找到 fn_doc_id + file_hash 完全匹配的记录。
            // 这可能意味着：
            // 1. FileNet 中存在此内容，但我们的 filenet_documents 表中从未记录过此 fn_doc_id (罕见，除非手动清理了DB但FileNet没清)
            // 2. 或 file_hash 匹配，但 fn_doc_id 不同 (这不应被 uploadContentToFileNetAndGetIds 标记为 contentWasReused)
            // 3. 或 fn_doc_id 匹配，但 file_hash 不同 (内容变了，也不应被标记 contentWasReused)
            // 对于这种情况，我们仍然需要创建一个新的 filenet_documents 记录，因为它对我们的系统来说是"新"的关联。
            console.warn(`[uploadToFileNet] 内容在FileNet复用 (fn_doc_id: ${contentInfo.fn_doc_id}), 但在本地数据库未找到完全匹配的记录。将创建新的本地数据库条目。`);
            documentIdForDbRecord = uuidv4();
            // existingDbRecord 仍然是 null，所以会进入下面的 INSERT 逻辑
        }
    }

    // 如果不是内容复用，或者内容复用但在本地DB未找到，则创建新的数据库记录
    if (!existingDbRecord) {
        documentIdForDbRecord = documentIdForDbRecord || uuidv4(); // 如果因上述 warn 而未赋值，则生成新的
        const finalOriginalName = contentInfo.original_name;

        try {
            await db.query(
                'INSERT INTO filenet_documents (id, fn_doc_id, original_name, file_size, mime_type, extension, version, file_hash, created_by, last_modified_by, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)',
                [
                    documentIdForDbRecord,
                    contentInfo.fn_doc_id,
                    finalOriginalName,
                    contentInfo.file_size,
                    contentInfo.mime_type,
                    contentInfo.extension,
                    1, // 初始版本
                    contentInfo.file_hash,
                    userId,
                    userId
                ]
            );
            console.log(`[uploadToFileNet] 新文档记录已创建 (ID: ${documentIdForDbRecord})，FileNet ID: ${contentInfo.fn_doc_id}, 文件名: ${finalOriginalName}`);
        } catch (dbError) {
            // 特别处理 unique_fn_doc_id 重复的错误，这不应该在 contentWasReused=false 时发生
            // 但如果因为某种原因（例如并发或之前逻辑不完善），新的 fn_doc_id 依然在数据库中已存在，这里会捕获
            if (dbError.code === 'ER_DUP_ENTRY' && dbError.message.includes('unique_fn_doc_id')) {
                console.error(`[uploadToFileNet] 尝试插入新的 filenet_documents 记录时发生 fn_doc_id 重复错误。fn_doc_id: ${contentInfo.fn_doc_id}. 这可能表示一个逻辑问题，或者一个之前未被正确处理的FileNet ID。`);
                // 尝试查找这条意外存在的记录的ID
                const conflictingRecord = await db.queryOne('SELECT id FROM filenet_documents WHERE fn_doc_id = ? AND is_deleted = FALSE LIMIT 1', [contentInfo.fn_doc_id]);
                if (conflictingRecord) {
                    console.warn(`[uploadToFileNet] 找到了具有冲突 fn_doc_id 的现有记录 ID: ${conflictingRecord.id}。将使用此现有记录ID。`);
                    documentIdForDbRecord = conflictingRecord.id;
                    // 这里我们不抛出错误，而是尝试使用找到的记录ID，因为目标是确保前端拿到一个有效的 dbId
                } else {
                    // 如果连冲突记录都找不到（非常奇怪），则只能抛出原始错误
                    throw dbError;
                }
            } else {
                throw dbError; // 其他类型的数据库错误，直接抛出
            }
        }
    }

    // (可选) 版本记录逻辑，应该基于 documentIdForDbRecord
    const versionsTableExists = await db.queryOne("SHOW TABLES LIKE 'filenet_document_versions'");
    if (versionsTableExists) {
        // 检查此 fn_doc_id + hash 是否已在此 documentIdForDbRecord 的版本历史中
        const existingVersion = await db.queryOne(
            'SELECT id FROM filenet_document_versions WHERE doc_id = ? AND fn_doc_id = ? AND file_hash = ?',
            [documentIdForDbRecord, contentInfo.fn_doc_id, contentInfo.file_hash]
        );
        if (!existingVersion) {
            const versionId = uuidv4();
            // 获取当前文档的最大版本号，新版本+1 (如果需要严格版本递增)
            // 或者直接使用 filenet_documents 表中的 version 字段 (如果该字段已正确更新)
            const currentDocInfo = await db.queryOne('SELECT version FROM filenet_documents WHERE id = ?', [documentIdForDbRecord]);
            const newVersionNumber = currentDocInfo ? (currentDocInfo.version || 1) : 1; // 如果是新记录，可能是1

            await db.query(
                'INSERT INTO filenet_document_versions (id, doc_id, fn_doc_id, version, file_hash, modified_by, file_size, comment) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                [versionId, documentIdForDbRecord, contentInfo.fn_doc_id, newVersionNumber, contentInfo.file_hash, userId, contentInfo.file_size, 'New version from upload']
            );
            console.log(`[uploadToFileNet] 版本记录已创建 for doc_id ${documentIdForDbRecord}, version ${newVersionNumber}`);
        } else {
            console.log(`[uploadToFileNet] 版本记录已存在 for doc_id ${documentIdForDbRecord} with this content, skipping version creation.`);
        }
    }

    return {
        success: true,
        message: '文件上传成功处理',
        docId: contentInfo.fn_doc_id, // FileNet ID
        dbId: documentIdForDbRecord,  // 我们数据库中的 filenet_documents.id
        originalName: contentInfo.original_name,
        fileHash: contentInfo.file_hash,
        contentWasReused: contentInfo.contentWasReused
    };
}

/**
 * 从 FileNet 下载文件
 * @param {string} docId FileNet 文档 ID (可以带或不带大括号)
 * @returns {Promise<Object>} 包含文件流和文件名
 */
async function downloadFromFileNet(docId) {
    if (!config.filenet || !config.filenet.host) {
        console.error('FileNet 配置缺失或不完整');
        throw new Error('FileNet configuration is missing or incomplete.');
    }

    const { host, port } = config.filenet;
    // DocId 可能包含大括号，API 文档说可以不去掉，但请求时务必参数 encoding 一下。
    // 通常直接在 URL 中使用时，浏览器或 axios 会处理编码。
    // 为确保兼容性，我们移除大括号（如果存在）。
    const cleanDocId = docId.replace(/[{}]/g, "");
    const downloadUrl = `http://${host}:${port}/common/download?docId=${encodeURIComponent(cleanDocId)}`;

    console.log(`准备从 FileNet 下载: ${downloadUrl}`);

    try {
        const response = await axios({
            method: 'GET',
            url: downloadUrl,
            responseType: 'stream'
        });

        console.log('FileNet 下载响应状态:', response.status);
        // 从 Content-Disposition 获取文件名
        let fileName = `filenet_document_${cleanDocId}`;
        const disposition = response.headers['content-disposition'];
        if (disposition) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
                fileName = matches[1].replace(/['"]/g, '');
                // 解码文件名 (UTF-8 和 URL 编码)
                try {
                    fileName = decodeURIComponent(fileName);
                } catch (e) {
                    // 如果 decodeURIComponent 失败，尝试其他方式或使用原始匹配
                    console.warn('标准 decodeURIComponent 文件名失败, 尝试 RFC 5987 解码');
                    const rfc5987matches = /filename\*=['"]?UTF-8''([^;\n]+)['"]?/i.exec(disposition);
                    if (rfc5987matches && rfc5987matches[1]) {
                        try {
                            fileName = decodeURIComponent(rfc5987matches[1]);
                        } catch (e2) {
                            console.error('RFC 5987 解码文件名也失败:', e2);
                        }
                    }
                }
            }
        }
        console.log('推断的文件名:', fileName);

        return {
            success: true,
            fileName: fileName,
            stream: response.data
        };
    } catch (error) {
        console.error('从 FileNet 下载时发生错误:', error.isAxiosError ? error.message : error);
        if (error.response) {
            console.error('FileNet 错误响应数据:', error.response.data ? await streamToString(error.response.data) : 'N/A');
            console.error('FileNet 错误响应状态:', error.response.status);
            throw new Error(`FileNet download failed with status ${error.response.status}`);
        }
        throw error;
    }
}

// 辅助函数：将流转换为字符串 (用于错误处理)
function streamToString(stream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('error', reject);
        stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
    });
}

// 获取 FileNet 文档列表
async function getFileNetDocumentsFromDB(options = {}) {
    // 简单的分页和排序示例，您可以根据需要扩展
    const page = parseInt(options.page, 10) || 1;
    const limit = parseInt(options.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const sortBy = options.sortBy || 'uploaded_at'; // 默认按上传时间排序
    const sortOrder = options.sortOrder === 'ASC' ? 'ASC' : 'DESC'; // 默认降序
    const showDeleted = options.showDeleted === 'true'; // 是否显示已删除文档

    console.log('查询文档列表参数:', { page, limit, offset, sortBy, sortOrder, showDeleted });

    try {
        // 先检查filenet_documents表是否存在
        const tableCheck = await db.queryOne("SHOW TABLES LIKE 'filenet_documents'");
        if (!tableCheck) {
            console.error('filenet_documents表不存在，需要初始化数据库');
            throw new Error('FileNet documents table does not exist. Database needs initialization.');
        }

        // 获取表的列信息来检查哪些字段存在
        const columnsInfo = await db.query("SHOW COLUMNS FROM filenet_documents");
        const existingColumns = columnsInfo.map(col => col.Field);

        console.log('filenet_documents表现有列:', existingColumns);

        // 根据现有列构建查询
        let selectFields = ['id', 'fn_doc_id', 'original_name', 'uploaded_at', 'created_at', 'updated_at'];

        // 这些是新添加的列，可能不存在于旧表中
        const possibleColumns = [
            'file_size', 'mime_type', 'extension', 'version', 'file_hash',
            'created_by', 'last_modified_by', 'template_id', 'is_deleted'
        ];

        // 只选择实际存在的列
        for (const col of possibleColumns) {
            if (existingColumns.includes(col)) {
                selectFields.push(col);
            }
        }

        // 构建SQL查询，使用实际存在的字段
        let sql = `
            SELECT ${selectFields.join(', ')}
            FROM filenet_documents
            WHERE 1=1
        `;

        // 只显示未删除文档，除非指定显示删除的（且is_deleted列存在）
        if (!showDeleted && existingColumns.includes('is_deleted')) {
            sql += ' AND is_deleted = FALSE';
        }

        // 检查排序列是否存在
        if (!existingColumns.includes(sortBy)) {
            sortBy = 'uploaded_at'; // 如果指定的排序列不存在，回退到uploaded_at
            console.log(`指定的排序列 ${sortBy} 不存在，使用 uploaded_at 代替`);
        }

        // 添加排序
        sql += ` ORDER BY ${db.pool.escapeId(sortBy)} ${sortOrder}`;

        // 添加分页
        sql += ' LIMIT ? OFFSET ?';

        const documents = await db.query(sql, [limit, offset]);

        // 清理和修复文件名编码问题
        for (const doc of documents) {
            try {
                // 尝试清理文件名
                let originalName = doc.original_name;

                // 记录原始文件名，帮助调试
                console.log(`文档 ${doc.id} 原始文件名:`, originalName);
                console.log(`文档 ${doc.id} 文件名二进制:`, Buffer.from(originalName).toString('hex'));

                // 尝试修复乱码
                if (/%[0-9A-F]{2}/.test(originalName)) {
                    try {
                        originalName = decodeURIComponent(originalName);
                    } catch (e) {
                        console.warn(`解码文件名失败 (${doc.id}):`, e);
                    }
                }

                doc.original_name = originalName;
            } catch (e) {
                console.error(`处理文档 ${doc.id} 文件名时出错:`, e);
            }
        }

        console.log(`查询到 ${documents.length} 条文档记录`);

        // 构建计数查询
        let countSql = 'SELECT COUNT(*) as total FROM filenet_documents WHERE 1=1';
        if (!showDeleted && existingColumns.includes('is_deleted')) {
            countSql += ' AND is_deleted = FALSE';
        }

        const totalCountResult = await db.queryOne(countSql);
        const totalCount = totalCountResult ? totalCountResult.total : 0;

        return {
            success: true,
            documents: documents,
            pagination: {
                page,
                limit,
                totalCount,
                totalPages: Math.ceil(totalCount / limit)
            }
        };
    } catch (error) {
        console.error('从数据库获取文档列表失败:', error);
        console.error('错误堆栈:', error.stack);
        throw error;
    }
}

/**
 * 从FileNet下载文档
 * @param {string} docId FileNet文档ID
 * @returns {Promise<Buffer>} 文档内容
 */
async function downloadDocument(docId) {
    const result = await downloadFromFileNet(docId);

    // 将流转换为Buffer
    return new Promise((resolve, reject) => {
        const chunks = [];
        result.stream.on('data', (chunk) => chunks.push(chunk));
        result.stream.on('error', reject);
        result.stream.on('end', () => {
            try {
                const buffer = Buffer.concat(chunks);
                resolve(buffer);
            } catch (error) {
                reject(error);
            }
        });
    });
}

/**
 * 在FileNet中复制一个文档
 * @param {string} sourceDocId - 源文档的FileNet ID (fn_doc_id)
 * @param {string} newFileName - 新文档的名称
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 新文档的信息，包含 fn_doc_id
 */
async function copyFileNetDocument(sourceDocId, newFileName, userId = 'system') {
    if (!config.filenet || !config.filenet.host) {
        throw new Error('FileNet 配置缺失，无法复制文档');
    }

    console.log(`[copyFileNetDocument] 准备从 ${sourceDocId} 复制文档为 ${newFileName}`);

    // 下载源文档
    const sourceDocument = await downloadFromFileNet(sourceDocId);
    if (!sourceDocument || !sourceDocument.stream) {
        throw new Error(`从FileNet下载源文档失败: ${sourceDocId}`);
    }

    // 创建临时文件以存储下载的内容
    const tempDir = path.join(process.cwd(), 'tmp');
    if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilePath = path.join(tempDir, `temp-${Date.now()}-${uuidv4()}`);

    try {
        // 将流写入临时文件
        const writeStream = fs.createWriteStream(tempFilePath);
        sourceDocument.stream.pipe(writeStream);

        await new Promise((resolve, reject) => {
            writeStream.on('finish', resolve);
            writeStream.on('error', reject);
        });

        // 确定文件大小
        const fileStats = fs.statSync(tempFilePath);
        const fileSize = fileStats.size;

        // 上传为新文档
        const uploadResult = await uploadContentToFileNetAndGetIds(
            { path: tempFilePath, size: fileSize },
            newFileName,
            userId
        );

        // 判断是否是内容复用（相同的fn_doc_id）
        const isReused = uploadResult.contentWasReused;
        if (isReused) {
            console.log(`[copyFileNetDocument] 注意：文档内容与已有文档相同，FileNet复用了现有文档ID: ${uploadResult.fn_doc_id}`);
        } else {
            console.log(`[copyFileNetDocument] 复制文档成功，创建了新的FileNet文档ID: ${uploadResult.fn_doc_id}`);
        }

        console.log(`[copyFileNetDocument] 复制文档完成，返回的FileNet文档ID: ${uploadResult.fn_doc_id}`);

        return {
            fn_doc_id: uploadResult.fn_doc_id,
            original_name: newFileName,
            file_size: uploadResult.file_size,
            mime_type: uploadResult.mime_type,
            extension: uploadResult.extension,
            file_hash: uploadResult.file_hash,
            contentWasReused: isReused  // 添加复用标志
        };
    } catch (error) {
        console.error(`[copyFileNetDocument] 复制文档失败:`, error);
        throw new Error(`复制文档失败: ${error.message}`);
    } finally {
        // 清理临时文件
        if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
        }
    }
}

module.exports = {
    calculateFileHash,
    getFileExtension,
    getMimeType,
    uploadContentToFileNetAndGetIds,
    uploadToFileNet,
    downloadFromFileNet,
    getFileNetDocumentsFromDB,
    downloadDocument,
    copyFileNetDocument
}; 