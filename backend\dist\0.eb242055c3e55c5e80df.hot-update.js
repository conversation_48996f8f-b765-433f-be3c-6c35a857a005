"use strict";
exports.id = 0;
exports.ids = null;
exports.modules = {

/***/ 64:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OnlyOfficeJwtService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.OnlyOfficeJwtService = void 0;
const common_1 = __webpack_require__(5);
const config_1 = __webpack_require__(8);
const jwt = __webpack_require__(65);
const jwt_config_service_1 = __webpack_require__(63);
const hybrid_config_service_1 = __webpack_require__(69);
let OnlyOfficeJwtService = OnlyOfficeJwtService_1 = class OnlyOfficeJwtService {
    constructor(configService, jwtConfigService, hybridConfigService) {
        this.configService = configService;
        this.jwtConfigService = jwtConfigService;
        this.hybridConfigService = hybridConfigService;
        this.logger = new common_1.Logger(OnlyOfficeJwtService_1.name);
    }
    async generateToken(payload) {
        try {
            const jwtConfig = await this.jwtConfigService.getOnlyOfficeJwtConfig();
            const serverHost = this.configService.get('SERVER_HOST', '*************');
            const serverPort = this.configService.get('PORT', '3000');
            const documentServerUrl = this.configService.get('ONLYOFFICE_DOCUMENT_SERVER_URL', 'http://*************/');
            const jwtPayload = {
                ...payload,
                iss: `http://${serverHost}:${serverPort}`,
                aud: documentServerUrl,
                exp: Math.floor(Date.now() / 1000) + 3600,
                nbf: Math.floor(Date.now() / 1000) - 60,
                iat: Math.floor(Date.now() / 1000),
            };
            const token = jwt.sign(jwtPayload, jwtConfig.secret, {
                algorithm: jwtConfig.algorithm,
                header: {
                    alg: jwtConfig.algorithm,
                    typ: 'JWT'
                }
            });
            this.logger.log('OnlyOffice JWT令牌生成成功');
            this.logger.debug(`JWT载荷: ${JSON.stringify({
                ...jwtPayload,
                document: jwtPayload.document ? {
                    ...jwtPayload.document,
                    url: '[REDACTED]'
                } : undefined
            })}`);
            return token;
        }
        catch (error) {
            this.logger.error('生成OnlyOffice JWT令牌失败:', error);
            throw new Error(`JWT令牌生成失败: ${error.message}`);
        }
    }
    async signConfig(config) {
        try {
            const token = await this.generateToken(config);
            return {
                ...config,
                token: token
            };
        }
        catch (error) {
            this.logger.error('为OnlyOffice配置签名失败:', error);
            throw error;
        }
    }
    async verifyToken(token) {
        try {
            const jwtConfig = await this.jwtConfigService.getOnlyOfficeJwtConfig();
            const decoded = jwt.verify(token, jwtConfig.secret, {
                algorithms: [jwtConfig.algorithm]
            });
            this.logger.log('JWT token验证成功');
            return { valid: true, payload: decoded };
        }
        catch (error) {
            this.logger.warn(`JWT token验证失败: ${error.message}`);
            return { valid: false, error: error.message };
        }
    }
};
exports.OnlyOfficeJwtService = OnlyOfficeJwtService;
exports.OnlyOfficeJwtService = OnlyOfficeJwtService = OnlyOfficeJwtService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof jwt_config_service_1.JwtConfigService !== "undefined" && jwt_config_service_1.JwtConfigService) === "function" ? _b : Object, typeof (_c = typeof hybrid_config_service_1.HybridConfigService !== "undefined" && hybrid_config_service_1.HybridConfigService) === "function" ? _c : Object])
], OnlyOfficeJwtService);


/***/ })

};
exports.runtime =
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("6e54b472f58415252bbe")
/******/ })();
/******/ 
/******/ }
;