const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '../.env' });

async function initUsers() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT) || 3306,
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'onlyoffice_system'
    });

    console.log('🔗 数据库连接成功');

    // 生成密码哈希
    const plainPassword = 'password123';
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(plainPassword, saltRounds);
    
    console.log('🔐 生成新密码哈希:', passwordHash);

    // 更新管理员用户密码
    const updateSql = `
      UPDATE users 
      SET password_hash = ?, updated_at = NOW() 
      WHERE username = 'admin'
    `;

    const [updateResult] = await connection.execute(updateSql, [passwordHash]);
    
    if (updateResult.affectedRows > 0) {
      console.log('✅ 管理员密码更新成功');
      
      // 验证更新结果
      const [users] = await connection.execute(
        'SELECT id, username, email, status FROM users WHERE username = ?',
        ['admin']
      );
      
      if (users.length > 0) {
        console.log('👤 管理员用户信息:', {
          id: users[0].id,
          username: users[0].username,
          email: users[0].email,
          status: users[0].status
        });
        console.log('🗝️  登录凭据: 用户名: admin, 密码: password123');
      }
    } else {
      console.log('❌ 未找到管理员用户，需要创建');
      
      // 创建管理员用户
      const createSql = `
        INSERT INTO users (id, username, email, password_hash, full_name, status, role_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      // 获取超级管理员角色ID
      const [roles] = await connection.execute(
        'SELECT id FROM user_roles WHERE name = ?',
        ['super_admin']
      );
      
      const roleId = roles.length > 0 ? roles[0].id : 'role-super-admin';
      
      const [createResult] = await connection.execute(createSql, [
        'user-admin',
        'admin', 
        '<EMAIL>',
        passwordHash,
        '系统管理员',
        'active',
        roleId
      ]);
      
      if (createResult.affectedRows > 0) {
        console.log('✅ 管理员用户创建成功');
        console.log('🗝️  登录凭据: 用户名: admin, 密码: password123');
      }
    }

  } catch (error) {
    console.error('❌ 初始化用户失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔒 数据库连接已关闭');
    }
  }
}

// 执行初始化
initUsers()
  .then(() => {
    console.log('🎉 用户初始化完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 用户初始化失败:', error);
    process.exit(1);
  }); 