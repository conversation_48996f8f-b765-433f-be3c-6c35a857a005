<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理 - OnlyOffice</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 0;
            text-align: center;
            margin-bottom: 20px;
        }

        header h1 {
            font-size: 22px;
        }

        .action-bar {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-bar button {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .action-bar button:hover {
            background-color: #2980b9;
        }

        .action-bar button.secondary {
            background-color: #95a5a6;
            color: white;
        }

        .action-bar button.secondary:hover {
            background-color: #7f8c8d;
        }

        table.data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table.data-table th,
        table.data-table td {
            padding: 10px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        table.data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        table.data-table tr:hover {
            background-color: #f9f9f9;
        }

        table.data-table .actions button {
            padding: 5px 8px;
            font-size: 13px;
            margin-right: 5px;
            cursor: pointer;
            border-radius: 3px;
            border: none;
            color: white;
        }

        table.data-table .actions button.edit {
            background-color: #f39c12;
        }

        table.data-table .actions button.edit:hover {
            background-color: #e67e22;
        }

        table.data-table .actions button.delete {
            background-color: #e74c3c;
        }

        table.data-table .actions button.delete:hover {
            background-color: #c0392b;
        }

        .status-enabled {
            color: green;
            font-weight: bold;
        }

        .status-disabled {
            color: #aaa;
        }

        .loading,
        .empty-list {
            text-align: center;
            padding: 20px;
            color: #777;
        }

        .error-message {
            color: red;
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid red;
            background-color: #ffebeb;
            border-radius: 4px;
        }

        /* Modal Styles (similar to index.html, but could be refactored into a common CSS if used widely) */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 25px;
            border: 1px solid #888;
            width: 70%;
            max-width: 700px;
            border-radius: 5px;
            position: relative;
        }

        .modal-header {
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            color: #2c3e50;
        }

        .close-button {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input[type="text"],
        .form-group textarea,
        .form-group select {
            width: calc(100% - 22px);
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .form-group textarea {
            min-height: 80px;
            resize: vertical;
        }

        .modal-footer {
            padding-top: 15px;
            border-top: 1px solid #eee;
            margin-top: 20px;
            text-align: right;
        }

        .modal-footer button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }

        .modal-footer button.primary {
            background-color: #3498db;
            color: white;
        }

        .modal-footer button.secondary {
            background-color: #ecf0f1;
            color: #333;
        }

        #documentForTemplateListContainer {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 5px;
            margin-bottom: 10px;
        }

        .document-item {
            padding: 5px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }

        .document-item:hover,
        .document-item.selected {
            background-color: #e9ecef;
        }

        #uploadSourceStatus {
            font-size: 0.8em;
            margin-top: 5px;
        }

        #templateSourceFileUpload {
            margin-top: 5px;
            margin-bottom: 5px;
            display: block;
        }
    </style>
</head>

<body>
    <header>
        <h1>模板管理</h1>
        <div style="margin-top: 5px;">
            <a href="/navigation" style="color: #fff; text-decoration: underline; font-size: 14px;">功能导航</a>
        </div>
    </header>
    <div class="container">
        <div class="action-bar">
            <button id="createNewTemplateBtn">创建新模板</button>
            <button id="backToIndexBtn" class="secondary">返回首页</button>
        </div>
        <div id="errorMessage" class="error-message" style="display:none;"></div>
        <div id="templateTableContainer">
            <!-- Templates will be loaded here -->
            <div class="loading">正在加载模板列表...</div>
        </div>
    </div>

    <!-- Create/Edit Template Modal -->
    <div id="templateFormModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">创建新模板</h3>
                <span class="close-button" id="closeFormModalBtn">&times;</span>
            </div>
            <form id="templateForm">
                <input type="hidden" id="templateIdInput">
                <div class="form-group">
                    <label for="templateNameInput">模板名称 <span style="color:red;">*</span></label>
                    <input type="text" id="templateNameInput" required>
                </div>
                <div class="form-group">
                    <label for="templateDescriptionInput">描述</label>
                    <textarea id="templateDescriptionInput"></textarea>
                </div>
                <div class="form-group">
                    <label for="sourceDocumentInput">源文档 (doc_id) <span style="color:red;">*</span></label>
                    <p style="font-size:0.85em; color: #666; margin-bottom: 5px;">您可以从下方列表选择一个已存在的文档，或直接上传一个新文件作为模板的基础。
                    </p>

                    <input type="text" id="sourceDocumentIdInput" placeholder="将从下方列表选择或显示已选/已上传文档ID" readonly
                        style="background:#eee; margin-bottom: 5px;">

                    <div>
                        <label for="templateSourceFileUpload"
                            style="font-size:0.9em; font-weight:normal;">上传新文件作为源:</label>
                        <input type="file" id="templateSourceFileUpload" accept=".docx,.xlsx,.pptx">
                        <button type="button" id="uploadSourceFileBtn"
                            style="padding: 5px 10px; font-size: 0.9em;">上传并指定</button>
                        <div id="uploadSourceStatus"></div>
                    </div>

                    <label style="font-size:0.9em; font-weight:normal; margin-top:10px;">或从现有文档列表选择:</label>
                    <div id="documentForTemplateListContainer">
                        <div class="loading">正在加载可用文档...</div>
                    </div>
                    <small>如果列表为空，请先到首页上传一些文档，或使用上面的上传功能。</small>
                </div>
                <div class="form-group">
                    <label for="templateStatusInput">状态</label>
                    <select id="templateStatusInput">
                        <option value="enabled">启用</option>
                        <option value="disabled">禁用</option>
                    </select>
                </div>
                <div id="formErrorMessage" class="error-message" style="display:none;"></div>
            </form>
            <div class="modal-footer">
                <button id="cancelFormBtn" class="secondary">取消</button>
                <button id="saveTemplateBtn" class="primary">保存模板</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const templateTableContainer = document.getElementById('templateTableContainer');
            const createNewTemplateBtn = document.getElementById('createNewTemplateBtn');
            const templateFormModal = document.getElementById('templateFormModal');
            const closeFormModalBtn = document.getElementById('closeFormModalBtn');
            const cancelFormBtn = document.getElementById('cancelFormBtn');
            const saveTemplateBtn = document.getElementById('saveTemplateBtn');
            const templateForm = document.getElementById('templateForm');
            const modalTitle = document.getElementById('modalTitle');
            const errorMessageDiv = document.getElementById('errorMessage');
            const formErrorMessageDiv = document.getElementById('formErrorMessage');
            const backToIndexBtn = document.getElementById('backToIndexBtn');

            const templateIdInput = document.getElementById('templateIdInput');
            const templateNameInput = document.getElementById('templateNameInput');
            const templateDescriptionInput = document.getElementById('templateDescriptionInput');
            const sourceDocumentIdInput = document.getElementById('sourceDocumentIdInput');
            const documentForTemplateListContainer = document.getElementById('documentForTemplateListContainer');
            const templateStatusInput = document.getElementById('templateStatusInput');
            const templateSourceFileUpload = document.getElementById('templateSourceFileUpload');
            const uploadSourceFileBtn = document.getElementById('uploadSourceFileBtn');
            const uploadSourceStatus = document.getElementById('uploadSourceStatus');

            let currentEditingTemplateId = null;
            let selectedSourceDocId = null;

            // 设置基本事件监听
            backToIndexBtn.addEventListener('click', () => { window.location.href = '/'; });
            createNewTemplateBtn.addEventListener('click', () => openFormModal());
            closeFormModalBtn.addEventListener('click', closeFormModal);
            cancelFormBtn.addEventListener('click', closeFormModal);
            // 关闭模态窗口的点击外部区域
            window.addEventListener('click', (event) => {
                if (event.target == templateFormModal) closeFormModal();
            });

            // 文件上传事件处理
            uploadSourceFileBtn.addEventListener('click', handleFileUpload);

            // 保存模板事件处理
            saveTemplateBtn.addEventListener('click', handleTemplateSave);

            // 添加刷新按钮
            const actionBar = document.querySelector('.action-bar');
            if (actionBar) {
                const refreshBtn = document.createElement('button');
                refreshBtn.textContent = '刷新列表';
                refreshBtn.style.backgroundColor = '#3498db';
                refreshBtn.style.marginLeft = '10px';
                refreshBtn.addEventListener('click', function () {
                    console.log('手动触发刷新模板列表');
                    loadTemplates();
                });
                actionBar.appendChild(refreshBtn);
            }

            // 初始加载模板列表
            console.log('初始化页面 - 开始加载模板列表');
            loadTemplates();

            // 错误提示函数
            function showError(message, isFormError = false) {
                const div = isFormError ? formErrorMessageDiv : errorMessageDiv;
                div.textContent = message;
                div.style.display = 'block';
            }
            function clearError(isFormError = false) {
                const div = isFormError ? formErrorMessageDiv : errorMessageDiv;
                div.textContent = '';
                div.style.display = 'none';
            }

            async function loadTemplates() {
                templateTableContainer.innerHTML = '<div class="loading">正在加载模板列表...</div>';
                clearError();
                console.log('开始加载模板列表...');
                try {
                    // Fetch all templates, including disabled ones for admin page
                    console.log('发送API请求: /api/templates?limit=100&status=all');
                    const response = await fetch('/api/templates?limit=100&status=all');
                    console.log('收到API响应状态码:', response.status);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('API响应非200:', errorText);
                        throw new Error(`HTTP error ${response.status}: ${errorText}`);
                    }

                    const responseText = await response.text();
                    console.log('原始响应内容:', responseText);

                    // 尝试解析JSON
                    let result;
                    try {
                        result = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('JSON解析错误:', parseError, '原始内容:', responseText);
                        throw new Error('响应内容不是有效的JSON格式');
                    }

                    // 打印API响应，帮助调试
                    console.log('模板列表API响应解析后:', result);

                    if (result.success && result.data && Array.isArray(result.data.templates)) {
                        console.log(`找到 ${result.data.templates.length} 个模板，正在渲染...`);
                        renderTemplatesTable(result.data.templates);
                    } else if (result.success && Array.isArray(result.data)) {
                        // 备选结构: API可能直接返回数组
                        console.log(`找到 ${result.data.length} 个模板 (备选数据结构)，正在渲染...`);
                        renderTemplatesTable(result.data);
                    } else if (result.success && result.templates && Array.isArray(result.templates)) {
                        // 另一种可能的结构
                        console.log(`找到 ${result.templates.length} 个模板 (第三种数据结构)，正在渲染...`);
                        renderTemplatesTable(result.templates);
                    } else {
                        console.error('无法解析模板数据:', result);
                        showError('加载模板列表失败: 返回数据结构不符合预期');
                        templateTableContainer.innerHTML = '<div class="empty-list">无法加载模板。</div>';
                    }
                } catch (error) {
                    console.error('Error loading templates:', error);
                    showError('网络错误，加载模板列表失败: ' + error.message);
                    templateTableContainer.innerHTML = '<div class="empty-list">加载模板出错。</div>';
                }
            }

            function renderTemplatesTable(templates) {
                // 安全检查，确保templates是数组
                if (!Array.isArray(templates)) {
                    console.error('renderTemplatesTable接收到无效数据:', templates);
                    templateTableContainer.innerHTML = '<div class="empty-list">模板数据格式错误。</div>';
                    return;
                }

                console.log(`开始渲染 ${templates.length} 个模板`);

                if (templates.length === 0) {
                    templateTableContainer.innerHTML = '<div class="empty-list">没有找到任何模板。</div>';
                    return;
                }

                let tableHtml = `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>描述</th>
                                <th>源文档ID</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>`;

                templates.forEach(template => {
                    // 检查每个模板对象的关键属性
                    if (!template || !template.id) {
                        console.warn('发现无效模板对象:', template);
                        return; // 跳过无效模板
                    }

                    // 安全获取属性，避免undefined错误
                    const name = template.name || '未命名模板';
                    const description = template.description || '-';
                    const docId = template.doc_id || '未指定';
                    const sourceName = template.source_document_name || 'N/A';
                    const status = template.status || 'unknown';

                    try {
                        tableHtml += `
                            <tr>
                                <td>${escapeHtml(name)}</td>
                                <td>${escapeHtml(description)}</td>
                                <td><small>${escapeHtml(docId)}<br/>(${escapeHtml(sourceName)})</small></td>
                                <td class="status-${status.toLowerCase()}">${status}</td>
                                <td>${template.created_at ? new Date(template.created_at).toLocaleString() : '未知'}</td>
                                <td>${template.updated_at ? new Date(template.updated_at).toLocaleString() : '未知'}</td>
                                <td class="actions">
                                    <button class="edit" data-id="${template.id}">编辑</button>
                                    <button class="delete" data-id="${template.id}">删除</button>
                                </td>
                            </tr>`;
                    } catch (e) {
                        console.error('渲染模板行时出错:', e, template);
                    }
                });

                tableHtml += '</tbody></table>';
                templateTableContainer.innerHTML = tableHtml;
                console.log('模板表格渲染完成');

                // Add event listeners for edit and delete buttons
                document.querySelectorAll('.data-table .edit').forEach(btn => {
                    btn.addEventListener('click', (e) => handleEditTemplate(e.target.dataset.id));
                });
                document.querySelectorAll('.data-table .delete').forEach(btn => {
                    btn.addEventListener('click', (e) => handleDeleteTemplate(e.target.dataset.id));
                });
            }

            // Placeholder for escapeHtml if not globally available
            function escapeHtml(unsafe) {
                if (typeof unsafe !== 'string') return '';
                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }

            // --- Modal & Form Logic ---
            function openFormModal(template = null) {
                clearError(true); // Clear form-specific errors
                templateForm.reset();
                selectedSourceDocId = null; // Reset selected doc
                sourceDocumentIdInput.value = '';
                templateSourceFileUpload.value = ''; // Clear file input
                uploadSourceStatus.textContent = ''; // Clear upload status

                if (template) {
                    modalTitle.textContent = '编辑模板';
                    currentEditingTemplateId = template.id;
                    templateIdInput.value = template.id;
                    templateNameInput.value = template.name;
                    templateDescriptionInput.value = template.description || '';
                    templateStatusInput.value = template.status;
                    sourceDocumentIdInput.value = template.doc_id; // Display current doc_id
                    selectedSourceDocId = template.doc_id; // Set it as selected
                    // For editing, source document selection might be locked or handled differently
                    // For simplicity, we'll still load documents, but pre-select if matches
                    documentForTemplateListContainer.innerHTML = `<p style="color: #555;">当前源文档ID: ${template.doc_id}. 如需更改，请从下方选择新文档。</p>`;
                    loadDocumentsForTemplateSelection(template.doc_id);
                } else {
                    modalTitle.textContent = '创建新模板';
                    currentEditingTemplateId = null;
                    templateIdInput.value = '';
                    documentForTemplateListContainer.innerHTML = '<div class="loading">正在加载可用文档...</div>';
                    loadDocumentsForTemplateSelection();
                }
                templateFormModal.style.display = 'block';
            }

            function closeFormModal() {
                templateFormModal.style.display = 'none';
            }

            async function loadDocumentsForTemplateSelection(currentSelectedDocId = null) {
                documentForTemplateListContainer.innerHTML = '<div class="loading">加载文档...</div>';
                console.log('开始加载可用文档列表，当前选中ID:', currentSelectedDocId);
                try {
                    // 优先尝试从FileNet获取文档列表
                    console.log('尝试从 /api/filenet/documents 获取文档');
                    const response = await fetch('/api/filenet/documents?limit=100');
                    if (!response.ok) throw new Error(`获取文档列表失败: ${response.statusText}`);
                    const result = await response.json();
                    console.log('文档列表API返回:', result);

                    let documents = [];
                    // 处理不同的API响应结构
                    if (result.success && Array.isArray(result.documents)) {
                        documents = result.documents;
                        console.log(`从 FileNet 获取到 ${documents.length} 个文档`);
                    } else if (result.success && result.data && Array.isArray(result.data.documents)) {
                        documents = result.data.documents;
                        console.log(`从 FileNet 获取到 ${documents.length} 个文档（嵌套结构）`);
                    } else {
                        console.warn('未找到有效的文档数据，返回结构:', result);
                        documentForTemplateListContainer.innerHTML = '<p>未能找到可用的文档。请先上传文档。</p>';
                        return;
                    }

                    // 格式化文档列表，确保统一的数据结构
                    const formattedDocs = documents.map(doc => ({
                        id: doc.id || doc.dbId || doc.fn_doc_id,
                        name: doc.original_name || doc.name || '未命名文档',
                        type: doc.extension || doc.mime_type || 'unknown'
                    }));

                    console.log('文档数据处理完成，准备渲染');
                    renderDocumentSelectionList(formattedDocs, currentSelectedDocId);
                } catch (error) {
                    console.error('加载文档列表时出错:', error);
                    documentForTemplateListContainer.innerHTML = '<p>加载文档列表失败: ' + error.message + '</p>';
                }
            }

            function renderDocumentSelectionList(documents, currentSelectedDocId) {
                console.log('渲染文档选择列表，文档数量:', documents.length, '当前选中ID:', currentSelectedDocId);
                if (!documents || documents.length === 0) {
                    documentForTemplateListContainer.innerHTML = '<p>没有可用的文档作为模板源。请先上传文档。</p>';
                    return;
                }

                let listHtml = '';
                documents.forEach(doc => {
                    // 检查ID匹配 - 可能需要处理字符串/非字符串比较
                    const docIdStr = String(doc.id);
                    const currentIdStr = currentSelectedDocId ? String(currentSelectedDocId) : '';
                    const isSelected = docIdStr === currentIdStr;

                    if (isSelected) {
                        console.log('找到匹配的已选文档:', doc);
                    }

                    listHtml += `<div class="document-item ${isSelected ? 'selected' : ''}" data-doc-id="${escapeHtml(docIdStr)}" data-doc-name="${escapeHtml(doc.name)}">
                                    ${escapeHtml(doc.name)} 
                                    <small>(ID: ${escapeHtml(docIdStr.substring(0, 8))}...)</small>
                                 </div>`;
                });

                documentForTemplateListContainer.innerHTML = listHtml;
                console.log('文档选择列表渲染完成');

                document.querySelectorAll('#documentForTemplateListContainer .document-item').forEach(item => {
                    item.addEventListener('click', () => {
                        console.log('选择文档:', item.dataset.docId, item.dataset.docName);
                        document.querySelectorAll('#documentForTemplateListContainer .document-item.selected').forEach(el => el.classList.remove('selected'));
                        item.classList.add('selected');
                        selectedSourceDocId = item.dataset.docId;
                        sourceDocumentIdInput.value = selectedSourceDocId; // Update the readonly input
                        clearError(true);
                    });
                });
            }

            // 文件上传处理函数
            async function handleFileUpload() {
                const file = templateSourceFileUpload.files[0];
                if (!file) {
                    uploadSourceStatus.textContent = '请先选择一个文件。';
                    uploadSourceStatus.style.color = 'red';
                    return;
                }

                uploadSourceStatus.textContent = '正在上传文件...';
                uploadSourceStatus.style.color = 'blue';
                uploadSourceFileBtn.disabled = true;
                console.log('准备上传文件:', file.name, 'Size:', file.size);

                const formData = new FormData();
                formData.append('file', file);

                try {
                    console.log('开始调用上传API');
                    const response = await fetch('/api/filenet/upload', {
                        method: 'POST',
                        body: formData
                    });

                    console.log('上传API返回状态:', response.status);
                    const result = await response.json();
                    console.log('上传API返回数据:', result);

                    if (response.ok && result.success && result.dbId) {
                        selectedSourceDocId = result.dbId;
                        sourceDocumentIdInput.value = result.dbId;
                        uploadSourceStatus.textContent = `上传成功！源文档ID: ${result.dbId} (${result.originalName})`;
                        uploadSourceStatus.style.color = 'green';
                        console.log('文件上传成功，获得ID:', result.dbId);
                        templateSourceFileUpload.value = ''; // Clear file input
                        clearError(true); // Clear any previous form errors e.g. "Please select source doc"
                        // 成功后刷新可用文档列表
                        loadDocumentsForTemplateSelection(result.dbId);
                    } else {
                        console.error('上传失败:', result);
                        uploadSourceStatus.textContent = `上传失败: ${result.message || '未知错误'}`;
                        uploadSourceStatus.style.color = 'red';
                    }
                } catch (error) {
                    console.error('上传文件时发生错误:', error);
                    uploadSourceStatus.textContent = '上传时发生网络错误: ' + (error.message || '未知错误');
                    uploadSourceStatus.style.color = 'red';
                } finally {
                    uploadSourceFileBtn.disabled = false;
                }
            }

            // 保存模板处理函数
            async function handleTemplateSave() {
                clearError(true);
                const name = templateNameInput.value.trim();
                const doc_id = selectedSourceDocId; // This will be set by new upload or list selection

                console.log('准备保存模板，名称:', name, '源文档ID:', doc_id, '描述长度:', templateDescriptionInput.value.length);

                if (!name) {
                    showError('模板名称不能为空。', true);
                    console.error('保存失败：模板名称为空');
                    return;
                }
                if (!doc_id) {
                    showError('请选择或上传一个源文档。', true);
                    console.error('保存失败：未选择源文档');
                    return;
                }

                const templateData = {
                    name: name,
                    description: templateDescriptionInput.value.trim(),
                    doc_id: doc_id,
                    status: templateStatusInput.value
                };

                console.log('提交的模板数据:', templateData);

                let url = '/api/templates';
                let method = 'POST';
                if (currentEditingTemplateId) {
                    url = `/api/templates/${currentEditingTemplateId}`;
                    method = 'PUT';
                    console.log('更新现有模板, ID:', currentEditingTemplateId);
                } else {
                    console.log('创建新模板');
                }

                saveTemplateBtn.textContent = '保存中...';
                saveTemplateBtn.disabled = true;

                try {
                    console.log(`开始向 ${url} 发送 ${method} 请求`);
                    const response = await fetch(url, {
                        method: method,
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(templateData)
                    });

                    console.log('API响应状态:', response.status);
                    const result = await response.json();
                    console.log('API响应数据:', result);

                    if (response.ok && result.success) {
                        console.log('模板保存成功', result.data || result.template || '无返回数据');
                        alert('模板保存成功！');
                        closeFormModal();
                        loadTemplates(); // Refresh the list
                    } else {
                        const errorMsg = result.message || '未知错误';
                        console.error('模板保存失败:', errorMsg, result);
                        showError(`保存失败: ${errorMsg}`, true);
                    }
                } catch (error) {
                    console.error('模板保存过程中发生错误:', error);
                    showError(`网络错误，保存失败: ${error.message}`, true);
                } finally {
                    saveTemplateBtn.textContent = '保存模板';
                    saveTemplateBtn.disabled = false;
                }
            }

            async function handleEditTemplate(templateId) {
                clearError();
                try {
                    const response = await fetch(`/api/templates/${templateId}`);
                    if (!response.ok) throw new Error(`HTTP error ${response.status}`);
                    const result = await response.json();

                    if (result.success && result.data) {
                        // 提供两个编辑选项，一个编辑属性，一个编辑内容
                        const template = result.data;
                        const docId = template.doc_id;

                        // 创建一个模态对话框，让用户选择要执行的操作
                        const actionModal = document.createElement('div');
                        actionModal.className = 'modal';
                        actionModal.style.display = 'block';

                        actionModal.innerHTML = `
                            <div class="modal-content" style="max-width:400px;">
                                <div class="modal-header">
                                    <h3>选择编辑类型</h3>
                                    <span class="close-button" id="closeActionModalBtn">&times;</span>
                                </div>
                                <div style="padding: 20px;">
                                    <p>您希望如何编辑模板 "${escapeHtml(template.name)}"?</p>
                                    <div style="margin-top: 20px;">
                                        <button id="editPropertiesBtn" class="primary" style="margin-right: 10px; width: 45%;">
                                            编辑模板属性<br>
                                            <small>(名称、描述等)</small>
                                        </button>
                                        <button id="editContentBtn" class="primary" style="width: 45%;">
                                            编辑模板内容<br>
                                            <small>(文档内部)</small>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                        document.body.appendChild(actionModal);

                        // 关闭按钮事件
                        document.getElementById('closeActionModalBtn').addEventListener('click', () => {
                            document.body.removeChild(actionModal);
                        });

                        // 编辑属性按钮
                        document.getElementById('editPropertiesBtn').addEventListener('click', () => {
                            document.body.removeChild(actionModal);
                            openFormModal(template); // 打开原来的编辑属性模态框
                        });

                        // 编辑内容按钮
                        document.getElementById('editContentBtn').addEventListener('click', () => {
                            document.body.removeChild(actionModal);
                            if (docId) {
                                console.log(`准备编辑模板内容，源文档ID: ${docId}`);
                                // 修改：尝试使用直接使用ID路由而不是fn_doc_id参数
                                window.location.href = `/editor/${docId}?from=templateAdmin`;
                            } else {
                                showError('此模板没有关联的源文档ID，因此无法编辑其内容。');
                                console.warn('模板编辑内容失败：模板 ' + templateId + ' 没有 doc_id。');
                            }
                        });
                    } else {
                        showError('无法加载模板数据进行编辑: ' + (result.message || '未知错误'));
                        console.error('加载模板数据失败 (API未成功或数据格式不符):', result);
                    }
                } catch (error) {
                    showError('网络错误，加载模板进行编辑失败: ' + error.message);
                    console.error('Error fetching template for edit:', error);
                }
            }

            async function handleDeleteTemplate(templateId) {
                if (!confirm('确定要删除这个模板吗？此操作会将模板标记为已删除。')) return;
                clearError();
                try {
                    const response = await fetch(`/api/templates/${templateId}`, { method: 'DELETE' });
                    const result = await response.json();
                    if (response.ok && result.success) {
                        loadTemplates(); // Refresh list
                    } else {
                        showError('删除模板失败: ' + (result.message || '未知错误'));
                    }
                } catch (error) {
                    showError('网络错误，删除模板失败: ' + error.message);
                    console.error('Error deleting template:', error);
                }
            }
        });
    </script>
</body>

</html>