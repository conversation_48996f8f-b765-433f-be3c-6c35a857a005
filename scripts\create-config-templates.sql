-- OnlyOffice 配置模板系统数据库表
-- 用于存储多种编辑器配置模板（法务版、只读版、简化版等）

-- 配置模板主表
CREATE TABLE IF NOT EXISTS config_templates (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '模板名称，如：法务编辑版、员工只读版',
    description TEXT COMMENT '模板描述',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_name (name),
    INDEX idx_is_default (is_default),
    INDEX idx_is_active (is_active)
);

-- 配置项详表
CREATE TABLE IF NOT EXISTS config_template_items (
    id VARCHAR(100) PRIMARY KEY,
    template_id VARCHAR(36) NOT NULL COMMENT '关联配置模板ID',
    config_group VARCHAR(50) NOT NULL COMMENT '配置分组：permissions, customization, layout, features, user, server',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键：chat, comments, download等',
    config_value TEXT COMMENT '配置值（JSON格式存储复杂值）',
    value_type ENUM('string', 'number', 'boolean', 'object', 'array') DEFAULT 'string' COMMENT '值类型',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用此配置项',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否为必需配置',
    description TEXT COMMENT '配置项描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (template_id) REFERENCES config_templates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_template_config (template_id, config_group, config_key),
    INDEX idx_template_group (template_id, config_group),
    INDEX idx_is_enabled (is_enabled)
);

-- 插入预设配置模板
INSERT IGNORE INTO config_templates (id, name, description, is_default, is_active) VALUES
('default-edit', '默认编辑版', '标准的编辑器配置，包含基本编辑功能', TRUE, TRUE),
('legal-edit', '法务编辑版', '完整功能的编辑器，适用于法务人员，包含所有编辑和审阅工具', FALSE, TRUE),
('employee-readonly', '员工只读版', '只读模式，员工只能查看文档，不能编辑或下载', FALSE, TRUE),
('simple-edit', '功能简化版', '基础编辑功能，隐藏聊天、注释等干扰功能', FALSE, TRUE);

-- 清理已有的配置项数据（如果存在）
DELETE FROM config_template_items WHERE template_id IN ('default-edit', 'legal-edit', 'employee-readonly', 'simple-edit');

-- 插入默认编辑版配置项
INSERT INTO config_template_items (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description) VALUES
-- 权限配置
('def-perm-edit', 'default-edit', 'permissions', 'edit', 'true', 'boolean', TRUE, TRUE, '允许编辑文档'),
('def-perm-download', 'default-edit', 'permissions', 'download', 'true', 'boolean', TRUE, FALSE, '允许下载文档'),
('def-perm-print', 'default-edit', 'permissions', 'print', 'true', 'boolean', TRUE, FALSE, '允许打印文档'),
('def-perm-comment', 'default-edit', 'permissions', 'comment', 'true', 'boolean', TRUE, FALSE, '允许添加评论'),
('def-perm-chat', 'default-edit', 'permissions', 'chat', 'false', 'boolean', TRUE, FALSE, '允许聊天功能'),
('def-perm-review', 'default-edit', 'permissions', 'review', 'true', 'boolean', TRUE, FALSE, '允许审阅模式'),
('def-perm-copy', 'default-edit', 'permissions', 'copy', 'true', 'boolean', TRUE, FALSE, '允许复制内容'),

-- 界面自定义配置
('def-cust-compactHeader', 'default-edit', 'customization', 'compactHeader', 'false', 'boolean', TRUE, FALSE, '使用紧凑头部'),
('def-cust-hideRightMenu', 'default-edit', 'customization', 'hideRightMenu', 'false', 'boolean', TRUE, FALSE, '隐藏右侧菜单'),
('def-cust-forcesave', 'default-edit', 'customization', 'forcesave', 'true', 'boolean', TRUE, FALSE, '启用强制保存'),
('def-cust-autosave', 'default-edit', 'customization', 'autosave', 'true', 'boolean', TRUE, FALSE, '启用自动保存'),
('def-cust-help', 'default-edit', 'customization', 'help', 'true', 'boolean', TRUE, FALSE, '显示帮助按钮'),
('def-cust-about', 'default-edit', 'customization', 'about', 'true', 'boolean', TRUE, FALSE, '显示关于按钮'),
('def-cust-zoom', 'default-edit', 'customization', 'zoom', '100', 'number', TRUE, FALSE, '默认缩放比例'),
('def-cust-uiTheme', 'default-edit', 'customization', 'uiTheme', 'theme-light', 'string', TRUE, FALSE, 'UI主题'),

-- 用户配置
('def-user-id', 'default-edit', 'user', 'id', 'user-1', 'string', TRUE, TRUE, '用户ID'),
('def-user-name', 'default-edit', 'user', 'name', 'OnlyOffice用户', 'string', TRUE, TRUE, '用户姓名'),
('def-user-group', 'default-edit', 'user', 'group', 'editors', 'string', TRUE, FALSE, '用户组'),

-- 服务器配置
('def-server-lang', 'default-edit', 'server', 'lang', 'zh', 'string', TRUE, FALSE, '界面语言'),
('def-server-region', 'default-edit', 'server', 'region', 'zh-CN', 'string', TRUE, FALSE, '地区设置');

-- 插入法务编辑版配置项（继承默认配置，添加特殊权限）
INSERT INTO config_template_items (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description) 
SELECT CONCAT('leg-', SUBSTRING(MD5(CONCAT(config_group, config_key)), 1, 8)), 'legal-edit', config_group, config_key, config_value, value_type, is_enabled, is_required, description
FROM config_template_items WHERE template_id = 'default-edit';

-- 法务版特殊配置：启用所有功能
UPDATE config_template_items SET config_value = 'true' 
WHERE template_id = 'legal-edit' AND config_group = 'permissions' AND config_key IN ('chat', 'review', 'comment');

-- 插入员工只读版配置项
INSERT INTO config_template_items (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description) 
SELECT CONCAT('ro-', SUBSTRING(MD5(CONCAT(config_group, config_key)), 1, 8)), 'employee-readonly', config_group, config_key, 
    CASE 
        WHEN config_group = 'permissions' AND config_key IN ('edit', 'download', 'print', 'comment', 'chat') THEN 'false'
        ELSE config_value
    END, 
    value_type, is_enabled, is_required, description
FROM config_template_items WHERE template_id = 'default-edit';

-- 插入功能简化版配置项
INSERT INTO config_template_items (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description) 
SELECT CONCAT('sim-', SUBSTRING(MD5(CONCAT(config_group, config_key)), 1, 8)), 'simple-edit', config_group, config_key,
    CASE 
        WHEN config_group = 'permissions' AND config_key IN ('chat', 'comment') THEN 'false'
        WHEN config_group = 'customization' AND config_key = 'compactHeader' THEN 'true'
        ELSE config_value
    END,
    value_type, is_enabled, is_required, description
FROM config_template_items WHERE template_id = 'default-edit';

-- 创建视图：方便查询完整的模板配置
DROP VIEW IF EXISTS v_config_template_full;
CREATE VIEW v_config_template_full AS
SELECT 
    t.id as template_id,
    t.name as template_name,
    t.description as template_description,
    t.is_default,
    t.is_active,
    i.config_group,
    i.config_key,
    i.config_value,
    i.value_type,
    i.is_enabled,
    i.is_required,
    i.description as item_description
FROM config_templates t
LEFT JOIN config_template_items i ON t.id = i.template_id
WHERE t.is_active = TRUE AND i.is_enabled = TRUE
ORDER BY t.name, i.config_group, i.config_key; 