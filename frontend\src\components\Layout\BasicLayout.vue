<template>
  <a-layout class="basic-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :width="260"
      :collapsed-width="80"
      class="layout-sider"
      theme="light"
    >
      <!-- Logo区域 -->
      <div class="logo">
        <div class="logo-icon">📄</div>
        <div v-if="!collapsed" class="logo-content">
          <h1 class="logo-title">OnlyOffice</h1>
          <p class="logo-subtitle">企业管理系统</p>
        </div>
      </div>

      <!-- 导航菜单 -->
      <a-menu
        :selected-keys="selectedKeys"
        :open-keys="openKeys"
        mode="inline"
        theme="light"
        :inline-collapsed="collapsed"
        @click="handleMenuClick"
        @open-change="handleOpenChange"
        class="navigation-menu"
      >
        <!-- 主要功能组 -->
        <div v-if="!collapsed" class="nav-group-title">主要功能</div>

        <!-- 系统首页 -->
        <a-menu-item key="/dashboard" class="nav-menu-item">
          <template #icon>
            <dashboard-outlined />
          </template>
          <span class="nav-text">系统首页</span>
        </a-menu-item>

        <!-- 文档管理 -->
        <a-menu-item key="/documents" class="nav-menu-item">
          <template #icon>
            <file-text-outlined />
          </template>
          <span class="nav-text">文档管理</span>
          <span v-if="!collapsed" class="nav-badge">12</span>
        </a-menu-item>

        <!-- 文档模板管理 -->
        <a-menu-item key="/templates" class="nav-menu-item">
          <template #icon>
            <snippets-outlined />
          </template>
          <span class="nav-text">文档模板管理</span>
        </a-menu-item>

        <!-- OnlyOffice模板管理 -->
        <a-menu-item key="/onlyoffice-config" class="nav-menu-item">
          <template #icon>
            <setting-outlined />
          </template>
          <span class="nav-text">OnlyOffice模板管理</span>
        </a-menu-item>

        <!-- 系统配置组 -->
        <div v-if="!collapsed" class="nav-group-title">系统配置</div>

        <!-- 用户管理 -->
        <a-menu-item key="/users" class="nav-menu-item">
          <template #icon>
            <user-outlined />
          </template>
          <span class="nav-text">用户管理</span>
        </a-menu-item>

        <!-- 权限管理 -->
        <a-menu-item key="/permissions" class="nav-menu-item">
          <template #icon>
            <safety-certificate-outlined />
          </template>
          <span class="nav-text">权限管理</span>
        </a-menu-item>

        <!-- 系统配置管理 -->
        <a-menu-item key="/system-config" class="nav-menu-item">
          <template #icon>
            <setting-outlined />
          </template>
          <span class="nav-text">系统配置管理</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <a-layout class="layout-content" :class="{ collapsed }">
      <!-- 顶部导航栏 -->
      <a-layout-header class="layout-header">
        <div class="header-left">
          <a-button type="text" @click="toggleCollapsed" class="trigger">
            <template #icon>
              <menu-fold-outlined v-if="!collapsed" />
              <menu-unfold-outlined v-else />
            </template>
          </a-button>

          <!-- 面包屑 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              <router-link v-if="item.path !== route.path" :to="item.path">
                {{ item.title }}
              </router-link>
              <span v-else>{{ item.title }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息下拉菜单 -->
          <a-dropdown placement="bottomRight">
            <template #overlay>
              <a-menu @click="handleUserMenuClick" class="user-dropdown-menu">
                <a-menu-item key="profile">
                  <user-outlined />
                  <span>个人中心</span>
                </a-menu-item>
                <a-menu-item key="settings">
                  <setting-outlined />
                  <span>系统设置</span>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <logout-outlined />
                  <span>退出登录</span>
                </a-menu-item>
              </a-menu>
            </template>
            <div class="user-info">
              <a-avatar class="user-avatar" :src="authStore.userInfo?.avatar">
                {{ authStore.userInfo?.username?.[0]?.toUpperCase() || 'A' }}
              </a-avatar>
              <span class="user-name">{{ authStore.userInfo?.username || '系统管理员' }}</span>
              <down-outlined class="dropdown-icon" />
            </div>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 主要内容区 -->
      <a-layout-content
        class="main-content"
        :class="{ 'fullscreen-content': route.meta?.fullscreen }"
      >
        <router-view v-slot="{ Component }">
          <transition name="page" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </a-layout-content>

      <!-- 底部 -->
      <a-layout-footer v-if="!route.meta?.fullscreen" class="layout-footer">
        <div class="footer-content">
          <span>OnlyOffice集成系统 © {{ currentYear }} Created with ❤️</span>
        </div>
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  DownOutlined,
  LogoutOutlined,
  DashboardOutlined,
  FileTextOutlined,
  SnippetsOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 当前年份
const currentYear = new Date().getFullYear()

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title as string,
  }))
})

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  router.push(key)
}

// 处理子菜单展开
const handleOpenChange = (keys: string[]) => {
  openKeys.value = keys
}

// 处理用户菜单点击
const handleUserMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/config')
      break
    case 'logout':
      handleLogout()
      break
    default:
      break
  }
}

// 退出登录处理
const handleLogout = async () => {
  try {
    console.log('🚪 用户点击退出登录')
    await authStore.logout()
    console.log('✅ 登出完成，即将跳转到登录页')
  } catch (error) {
    console.error('退出登录失败:', error)
    message.error('退出登录失败，但已清除本地数据')
    router.push('/login')
  }
}

// 监听路由变化，更新选中的菜单项
watch(
  () => route.path,
  path => {
    selectedKeys.value = [path]
    const segments = path.split('/').filter(Boolean)
    if (segments.length > 1) {
      openKeys.value = [`/${segments[0]}`]
    }
  },
  { immediate: true }
)

onMounted(() => {
  selectedKeys.value = [route.path]
})
</script>

<style scoped>
.basic-layout {
  height: 100vh;
  background: var(--background-light);
}

.layout-sider {
  background: #ffffff !important;
  border-right: 1px solid var(--border-color);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 覆盖Ant Design的默认样式 */
.layout-sider :deep(.ant-layout-sider-children) {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64px;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.logo-icon {
  font-size: 28px;
  margin-right: 12px;
  transition: margin 0.3s ease;
}

/* 折叠状态下的logo样式 */
.layout-sider.ant-layout-sider-collapsed .logo {
  padding: 16px 12px;
}

.layout-sider.ant-layout-sider-collapsed .logo-icon {
  margin-right: 0;
}

.logo-content {
  text-align: left;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.layout-sider.ant-layout-sider-collapsed .logo-content {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.logo-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.2;
}

.logo-subtitle {
  margin: 0;
  font-size: 12px;
  color: var(--text-light);
  line-height: 1;
}

/* 导航菜单样式 */
.navigation-menu {
  padding: 20px 0;
  border: none !important;
  background: transparent !important;
}

.nav-group-title {
  padding: 0 20px 12px;
  font-size: 12px;
  color: var(--text-light);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.nav-group-title:first-child {
  margin-top: 0;
}

.nav-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px !important;
  margin: 0 !important;
  color: var(--text-primary) !important;
  border-left: 3px solid transparent !important;
  transition: all 0.3s ease !important;
  border-radius: 0 !important;
  background: transparent !important;
}

/* 折叠状态下的菜单项样式 */
.layout-sider.ant-layout-sider-collapsed .nav-menu-item {
  padding: 16px !important;
  justify-content: center !important;
  text-align: center;
}

/* 折叠状态下隐藏文字，只显示图标 */
.layout-sider.ant-layout-sider-collapsed .nav-menu-item .nav-text,
.layout-sider.ant-layout-sider-collapsed .nav-menu-item .nav-badge {
  display: none !important;
}

/* 确保折叠状态下图标可见且居中 */
.layout-sider.ant-layout-sider-collapsed .nav-menu-item .anticon {
  font-size: 18px !important;
  color: var(--text-primary) !important;
  margin: 0 !important;
}

.nav-menu-item:hover,
.nav-menu-item.ant-menu-item-selected {
  background: var(--primary-light) !important;
  color: var(--primary-color) !important;
  border-left-color: var(--primary-color) !important;
}

/* 折叠状态下的hover和选中样式 */
.layout-sider.ant-layout-sider-collapsed .nav-menu-item:hover,
.layout-sider.ant-layout-sider-collapsed .nav-menu-item.ant-menu-item-selected {
  background: var(--primary-light) !important;
  border-left: 3px solid var(--primary-color) !important;
}

.layout-sider.ant-layout-sider-collapsed .nav-menu-item:hover .anticon,
.layout-sider.ant-layout-sider-collapsed .nav-menu-item.ant-menu-item-selected .anticon {
  color: var(--primary-color) !important;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.nav-badge {
  margin-left: auto;
  background: #ff4757;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  font-weight: 600;
}

.layout-content {
  background: var(--background-light);
  transition: all 0.3s ease;
}

.layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: #ffffff !important;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  height: 100%;
}

.trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  color: var(--text-secondary);
  border: none;
  background: transparent;
}

.trigger:hover {
  color: var(--primary-color);
  background: var(--primary-light);
}

.breadcrumb {
  margin-left: 24px;
  display: flex;
  align-items: center;
  height: 40px;
}

.breadcrumb :deep(.ant-breadcrumb-link) {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.breadcrumb :deep(.ant-breadcrumb-link):hover {
  color: var(--primary-color);
}

.header-right {
  display: flex;
  align-items: center;
  height: 100%;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  height: 40px;
  min-width: 120px;
}

.user-info:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.user-avatar {
  width: 32px !important;
  height: 32px !important;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-hover) 100%
  ) !important;
  color: white !important;
  font-weight: 600;
  font-size: 14px;
}

.user-name {
  margin: 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.dropdown-icon {
  margin-left: 8px;
  color: var(--text-secondary);
  font-size: 12px;
}

.user-dropdown-menu {
  border-radius: 8px !important;
  box-shadow: var(--card-shadow-hover) !important;
  border: 1px solid var(--border-color) !important;
  min-width: 160px;
}

.user-dropdown-menu :deep(.ant-menu-item) {
  padding: 8px 16px !important;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.user-dropdown-menu :deep(.ant-menu-item):hover {
  background: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

.main-content {
  padding: 24px;
  background: var(--background-light);
  min-height: calc(100vh - 64px - 64px);
  overflow-x: hidden;
}

.fullscreen-content {
  padding: 0 !important;
  height: calc(100vh - 64px) !important;
  min-height: calc(100vh - 64px) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

.layout-footer {
  text-align: center;
  background: #ffffff !important;
  border-top: 1px solid var(--border-color);
  padding: 12px 24px;
}

.footer-content {
  color: var(--text-light);
  font-size: 14px;
}

/* 页面切换动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 响应式适配 */
@media (max-width: 1024px) {
  .breadcrumb {
    display: none;
  }

  .user-name {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-left {
    flex: 1;
  }

  .logo-subtitle {
    display: none;
  }

  .main-content {
    padding: 16px;
  }

  .user-info {
    padding: 6px 12px;
  }
}
</style>
