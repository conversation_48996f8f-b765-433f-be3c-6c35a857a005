{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["./src/*"], "@/config/*": ["./src/config/*"], "@/controllers/*": ["./src/controllers/*"], "@/services/*": ["./src/services/*"], "@/modules/*": ["./src/modules/*"], "@/middleware/*": ["./src/middleware/*"], "@/guards/*": ["./src/guards/*"], "@/interceptors/*": ["./src/interceptors/*"], "@/decorators/*": ["./src/decorators/*"], "@/dto/*": ["./src/dto/*"], "@/entities/*": ["./src/entities/*"], "@/interfaces/*": ["./src/interfaces/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/common/*": ["./src/common/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*.spec.ts"]}