# OnlyOffice NestJS Backend - 环境变量配置
# 复制此文件为 .env 并填入实际配置值

# ================================
# 应用基础配置
# ================================
NODE_ENV=development
PORT=3001
API_PREFIX=api

# ================================
# 数据库配置 (兼容现有Express项目)
# ================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=onlyoffice_system
DB_USER=your_username
DB_PASSWORD=your_password
DB_SYNCHRONIZE=false
DB_LOGGING=false
DB_CONNECTION_TIMEOUT=60000
DB_ACQUIRE_TIMEOUT=60000

# ================================
# JWT认证配置
# ================================
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h
JWT_ISSUER=onlyoffice-nestjs
JWT_AUDIENCE=onlyoffice-client

# ================================
# CORS安全配置
# ================================
CORS_ORIGIN=http://localhost:8080
CORS_CREDENTIALS=true
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Accept,Authorization

# ================================
# 速率限制配置
# ================================
THROTTLE_TTL=60
THROTTLE_LIMIT=100
THROTTLE_GLOBAL_TTL=900
THROTTLE_GLOBAL_LIMIT=1000

# ================================
# OnlyOffice服务器配置
# ================================
ONLYOFFICE_SERVER_URL=http://localhost/
ONLYOFFICE_DOCUMENT_SERVER_URL=http://localhost:8000/
ONLYOFFICE_SECRET_KEY=your-onlyoffice-secret-key
ONLYOFFICE_JWT_SECRET=your-onlyoffice-jwt-secret
ONLYOFFICE_CALLBACK_URL=http://localhost:3001/api/documents/callback

# ================================
# FileNet企业集成配置
# ================================
FILENET_URL=your-filenet-server-url
FILENET_USERNAME=your-filenet-username
FILENET_PASSWORD=your-filenet-password
FILENET_OBJECT_STORE=your-object-store-name
FILENET_TIMEOUT=30000

# ================================
# 文件存储配置
# ================================
UPLOAD_PATH=./uploads
TMP_PATH=./tmp
MAX_FILE_SIZE=104857600
ALLOWED_FILE_TYPES=.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt
STORAGE_TYPE=local

# ================================
# 缓存配置 (支持内存和Redis)
# ================================
CACHE_TYPE=memory
CACHE_TTL=3600
CACHE_MAX_SIZE=100
CACHE_GLOBAL=true

# ================================
# Redis配置 (用于缓存和会话)
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TIMEOUT=5000

# ================================
# 日志配置
# ================================
LOG_LEVEL=info
LOG_DIR=./logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7
LOG_FORMAT=json
LOG_TIMESTAMP=true

# ================================
# Swagger API文档配置
# ================================
SWAGGER_ENABLED=true
SWAGGER_TITLE=OnlyOffice NestJS API
SWAGGER_DESCRIPTION=OnlyOffice集成系统后端API文档
SWAGGER_VERSION=1.0.0
SWAGGER_PATH=api/docs
SWAGGER_TAGS=health,auth,documents,config,filenet

# ================================
# 性能监控配置
# ================================
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1000
REQUEST_TIMEOUT=30000
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# ================================
# 安全配置
# ================================
ENABLE_SECURITY_HEADERS=true
HELMET_ENABLED=true
COMPRESSION_ENABLED=true
TRUST_PROXY=false

# ================================
# 验证管道配置
# ================================
VALIDATION_PIPE_ENABLED=true
VALIDATION_WHITELIST=true
VALIDATION_FORBID_NON_WHITELISTED=true
VALIDATION_TRANSFORM=true

# ================================
# 数据库迁移配置
# ================================
TYPEORM_MIGRATION_RUN=false
TYPEORM_MIGRATION_DIR=src/migrations
TYPEORM_ENTITY_DIR=src/entities

# ================================
# 开发环境配置
# ================================
HOT_RELOAD=true
DEBUG_MODE=true
WATCH_MODE=true
SOURCE_MAPS=true

# ================================
# 测试环境配置
# ================================
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_NAME=onlyoffice_test
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password

# ================================
# 生产环境配置
# ================================
PRODUCTION_DB_SSL=true
PRODUCTION_LOG_LEVEL=warn
PRODUCTION_CACHE_TTL=7200
PRODUCTION_RATE_LIMIT=50

# ================================
# 第三方服务配置
# ================================
EMAIL_SERVICE_ENABLED=false
EMAIL_FROM=<EMAIL>
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_SECURE=false

# ================================
# 监控和告警配置
# ================================
ALERT_EMAIL=<EMAIL>
ERROR_THRESHOLD=10
RESPONSE_TIME_THRESHOLD=2000
MEMORY_THRESHOLD=80

# ================================
# 微服务配置 (预留扩展)
# ================================
MICROSERVICE_MODE=false
SERVICE_DISCOVERY_ENABLED=false
CONSUL_HOST=localhost
CONSUL_PORT=8500

# ================================
# API版本控制
# ================================
API_VERSION=v1
VERSION_HEADER=X-API-Version
DEPRECATION_WARNING=false 