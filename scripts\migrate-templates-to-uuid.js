/**
 * 迁移脚本：将 template_categories, templates, template_versions 表的ID从自增整数改为UUID格式
 * 并更新 filenet_documents.template_id
 * 该脚本会执行以下操作：
 * 1. 备份原始表
 * 2. 创建新的表结构（使用UUID作为主键和外键）
 * 3. 迁移数据到新表 (依赖 filenet_documents_id_mapping.json)
 * 4. 重命名表
 * 5. 更新 filenet_documents.template_id
 */
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const db = require('../services/database'); // 确保路径正确
// const config = require('../config'); // 如果需要config

// --- 配置 ---
// 迁移过程中生成的ID映射文件的名称
const TEMPLATE_CATEGORIES_ID_MAPPING_FILE = 'template_categories_id_mapping.json';
const TEMPLATES_ID_MAPPING_FILE = 'templates_id_mapping.json';
// 依赖的 filenet_documents ID 映射文件 (由 migrate-to-uuid.js 生成)
const FILENET_DOCUMENTS_ID_MAPPING_FILE = 'filenet_documents_id_mapping.json';


async function migrateTemplateTablesToUUID() {
    console.log('开始迁移模板相关表到UUID主键...');

    // 加载 filenet_documents 的 ID 映射
    let fnDocsIdMapping = {};
    const fnDocsMappingPath = path.join(process.cwd(), FILENET_DOCUMENTS_ID_MAPPING_FILE);
    if (fs.existsSync(fnDocsMappingPath)) {
        try {
            fnDocsIdMapping = JSON.parse(fs.readFileSync(fnDocsMappingPath, 'utf-8'));
            console.log(`${FILENET_DOCUMENTS_ID_MAPPING_FILE} 加载成功。`);
        } catch (e) {
            console.error(`错误：无法解析 ${FILENET_DOCUMENTS_ID_MAPPING_FILE}。请确保它是有效的 JSON 文件。`, e);
            process.exit(1);
        }
    } else {
        console.warn(`警告：${FILENET_DOCUMENTS_ID_MAPPING_FILE} 未找到。templates.doc_id 和 template_versions.doc_id 可能无法正确迁移。`);
        // 可以选择在此处退出，或者继续但带有警告
        // process.exit(1); 
    }

    try {
        const connected = await db.testConnection();
        if (!connected) {
            console.error('无法连接到数据库，迁移操作取消');
            process.exit(1);
        }

        await db.transaction(async (connection) => {
            console.log('开始数据库事务...');

            // 1. 备份表
            await backupOriginalTables(connection);

            // 2. 迁移 template_categories
            const categoriesIdMapping = await migrateTemplateCategories(connection);
            fs.writeFileSync(
                path.join(process.cwd(), TEMPLATE_CATEGORIES_ID_MAPPING_FILE),
                JSON.stringify(categoriesIdMapping, null, 2)
            );
            console.log(`${TEMPLATE_CATEGORIES_ID_MAPPING_FILE} 已保存。`);

            // 3. 迁移 templates
            const templatesIdMapping = await migrateTemplates(connection, categoriesIdMapping, fnDocsIdMapping);
            fs.writeFileSync(
                path.join(process.cwd(), TEMPLATES_ID_MAPPING_FILE),
                JSON.stringify(templatesIdMapping, null, 2)
            );
            console.log(`${TEMPLATES_ID_MAPPING_FILE} 已保存。`);

            // 4. 迁移 template_versions
            await migrateTemplateVersions(connection, templatesIdMapping, fnDocsIdMapping);

            // 5. 更新 filenet_documents.template_id
            await updateFilenetDocumentsTemplateId(connection, templatesIdMapping);

            console.log('所有模板相关表迁移成功完成！');
        });
    } catch (error) {
        console.error('迁移失败:', error);
        process.exit(1);
    }
}

async function backupOriginalTables(connection) {
    console.log('备份原始模板相关表...');
    const tablesToBackup = ['template_categories', 'templates', 'template_versions'];
    for (const tableName of tablesToBackup) {
        const [tableExists] = await connection.query(`SHOW TABLES LIKE '${tableName}'`);
        if (tableExists.length > 0) {
            const backupTableName = `${tableName}_backup_uuid_migration`;
            await connection.query(`DROP TABLE IF EXISTS ${backupTableName}`);
            console.log(`备份 ${tableName} 到 ${backupTableName}...`);
            await connection.query(`CREATE TABLE ${backupTableName} LIKE ${tableName}`);
            await connection.query(`INSERT INTO ${backupTableName} SELECT * FROM ${tableName}`);
            console.log(`${tableName} 表备份完成。`);
        } else {
            console.log(`${tableName} 表不存在，跳过备份。`);
        }
    }
}

async function migrateTemplateCategories(connection) {
    console.log('迁移 template_categories 表...');
    const idMapping = {};
    const oldTableName = 'template_categories';
    const newTableName = 'template_categories_new_uuid';
    const finalTableName = 'template_categories';

    const [tableExists] = await connection.query(`SHOW TABLES LIKE '${oldTableName}'`);
    if (tableExists.length === 0) {
        console.log(`${oldTableName} 表不存在，跳过迁移。`);
        return idMapping;
    }

    await connection.query(`DROP TABLE IF EXISTS ${newTableName}`);
    await connection.query(`
        CREATE TABLE ${newTableName} (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            parent_id VARCHAR(36) NULL,
            description TEXT,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_parent_id (parent_id),
            FOREIGN KEY (parent_id) REFERENCES ${newTableName}(id) ON DELETE SET NULL 
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    // 注意: 外键到自身 newTableName，在重命名后需要指向 finalTableName。initDatabase 会处理最终的FK。

    const [oldRecords] = await connection.query(`SELECT * FROM ${oldTableName}`);
    console.log(`从 ${oldTableName} 读取了 ${oldRecords.length} 条记录`);

    for (const record of oldRecords) {
        const newUuid = uuidv4();
        idMapping[record.id] = newUuid;
    }

    for (const record of oldRecords) {
        await connection.query(
            `INSERT INTO ${newTableName} (id, name, parent_id, description, sort_order, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
                idMapping[record.id],
                record.name,
                record.parent_id ? idMapping[record.parent_id] : null, // 使用映射更新 parent_id
                record.description,
                record.sort_order,
                record.created_at,
                record.updated_at,
            ]
        );
    }

    await connection.query(`DROP TABLE IF EXISTS ${finalTableName}_old_uuid_migration`);
    await connection.query(`RENAME TABLE ${finalTableName} TO ${finalTableName}_old_uuid_migration`);
    await connection.query(`RENAME TABLE ${newTableName} TO ${finalTableName}`);
    console.log(`${finalTableName} 表结构更新并数据迁移完成。`);
    return idMapping;
}

async function migrateTemplates(connection, categoriesIdMapping, fnDocsIdMapping) {
    console.log('迁移 templates 表...');
    const idMapping = {};
    const oldTableName = 'templates';
    const newTableName = 'templates_new_uuid';
    const finalTableName = 'templates';

    const [tableExists] = await connection.query(`SHOW TABLES LIKE '${oldTableName}'`);
    if (tableExists.length === 0) {
        console.log(`${oldTableName} 表不存在，跳过迁移。`);
        return idMapping;
    }

    await connection.query(`DROP TABLE IF EXISTS ${newTableName}`);
    await connection.query(`
        CREATE TABLE ${newTableName} (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            category_id VARCHAR(36) NULL,
            doc_id VARCHAR(36) NOT NULL, /* FK to filenet_documents.id */
            description TEXT,
            current_version INT DEFAULT 1,
            created_by VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by VARCHAR(100),
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status ENUM('enabled', 'disabled') DEFAULT 'enabled',
            is_deleted BOOLEAN DEFAULT FALSE,
            INDEX idx_category (category_id),
            INDEX idx_status (status),
            INDEX idx_is_deleted (is_deleted)
            /* FKs to template_categories and filenet_documents will be established by initDatabase */
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    const [oldRecords] = await connection.query(`SELECT * FROM ${oldTableName}`);
    console.log(`从 ${oldTableName} 读取了 ${oldRecords.length} 条记录`);

    for (const record of oldRecords) {
        const newUuid = uuidv4();
        idMapping[record.id] = newUuid; // 旧的 templates.id (INT) -> 新的 templates.id (UUID)
    }

    for (const record of oldRecords) {
        const newTemplateUuid = idMapping[record.id];
        const newCategoryUuid = record.category_id ? categoriesIdMapping[record.category_id] : null;

        let newFileNetDocUuid = null;
        if (record.doc_id) { // doc_id 是旧的 filenet_documents INT ID
            newFileNetDocUuid = fnDocsIdMapping[record.doc_id.toString()]; // fnDocsIdMapping 的 key 是 string
            if (!newFileNetDocUuid) {
                console.warn(`警告 (templates): 无法在 ${FILENET_DOCUMENTS_ID_MAPPING_FILE} 中找到旧 filenet_documents.id '${record.doc_id}' 的UUID映射. 将尝试直接使用值 (可能导致FK问题). Template Name: ${record.name}`);
                // 如果希望更严格，可以设置 newFileNetDocUuid = null 或抛出错误
                newFileNetDocUuid = record.doc_id; // 保持原样如果找不到，依赖FK检查
            }
        } else {
            console.error(`错误 (templates): 记录 ${record.name} (旧ID ${record.id}) 的 doc_id 为空，这不符合 schema (NOT NULL). 跳过此记录的 doc_id.`);
            // 根据业务逻辑决定如何处理，这里我们假设 doc_id 必须有值
            // continue; // 或者设置一个默认的、已知的UUID（不推荐）
            newFileNetDocUuid = '00000000-0000-0000-0000-000000000000'; // Placeholder, adjust as needed
        }


        await connection.query(
            `INSERT INTO ${newTableName} (id, name, category_id, doc_id, description, current_version, created_by, created_at, updated_by, updated_at, status, is_deleted)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                newTemplateUuid,
                record.name,
                newCategoryUuid,
                newFileNetDocUuid, // 使用转换后的 filenet_documents UUID
                record.description,
                record.current_version,
                record.created_by,
                record.created_at,
                record.updated_by,
                record.updated_at,
                record.status,
                record.is_deleted,
            ]
        );
    }

    await connection.query(`DROP TABLE IF EXISTS ${finalTableName}_old_uuid_migration`);
    await connection.query(`RENAME TABLE ${finalTableName} TO ${finalTableName}_old_uuid_migration`);
    await connection.query(`RENAME TABLE ${newTableName} TO ${finalTableName}`);
    console.log(`${finalTableName} 表结构更新并数据迁移完成。`);
    return idMapping;
}

async function migrateTemplateVersions(connection, templatesIdMapping, fnDocsIdMapping) {
    console.log('迁移 template_versions 表...');
    const oldTableName = 'template_versions';
    const newTableName = 'template_versions_new_uuid';
    const finalTableName = 'template_versions';

    const [tableExists] = await connection.query(`SHOW TABLES LIKE '${oldTableName}'`);
    if (tableExists.length === 0) {
        console.log(`${oldTableName} 表不存在，跳过迁移。`);
        return;
    }

    await connection.query(`DROP TABLE IF EXISTS ${newTableName}`);
    await connection.query(`
        CREATE TABLE ${newTableName} (
            id VARCHAR(36) PRIMARY KEY,
            template_id VARCHAR(36) NOT NULL, /* FK to templates.id */
            version INT NOT NULL,
            doc_id VARCHAR(36) NOT NULL, /* FK to filenet_documents.id */
            comment TEXT,
            modified_by VARCHAR(100),
            modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_hash VARCHAR(64),
            UNIQUE KEY unique_template_version (template_id, version)
            /* FKs will be established by initDatabase */
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    const [oldRecords] = await connection.query(`SELECT * FROM ${oldTableName}`);
    console.log(`从 ${oldTableName} 读取了 ${oldRecords.length} 条记录`);

    for (const record of oldRecords) {
        const newVersionUuid = uuidv4();
        const newTemplateUuid = record.template_id ? templatesIdMapping[record.template_id] : null;

        let newFileNetDocUuid = null;
        if (record.doc_id) { // doc_id 是旧的 filenet_documents INT ID
            newFileNetDocUuid = fnDocsIdMapping[record.doc_id.toString()];
            if (!newFileNetDocUuid) {
                console.warn(`警告 (template_versions): 无法在 ${FILENET_DOCUMENTS_ID_MAPPING_FILE} 中找到旧 filenet_documents.id '${record.doc_id}' 的UUID映射. 将尝试直接使用值. Version ID (old): ${record.id}`);
                newFileNetDocUuid = record.doc_id;
            }
        } else {
            console.error(`错误 (template_versions): 记录 (旧ID ${record.id}) 的 doc_id 为空，这不符合 schema (NOT NULL). 跳过此记录的 doc_id.`);
            newFileNetDocUuid = '00000000-0000-0000-0000-000000000000'; // Placeholder
        }

        if (!newTemplateUuid) {
            console.warn(`警告 (template_versions): 找不到 template_id '${record.template_id}' 的映射. 旧版本ID: ${record.id}. 跳过此记录.`);
            continue;
        }

        await connection.query(
            `INSERT INTO ${newTableName} (id, template_id, version, doc_id, comment, modified_by, modified_at, file_hash)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                newVersionUuid,
                newTemplateUuid, // 使用转换后的 templates UUID
                record.version,
                newFileNetDocUuid, // 使用转换后的 filenet_documents UUID
                record.comment,
                record.modified_by,
                record.modified_at,
                record.file_hash,
            ]
        );
    }

    await connection.query(`DROP TABLE IF EXISTS ${finalTableName}_old_uuid_migration`);
    await connection.query(`RENAME TABLE ${finalTableName} TO ${finalTableName}_old_uuid_migration`);
    await connection.query(`RENAME TABLE ${newTableName} TO ${finalTableName}`);
    console.log(`${finalTableName} 表结构更新并数据迁移完成。`);
}

async function updateFilenetDocumentsTemplateId(connection, templatesIdMapping) {
    console.log('更新 filenet_documents 表中的 template_id...');
    const tableName = 'filenet_documents';

    const [tableExists] = await connection.query(`SHOW TABLES LIKE '${tableName}'`);
    if (tableExists.length === 0) {
        console.log(`${tableName} 表不存在，跳过 template_id 更新。`);
        return;
    }

    // 确保 template_id 列是 VARCHAR(36)
    // services/database.js 中的 initDatabase 应该已经处理了列类型。
    // 如果需要在这里强制修改类型（不推荐，最好由initDatabase处理schema）：
    // try {
    //     await connection.query(`ALTER TABLE ${tableName} MODIFY COLUMN template_id VARCHAR(36) NULL`);
    //     console.log(`${tableName}.template_id 类型已确认为 VARCHAR(36)`);
    // } catch (alterError) {
    //     console.warn(`警告：修改 ${tableName}.template_id 类型失败: ${alterError.message}. 如果类型不匹配，更新可能会失败。`);
    // }


    // 获取所有需要更新 template_id 的文档
    // 旧的 template_id 是 INT。新的 template_id (来自 templatesIdMapping 的 value) 是 UUID VARCHAR(36)
    // templatesIdMapping: { old_template_int_id: new_template_uuid }

    // 为了避免逐条更新，可以收集所有需要更新的记录
    // 但如果数据量巨大，分批处理可能更好。这里为了简化，我们逐条处理，如果旧template_id存在于映射中。
    const [documents] = await connection.query(`SELECT id, template_id FROM ${tableName} WHERE template_id IS NOT NULL`);
    console.log(`在 ${tableName} 中找到 ${documents.length} 条记录具有非空的 template_id。`);

    let updatedCount = 0;
    for (const doc of documents) {
        const oldTemplateIdInt = doc.template_id; // 这应该是INT或可以转换为INT的字符串
        const newTemplateUuid = templatesIdMapping[oldTemplateIdInt]; // templatesIdMapping的key是INT

        if (newTemplateUuid) {
            try {
                await connection.query(
                    `UPDATE ${tableName} SET template_id = ? WHERE id = ? AND template_id = ?`,
                    [newTemplateUuid, doc.id, oldTemplateIdInt] // 用旧template_id做条件防止重复执行
                );
                updatedCount++;
            } catch (updateError) {
                console.error(`更新 filenet_documents.id = ${doc.id} 的 template_id (从 ${oldTemplateIdInt} 到 ${newTemplateUuid}) 失败: ${updateError.message}`);
            }
        } else {
            // 如果旧的 template_id 无法在映射中找到，记录一个警告
            // 这可能意味着数据不一致，或者该 template_id 对应的 template 不再存在或未被迁移
            console.warn(`警告: filenet_documents.id = ${doc.id} 的旧 template_id '${oldTemplateIdInt}' 在映射中未找到对应的UUID。`);
        }
    }
    console.log(`在 ${tableName} 中成功更新了 ${updatedCount} 条记录的 template_id。`);
}


// 执行迁移
migrateTemplateTablesToUUID()
    .then(() => {
        console.log('模板相关表迁移脚本执行完毕！');
        process.exit(0);
    })
    .catch((error) => {
        console.error('模板相关表迁移脚本执行失败:', error);
        process.exit(1);
    }); 