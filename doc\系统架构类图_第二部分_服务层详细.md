# OnlyOffice系统架构类图 - 第二部分：服务层详细设计

## 服务层详细类图

```mermaid
classDiagram
    %% 数据库服务类
    class DatabaseService {
        +pool: ConnectionPool
        +query(sql, params): Promise~Array~
        +queryOne(sql, params): Promise~Object~
        +transaction(callback): Promise~Any~
        +initDatabase(): Promise~void~
        +testConnection(): Promise~boolean~
    }

    %% 文档服务类
    class DocumentService {
        +getDocumentList(): Promise~Array~
        +deleteDocument(id): Promise~boolean~
        +getDocumentConfig(id): Promise~Object~
        +getDocumentRawById(id): Promise~Buffer~
        +getDocumentById(id): Promise~Object~
        +createDocumentRecord(data): Promise~Object~
        +handleCallback(data): Promise~boolean~
        +uploadDocument(file): Promise~Object~
        +getFileNetDocumentConfig(fnDocId): Promise~Object~
        -generateDocumentKey(id): string
        -buildCallbackUrl(id): string
        -processCallbackData(data): Object
    }

    %% 配置模板服务类
    class ConfigTemplateService {
        +getAllTemplates(): Promise~Array~
        +getTemplateById(id): Promise~Object~
        +getTemplateByName(name): Promise~Object~
        +getDefaultTemplate(): Promise~Object~
        +parseConfigItems(items): Object
        +buildEditorConfig(templateName, fileId, overrides, isAPI): Promise~Object~
        +createTemplate(data): Promise~Object~
        +updateTemplate(id, data): Promise~boolean~
        +deleteTemplate(id): Promise~boolean~
        +getConfigGroups(): Array
        +setDefaultTemplate(id): Promise~boolean~
        +clearDefaultTemplates(): Promise~boolean~
        -mergeDeep(target, source): void
        -applyConfigOverrides(config, overrides): Object
    }

    %% 文件存储服务类
    class FileStorageService {
        +saveFile(fileData): Promise~Object~
        +getFileById(id): Promise~Object~
        +getAllFiles(): Promise~Array~
        +updateFile(id, data): Promise~boolean~
        +deleteFile(id): Promise~boolean~
        +physicalDeleteFile(id): Promise~boolean~
        +generateStorageName(originalName): string
        +getFileExtension(filename): string
        +getMimeType(filename): string
        -ensureUploadDir(): void
        -moveFileToStorage(tempPath, storagePath): Promise~void~
    }

    %% FileNet服务类
    class FileNetService {
        +calculateFileHash(buffer): string
        +getFileExtension(filename): string
        +getMimeType(filename): string
        +uploadContentToFileNetAndGetIds(content, filename): Promise~Object~
        +uploadToFileNet(filePath, metadata): Promise~Object~
        +downloadFromFileNet(fnDocId): Promise~Buffer~
        +getFileNetDocumentsFromDB(): Promise~Array~
        +downloadDocument(fnDocId): Promise~Object~
        +copyFileNetDocument(sourceFnDocId, newName): Promise~Object~
        -createFileNetConnection(): Object
        -handleFileNetError(error): Error
    }

    %% 配置服务类
    class ConfigService {
        +getConfig(): Promise~Object~
        +saveConfig(config): Promise~boolean~
        +resetConfig(): Promise~boolean~
        +getCurrentConfig(): Promise~Object~
        +getConfigDescriptions(): Object
        +DEFAULT_CONFIG: Object
        -mergeConfig(defaultConfig, userConfig): Object
        -validateConfig(config): boolean
    }

    %% 模板服务类 (Legacy)
    class TemplateService {
        +getTemplates(): Promise~Array~
        +getTemplateById(id): Promise~Object~
        +createTemplate(data): Promise~Object~
        +updateTemplate(id, data): Promise~boolean~
        +deleteTemplate(id): Promise~boolean~
        +createDocumentFromTemplate(templateId, data): Promise~Object~
        -validateTemplateData(data): boolean
    }

    %% JWT服务类
    class JWTService {
        +generateJWT(payload): string
        +verifyJWT(token): Object
        -getSecretKey(): string
    }

    %% 外部系统接口
    class OnlyOfficeServer {
        <<external>>
        +documentServerUrl: string
        +callbackUrl: string
    }

    class FileNetContentEngine {
        <<external>>
        +connectionUrl: string
        +credentials: Object
    }

    class MySQLDatabase {
        <<external>>
        +host: string
        +database: string
    }

    class FileSystem {
        <<external>>
        +uploadDir: string
        +tmpDir: string
    }

    %% 服务间依赖关系
    DocumentService --> DatabaseService
    DocumentService --> FileStorageService
    DocumentService --> JWTService
    DocumentService --> ConfigService
    DocumentService --> OnlyOfficeServer

    ConfigTemplateService --> DatabaseService
    ConfigTemplateService --> ConfigService

    FileStorageService --> DatabaseService
    FileStorageService --> FileSystem

    FileNetService --> DatabaseService
    FileNetService --> FileNetContentEngine

    TemplateService --> DatabaseService

    %% 外部系统依赖
    DatabaseService --> MySQLDatabase

    %% 服务层到配置层依赖
    DocumentService --> ConfigService
    ConfigTemplateService --> ConfigService
    FileStorageService --> ConfigService
    FileNetService --> ConfigService

    %% 样式定义 - 分别定义每个类的样式
    classDef primaryService fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    classDef serviceClass fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef externalClass fill:#fce4ec,stroke:#e91e63,stroke-width:2px

    %% 应用样式到类 - 分别为每个类设置样式
    class DocumentService primaryService
    class ConfigTemplateService primaryService
    class DatabaseService serviceClass
    class FileStorageService serviceClass
    class FileNetService serviceClass
    class ConfigService serviceClass
    class TemplateService serviceClass
    class JWTService serviceClass
    class OnlyOfficeServer externalClass
    class FileNetContentEngine externalClass
    class MySQLDatabase externalClass
    class FileSystem externalClass
```

## 服务层接口详细说明

### 🗄️ DatabaseService (数据库服务)
**职责**: 数据库连接管理和SQL操作封装
- `query()`: 执行SQL查询，返回结果集
- `queryOne()`: 执行查询返回单个结果
- `transaction()`: 事务处理
- `initDatabase()`: 初始化数据库表结构
- `testConnection()`: 测试数据库连接状态

### 📄 DocumentService (文档服务) - 核心服务
**职责**: OnlyOffice文档集成和管理
- `getDocumentConfig()`: 生成OnlyOffice编辑器配置
- `handleCallback()`: 处理OnlyOffice回调事件
- `uploadDocument()`: 文档上传处理
- `getFileNetDocumentConfig()`: FileNet文档配置生成
- 内部方法：文档密钥生成、回调URL构建等

### ⚙️ ConfigTemplateService (配置模板服务) - 核心服务
**职责**: 编辑器配置模板管理
- `buildEditorConfig()`: 根据模板构建编辑器配置
- `parseConfigItems()`: 解析配置项
- `getAllTemplates()`: 获取所有配置模板
- `setDefaultTemplate()`: 设置默认模板
- 内部方法：深度合并配置、应用配置覆盖等

### 💾 FileStorageService (文件存储服务)
**职责**: 本地文件系统管理
- `saveFile()`: 保存文件到本地存储
- `getFileById()`: 根据ID获取文件信息
- `physicalDeleteFile()`: 物理删除文件
- `generateStorageName()`: 生成存储文件名
- 文件类型识别和MIME类型处理

### 🌐 FileNetService (FileNet服务)
**职责**: FileNet系统集成
- `uploadToFileNet()`: 上传文档到FileNet
- `downloadFromFileNet()`: 从FileNet下载文档
- `copyFileNetDocument()`: 复制FileNet文档
- `calculateFileHash()`: 计算文件哈希值
- FileNet连接管理和错误处理

### 🔧 ConfigService (配置服务)
**职责**: 系统配置管理
- `getConfig()`: 获取系统配置
- `saveConfig()`: 保存配置更改
- `resetConfig()`: 重置为默认配置
- `mergeConfig()`: 配置合并逻辑

### 🔐 JWTService (JWT服务)
**职责**: JWT令牌管理
- `generateJWT()`: 生成JWT令牌
- `verifyJWT()`: 验证JWT令牌
- 安全密钥管理

### 📋 TemplateService (模板服务) - Legacy
**职责**: 文档模板管理 (旧版本，可能被ConfigTemplateService替代)
- `createDocumentFromTemplate()`: 从模板创建文档
- 模板CRUD操作

## 服务层设计模式

### 🏗️ 依赖注入模式
- 服务通过require()注入其他服务
- 松耦合设计，便于测试和维护

### 🔄 适配器模式
- FileNetService作为FileNet系统的适配器
- DocumentService作为OnlyOffice的适配器

### 🏭 工厂模式
- ConfigTemplateService中的配置构建器
- DocumentService中的配置生成器

### 🎭 门面模式
- 各服务为复杂的外部系统提供简化接口
- 统一的错误处理和日志记录

## 数据流转流程

### 📋 文档编辑流程
```
1. 用户请求编辑文档
2. DocumentService.getDocumentConfig()
3. ConfigTemplateService.buildEditorConfig()
4. JWTService.generateJWT()
5. 返回OnlyOffice配置
```

### 💾 文档保存流程
```
1. OnlyOffice回调
2. DocumentService.handleCallback()
3. FileStorageService.updateFile()
4. DatabaseService.query() 更新记录
```

### 🔧 配置管理流程
```
1. 管理员配置模板
2. ConfigTemplateService.createTemplate()
3. DatabaseService.query() 保存配置
4. ConfigService.getConfig() 获取系统配置
```

## 服务层优化建议

### 🚀 性能优化
- 数据库连接池管理
- 缓存常用配置模板
- 异步处理文件操作

### 🔒 安全加固
- JWT密钥管理
- 文件上传验证
- SQL注入防护

### 🧪 测试覆盖
- 单元测试每个服务方法
- 集成测试服务间交互
- Mock外部系统依赖