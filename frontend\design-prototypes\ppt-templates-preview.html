<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT 科技风模板预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 40px 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #1e293b, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: #64748b;
            font-weight: 400;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .template-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .template-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(15, 23, 42, 0.15);
        }

        .template-preview {
            width: 100%;
            height: 180px;
            border-radius: 8px;
            background: #f1f5f9;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            border: 1px solid #e2e8f0;
        }

        .template-preview iframe {
            width: 100%;
            height: 100%;
            border: none;
            transform: scale(0.25);
            transform-origin: top left;
            width: 400%;
            height: 400%;
        }

        .template-info h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .template-info p {
            font-size: 0.95rem;
            color: #64748b;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .template-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 20px;
        }

        .tag {
            background: #eff6ff;
            color: #2563eb;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .template-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            text-align: center;
            flex: 1;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .usage-guide {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
            border: 1px solid #e2e8f0;
        }

        .usage-guide h2 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 20px;
        }

        .usage-guide h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin: 20px 0 10px 0;
        }

        .usage-guide p, .usage-guide li {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .usage-guide ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .code-block {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'JetBrains Mono', 'SF Mono', 'Consolas', monospace;
            font-size: 0.9rem;
            color: #374151;
            margin: 10px 0;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2.2rem; }
            .templates-grid { grid-template-columns: 1fr; }
            .template-actions { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PPT 科技风模板预览</h1>
            <p>5款专业的白色科技风PPT模板，适用于技术演示和产品展示</p>
        </div>

        <div class="templates-grid">
            <div class="template-card">
                <div class="template-preview" onclick="window.open('ppt-tech-minimal-white.html', '_blank')">
                    <iframe src="ppt-tech-minimal-white.html" scrolling="no"></iframe>
                </div>
                <div class="template-info">
                    <h3>极简科技风</h3>
                    <p>简洁线条设计，适合企业汇报和产品介绍，突出内容重点。</p>
                    <div class="template-tags">
                        <span class="tag">极简</span>
                        <span class="tag">企业级</span>
                        <span class="tag">几何</span>
                    </div>
                    <div class="template-actions">
                        <a href="ppt-tech-minimal-white.html" target="_blank" class="btn btn-primary">预览</a>
                        <a href="ppt-tech-minimal-white.html" download class="btn btn-secondary">下载</a>
                    </div>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview" onclick="window.open('ppt-tech-geometric-white.html', '_blank')">
                    <iframe src="ppt-tech-geometric-white.html" scrolling="no"></iframe>
                </div>
                <div class="template-info">
                    <h3>几何科技风</h3>
                    <p>六边形和三角形元素，适合技术架构展示和创新方案介绍。</p>
                    <div class="template-tags">
                        <span class="tag">几何</span>
                        <span class="tag">架构</span>
                        <span class="tag">创新</span>
                    </div>
                    <div class="template-actions">
                        <a href="ppt-tech-geometric-white.html" target="_blank" class="btn btn-primary">预览</a>
                        <a href="ppt-tech-geometric-white.html" download class="btn btn-secondary">下载</a>
                    </div>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview" onclick="window.open('ppt-tech-grid-white.html', '_blank')">
                    <iframe src="ppt-tech-grid-white.html" scrolling="no"></iframe>
                </div>
                <div class="template-info">
                    <h3>网格科技风</h3>
                    <p>电路板风格设计，适合系统架构和工程技术方案展示。</p>
                    <div class="template-tags">
                        <span class="tag">电路板</span>
                        <span class="tag">系统</span>
                        <span class="tag">工程</span>
                    </div>
                    <div class="template-actions">
                        <a href="ppt-tech-grid-white.html" target="_blank" class="btn btn-primary">预览</a>
                        <a href="ppt-tech-grid-white.html" download class="btn btn-secondary">下载</a>
                    </div>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview" onclick="window.open('ppt-tech-data-white.html', '_blank')">
                    <iframe src="ppt-tech-data-white.html" scrolling="no"></iframe>
                </div>
                <div class="template-info">
                    <h3>数据可视化风</h3>
                    <p>图表和数据元素设计，适合数据报告和性能指标展示。</p>
                    <div class="template-tags">
                        <span class="tag">数据</span>
                        <span class="tag">图表</span>
                        <span class="tag">分析</span>
                    </div>
                    <div class="template-actions">
                        <a href="ppt-tech-data-white.html" target="_blank" class="btn btn-primary">预览</a>
                        <a href="ppt-tech-data-white.html" download class="btn btn-secondary">下载</a>
                    </div>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview" onclick="window.open('ppt-tech-futuristic-white.html', '_blank')">
                    <iframe src="ppt-tech-futuristic-white.html" scrolling="no"></iframe>
                </div>
                <div class="template-info">
                    <h3>未来主义风</h3>
                    <p>全息效果和粒子动画，适合前沿技术和概念展示。</p>
                    <div class="template-tags">
                        <span class="tag">未来</span>
                        <span class="tag">全息</span>
                        <span class="tag">动画</span>
                    </div>
                    <div class="template-actions">
                        <a href="ppt-tech-futuristic-white.html" target="_blank" class="btn btn-primary">预览</a>
                        <a href="ppt-tech-futuristic-white.html" download class="btn btn-secondary">下载</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="usage-guide">
            <h2>使用指南</h2>
            
            <h3>模板变量</h3>
            <p>每个模板都支持以下占位符变量：</p>
            <div class="code-block">{{ page_title }}           - 页面标题<br>{{ main_heading }}         - 主标题<br>{{ page_content }}         - 主要内容<br>{{ current_page_number }}  - 当前页码<br>{{ total_page_count }}     - 总页数</div>

            <h3>使用方法</h3>
            <ul>
                <li><strong>直接编辑</strong>：下载HTML文件，用文本编辑器替换占位符</li>
                <li><strong>模板引擎</strong>：配合Handlebars、Mustache等模板引擎动态生成</li>
                <li><strong>导出PDF</strong>：浏览器打印功能可直接导出为PDF</li>
            </ul>

            <h3>技术特点</h3>
            <ul>
                <li>1280x720分辨率（16:9），标准PPT尺寸</li>
                <li>纯CSS实现，无外部依赖</li>
                <li>响应式设计，自适应不同屏幕</li>
                <li>现代浏览器兼容</li>
            </ul>

            <h3>自定义样式</h3>
            <p>每个模板都使用CSS变量定义颜色和尺寸，可以轻松自定义：</p>
            <div class="code-block">:root {<br>  --tech-blue: #3b82f6;  /* 主色调 */<br>  --tech-gray: #64748b;  /* 文字颜色 */<br>  /* 更多变量... */<br>}</div>
        </div>
    </div>
</body>
</html> 