const bcrypt = require('bcrypt');

async function testPassword() {
    console.log('🔄 测试密码验证...');
    
    const password = 'admin123';
    const hash = '$2b$10$tBVbb2iiFqMyqSLvs2SS1OmT4qh3RPF.90qf2jRPGKQqlJNgng2TG';
    
    console.log('📋 原始密码:', password);
    console.log('📋 哈希值:', hash);
    
    try {
        const isValid = await bcrypt.compare(password, hash);
        console.log('✅ 密码验证结果:', isValid);
        
        // 生成新的哈希进行对比
        console.log('🔄 生成新的密码哈希...');
        const newHash = await bcrypt.hash(password, 10);
        console.log('📋 新哈希值:', newHash);
        
        const newIsValid = await bcrypt.compare(password, newHash);
        console.log('✅ 新哈希验证结果:', newIsValid);
        
    } catch (error) {
        console.error('❌ 验证失败:', error);
    }
}

testPassword(); 