const axios = require('axios');

async function getToken() {
  try {
    console.log('🔐 正在获取JWT令牌...');
    console.log('📡 请求地址: http://localhost:3000/api/auth/login');
    
    const response = await axios.post('http://localhost:3000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10秒超时
    });

    console.log('📋 响应状态:', response.status);
    console.log('📋 响应数据:', JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      const token = response.data.data.access_token;
      console.log('✅ 登录成功！');
      console.log(`🎫 访问令牌: ${token}`);
      console.log('\n📋 使用方法:');
      console.log('1. 打开浏览器访问: http://localhost:3000/api-docs');
      console.log('2. 点击右上角的"Authorize"按钮');
      console.log('3. 在弹出框中输入令牌（无需Bearer前缀）');
      console.log('4. 点击"Authorize"按钮完成认证');
      
      // 测试token是否有效
      console.log('\n🧪 测试令牌有效性...');
      const testResponse = await axios.get('http://localhost:3000/api/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 5000
      });
      
      console.log('✅ 令牌测试成功！API返回:', testResponse.data.message);
      
    } else {
      console.error('❌ 登录失败:', response.data.error);
    }
  } catch (error) {
    console.error('❌ 获取令牌失败:');
    console.error('错误类型:', error.constructor.name);
    
    if (error.code) {
      console.error('错误代码:', error.code);
    }
    
    if (error.response) {
      console.error('HTTP状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('请求失败 - 无响应');
      console.error('请求详情:', error.request.method, error.request.path);
    } else {
      console.error('网络错误:', error.message);
    }
    
    // 检查服务器是否运行
    console.log('\n🔍 检查服务器状态...');
    try {
      const healthResponse = await axios.get('http://localhost:3000/api/health', { timeout: 3000 });
      console.log('✅ 服务器运行正常');
    } catch (healthError) {
      console.error('❌ 服务器可能未运行或无法访问');
    }
  }
}

getToken(); 