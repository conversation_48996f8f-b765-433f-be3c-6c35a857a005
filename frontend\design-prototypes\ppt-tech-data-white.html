<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        :root {
            --data-white: #ffffff;
            --data-gray-50: #f8fafc;
            --data-gray-100: #f1f5f9;
            --data-gray-200: #e2e8f0;
            --data-gray-400: #94a3b8;
            --data-gray-600: #475569;
            --data-gray-900: #0f172a;
            --data-blue: #0ea5e9;
            --data-purple: #8b5cf6;
            --data-green: #10b981;
            --data-orange: #f59e0b;
            --data-red: #ef4444;
            --data-shadow: rgba(15, 23, 42, 0.1);
            --data-glow: rgba(14, 165, 233, 0.3);
        }

        html {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--data-white) 0%, var(--data-gray-50) 100%);
        }

        body {
            width: 1280px;
            height: 720px;
            margin: 0;
            padding: 0;
            position: relative;
            overflow: hidden;
            background: var(--data-white);
            font-family: 'Inter', 'SF Pro Display', sans-serif;
            flex-shrink: 0;
        }
        
        .slide-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: var(--data-gray-900);
            position: relative;
            background: var(--data-white);
        }
        
        /* 数据可视化背景 */
        .data-visualization-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            opacity: 0.03;
        }
        
        .chart-lines {
            position: absolute;
            top: 100px;
            right: 50px;
            width: 200px;
            height: 150px;
            background-image: 
                linear-gradient(45deg, var(--data-blue) 2px, transparent 2px),
                linear-gradient(-45deg, var(--data-purple) 2px, transparent 2px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            border-radius: 8px;
        }
        
        .data-points {
            position: absolute;
            bottom: 80px;
            left: 80px;
            width: 150px;
            height: 100px;
        }
        
        .data-point {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--data-blue);
            border-radius: 50%;
        }
        
        .data-point:nth-child(1) { top: 20px; left: 20px; }
        .data-point:nth-child(2) { top: 40px; left: 50px; }
        .data-point:nth-child(3) { top: 10px; left: 80px; }
        .data-point:nth-child(4) { top: 60px; left: 110px; }
        
        .slide-header {
            padding: 50px 80px 20px 80px;
            position: relative;
            z-index: 1;
            background: var(--data-white);
            border-bottom: 1px solid var(--data-gray-200);
        }
        
        .slide-title {
            font-size: clamp(2.4rem, 4vw, 4rem);
            font-weight: 700;
            color: var(--data-gray-900);
            margin: 0;
            line-height: 1.1;
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, var(--data-gray-900) 0%, var(--data-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .slide-content {
            flex: 1;
            padding: 30px 80px 50px 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .content-main {
            font-size: clamp(1.1rem, 2.5vw, 1.6rem);
            line-height: 1.7;
            color: var(--data-gray-600);
            font-weight: 400;
        }
        
        /* 数据指标列表 */
        .content-points {
            list-style: none;
            padding: 0;
            margin: 30px 0 0 0;
        }
        
        .content-points li {
            margin-bottom: 22px;
            padding-left: 45px;
            position: relative;
            font-size: 1.3rem;
            color: var(--data-gray-900);
            font-weight: 500;
        }
        
        .content-points li:before {
            content: "";
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--data-blue);
            border-radius: 2px;
            box-shadow: 0 0 6px var(--data-glow);
        }
        
        .content-points li:after {
            content: "";
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid var(--data-blue);
            border-radius: 2px;
            opacity: 0.2;
        }
        
        /* 数据仪表板 */
        .data-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .metric-card {
            background: var(--data-white);
            border: 1px solid var(--data-gray-200);
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--data-blue), var(--data-purple));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover::before {
            transform: scaleX(1);
        }
        
        .metric-card:hover {
            border-color: var(--data-blue);
            box-shadow: 0 12px 40px var(--data-shadow);
            transform: translateY(-4px);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--data-blue);
            display: block;
            margin-bottom: 5px;
            letter-spacing: -0.02em;
        }
        
        .metric-label {
            font-size: 0.95rem;
            color: var(--data-gray-600);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 8px;
        }
        
        .metric-change {
            font-size: 0.85rem;
            font-weight: 500;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
        }
        
        .metric-change.positive {
            background: rgba(16, 185, 129, 0.1);
            color: var(--data-green);
        }
        
        .metric-change.negative {
            background: rgba(239, 68, 68, 0.1);
            color: var(--data-red);
        }
        
        /* 进度条样式 */
        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--data-gray-100);
            border-radius: 3px;
            margin: 15px 0 10px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--data-blue), var(--data-purple));
            border-radius: 3px;
            transition: width 1s ease;
        }
        
        .slide-footer {
            position: absolute;
            bottom: 25px;
            right: 80px;
            font-size: 14px;
            color: var(--data-gray-400);
            font-weight: 500;
            z-index: 1;
        }

        /* 数据趋势指示器 */
        .trend-indicators {
            position: absolute;
            top: 40px;
            right: 80px;
            display: flex;
            gap: 15px;
            z-index: 1;
        }
        
        .trend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            color: var(--data-gray-600);
            font-weight: 500;
        }
        
        .trend-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .trend-dot.up { background: var(--data-green); }
        .trend-dot.down { background: var(--data-red); }
        .trend-dot.stable { background: var(--data-orange); }
        
        /* 数据图表装饰 */
        .chart-decoration {
            position: absolute;
            bottom: 40px;
            right: 80px;
            width: 80px;
            height: 60px;
            pointer-events: none;
        }
        
        .chart-bar {
            position: absolute;
            bottom: 0;
            width: 8px;
            background: var(--data-blue);
            border-radius: 2px 2px 0 0;
            opacity: 0.3;
        }
        
        .chart-bar:nth-child(1) { left: 0; height: 20px; }
        .chart-bar:nth-child(2) { left: 12px; height: 35px; }
        .chart-bar:nth-child(3) { left: 24px; height: 25px; }
        .chart-bar:nth-child(4) { left: 36px; height: 45px; }
        .chart-bar:nth-child(5) { left: 48px; height: 30px; }
        .chart-bar:nth-child(6) { left: 60px; height: 40px; }
        
        @media (max-width: 1280px) {
            body {
                width: 100vw;
                height: 56.25vw;
                max-height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="data-visualization-bg">
            <div class="chart-lines"></div>
            <div class="data-points">
                <div class="data-point"></div>
                <div class="data-point"></div>
                <div class="data-point"></div>
                <div class="data-point"></div>
            </div>
        </div>
        
        <div class="trend-indicators">
            <div class="trend-item">
                <div class="trend-dot up"></div>
                <span>性能</span>
            </div>
            <div class="trend-item">
                <div class="trend-dot stable"></div>
                <span>可用性</span>
            </div>
            <div class="trend-item">
                <div class="trend-dot up"></div>
                <span>效率</span>
            </div>
        </div>
        
        <div class="chart-decoration">
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
        </div>
        
        <header class="slide-header">
            <h1 class="slide-title">{{ main_heading }}</h1>
        </header>
        
        <main class="slide-content">
            <div class="content-main">
                {{ page_content }}

                <!--
                <ul class="content-points">
                    <li>实时数据流分析处理能力提升 300%</li>
                    <li>机器学习模型预测准确率达到 95.8%</li>
                    <li>系统响应时间优化至亚毫秒级别</li>
                    <li>数据处理吞吐量提升至 PB/天级别</li>
                </ul>
                -->

                <!--
                <div class="data-dashboard">
                    <div class="metric-card">
                        <span class="metric-value">2.8M</span>
                        <span class="metric-label">日活用户</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%;"></div>
                        </div>
                        <span class="metric-change positive">+12.5%</span>
                    </div>
                    <div class="metric-card">
                        <span class="metric-value">99.97%</span>
                        <span class="metric-label">系统可用性</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 99%;"></div>
                        </div>
                        <span class="metric-change positive">+0.3%</span>
                    </div>
                    <div class="metric-card">
                        <span class="metric-value">145ms</span>
                        <span class="metric-label">平均响应</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 70%;"></div>
                        </div>
                        <span class="metric-change negative">-8.2%</span>
                    </div>
                </div>
                -->
            </div>
        </main>
        
        <footer class="slide-footer">
            {{ current_page_number }} / {{ total_page_count }}
        </footer>
    </div>
</body>
</html> 