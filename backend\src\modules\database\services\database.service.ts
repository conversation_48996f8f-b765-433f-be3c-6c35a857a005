import { Injectable, OnModuleInit } from '@nestjs/common';
import * as mysql from 'mysql2/promise';
import envConfig from '../../../config/env.config';
import * as dotenv from 'dotenv';
import { 
  DatabaseRow, 
  CountResult
} from '../types/database.types';

// 加载环境变量
dotenv.config();



/**
 * 数据库服务
 * 迁移自原有的services/database.js
 * 提供数据库连接池和查询方法，保持原有逻辑不变
 * 使用统一的环境变量配置系统
 */
@Injectable()
export class DatabaseService implements OnModuleInit {
  private pool: mysql.Pool;
  private isInitialized = false;

  async onModuleInit() {
    await this.initializeConnection();
    await this.initDatabase();
  }

  /**
   * 初始化数据库连接池
   */
  private async initializeConnection() {
    try {
      console.log('[DatabaseService] 正在初始化数据库连接池...');
      
      const dbConfig = {
        host: envConfig.database.host,
        port: envConfig.database.port,
        user: envConfig.database.user,
        password: envConfig.database.password,
        database: envConfig.database.name,
        waitForConnections: true,
        connectionLimit: 10, // 可以考虑添加到配置中
        queueLimit: 0,
        namedPlaceholders: true,
        charset: 'utf8mb4'
      };

      console.log('[DatabaseService] 数据库配置:', {
        ...dbConfig,
        password: dbConfig.password ? '***已设置***' : '***未设置***'
      });

      this.pool = mysql.createPool(dbConfig);
      
      // 测试连接
      await this.testConnection();
      console.log('[DatabaseService] 数据库连接池初始化成功');
      
    } catch (error) {
      console.error('[DatabaseService] 数据库连接池初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化数据库
   * 创建必要的表结构 - 完全迁移自原有逻辑
   */
  async initDatabase() {
    if (this.isInitialized) {
      return true;
    }

    try {
      console.log('[DatabaseService] 正在检查数据库初始化状态...');

      // 创建系统配置表（如果不存在）
      await this.query(`
        CREATE TABLE IF NOT EXISTS system_settings (
          setting_key VARCHAR(100) PRIMARY KEY,
          setting_value TEXT NOT NULL,
          description TEXT,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 检查数据库是否已经初始化过
      const initStatus = await this.query(`
        SELECT setting_value FROM system_settings WHERE setting_key = 'database_initialized'
      `);

      // 如果有初始化记录且值为true，则跳过初始化
      if (initStatus.length > 0 && (initStatus[0] as { setting_value: string }).setting_value === 'true') {
        console.log('[DatabaseService] 数据库已经完成初始化，跳过初始化步骤');
        this.isInitialized = true;
        return true;
      }

      console.log('[DatabaseService] 正在初始化数据库结构...');

      // 创建文件表
      await this.query(`
        CREATE TABLE IF NOT EXISTS files (
          id VARCHAR(64) PRIMARY KEY,
          original_name VARCHAR(255) NOT NULL,
          storage_name VARCHAR(255) NOT NULL,
          file_size BIGINT NOT NULL,
          mime_type VARCHAR(100),
          extension VARCHAR(20),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          last_modified_by VARCHAR(100),
          version INT DEFAULT 1,
          is_deleted BOOLEAN DEFAULT FALSE,
          INDEX idx_original_name (original_name),
          INDEX idx_storage_name (storage_name),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 检查filenet_documents表是否存在
      const tableExists = await this.query(`
        SELECT COUNT(*) as count FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'filenet_documents'
      `);

      if ((tableExists[0] as { count: number }).count === 0) {
        // 如果表不存在，创建它
        await this.query(`
          CREATE TABLE IF NOT EXISTS filenet_documents (
            id VARCHAR(36) PRIMARY KEY,
            fn_doc_id VARCHAR(255) NOT NULL COMMENT 'FileNet Document ID',
            original_name VARCHAR(255) NOT NULL COMMENT 'Original file name',
            file_size BIGINT COMMENT 'File size in bytes',
            mime_type VARCHAR(100) COMMENT 'MIME type of the file',
            extension VARCHAR(20) COMMENT 'File extension',
            version INT DEFAULT 1 COMMENT 'Current document version',
            file_hash VARCHAR(64) COMMENT 'SHA-256 hash of file content',
            created_by VARCHAR(100) COMMENT 'User who created the document',
            last_modified_by VARCHAR(100) COMMENT 'User who last modified the document',
            template_id VARCHAR(36) COMMENT 'ID of template if document is based on template',
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of upload to FileNet',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
            is_deleted BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag',
            UNIQUE KEY unique_fn_doc_id (fn_doc_id),
            INDEX idx_original_name_filenet (original_name),
            INDEX idx_file_hash (file_hash),
            INDEX idx_template_id (template_id),
            INDEX idx_is_deleted (is_deleted)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `);
      } else {
        console.log('[DatabaseService] filenet_documents表已存在，检查缺失的列...');
        
        // 检查并添加缺失的列的逻辑...
        await this.checkAndAddMissingColumns();
      }

      // 创建文件版本表
      await this.query(`
        CREATE TABLE IF NOT EXISTS filenet_document_versions (
          id VARCHAR(36) PRIMARY KEY,
          doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id',
          fn_doc_id VARCHAR(255) NOT NULL COMMENT 'FileNet Document ID for this version',
          version INT NOT NULL COMMENT 'Version number',
          file_hash VARCHAR(64) COMMENT 'SHA-256 hash of this version',
          modified_by VARCHAR(100) COMMENT 'User who created this version',
          modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When this version was created',
          file_size BIGINT COMMENT 'File size of this version in bytes',
          comment TEXT COMMENT 'Version comment/description',
          UNIQUE KEY unique_doc_version (doc_id, version),
          INDEX idx_version_fn_doc_id (fn_doc_id),
          FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 创建模板分类表
      await this.query(`
        CREATE TABLE IF NOT EXISTS template_categories (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(100) NOT NULL COMMENT 'Category name',
          parent_id VARCHAR(36) COMMENT 'Parent category ID for hierarchical categories',
          description TEXT COMMENT 'Category description',
          sort_order INT DEFAULT 0 COMMENT 'Sort order for display',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_parent_id (parent_id),
          FOREIGN KEY (parent_id) REFERENCES template_categories(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 创建模板表
      await this.query(`
        CREATE TABLE IF NOT EXISTS templates (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(255) NOT NULL COMMENT 'Template name',
          category_id VARCHAR(36) COMMENT 'Category ID',
          doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id',
          description TEXT COMMENT 'Template description',
          current_version INT DEFAULT 1 COMMENT 'Current active version',
          created_by VARCHAR(100) COMMENT 'Creator',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_by VARCHAR(100) COMMENT 'Last updater',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          status ENUM('enabled', 'disabled') DEFAULT 'enabled' COMMENT 'Template status',
          is_deleted BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag',
          INDEX idx_category (category_id),
          INDEX idx_status (status),
          INDEX idx_is_deleted (is_deleted),
          FOREIGN KEY (category_id) REFERENCES template_categories(id) ON DELETE SET NULL,
          FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 创建模板版本表
      await this.query(`
        CREATE TABLE IF NOT EXISTS template_versions (
          id VARCHAR(36) PRIMARY KEY,
          template_id VARCHAR(36) NOT NULL COMMENT 'Reference to templates.id',
          version INT NOT NULL COMMENT 'Version number',
          doc_id VARCHAR(36) NOT NULL COMMENT 'Reference to filenet_documents.id for this version',
          comment TEXT COMMENT 'Version comment',
          modified_by VARCHAR(100) COMMENT 'User who created this version',
          modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When this version was created',
          file_hash VARCHAR(64) COMMENT 'SHA-256 hash of this version',
          FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE CASCADE,
          FOREIGN KEY (doc_id) REFERENCES filenet_documents(id) ON DELETE RESTRICT,
          UNIQUE KEY unique_template_version (template_id, version)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 处理file_versions表和视图
      await this.query(`DROP TABLE IF EXISTS file_versions`);
      await this.query(`
        CREATE OR REPLACE VIEW file_versions AS 
        SELECT * FROM filenet_document_versions
      `);

      // ====================================
      // 创建用户管理系统表 (新增)
      // ====================================
      console.log('[DatabaseService] 正在创建用户管理系统表...');

      // 用户角色表
      await this.query(`
        CREATE TABLE IF NOT EXISTS user_roles (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
          display_name VARCHAR(100) NOT NULL COMMENT '角色显示名称',
          description TEXT COMMENT '角色描述',
          permissions JSON COMMENT '权限列表JSON',
          is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统预设角色',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
          sort_order INT DEFAULT 0 COMMENT '排序序号',
          created_by VARCHAR(36) COMMENT '创建者ID',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_by VARCHAR(36) COMMENT '更新者ID',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_name (name),
          INDEX idx_is_active (is_active),
          INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 用户主表
      await this.query(`
        CREATE TABLE IF NOT EXISTS users (
          id VARCHAR(36) PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
          email VARCHAR(100) UNIQUE COMMENT '邮箱地址',
          password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
          full_name VARCHAR(100) COMMENT '真实姓名',
          phone VARCHAR(20) COMMENT '电话号码',
          avatar_url VARCHAR(500) COMMENT '头像URL',
          status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active' COMMENT '用户状态',
          role_id VARCHAR(36) COMMENT '角色ID',
          department VARCHAR(100) COMMENT '部门',
          position VARCHAR(100) COMMENT '职位',
          last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
          last_login_ip VARCHAR(45) COMMENT '最后登录IP',
          password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '密码修改时间',
          failed_login_attempts INT DEFAULT 0 COMMENT '失败登录尝试次数',
          locked_until TIMESTAMP NULL COMMENT '账户锁定截止时间',
          email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
          phone_verified BOOLEAN DEFAULT FALSE COMMENT '电话是否已验证',
          two_factor_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因子认证',
          created_by VARCHAR(36) COMMENT '创建者ID',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_by VARCHAR(36) COMMENT '更新者ID',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          deleted_at TIMESTAMP NULL COMMENT '软删除时间',
          
          INDEX idx_username (username),
          INDEX idx_email (email),
          INDEX idx_status (status),
          INDEX idx_role_id (role_id),
          INDEX idx_last_login (last_login_at),
          INDEX idx_created_at (created_at),
          INDEX idx_deleted_at (deleted_at),
          FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 权限表
      await this.query(`
        CREATE TABLE IF NOT EXISTS user_permissions (
          id VARCHAR(36) PRIMARY KEY,
          code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
          name VARCHAR(100) NOT NULL COMMENT '权限名称',
          description TEXT COMMENT '权限描述',
          module VARCHAR(50) NOT NULL COMMENT '所属模块',
          resource VARCHAR(100) COMMENT '资源标识',
          action VARCHAR(50) COMMENT '操作类型 (create,read,update,delete,execute)',
          conditions JSON COMMENT '权限条件JSON',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
          sort_order INT DEFAULT 0 COMMENT '排序序号',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          
          INDEX idx_code (code),
          INDEX idx_module (module),
          INDEX idx_resource (resource),
          INDEX idx_action (action),
          INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 用户会话表
      await this.query(`
        CREATE TABLE IF NOT EXISTS user_sessions (
          id VARCHAR(36) PRIMARY KEY,
          user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
          session_token VARCHAR(255) NOT NULL UNIQUE COMMENT '会话令牌',
          refresh_token VARCHAR(255) COMMENT '刷新令牌',
          device_type VARCHAR(50) COMMENT '设备类型',
          device_name VARCHAR(100) COMMENT '设备名称',
          user_agent TEXT COMMENT '用户代理字符串',
          ip_address VARCHAR(45) COMMENT '登录IP地址',
          location VARCHAR(200) COMMENT '登录地点',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
          expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
          last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_id (user_id),
          INDEX idx_session_token (session_token),
          INDEX idx_is_active (is_active),
          INDEX idx_expires_at (expires_at),
          INDEX idx_last_activity (last_activity_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 审计日志表
      await this.query(`
        CREATE TABLE IF NOT EXISTS audit_logs (
          id VARCHAR(36) PRIMARY KEY,
          user_id VARCHAR(36) COMMENT '操作用户ID',
          action VARCHAR(100) NOT NULL COMMENT '操作动作',
          resource_type VARCHAR(50) COMMENT '资源类型',
          resource_id VARCHAR(36) COMMENT '资源ID',
          details JSON COMMENT '操作详情JSON',
          ip_address VARCHAR(45) COMMENT 'IP地址',
          user_agent TEXT COMMENT '用户代理',
          status ENUM('success', 'failed', 'error') DEFAULT 'success' COMMENT '操作状态',
          error_message TEXT COMMENT '错误信息',
          execution_time INT COMMENT '执行时间(毫秒)',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
          INDEX idx_user_id (user_id),
          INDEX idx_action (action),
          INDEX idx_resource_type (resource_type),
          INDEX idx_resource_id (resource_id),
          INDEX idx_status (status),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 密码历史表
      await this.query(`
        CREATE TABLE IF NOT EXISTS password_history (
          id VARCHAR(36) PRIMARY KEY,
          user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
          password_hash VARCHAR(255) NOT NULL COMMENT '历史密码哈希',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_id (user_id),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // 插入系统预设角色
      await this.query(`
        INSERT IGNORE INTO user_roles (id, name, display_name, description, permissions, is_system, is_active, sort_order) VALUES
        ('role-super-admin', 'super_admin', '超级管理员', '系统最高权限管理员，拥有所有权限', JSON_ARRAY('*'), TRUE, TRUE, 1),
        ('role-admin', 'admin', '管理员', '系统管理员，拥有大部分管理权限', JSON_ARRAY('users.*', 'documents.*', 'templates.*', 'config.*', 'system.read'), TRUE, TRUE, 2),
        ('role-editor', 'editor', '编辑者', '内容编辑者，可以编辑文档和模板', JSON_ARRAY('documents.*', 'templates.*', 'config.read'), TRUE, TRUE, 3),
        ('role-viewer', 'viewer', '查看者', '只读用户，只能查看内容', JSON_ARRAY('documents.read', 'templates.read', 'config.read'), TRUE, TRUE, 4),
        ('role-guest', 'guest', '访客', '访客用户，最小权限', JSON_ARRAY('documents.read'), TRUE, TRUE, 5)
      `);

      // 插入默认管理员用户 (密码: admin123 - bcrypt哈希)
      await this.query(`
        INSERT IGNORE INTO users (id, username, email, password_hash, full_name, status, role_id, created_by) VALUES
        ('user-admin', 'admin', '<EMAIL>', '$2b$10$rOh4P1.q/nF7aGdl.N7gq.L5k3vZfU8.5/gqA.5WP0lBwHx2f8azO', '系统管理员', 'active', 'role-super-admin', 'system'),
        ('user-demo', 'demo', '<EMAIL>', '$2b$10$rOh4P1.q/nF7aGdl.N7gq.L5k3vZfU8.5/gqA.5WP0lBwHx2f8azO', '演示用户', 'active', 'role-editor', 'system')
      `);

      console.log('[DatabaseService] 用户管理系统表创建完成');

      // 在初始化完成后，设置初始化标记
      await this.query(
        `INSERT INTO system_settings (setting_key, setting_value, description) 
         VALUES ('database_initialized', 'true', '标记数据库是否已完成初始化') 
         ON DUPLICATE KEY UPDATE setting_value = 'true', updated_at = CURRENT_TIMESTAMP`
      );

      console.log('[DatabaseService] 数据库初始化完成');
      this.isInitialized = true;
      return true;
      
    } catch (error) {
      console.error('[DatabaseService] 数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查并添加缺失的列
   */
  private async checkAndAddMissingColumns() {
    const columnsToAdd = [
      { name: 'file_size', definition: "BIGINT COMMENT 'File size in bytes'", after: 'original_name' },
      { name: 'mime_type', definition: "VARCHAR(100) COMMENT 'MIME type of the file'", after: 'file_size' },
      { name: 'extension', definition: "VARCHAR(20) COMMENT 'File extension'", after: 'mime_type' },
      { name: 'version', definition: "INT DEFAULT 1 COMMENT 'Current document version'", after: 'extension' },
      { name: 'file_hash', definition: "VARCHAR(64) COMMENT 'SHA-256 hash of file content'", after: 'version' },
      { name: 'created_by', definition: "VARCHAR(100) COMMENT 'User who created the document'", after: 'file_hash' },
      { name: 'last_modified_by', definition: "VARCHAR(100) COMMENT 'User who last modified the document'", after: 'created_by' },
      { name: 'template_id', definition: "VARCHAR(36) COMMENT 'ID of template if document is based on template'", after: 'last_modified_by' },
      { name: 'is_deleted', definition: "BOOLEAN DEFAULT FALSE COMMENT 'Soft delete flag'", after: 'updated_at' }
    ];

    for (const column of columnsToAdd) {
      await this.checkAndAddColumn('filenet_documents', column.name, column.definition, column.after);
    }
  }

  /**
   * 检查并添加单个列
   */
  private async checkAndAddColumn(tableName: string, columnName: string, columnDefinition: string, afterColumn = '') {
    try {
      const columnExists = await this.query(`
        SELECT COUNT(*) as count 
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = ? 
        AND column_name = ?
      `, [tableName, columnName]);

      if ((columnExists[0] as CountResult).count === 0) {
        const afterClause = afterColumn ? ` AFTER ${afterColumn}` : '';
        await this.query(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDefinition}${afterClause}`);
        console.log(`[DatabaseService] 成功添加列 ${columnName} 到 ${tableName}`);
      } else {
        console.log(`[DatabaseService] 列 ${columnName} 已存在于 ${tableName}`);
      }
    } catch (error) {
      console.log(`[DatabaseService] 添加列 ${columnName} 出错:`, error.message);
    }
  }

  /**
   * 测试数据库连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.pool.getConnection();
      connection.release();
      console.log('[DatabaseService] 数据库连接测试成功');
      return true;
    } catch (error) {
      console.error('[DatabaseService] 数据库连接测试失败:', error);
      return false;
    }
  }

  /**
   * 执行SQL查询
   * @param {string} sql SQL语句
   * @param {Array|Object} params 查询参数
   * @returns {Promise<Array>} 查询结果
   */
  async query<T extends DatabaseRow = DatabaseRow>(sql: string, params: unknown[] = []): Promise<T[]> {
    try {
      const [rows] = await this.pool.query(sql, params);
      return rows as T[];
    } catch (error) {
      console.error('[DatabaseService] SQL查询失败:', error);
      throw error;
    }
  }

  /**
   * 执行单个SQL查询并返回第一行结果
   * @param {string} sql SQL语句
   * @param {Array|Object} params 查询参数
   * @returns {Promise<Object>} 查询结果的第一行
   */
  async queryOne<T extends DatabaseRow = DatabaseRow>(sql: string, params: unknown[] = []): Promise<T | null> {
    const rows = await this.query<T>(sql, params);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 执行事务
   * @param {Function} callback 事务回调函数，接收connection参数
   * @returns {Promise<T>} 事务执行结果
   */
  async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
    const connection = await this.pool.getConnection();

    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取连接池引用（用于高级操作）
   */
  getPool(): mysql.Pool {
    return this.pool;
  }
} 