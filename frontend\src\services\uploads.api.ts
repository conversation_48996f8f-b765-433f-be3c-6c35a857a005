import { ApiService } from './api'
import type { PaginationParams, PaginationResponse } from '@/types/api.types'

export interface UploadedFile {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  url: string
  category?: string
  description?: string
  isPublic: boolean
  uploadedBy: string
  uploadedAt: string
}

export interface UploadFileResponse {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  url: string
  uploadedAt: string
}

export interface FileListQueryParams extends PaginationParams {
  search?: string
  mimeType?: string
  uploadedBy?: string
  startDate?: string
  endDate?: string
}

export interface FileUploadOptions {
  category?: string
  description?: string
  isPublic?: boolean
}

/**
 * 文件上传API服务
 */
export class UploadsApiService {
  /**
   * 上传单个文件
   */
  static async uploadFile(file: File, options?: FileUploadOptions): Promise<UploadFileResponse> {
    const formData = new FormData()
    formData.append('file', file)

    if (options?.category) {
      formData.append('category', options.category)
    }

    if (options?.description) {
      formData.append('description', options.description)
    }

    if (options?.isPublic !== undefined) {
      formData.append('isPublic', String(options.isPublic))
    }

    return ApiService.post<UploadFileResponse>('/uploads', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 批量上传文件
   */
  static async uploadMultipleFiles(
    files: File[],
    options?: FileUploadOptions
  ): Promise<UploadFileResponse[]> {
    const formData = new FormData()

    files.forEach(file => {
      formData.append(`files`, file)
    })

    if (options?.category) {
      formData.append('category', options.category)
    }

    if (options?.description) {
      formData.append('description', options.description)
    }

    if (options?.isPublic !== undefined) {
      formData.append('isPublic', String(options.isPublic))
    }

    return ApiService.post<UploadFileResponse[]>('/uploads/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 获取文件列表
   */
  static async getFiles(params?: FileListQueryParams): Promise<PaginationResponse<UploadedFile>> {
    return ApiService.get<PaginationResponse<UploadedFile>>('/uploads', { params })
  }

  /**
   * 获取文件信息
   */
  static async getFileInfo(id: string): Promise<UploadedFile> {
    return ApiService.get<UploadedFile>(`/uploads/${id}/info`)
  }

  /**
   * 下载文件
   */
  static async downloadFile(id: string, filename?: string): Promise<void> {
    return ApiService.download(`/uploads/${id}/download`, filename)
  }

  /**
   * 删除文件
   */
  static async deleteFile(id: string): Promise<void> {
    return ApiService.delete<void>(`/uploads/${id}`)
  }

  /**
   * 获取文件访问URL
   */
  static async getFileUrl(id: string): Promise<{ url: string }> {
    return ApiService.get<{ url: string }>(`/uploads/${id}/url`)
  }

  /**
   * 检查文件是否存在
   */
  static async checkFileExists(filename: string): Promise<{ exists: boolean }> {
    return ApiService.get<{ exists: boolean }>('/uploads/check-exists', {
      params: { filename },
    })
  }
}

export default UploadsApiService
