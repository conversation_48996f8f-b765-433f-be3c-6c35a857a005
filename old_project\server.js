/**
 * OnlyOffice文档服务器集成应用入口文件
 */
const app = require('./app');
const config = require('./config');
const db = require('./services/database');
const fileStorage = require('./services/fileStorage');

// 数据库初始化和迁移
async function initializeApp() {
    try {
        // 测试数据库连接
        const dbConnected = await db.testConnection();
        if (!dbConnected) {
            console.error('无法连接到数据库，请检查配置');
            process.exit(1);
        }

        // 初始化数据库表结构
        await db.initDatabase();

        console.log('旧文件映射迁移步骤已移除或已完成。');

        // 启动服务器
        startServer();
    } catch (error) {
        console.error('应用初始化失败:', error);
        process.exit(1);
    }
}

// 启动服务器
function startServer() {
    app.listen(config.server.port, () => {
        console.log(`服务器运行在 http://localhost:${config.server.port}`);
        console.log(`对外访问地址: http://${config.server.host}:${config.server.port}`);
        console.log(`文档服务器地址: ${config.documentServer.url}`);
        console.log(`上传目录: ${config.storage.uploadDir}`);
        console.log(`数据库连接: ${config.database?.host || '使用配置文件中的连接信息'}`);

        console.log('=== 重要提示 ===');
        console.log('1. 确保OnlyOffice服务器能够通过网络访问此Node.js服务器');
        console.log('2. 如果使用防火墙，请确保允许OnlyOffice服务器访问此服务器的端口');
        console.log('3. 如测试文件可以在浏览器中下载但OnlyOffice无法打开，请检查网络连接');
    });
}

// 错误处理
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});

// 启动应用
initializeApp(); 