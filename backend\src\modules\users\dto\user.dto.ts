import { IsString, IsEmail, IsOptional, IsEnum, IsBoolean, IsInt, IsDateString, Min<PERSON><PERSON>th, Max<PERSON><PERSON><PERSON>, Matches, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// 创建用户DTO
export class CreateUserDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    minLength: 3,
    maxLength: 50,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string;

  @ApiPropertyOptional({
    description: '邮箱地址',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: '密码',
    example: 'Password123',
    minLength: 6,
    maxLength: 50,
  })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含大小写字母和数字',
  })
  password: string;

  @ApiPropertyOptional({
    description: '真实姓名',
    example: '张三',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  full_name?: string;

  @ApiPropertyOptional({
    description: '手机号码',
    example: '13800138000',
  })
  @IsString()
  @IsOptional()
  @Matches(/^[1-9]\d{10}$/, {
    message: '请输入有效的手机号码',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
  })
  @IsString()
  @IsOptional()
  avatar_url?: string;

  @ApiPropertyOptional({
    description: '用户状态',
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    example: 'active',
  })
  @IsEnum(['active', 'inactive', 'suspended', 'deleted'])
  @IsOptional()
  status?: 'active' | 'inactive' | 'suspended' | 'deleted';

  @ApiPropertyOptional({
    description: '角色ID',
    example: 'role-123',
  })
  @IsString()
  @IsOptional()
  role_id?: string;

  @ApiPropertyOptional({
    description: '部门',
    example: '技术部',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  department?: string;

  @ApiPropertyOptional({
    description: '职位',
    example: '软件工程师',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  position?: string;

  @ApiPropertyOptional({
    description: '邮箱是否已验证',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  email_verified?: boolean;

  @ApiPropertyOptional({
    description: '手机是否已验证',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  phone_verified?: boolean;

  @ApiPropertyOptional({
    description: '是否启用两步验证',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  two_factor_enabled?: boolean;
}

// 更新用户DTO
export class UpdateUserDto {
  @ApiPropertyOptional({
    description: '用户名',
    example: 'admin',
    minLength: 3,
    maxLength: 50,
  })
  @IsString()
  @IsOptional()
  @MinLength(3)
  @MaxLength(50)
  username?: string;

  @ApiPropertyOptional({
    description: '邮箱地址',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    description: '真实姓名',
    example: '张三',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  full_name?: string;

  @ApiPropertyOptional({
    description: '手机号码',
    example: '13800138000',
  })
  @IsString()
  @IsOptional()
  @Matches(/^[1-9]\d{10}$/, {
    message: '请输入有效的手机号码',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
  })
  @IsString()
  @IsOptional()
  avatar_url?: string;

  @ApiPropertyOptional({
    description: '用户状态',
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    example: 'active',
  })
  @IsEnum(['active', 'inactive', 'suspended', 'deleted'])
  @IsOptional()
  status?: 'active' | 'inactive' | 'suspended' | 'deleted';

  @ApiPropertyOptional({
    description: '角色ID',
    example: 'role-123',
  })
  @IsString()
  @IsOptional()
  role_id?: string;

  @ApiPropertyOptional({
    description: '部门',
    example: '技术部',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  department?: string;

  @ApiPropertyOptional({
    description: '职位',
    example: '软件工程师',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  position?: string;

  @ApiPropertyOptional({
    description: '邮箱是否已验证',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  email_verified?: boolean;

  @ApiPropertyOptional({
    description: '手机是否已验证',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  phone_verified?: boolean;

  @ApiPropertyOptional({
    description: '是否启用两步验证',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  two_factor_enabled?: boolean;
}

// 修改密码DTO
export class ChangePasswordDto {
  @ApiProperty({
    description: '当前密码',
    example: 'OldPassword123',
  })
  @IsString()
  current_password: string;

  @ApiProperty({
    description: '新密码',
    example: 'NewPassword123',
    minLength: 6,
    maxLength: 50,
  })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含大小写字母和数字',
  })
  new_password: string;

  @ApiProperty({
    description: '确认新密码',
    example: 'NewPassword123',
  })
  @IsString()
  confirm_password: string;
}

// 重置密码DTO
export class ResetPasswordDto {
  @ApiProperty({
    description: '用户ID',
    example: 'user-123-456',
  })
  @IsString()
  user_id: string;

  @ApiProperty({
    description: '新密码',
    example: 'NewPassword123',
    minLength: 6,
    maxLength: 50,
  })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含大小写字母和数字',
  })
  new_password: string;
}

// 用户查询DTO
export class UserQueryDto {
  @ApiPropertyOptional({
    description: '页码（从1开始）',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  @Transform(({ value }) => parseInt(value))
  pageSize?: number = 10;

  @ApiPropertyOptional({
    description: '搜索关键词（用户名、邮箱、姓名）',
    example: '张三',
  })
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({
    description: '用户状态筛选',
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    example: 'active',
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'suspended', 'deleted'])
  status?: 'active' | 'inactive' | 'suspended' | 'deleted';

  @ApiPropertyOptional({
    description: '角色ID筛选',
    example: 'role-123',
  })
  @IsOptional()
  @IsString()
  role_id?: string;

  @ApiPropertyOptional({
    description: '部门筛选',
    example: '技术部',
  })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiPropertyOptional({
    description: '创建时间起始（ISO字符串）',
    example: '2024-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  created_start?: string;

  @ApiPropertyOptional({
    description: '创建时间结束（ISO字符串）',
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  created_end?: string;

  @ApiPropertyOptional({
    description: '排序字段',
    enum: ['username', 'email', 'full_name', 'created_at', 'last_login_at'],
    example: 'created_at',
  })
  @IsOptional()
  @IsString()
  @IsEnum(['username', 'email', 'full_name', 'created_at', 'last_login_at'])
  sort_by?: string;

  @ApiPropertyOptional({
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
  })
  @IsOptional()
  @IsString()
  @IsEnum(['ASC', 'DESC'])
  sort_order?: 'ASC' | 'DESC';
}

// 用户响应DTO（不包含敏感信息）
export class UserResponseDto {
  @ApiProperty({
    description: '用户ID',
    example: 'user-123-456',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  username: string;

  @ApiPropertyOptional({
    description: '邮箱地址',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiPropertyOptional({
    description: '真实姓名',
    example: '张三',
  })
  full_name?: string;

  @ApiPropertyOptional({
    description: '手机号码',
    example: '13800138000',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
  })
  avatar_url?: string;

  @ApiProperty({
    description: '用户状态',
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    example: 'active',
  })
  status: 'active' | 'inactive' | 'suspended' | 'deleted';

  @ApiPropertyOptional({
    description: '角色ID',
    example: 'role-123',
  })
  role_id?: string;

  @ApiPropertyOptional({
    description: '部门',
    example: '技术部',
  })
  department?: string;

  @ApiPropertyOptional({
    description: '职位',
    example: '软件工程师',
  })
  position?: string;

  @ApiPropertyOptional({
    description: '最后登录时间',
    example: '2024-12-19T10:30:00Z',
  })
  last_login_at?: Date;

  @ApiPropertyOptional({
    description: '最后登录IP',
    example: '*************',
  })
  last_login_ip?: string;

  @ApiProperty({
    description: '邮箱是否已验证',
    example: true,
  })
  email_verified: boolean;

  @ApiProperty({
    description: '手机是否已验证',
    example: false,
  })
  phone_verified: boolean;

  @ApiProperty({
    description: '是否启用两步验证',
    example: false,
  })
  two_factor_enabled: boolean;

  @ApiProperty({
    description: '创建时间',
    example: '2024-12-19T10:30:00Z',
  })
  created_at: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-12-19T10:30:00Z',
  })
  updated_at: Date;
  
  // 关联数据
  @ApiPropertyOptional({
    description: '角色名称',
    example: 'admin',
  })
  role_name?: string;

  @ApiPropertyOptional({
    description: '角色显示名称',
    example: '系统管理员',
  })
  role_display_name?: string;

  @ApiPropertyOptional({
    description: '角色权限列表',
    type: [String],
    example: ['user:create', 'user:read', 'user:update', 'user:delete'],
  })
  role_permissions?: string[];
}

// 用户详情响应DTO
export class UserDetailResponseDto extends UserResponseDto {
  @ApiProperty({
    description: '失败登录尝试次数',
    example: 0,
  })
  failed_login_attempts: number;

  @ApiPropertyOptional({
    description: '账户锁定至时间',
    example: '2024-12-19T10:30:00Z',
  })
  locked_until?: Date;

  @ApiPropertyOptional({
    description: '密码修改时间',
    example: '2024-12-19T10:30:00Z',
  })
  password_changed_at?: Date;
}

// 用户列表响应DTO
export class UserListResponseDto {
  @ApiProperty({
    description: '用户数据列表',
    type: [UserResponseDto],
  })
  data: UserResponseDto[];

  @ApiProperty({
    description: '总数量',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: '总页数',
    example: 10,
  })
  totalPages: number;
}

// 用户角色DTO
export class CreateUserRoleDto {
  @ApiProperty({
    description: '角色名称',
    example: 'admin',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @ApiProperty({
    description: '角色显示名称',
    example: '系统管理员',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  display_name: string;

  @ApiPropertyOptional({
    description: '角色描述',
    example: '拥有系统管理权限的用户角色',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '权限列表',
    type: [String],
    example: ['user:create', 'user:read', 'user:update', 'user:delete'],
  })
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({
    description: '角色是否启用',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: '排序序号',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  sort_order?: number;
}

export class UpdateUserRoleDto {
  @ApiPropertyOptional({
    description: '角色名称',
    example: 'admin',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({
    description: '角色显示名称',
    example: '系统管理员',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @MaxLength(100)
  display_name?: string;

  @ApiPropertyOptional({
    description: '角色描述',
    example: '拥有系统管理权限的用户角色',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '权限列表',
    type: [String],
    example: ['user:create', 'user:read', 'user:update', 'user:delete'],
  })
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({
    description: '角色是否启用',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: '排序序号',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  sort_order?: number;
}

// 用户统计响应DTO
export class UserStatsResponseDto {
  @ApiProperty({
    description: '用户总数',
    example: 1000,
  })
  total_users: number;

  @ApiProperty({
    description: '活跃用户数',
    example: 850,
  })
  active_users: number;

  @ApiProperty({
    description: '非活跃用户数',
    example: 100,
  })
  inactive_users: number;

  @ApiProperty({
    description: '被暂停用户数',
    example: 50,
  })
  suspended_users: number;

  @ApiProperty({
    description: '在线用户数',
    example: 25,
  })
  online_users: number;

  @ApiProperty({
    description: '今日新增用户数',
    example: 5,
  })
  new_users_today: number;

  @ApiProperty({
    description: '本周新增用户数',
    example: 20,
  })
  new_users_this_week: number;

  @ApiProperty({
    description: '本月新增用户数',
    example: 85,
  })
  new_users_this_month: number;
}

/**
 * 用户权限查询DTO
 * @description 用于查询用户权限列表的参数
 */
export class UserPermissionQueryDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
  })
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 1000,
  })
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(1000)
  @IsOptional()
  pageSize?: number = 10;

  @ApiPropertyOptional({
    description: '搜索关键词（用户名、邮箱、姓名）',
    example: '张三',
  })
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({
    description: '角色ID筛选',
    example: 'role-123',
  })
  @IsOptional()
  @IsString()
  roleId?: string;

  @ApiPropertyOptional({
    description: '权限代码筛选',
    example: 'documents.read',
  })
  @IsOptional()
  @IsString()
  permissionCode?: string;

  @ApiPropertyOptional({
    description: '部门筛选',
    example: '技术部',
  })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiPropertyOptional({
    description: '用户状态筛选',
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    example: 'active',
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'suspended', 'deleted'])
  status?: 'active' | 'inactive' | 'suspended' | 'deleted';

  @ApiPropertyOptional({
    description: '排序字段',
    enum: ['username', 'full_name', 'department', 'created_at'],
    example: 'username',
  })
  @IsOptional()
  @IsString()
  @IsEnum(['username', 'full_name', 'department', 'created_at'])
  sortBy?: string;

  @ApiPropertyOptional({
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'ASC',
  })
  @IsOptional()
  @IsString()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * 通用API响应类型定义
 */
export interface ApiSuccessResponse<T = unknown> {
  success: true;
  message: string;
  data?: T;
}

export interface ApiErrorResponse {
  success: false;
  message: string;
  error?: string;
}

export type ApiResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * 用户列表API响应类型
 */
export type UserListApiResponse = ApiResponse<UserListResponseDto>;

/**
 * 用户详情API响应类型
 */
export type UserDetailApiResponse = ApiResponse<UserDetailResponseDto>;

/**
 * 用户基本信息API响应类型
 */
export type UserApiResponse = ApiResponse<UserResponseDto>;

/**
 * 用户统计信息API响应类型
 */
export type UserStatsApiResponse = ApiResponse<UserStatsResponseDto>;

/**
 * 简单成功响应类型（如删除操作）
 */
export type SimpleApiResponse = ApiResponse<never>;

/**
 * 用户权限响应类型
 */
export interface UserPermissionData {
  user_id: string;
  username: string;
  full_name?: string;
  department?: string;
  role_id?: string;
  role_name?: string;
  role_display_name?: string;
  permissions: Array<{
    permission_id: string;
    permission_code: string;
    permission_name: string;
    module_name: string;
    resource_type: string;
    action: string;
    description?: string;
  }>;
}

export interface UserPermissionListData {
  data: UserPermissionData[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export type UserPermissionApiResponse = ApiResponse<UserPermissionListData>;

/**
 * 当前用户权限响应类型
 */
export interface CurrentUserPermissionData {
  permissions: Array<{
    permission_id: string;
    permission_code: string;
    permission_name: string;
    module_name: string;
    resource_type: string;
    action: string;
    conditions?: Record<string, unknown>;
    description?: string;
  }>;
  role_permissions: Array<{
    permission_id: string;
    permission_code: string;
    permission_name: string;
    module_name: string;
    resource_type: string;
    action: string;
    conditions?: Record<string, unknown>;
    description?: string;
  }>;
  effective_permissions: string[];
  modules: Array<{
    module_name: string;
    permissions: string[];
  }>;
}

export type CurrentUserPermissionApiResponse = ApiResponse<CurrentUserPermissionData>; 