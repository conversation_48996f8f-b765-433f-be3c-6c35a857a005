/**
 * 路由聚合器
 */
const express = require('express');
const router = express.Router();
const documentRoutes = require('./documents');
const editorRoutes = require('./editor');
const templateRoutes = require('./templates');
const config = require('../config');
const path = require('path');

// 首页路由
router.get('/', (req, res) => {
    res.sendFile('index.html', { root: './public' });
});

// 配置模板管理页面路由
router.get('/config-templates', (req, res) => {
    res.render('config-templates', {
        title: '配置模板管理 - OnlyOffice文档管理系统'
    });
});

// 修改此路由以使用内部数据库ID (UUID) 作为主要访问方式
// 旧的路径是 /editor/filenet/:docId (docId 被视为 fnDocId)
// 新的路径可以是 /editor/:internalDbId
router.get('/editor/:internalDbId', async (req, res, next) => {
    try {
        const internalDbId = req.params.internalDbId;
        const templateName = req.query.template; // 从查询参数获取模板名称
        const overrides = {}; // 可以从查询参数中提取配置覆盖

        // 提取URL参数中的配置覆盖
        if (req.query.hideChat === 'true') overrides['customization.chat'] = false;
        if (req.query.hideComments === 'true') overrides['customization.comments'] = false;
        if (req.query.readonly === 'true') overrides['permissions.edit'] = false;

        // 校验传入的ID是否为有效的UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(internalDbId)) {
            console.warn('无效的内部DB ID格式 (routes/index.js editor route):', internalDbId);
            return res.render('error', {
                message: '无效的文档ID格式。请确保URL中的ID是正确的内部数据库ID。',
                error: { status: 400, stack: 'Invalid internal DB ID format: ' + internalDbId }
            });
        }

        // 使用配置模板服务生成文档配置
        const configTemplateService = require('../services/configTemplateService');
        let docConfig;
        if (templateName) {
            // 使用指定的配置模板
            docConfig = await configTemplateService.buildEditorConfig(templateName, internalDbId, overrides);
        } else {
            // 使用默认模板
            docConfig = await configTemplateService.buildEditorConfig(null, internalDbId, overrides);
        }

        // 生成JWT令牌
        const jwtService = require('../services/jwt');
        const token = jwtService.generateJWT(docConfig);

        res.render('editor', {
            docTitle: docConfig.document.title,
            docServerUrl: config.documentServer.url,
            apiUrl: `${config.documentServer.url}/web-apps/apps/api/documents/api.js`,
            token: token,
            config: docConfig,
            internalDbId: internalDbId // 将这个不变的ID也传递给视图，供后续 AJAX 调用
        });
    } catch (error) {
        console.error('编辑文档失败 (from routes/index.js with internalDbId):', error);
        if (error.message && error.message.startsWith('找不到对应的文件')) { // getDocumentConfig 可能会抛出这个
            return res.render('error', {
                message: `文档 (ID: ${req.params.internalDbId}) 未找到或无法访问。`, // 使用更具体的错误信息
                error: { status: 404, stack: error.stack }
            });
        }
        next(error);
    }
});

// 健康检查路由
router.get('/health', async (req, res) => {
    const db = require('../services/database');
    let dbStatus = 'unknown';
    let totalFiles = 0;
    try {
        await db.testConnection();
        dbStatus = 'ok';
        const stats = await db.queryOne('SELECT COUNT(*) as count FROM files WHERE is_deleted = FALSE');
        totalFiles = stats ? stats.count : 0;
    } catch (e) {
        dbStatus = 'error';
    }

    res.json({
        status: 'ok',
        uptime: process.uptime(),
        timestamp: Date.now(),
        database_status: dbStatus,
        active_files_in_db: totalFiles
    });
});

// 添加连接测试路由
router.get('/test-connection', (req, res) => {
    try {
        const http = require('http');
        const os = require('os');

        console.log('开始连接测试...');
        const testResults = {
            server: {
                address: config.server.host,
                port: config.server.port,
                status: 'unknown'
            },
            documentServer: {
                address: config.documentServer.url,
                status: 'unknown'
            },
            network: {
                interfaces: []
            },
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch
            }
        };

        // 获取网络接口信息
        const networkInterfaces = os.networkInterfaces();
        for (const [name, interfaces] of Object.entries(networkInterfaces)) {
            for (const iface of interfaces) {
                if (iface.family === 'IPv4' || iface.family === 4) {
                    testResults.network.interfaces.push({
                        name,
                        address: iface.address,
                        netmask: iface.netmask,
                        internal: iface.internal
                    });
                }
            }
        }

        // 测试OnlyOffice服务器连接
        const documentServerUrl = new URL(config.documentServer.url);
        const docServerReq = http.get(config.documentServer.url, (docServerRes) => {
            testResults.documentServer.status = `OK (${docServerRes.statusCode})`;

            console.log(`OnlyOffice服务器连接测试成功: ${config.documentServer.url}`);
            res.json({
                success: true,
                message: '连接测试完成',
                results: testResults
            });
        });

        docServerReq.on('error', (error) => {
            testResults.documentServer.status = `Error: ${error.message}`;
            console.error(`OnlyOffice服务器连接失败: ${error.message}`);

            res.json({
                success: false,
                message: '连接测试完成，但存在问题',
                results: testResults
            });
        });

        docServerReq.setTimeout(5000, () => {
            docServerReq.abort();
            testResults.documentServer.status = 'Error: Connection timeout';
            console.error(`OnlyOffice服务器连接超时`);

            res.json({
                success: false,
                message: '连接测试完成，但存在问题',
                results: testResults
            });
        });
    } catch (error) {
        console.error('连接测试失败:', error);
        res.status(500).json({
            success: false,
            message: '连接测试过程中发生错误',
            error: error.message
        });
    }
});

// 添加文件查看测试路由
router.get('/view-file/:fileId', async (req, res, next) => {
    try {
        const fileId = req.params.fileId;
        const fileStorage = require('../services/fileStorage');
        const config = require('../config');
        const path = require('path');
        const fs = require('fs-extra');

        console.log('测试查看文件:', fileId);

        const fileInfo = await fileStorage.getFileById(fileId);

        if (!fileInfo) {
            return res.send(`
        <html>
        <head><title>文件不存在</title></head>
        <body>
          <h1>错误：找不到文件ID ${fileId}</h1>
          <p><a href="/">返回首页</a></p>
        </body>
        </html>
      `);
        }

        const displayFileName = fileInfo.original_name;
        const storageFileName = fileInfo.storage_name;
        const filePath = path.join(config.storage.uploadDir, storageFileName);
        const fileUrl = `http://${config.server.host}:${config.server.port}/api/documents/${fileId}`;

        if (!fs.existsSync(filePath)) {
            return res.send(`
        <html>
        <head><title>文件不存在</title></head>
        <body>
          <h1>错误：物理文件 ${storageFileName} (对应ID ${fileId}) 不存在于服务器</h1>
          <p><a href="/">返回首页</a></p>
        </body>
        </html>
      `);
        }

        const stats = fs.statSync(filePath);
        const fileSizeInMB = (stats.size / 1024 / 1024).toFixed(2);

        res.send(`
      <html>
      <head>
        <title>文件下载测试 - ${displayFileName}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
          .container { max-width: 800px; margin: 0 auto; }
          h1 { color: #2c3e50; }
          .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .btn { display: inline-block; padding: 10px 15px; background: #3498db; color: white; 
                text-decoration: none; border-radius: 4px; margin-right: 10px; }
          .btn:hover { background: #2980b9; }
          .warning { color: #e74c3c; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>文件下载测试</h1>
          <div class="info">
            <p><strong>文件名:</strong> ${displayFileName}</p>
            <p><strong>文件大小:</strong> ${fileSizeInMB} MB</p>
            <p><strong>文件ID:</strong> ${fileId}</p>
            <p><strong>实际存储名:</strong> ${storageFileName}</p>
            <p><strong>文件URL (API):</strong> <a href="${fileUrl}" target="_blank">${fileUrl}</a></p>
          </div>
          
          <div>
            <a href="${fileUrl}" class="btn" download="${displayFileName}">直接下载文件 (通过API)</a>
            <a href="/editor/${fileId}" class="btn">使用OnlyOffice打开</a>
            <a href="/" class="btn" style="background: #7f8c8d;">返回首页</a>
          </div>
          
          <div class="info" style="margin-top: 30px;">
            <h3>测试说明</h3>
            <p>请点击"直接下载文件"测试链接是否可用。如果可以成功下载，但OnlyOffice无法打开，说明可能是OnlyOffice服务器无法访问此URL。</p>
            <p class="warning">注意：如果您在浏览器中可以下载，但OnlyOffice服务器不能，可能是因为OnlyOffice服务器与Node.js服务器之间存在网络隔离。</p>
          </div>
        </div>
      </body>
      </html>
    `);
    } catch (error) {
        next(error);
    }
});

// 注册API路由
router.use('/api/documents', documentRoutes);
router.use('/api/editor', editorRoutes);
router.use('/api/templates', templateRoutes);

// 删除当前的回调处理代码，替换为直接实现
router.post('/api/callback', async (req, res) => {
    console.log('接收到OnlyOffice回调请求:', JSON.stringify(req.body));

    try {
        // 验证JWT令牌
        const token = req.headers.authorization || '';
        let isValidToken = false;

        if (token) {
            const jwtService = require('../services/jwt');
            const tokenParts = token.split(' ');
            if (tokenParts.length === 2 && tokenParts[0] === 'Bearer') {
                const decoded = jwtService.verifyJWT(tokenParts[1]);
                isValidToken = !!decoded;
            }
        }

        if (!isValidToken && process.env.NODE_ENV === 'production') {
            console.warn('回调验证失败: 无效的JWT令牌');
            return res.status(401).json({ error: 1, message: '无效的令牌' });
        }

        // 尽早返回响应，避免OnlyOffice超时
        res.json({ error: 0 });

        // 在后台处理回调，不影响响应速度
        setTimeout(async () => {
            try {
                const documentService = require('../services/document');
                const result = await documentService.handleCallback(req.body);
                console.log('文档保存处理结果:', result ? '成功' : '失败');
            } catch (error) {
                console.error('后台处理回调失败:', error);
            }
        }, 10);
    } catch (error) {
        console.error('处理OnlyOffice回调失败:', error);
        res.status(500).json({
            error: 1,
            message: '回调处理失败',
            errorDetails: error.message
        });
    }
});

// 兼容旧版API
router.get('/documents', (req, res) => {
    res.redirect(301, '/api/documents');
});

router.get('/edit/:id', (req, res) => {
    res.redirect(301, `/editor/${req.params.id}`);
});

// 添加旧路径兼容
router.post('/upload', (req, res) => {
    res.redirect(307, '/api/documents/upload'); // 307保留原有的POST请求方法
});

router.delete('/documents/:id', (req, res) => {
    res.redirect(307, `/api/documents/${req.params.id}`);
});

// 添加导航页面路由
router.get('/navigation', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/navigation.html'));
});

/**
 * 添加配置模板管理页面的路由
 */
router.get('/template-management', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/template-admin.html'));
});

module.exports = router; 