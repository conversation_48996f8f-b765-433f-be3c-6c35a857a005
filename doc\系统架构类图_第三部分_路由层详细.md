# OnlyOffice系统架构类图 - 第三部分：路由层详细设计

## 路由层详细类图

```mermaid
classDiagram
    %% 主路由聚合器
    class IndexRouter {
        +GET /: 首页
        +GET /config-templates: 配置模板管理页面
        +GET /editor/:internalDbId: 编辑文档(使用内部DB ID)
        +GET /health: 健康检查
        +GET /test-connection: 连接测试
        +GET /view-file/:fileId: 文件查看测试
        +POST /api/callback: OnlyOffice回调处理
        +GET /documents: 重定向兼容路由
        +GET /edit/:id: 重定向兼容路由
        +POST /upload: 重定向兼容路由
        +DELETE /documents/:id: 重定向兼容路由
        +GET /navigation: 导航页面
        +GET /template-management: 模板管理页面
        -registerSubRoutes(): void
        -setupMiddleware(): void
    }

    %% 编辑器路由
    class EditorRouter {
        +GET /:id: 编辑文档(文件ID)
        +POST /callback/:fileId?: 文档保存回调
        +GET /check-save-status/:fileId: 检查保存状态
        +POST /force-save/:fileId: 触发强制保存
        +GET /test-save/:fileId: 测试保存功能
        +GET /: 通过查询参数编辑FileNet文档
        +GET /filenet/:fnDocId: 通过路径编辑FileNet文档
        +GET /config/:id: 获取编辑器配置API
        -validateUUID(uuid): boolean
        -getDocumentType(extension): string
        -generateEditorConfig(fileId): Object
    }

    %% 文档管理路由
    class DocumentsRouter {
        +GET /: 获取文档列表
        +POST /upload: 上传文档
        +GET /:fileId: 下载/获取文档
        +DELETE /:id: 删除文档
        +PUT /:id: 更新文档信息
        +GET /filenet/:fnDocId: 获取FileNet文档
        +POST /create-from-template: 从模板创建文档
        -validateFileUpload(file): boolean
        -processUploadedFile(file): Object
        -handleFileNetDocument(fnDocId): Object
    }

    %% 配置模板路由
    class ConfigTemplatesRouter {
        +GET /: 获取所有配置模板
        +POST /: 创建配置模板
        +GET /:id: 获取单个模板详情
        +PUT /:id: 更新配置模板
        +DELETE /:id: 删除配置模板
        +GET /config-groups: 获取配置组信息
        +POST /:id/set-default: 设置为默认模板
        +GET /test/:templateName: 测试模板配置
        +POST /validate: 验证模板配置
        -validateTemplateData(data): boolean
        -parseConfigItems(items): Object
    }

    %% 模板路由 (Legacy)
    class TemplatesRouter {
        +GET /: 获取所有模板 (Swagger文档)
        +POST /: 创建新模板 (Swagger文档)
        +GET /:templateId: 获取模板详情 (Swagger文档)
        +PUT /:templateId: 更新模板 (Swagger文档)
        +DELETE /:templateId: 删除模板 (Swagger文档)
        +POST /:templateId/documents: 从模板创建文档
        -validateSwaggerSchema(data): boolean
        -handleAuthentication(): boolean
    }

    %% FileNet路由
    class FileNetRouter {
        +GET /documents: 获取FileNet文档列表
        +POST /upload: 上传文件到FileNet
        +GET /download/:fnDocId: 从FileNet下载文档
        +POST /copy: 复制FileNet文档
        +GET /metadata/:fnDocId: 获取文档元数据
        -setupMulterStorage(): Object
        -validateFileNetAccess(): boolean
        -processFileNetUpload(file): Object
    }

    %% 配置路由
    class ConfigRouter {
        +GET /config: 获取系统配置
        +POST /config: 保存系统配置
        +POST /config/reset: 重置配置
        +GET /config/descriptions: 获取配置说明
        -validateConfigData(config): boolean
        -mergeConfigChanges(current, updates): Object
    }

    %% API路由
    class APIRouter {
        +GET /filenet/documents: 获取FileNet文档列表
        -handlePagination(options): Object
        -processSortOptions(sortBy, sortOrder): Object
    }

    %% 调试路由
    class DebugRouter {
        +GET /info: 系统调试信息
        +GET /logs: 系统日志
        -checkDebugMode(): boolean
        -gatherSystemInfo(): Object
    }

    %% 中间件接口
    class ErrorMiddleware {
        <<interface>>
        +errorHandler(err, req, res, next): void
        +notFoundHandler(req, res, next): void
        +apiError(message, status, error): Error
    }

    class UploadMiddleware {
        <<interface>>
        +multerUpload: MulterInstance
        +validateFileType(file): boolean
        +checkFileSize(file): boolean
        +sanitizeFileName(filename): string
    }

    %% 服务层依赖
    class DocumentService {
        <<service>>
    }

    class ConfigTemplateService {
        <<service>>
    }

    class FileStorageService {
        <<service>>
    }

    class FileNetService {
        <<service>>
    }

    class ConfigService {
        <<service>>
    }

    class TemplateService {
        <<service>>
    }

    class JWTService {
        <<service>>
    }

    %% 路由层到服务层的依赖关系
    IndexRouter --> DocumentService
    IndexRouter --> ConfigTemplateService
    IndexRouter --> JWTService

    EditorRouter --> DocumentService
    EditorRouter --> ConfigTemplateService
    EditorRouter --> FileStorageService
    EditorRouter --> JWTService

    DocumentsRouter --> DocumentService
    DocumentsRouter --> FileStorageService
    DocumentsRouter --> FileNetService

    ConfigTemplatesRouter --> ConfigTemplateService

    TemplatesRouter --> TemplateService

    FileNetRouter --> FileNetService

    ConfigRouter --> ConfigService

    APIRouter --> FileNetService

    %% 中间件使用关系
    IndexRouter --> ErrorMiddleware
    EditorRouter --> ErrorMiddleware
    DocumentsRouter --> UploadMiddleware
    DocumentsRouter --> ErrorMiddleware
    ConfigTemplatesRouter --> ErrorMiddleware
    TemplatesRouter --> ErrorMiddleware
    FileNetRouter --> UploadMiddleware
    FileNetRouter --> ErrorMiddleware

    %% 路由聚合关系
    IndexRouter --> EditorRouter
    IndexRouter --> DocumentsRouter
    IndexRouter --> TemplatesRouter

    %% 样式定义 - 分别定义每个类的样式
    classDef mainRouter fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef routerClass fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef middlewareClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef serviceClass fill:#fff3e0,stroke:#f57c00,stroke-width:1px

    %% 应用样式到类 - 分别为每个类设置样式
    class IndexRouter mainRouter
    class EditorRouter routerClass
    class DocumentsRouter routerClass
    class ConfigTemplatesRouter routerClass
    class TemplatesRouter routerClass
    class FileNetRouter routerClass
    class ConfigRouter routerClass
    class APIRouter routerClass
    class DebugRouter routerClass
    class ErrorMiddleware middlewareClass
    class UploadMiddleware middlewareClass
    class DocumentService serviceClass
    class ConfigTemplateService serviceClass
    class FileStorageService serviceClass
    class FileNetService serviceClass
    class ConfigService serviceClass
    class TemplateService serviceClass
    class JWTService serviceClass
```

## 路由层接口详细说明

### 🏠 IndexRouter (主路由聚合器)
**职责**: 路由聚合和页面渲染
- **页面路由**:
  - `GET /`: 系统首页
  - `GET /config-templates`: 配置模板管理界面
  - `GET /navigation`: 系统导航页面
- **文档编辑**:
  - `GET /editor/:internalDbId`: 使用内部数据库ID编辑文档
- **系统监控**:
  - `GET /health`: 系统健康检查
  - `GET /test-connection`: 连接测试页面
- **OnlyOffice集成**:
  - `POST /api/callback`: 处理OnlyOffice回调
- **兼容性路由**: 重定向旧版API路径

### 📝 EditorRouter (编辑器路由)
**职责**: 文档编辑相关功能
- **编辑功能**:
  - `GET /:id`: 编辑指定文档
  - `GET /config/:id`: 获取编辑器配置(API)
- **FileNet集成**:
  - `GET /filenet/:fnDocId`: 编辑FileNet文档
  - `GET /`: 通过查询参数编辑FileNet文档
- **文档保存**:
  - `POST /callback/:fileId?`: 文档保存回调处理
  - `POST /force-save/:fileId`: 强制保存文档
  - `GET /check-save-status/:fileId`: 检查保存状态
- **测试功能**:
  - `GET /test-save/:fileId`: 测试保存功能

### 📂 DocumentsRouter (文档管理路由)
**职责**: 文档CRUD操作
- **文档管理**:
  - `GET /`: 获取文档列表
  - `POST /upload`: 文档上传
  - `GET /:fileId`: 下载/获取文档内容
  - `PUT /:id`: 更新文档信息
  - `DELETE /:id`: 删除文档
- **FileNet集成**:
  - `GET /filenet/:fnDocId`: 获取FileNet文档
- **模板功能**:
  - `POST /create-from-template`: 从模板创建文档

### ⚙️ ConfigTemplatesRouter (配置模板路由)
**职责**: 编辑器配置模板管理
- **模板CRUD**:
  - `GET /`: 获取所有配置模板
  - `POST /`: 创建新的配置模板
  - `GET /:id`: 获取模板详情
  - `PUT /:id`: 更新配置模板
  - `DELETE /:id`: 删除配置模板
- **配置管理**:
  - `GET /config-groups`: 获取配置组信息
  - `POST /:id/set-default`: 设置默认模板
- **测试验证**:
  - `GET /test/:templateName`: 测试模板配置
  - `POST /validate`: 验证模板配置

### 📋 TemplatesRouter (模板路由) - Legacy
**职责**: 文档模板管理 (旧版本，带Swagger文档)
- **完整的REST API**: 支持模板的增删改查
- **Swagger文档**: 完整的API文档和模式定义
- **认证支持**: 包含用户认证和权限控制
- **从模板创建文档**: `POST /:templateId/documents`

### 🌐 FileNetRouter (FileNet路由)
**职责**: FileNet系统集成
- **文档管理**:
  - `GET /documents`: 获取FileNet文档列表
  - `POST /upload`: 上传文件到FileNet
  - `GET /download/:fnDocId`: 从FileNet下载文档
- **文档操作**:
  - `POST /copy`: 复制FileNet文档
  - `GET /metadata/:fnDocId`: 获取文档元数据

### 🔧 ConfigRouter (配置路由)
**职责**: 系统配置管理
- **配置管理**:
  - `GET /config`: 获取当前系统配置
  - `POST /config`: 保存配置更改
  - `POST /config/reset`: 重置为默认配置
  - `GET /config/descriptions`: 获取配置项说明

### 🔌 APIRouter (API路由)
**职责**: 通用API接口
- **FileNet API**: `GET /filenet/documents` - 获取FileNet文档列表
- **分页支持**: 支持分页和排序参数

### �� DebugRouter (调试路由)
**职责**: 系统调试和监控
- **系统信息**: `GET /info` - 系统调试信息
- **日志查看**: `GET /logs` - 系统日志

## 路由层设计特点

### 🏗️ 架构模式

#### 分层路由架构
- **主路由聚合器**: IndexRouter作为入口点
- **功能路由模块**: 按功能划分的独立路由
- **中间件集成**: 统一的错误处理和文件上传

#### RESTful API设计
- **标准HTTP方法**: GET、POST、PUT、DELETE
- **资源导向**: 以资源为中心的URL设计
- **状态码规范**: 适当的HTTP状态码使用

### 🔧 功能特性

#### 编辑器集成
- **多种编辑方式**: 文件ID、FileNet文档ID
- **配置模板**: 灵活的编辑器配置管理
- **实时保存**: OnlyOffice回调处理

#### 文件管理
- **多存储支持**: 本地存储 + FileNet集成
- **文件上传**: Multer中间件处理
- **文件下载**: 支持预览和下载

#### 错误处理
- **统一错误处理**: ErrorMiddleware
- **API错误格式**: 标准化的错误响应
- **404处理**: 未找到资源的处理

### 🔄 数据流转

#### 文档编辑流程
```
用户请求 → IndexRouter → EditorRouter → DocumentService → OnlyOffice
```

#### 文档上传流程
```
文件上传 → DocumentsRouter → UploadMiddleware → FileStorageService → 数据库
```

#### 配置管理流程
```
配置请求 → ConfigTemplatesRouter → ConfigTemplateService → 数据库
```

### 📋 兼容性设计

#### 向后兼容
- **重定向路由**: 保持旧版API的兼容性
- **多ID支持**: 支持不同的文档ID格式
- **Legacy路由**: 保留旧版模板路由

#### API版本化
- **命名空间**: `/api/*` 路径前缀
- **功能分组**: 按功能模块组织路由
- **扩展性**: 便于添加新版本API

## 路由层优化建议

### 🚀 性能优化
- **路由缓存**: 缓存常用的配置数据
- **中间件优化**: 减少不必要的中间件调用
- **异步处理**: 大文件上传的异步处理

### 🔒 安全加固
- **输入验证**: 参数验证和清理
- **访问控制**: API访问权限控制
- **文件安全**: 文件类型和大小限制

### 📈 可维护性
- **代码分离**: 继续拆分复杂的路由
- **文档完善**: 补充API文档
- **测试覆盖**: 增加路由层测试 