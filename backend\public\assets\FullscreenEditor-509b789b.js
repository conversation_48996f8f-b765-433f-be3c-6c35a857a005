import{d as b,L as x,z as R,o as U,M as $,c as B,b as i,j as d,v as t,h as H,_ as V}from"./index-5218909a.js";import{u as z,a as A,b as I,E as O,c as j,N as P}from"./NotificationPanel-847d52aa.js";import"./editor-utils-5bc6f677.js";const T={class:"fullscreen-editor"},q=b({__name:"FullscreenEditor",setup(G){const f=x(),c=R(()=>f.params.id),{editorConfig:v,docTitle:g,isEditorReady:s,initializeEditor:E,destroyEditor:m,forceSave:p}=z(c),{saveStatus:l,updateSaveStatus:a,checkSaveStatus:_,startAutoCheck:y,stopAutoCheck:h}=A(c),{notification:S,showNotification:r,hideNotification:w}=I(),k=()=>{console.log("全屏编辑器已准备就绪"),r("编辑器已准备就绪","success",3e3),y(),_()},C=e=>{console.log("文档状态变更:",e),e.data===!0&&a("editing","有未保存的更改")},D=e=>{var n;console.log("文档保存事件:",e),(n=e.data)!=null&&n.url&&(a("saved","已保存"),r("文档已自动保存","success",2e3))},F=e=>{console.error("编辑器错误:",e);const o=e;let n="编辑器发生错误";o.data&&(o.data.errorDescription?n=o.data.errorDescription:typeof o.data=="string"&&(n=o.data)),r(n,"error")},M=async()=>{if(!s.value){r("编辑器未就绪","warning");return}try{a("saving","正在保存..."),r("正在保存文档...","info"),await p(),a("saved","保存成功"),r("文档保存成功","success")}catch(e){console.error("强制保存失败:",e),a("error","保存失败");const o=e instanceof Error?e.message:"未知错误";r(`保存失败: ${o}`,"error")}},N=async()=>{if(!s.value){r("编辑器未就绪","warning");return}try{r("正在加密文档...","info"),r("文档已加密，只能填写表单","success")}catch(e){console.error("加密失败:",e);const o=e instanceof Error?e.message:"未知错误";r(`加密失败: ${o}`,"error")}},L=async()=>{if(!s.value){r("编辑器未就绪","warning");return}try{r("正在解锁文档...","info"),r("文档已解锁，可以正常编辑","success")}catch(e){console.error("解锁失败:",e);const o=e instanceof Error?e.message:"未知错误";r(`解锁失败: ${o}`,"error")}},u=e=>{if(l.value.status==="editing"){const o="您有未保存的更改，确定要离开吗？";return e.returnValue=o,o}};return U(async()=>{console.log("初始化全屏编辑器页面:",c.value);try{await E()}catch(e){console.error("初始化编辑器失败:",e);const o=e instanceof Error?e.message:"未知错误";r(`初始化失败: ${o}`,"error")}window.addEventListener("beforeunload",u)}),$(()=>{console.log("销毁全屏编辑器页面"),h(),m(),window.removeEventListener("beforeunload",u)}),(e,o)=>(H(),B("div",T,[i(" 编辑器头部 "),d(O,{"doc-title":t(g),"save-status":t(l),"is-ready":t(s),onForceSave:M,onLockDocument:N,onUnlockDocument:L},null,8,["doc-title","save-status","is-ready"]),i(" 编辑器容器 "),d(j,{"is-ready":t(s),config:t(v),onEditorReady:k,onDocumentStateChange:C,onSave:D,onError:F},null,8,["is-ready","config"]),i(" 通知组件 "),d(P,{notification:t(S),onHide:t(w)},null,8,["notification","onHide"])]))}});const W=V(q,[["__scopeId","data-v-1e29e51e"],["__file","D:/Code/OnlyOffice/frontend/src/pages/editor/FullscreenEditor.vue"]]);export{W as default};
