# OnlyOffice NestJS 项目初始化脚本
# 
# 功能：
# 1. 安装所有必需的npm包
# 2. 配置TypeScript和ESLint
# 3. 创建必要的目录结构
# 4. 设置开发环境
#
# 使用方法：
# PowerShell -ExecutionPolicy Bypass -File init-nestjs-project.ps1
#
# 作者：OnlyOffice Team
# 版本：2.0.0
# 更新：2024-12-19

Write-Host "🚀 开始初始化 OnlyOffice NestJS 项目..." -ForegroundColor Green

# 检查Node.js和npm
Write-Host "📋 检查环境依赖..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm 版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Error "❌ 请先安装 Node.js 和 npm"
    exit 1
}

# 检查是否在正确的目录
if (-not (Test-Path "package.json")) {
    Write-Error "❌ 请在项目根目录执行此脚本"
    exit 1
}

Write-Host "📦 安装 NestJS 核心依赖..." -ForegroundColor Yellow

# NestJS 核心包
$nestjsPackages = @(
    "@nestjs/core@^10.0.0",
    "@nestjs/common@^10.0.0", 
    "@nestjs/platform-express@^10.0.0",
    "@nestjs/config@^3.0.0"
)

foreach ($package in $nestjsPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    npm install $package
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ 安装 $package 失败"
        exit 1
    }
}

Write-Host "🔐 安装认证和安全相关依赖..." -ForegroundColor Yellow

# 认证和安全包
$authPackages = @(
    "@nestjs/jwt@^10.0.0",
    "@nestjs/passport@^10.0.0",
    "passport@^0.6.0",
    "passport-jwt@^4.0.0",
    "passport-local@^1.0.0",
    "bcryptjs@^2.4.3"
)

foreach ($package in $authPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    npm install $package
}

Write-Host "🗄️ 安装数据库相关依赖..." -ForegroundColor Yellow

# 数据库包
$dbPackages = @(
    "@nestjs/typeorm@^10.0.0",
    "typeorm@^0.3.0",
    "mysql2@^3.0.0"
)

foreach ($package in $dbPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    npm install $package
}

Write-Host "📚 安装API文档相关依赖..." -ForegroundColor Yellow

# Swagger API文档包
$swaggerPackages = @(
    "@nestjs/swagger@^7.0.0",
    "swagger-ui-express@^5.0.0"
)

foreach ($package in $swaggerPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    npm install $package
}

Write-Host "🔧 安装工具库依赖..." -ForegroundColor Yellow

# 工具库
$utilPackages = @(
    "class-validator@^0.14.0",
    "class-transformer@^0.5.0",
    "joi@^17.0.0",
    "@nestjs/throttler@^5.0.0",
    "helmet@^7.0.0",
    "compression@^1.7.4"
)

foreach ($package in $utilPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    npm install $package
}

Write-Host "🏥 安装健康检查依赖..." -ForegroundColor Yellow

# 健康检查包
$healthPackages = @(
    "@nestjs/terminus@^10.0.0",
    "@nestjs/axios@^3.0.0"
)

foreach ($package in $healthPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    npm install $package
}

Write-Host "🛠️ 安装开发环境依赖..." -ForegroundColor Yellow

# 开发依赖
$devPackages = @(
    "@nestjs/cli@^10.0.0",
    "@nestjs/schematics@^10.0.0",
    "@nestjs/testing@^10.0.0",
    "@types/express@^4.17.0",
    "@types/node@^20.0.0",
    "@types/passport-jwt@^3.0.0",
    "@types/passport-local@^1.0.0",
    "@types/bcryptjs@^2.4.0",
    "@types/compression@^1.7.0",
    "@typescript-eslint/eslint-plugin@^6.0.0",
    "@typescript-eslint/parser@^6.0.0",
    "eslint@^8.0.0",
    "eslint-config-prettier@^9.0.0",
    "eslint-plugin-prettier@^5.0.0",
    "prettier@^3.0.0",
    "jest@^29.0.0",
    "@types/jest@^29.0.0",
    "ts-jest@^29.0.0",
    "ts-loader@^9.0.0",
    "ts-node@^10.0.0",
    "tsconfig-paths@^4.0.0",
    "typescript@^5.0.0",
    "rimraf@^5.0.0"
)

foreach ($package in $devPackages) {
    Write-Host "安装开发依赖 $package..." -ForegroundColor Cyan
    npm install --save-dev $package
}

Write-Host "📁 创建必要的目录结构..." -ForegroundColor Yellow

# 创建目录
$directories = @(
    "src/modules/documents",
    "src/modules/documents/controllers",
    "src/modules/documents/services", 
    "src/modules/documents/dto",
    "src/modules/documents/entities",
    "src/modules/filenet",
    "src/modules/filenet/controllers",
    "src/modules/filenet/services",
    "src/entities",
    "src/interfaces",
    "src/utils",
    "logs",
    "uploads"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ 创建目录: $dir" -ForegroundColor Green
    }
}

Write-Host "📝 创建环境配置文件..." -ForegroundColor Yellow

# 创建 .env.example 文件
$envExample = @"
# 应用基础配置
NODE_ENV=development
PORT=3000
API_PREFIX=api

# CORS配置
CORS_ORIGINS=*

# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=onlyoffice
DB_SYNC=false
DB_LOGGING=true

# JWT配置
JWT_SECRET=your-very-long-and-secure-jwt-secret-key-at-least-32-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# OnlyOffice配置
ONLYOFFICE_DOCS_URL=http://localhost:80
ONLYOFFICE_CALLBACK_URL=http://localhost:3000/api/documents/callback
ONLYOFFICE_JWT_SECRET=your-onlyoffice-jwt-secret
ONLYOFFICE_JWT_HEADER=Authorization

# 文件存储配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=104857600

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 缓存配置
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# 限流配置  
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# 安全配置
BCRYPT_ROUNDS=12

# Swagger文档配置
SWAGGER_ENABLED=true
SWAGGER_PATH=api-docs
"@

Set-Content -Path ".env.example" -Value $envExample -Encoding UTF8
Write-Host "✅ 创建 .env.example 文件" -ForegroundColor Green

# 如果不存在.env文件，复制示例文件
if (-not (Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host "✅ 创建 .env 文件 (请根据实际情况修改配置)" -ForegroundColor Green
}

Write-Host "🔍 安装并配置ESLint..." -ForegroundColor Yellow

# 创建ESLint配置
$eslintConfig = @"
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    '@nestjs',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    'max-lines-per-function': ['error', 50],
  },
};
"@

Set-Content -Path ".eslintrc.js" -Value $eslintConfig -Encoding UTF8
Write-Host "✅ 创建 .eslintrc.js 配置" -ForegroundColor Green

# 创建Prettier配置
$prettierConfig = @"
{
  "singleQuote": true,
  "trailingComma": "all",
  "tabWidth": 2,
  "semi": true,
  "printWidth": 80,
  "endOfLine": "lf"
}
"@

Set-Content -Path ".prettierrc" -Value $prettierConfig -Encoding UTF8
Write-Host "✅ 创建 .prettierrc 配置" -ForegroundColor Green

Write-Host "📋 更新package.json脚本..." -ForegroundColor Yellow

# 读取现有package.json
$packageJson = Get-Content "package.json" | ConvertFrom-Json

# 更新scripts部分
$packageJson.scripts = @{
    "build" = "nest build"
    "format" = "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""
    "start" = "nest start"
    "start:dev" = "nest start --watch"
    "start:debug" = "nest start --debug --watch"
    "start:prod" = "node dist/main"
    "lint" = "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"
    "test" = "jest"
    "test:watch" = "jest --watch"
    "test:cov" = "jest --coverage"
    "test:debug" = "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"
    "test:e2e" = "jest --config ./test/jest-e2e.json"
    "typeorm" = "typeorm-ts-node-commonjs"
}

# 保存更新后的package.json
$packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json" -Encoding UTF8

Write-Host "🧪 配置Jest测试框架..." -ForegroundColor Yellow

# 创建Jest配置
$jestConfig = @"
{
  "moduleFileExtensions": ["js", "json", "ts"],
  "rootDir": "src",
  "testRegex": ".*\\.spec\\.ts$",
  "transform": {
    "^.+\\.(t|j)s$": "ts-jest"
  },
  "collectCoverageFrom": [
    "**/*.(t|j)s"
  ],
  "coverageDirectory": "../coverage",
  "testEnvironment": "node",
  "moduleNameMapping": {
    "^@/(.*)$": "<rootDir>/$1"
  }
}
"@

Set-Content -Path "jest.config.json" -Value $jestConfig -Encoding UTF8
Write-Host "✅ 创建 Jest 配置" -ForegroundColor Green

Write-Host "📚 生成开发指南..." -ForegroundColor Yellow

$devGuide = @"
# OnlyOffice NestJS 开发指南

## 🚀 快速开始

1. 安装依赖：
   ```bash
   npm install
   ```

2. 配置环境变量：
   ```bash
   # 复制并编辑环境配置
   cp .env.example .env
   # 根据实际情况修改数据库和其他配置
   ```

3. 启动开发服务器：
   ```bash
   npm run start:dev
   ```

4. 访问应用：
   - 应用地址：http://localhost:3000
   - API文档：http://localhost:3000/api-docs
   - 健康检查：http://localhost:3000/api/health

## 🛠️ 开发命令

- `npm run start:dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run lint` - 代码检查
- `npm run format` - 代码格式化
- `npm run test` - 运行测试
- `npm run test:cov` - 运行测试并生成覆盖率报告

## 📁 项目结构

```
src/
├── common/           # 通用组件
├── modules/          # 业务模块
├── entities/         # 数据库实体
├── interfaces/       # TypeScript接口
└── utils/            # 工具函数
```

## 🔧 配置说明

重要配置项请在 .env 文件中设置：

- 数据库连接信息
- JWT密钥
- OnlyOffice服务器地址
- 文件上传路径

## 📝 开发规范

请遵循项目代码规范：
- 使用TypeScript进行开发
- 遵循ESLint规则
- 编写单元测试
- 添加适当的注释

## 🐛 常见问题

1. 数据库连接失败：检查 .env 中的数据库配置
2. JWT验证失败：确保JWT_SECRET配置正确
3. 文件上传失败：检查UPLOAD_PATH目录权限

更多信息请查看项目README.md文件。
"@

Set-Content -Path "DEVELOPMENT.md" -Value $devGuide -Encoding UTF8
Write-Host "✅ 创建开发指南 DEVELOPMENT.md" -ForegroundColor Green

Write-Host "🎉 NestJS 项目初始化完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步操作：" -ForegroundColor Yellow
Write-Host "1. 编辑 .env 文件，配置数据库和其他必要参数" -ForegroundColor White
Write-Host "2. 启动数据库服务" -ForegroundColor White  
Write-Host "3. 运行 npm run start:dev 启动开发服务器" -ForegroundColor White
Write-Host "4. 访问 http://localhost:3000/api-docs 查看API文档" -ForegroundColor White
Write-Host ""
Write-Host "💡 提示：请查看 DEVELOPMENT.md 了解详细的开发指南" -ForegroundColor Cyan
Write-Host "🔧 项目支持热重载，修改代码后会自动重启" -ForegroundColor Cyan 