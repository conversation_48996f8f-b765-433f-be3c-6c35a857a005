# OnlyOffice集成系统

> 基于OnlyOffice的企业级文档管理和协作平台  
> **技术栈**: Vue 3 + TypeScript + Ant Design Pro + NestJS + MySQL  
> **版本**: v2.0.0  
> **更新时间**: 2024年12月24日

## 🎯 项目概述

OnlyOffice集成系统是一个现代化的文档管理平台，提供文档在线编辑、协作、模板管理等功能。系统从原来的Node.js+Express架构全面升级到Vue 3 + NestJS的现代化技术栈。

## 🚀 最新功能优化

### 📄 文档管理页面优化 (2024-12-24)

**✨ 操作区域重构**
- ✅ **去除自定义窗口弹窗**: 简化操作流程，提升用户体验
- ✅ **直接下拉选择配置模板**: 在操作区域直接选择配置模板，实时切换
- ✅ **按钮文本优化**: "内嵌编辑" → "内嵌"，"新窗口编辑" → "新窗"
- ✅ **操作列宽度优化**: 从16%调整为12%，整体布局更紧凑
- ✅ **小尺寸按钮**: 使用small尺寸，减少空间占用

**🔧 技术改进**
- 配置模板API调用优化，使用正确的后端地址
- 响应式设计，按钮和下拉框统一样式
- 实时配置切换，选择模板后即时生效

## 📁 项目结构

```
OnlyOffice/
├── frontend/                 # Vue 3 前端应用
│   ├── src/
│   │   ├── pages/
│   │   │   ├── Documents/    # 📄 文档管理页面 (已优化)
│   │   │   ├── Templates/    # 📋 模板管理
│   │   │   └── Config/       # ⚙️ 配置管理
│   │   ├── components/
│   │   │   └── DocumentActions.vue  # 📌 文档操作组件 (已重构)
│   │   └── utils/
├── backend/                  # NestJS 后端应用
│   ├── src/modules/
│   │   ├── documents/        # 文档管理模块
│   │   ├── config/           # 配置管理模块
│   │   └── editor/           # 编辑器模块
└── doc/                      # 📚 项目文档
```

## 🛠️ 环境配置

### 前端 (Vue 3 + TypeScript)
- **地址**: http://*************:8080
- **框架**: Vue 3.x + Composition API
- **UI组件**: Ant Design Vue 4.x
- **状态管理**: Pinia
- **构建工具**: Vite

### 后端 (NestJS + TypeScript)
- **地址**: http://*************:3000
- **API文档**: http://*************:3000/api-docs
- **框架**: NestJS 10.x
- **数据库**: MySQL 8.0
- **ORM**: 原生SQL查询

### 数据库配置
- **主机**: 环境变量配置
- **数据库**: onlyoffice_system
- **字符集**: utf8mb4
- **时区**: Asia/Shanghai

## 🚀 快速开始

### 1. 启动前端服务
```bash
cd frontend
npm install
npm run dev
# 访问: http://*************:8080
```

### 2. 启动后端服务
```bash
cd backend
npm install
npm run start:dev
# API文档: http://*************:3000/api-docs
```

### 3. 访问文档管理
打开浏览器访问 http://*************:8080，进入文档管理页面体验最新的操作界面。

## 📋 核心功能

### 📄 文档管理
- **文档上传**: 支持Word、Excel、PowerPoint、PDF等格式
- **在线编辑**: 内嵌模式和新窗口模式
- **配置模板**: 实时选择和切换编辑器配置
- **版本管理**: 文档版本历史追踪
- **批量操作**: 批量下载、删除等功能

### 📋 模板管理
- **文档模板**: Word/Excel/PPT模板创建和管理
- **配置模板**: OnlyOffice编辑器配置模板管理
- **权限配置**: 细粒度的权限控制模板

### ⚙️ 配置管理
- **编辑器配置**: OnlyOffice编辑器参数配置
- **用户权限**: 基于角色的权限管理
- **系统设置**: 全局配置参数管理

## 🔧 开发规范

### 命名规范
- **文件夹**: kebab-case (如: `document-actions`)
- **文件名**: PascalCase组件 + kebab-case其他
- **变量**: camelCase
- **常量**: SCREAMING_SNAKE_CASE
- **接口**: PascalCase + Interface后缀

### 代码质量
- **TypeScript**: 严格模式，禁用any类型
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **单元测试**: Jest测试框架

## 🔗 相关链接

- **前端地址**: http://*************:8080
- **后端API**: http://*************:3000
- **API文档**: http://*************:3000/api-docs
- **项目文档**: `/doc` 目录

## 📝 更新日志

### v2.0.0 (2024-12-24)
- ✅ 文档管理页面操作区域重构
- ✅ 去除自定义窗口弹窗，改为直接下拉选择
- ✅ 按钮文本优化，操作区域紧凑化
- ✅ 配置模板实时切换功能

### v1.x.x (历史版本)
- 基础文档管理功能
- OnlyOffice编辑器集成
- 用户权限管理系统

---

## 📞 技术支持

如有技术问题或建议，请：
1. 查看 `/doc` 目录下的详细文档
2. 访问 API 文档了解接口详情
3. 联系开发团队获取支持

**注意**: 本项目使用IP地址 `*************` 进行本地开发和测试，请确保网络环境配置正确。 