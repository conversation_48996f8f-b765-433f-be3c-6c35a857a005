const mysql = require('mysql2/promise');

async function testHistorySQL() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库测试历史查询SQL...');
    
    connection = await mysql.createConnection({
      host: '*************',
      port: 3306,
      user: 'onlyfile_user',
      password: '0nlyF!le$ecure#123',
      database: 'onlyfile'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 执行API中使用的确切SQL查询
    console.log('\n🔍 1. 执行API中使用的审计日志查询...');
    const apiQuery = `
      SELECT 
        id,
        COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.setting_key')), resource_id) as setting_key,
        COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.new_value')), '') as new_value,
        COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.old_value')), '') as old_value,
        COALESCE(user_id, 'system') as changed_by,
        created_at as changed_at,
        action as operation,
        COALESCE(JSON_UNQUOTE(JSON_EXTRACT(details, '$.description')), '') as description
      FROM audit_logs
      WHERE resource_type = 'system_config' 
         OR action LIKE '%config%'
         OR action LIKE '%setting%'
      ORDER BY created_at DESC
      LIMIT 10
    `;
    
    console.log('执行的SQL查询:');
    console.log(apiQuery.trim());
    
    const [results] = await connection.query(apiQuery);
    
    console.log(`\n✅ 查询结果数量: ${results.length}`);
    
    if (results.length > 0) {
      console.log('\n📋 查询结果详情:');
      results.forEach((record, index) => {
        console.log(`${index + 1}. 记录详情:`);
        console.log(`   ID: ${record.id}`);
        console.log(`   配置键: ${record.setting_key}`);
        console.log(`   新值: ${record.new_value}`);
        console.log(`   旧值: ${record.old_value}`);
        console.log(`   操作者: ${record.changed_by}`);
        console.log(`   时间: ${record.changed_at}`);
        console.log(`   操作: ${record.operation}`);
        console.log(`   描述: ${record.description}`);
        console.log('');
      });
    } else {
      console.log('❌ 查询结果为空');
    }
    
    // 2. 检查原始details字段
    console.log('\n🔍 2. 检查原始details字段...');
    const [rawDetails] = await connection.query(`
      SELECT id, details, resource_id, action
      FROM audit_logs 
      WHERE resource_type = 'system_config'
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    console.log('原始details字段内容:');
    rawDetails.forEach((record, index) => {
      console.log(`${index + 1}. ID: ${record.id}`);
      console.log(`   resource_id: ${record.resource_id}`);
      console.log(`   action: ${record.action}`);
      console.log(`   details类型: ${typeof record.details}`);
      console.log(`   details内容: ${JSON.stringify(record.details)}`);
      
      // 尝试解析details
      if (record.details) {
        try {
          const parsed = typeof record.details === 'string' ? JSON.parse(record.details) : record.details;
          console.log(`   解析后的details:`, parsed);
        } catch (error) {
          console.log(`   details解析失败: ${error.message}`);
        }
      }
      console.log('');
    });
    
    // 3. 测试fallback查询
    console.log('\n🔍 3. 测试fallback查询...');
    const fallbackQuery = `
      SELECT 
        CONCAT('sys_', setting_key, '_', UNIX_TIMESTAMP(updated_at)) as id,
        setting_key,
        setting_value as new_value,
        '' as old_value,
        'system' as changed_by,
        updated_at as changed_at,
        'initialize' as operation,
        description
      FROM system_settings
      WHERE updated_at IS NOT NULL
      ORDER BY updated_at DESC
      LIMIT 10
    `;
    
    const [fallbackResults] = await connection.query(fallbackQuery);
    console.log(`Fallback查询结果数量: ${fallbackResults.length}`);
    
    if (fallbackResults.length > 0) {
      console.log('Fallback查询结果 (前3条):');
      fallbackResults.slice(0, 3).forEach((record, index) => {
        console.log(`${index + 1}. ID: ${record.id}`);
        console.log(`   配置键: ${record.setting_key}`);
        console.log(`   新值: ${record.new_value}`);
        console.log(`   时间: ${record.changed_at}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error('详细错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

console.log('🚀 开始测试历史查询SQL...\n');
testHistorySQL(); 