const mysql = require('mysql2/promise');

async function testDirectSQL() {
  let connection;
  
  try {
    console.log('🔗 连接到数据库...');
    
    // 使用实际的数据库配置
    connection = await mysql.createConnection({
      host: '*************',
      port: 3306,
      user: 'onlyfile_user',
      password: '0nlyF!le$ecure#123',
      database: 'onlyfile'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 测试基础查询
    console.log('\n📊 1. 检查 system_settings 表结构...');
    const [columns] = await connection.query(`
      DESCRIBE system_settings
    `);
    console.log('表结构:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // 2. 统计总数
    console.log('\n📈 2. 统计配置项总数...');
    const [countResult] = await connection.query(`
      SELECT COUNT(*) as total FROM system_settings
    `);
    console.log(`总配置项: ${countResult[0].total}`);
    
    // 3. 检查有updated_at的记录
    console.log('\n🕒 3. 检查有更新时间的记录...');
    const [withUpdatedAt] = await connection.query(`
      SELECT COUNT(*) as count_with_time 
      FROM system_settings 
      WHERE updated_at IS NOT NULL
    `);
    console.log(`有更新时间的记录: ${withUpdatedAt[0].count_with_time}`);
    
    // 4. 测试原始的History查询（不包含UUID）
    console.log('\n🔍 4. 测试 History 查询（简化版）...');
    const [historyResults] = await connection.query(`
      SELECT 
        setting_key,
        setting_value as new_value,
        '' as old_value,
        'system' as changed_by,
        updated_at as changed_at,
        'update' as operation,
        description
      FROM system_settings
      WHERE updated_at IS NOT NULL
      ORDER BY updated_at DESC
      LIMIT 5
    `);
    
    console.log(`查询结果数量: ${historyResults.length}`);
    if (historyResults.length > 0) {
      console.log('前3条记录:');
      historyResults.slice(0, 3).forEach((record, index) => {
        console.log(`  ${index + 1}. ${record.setting_key}`);
        console.log(`     值: ${record.new_value}`);
        console.log(`     时间: ${record.changed_at}`);
        console.log('');
      });
    }
    
    // 5. 测试完整的History查询（包含ID生成）
    console.log('\n🆔 5. 测试完整 History 查询（包含ID）...');
    const [fullHistoryResults] = await connection.query(`
      SELECT 
        CONCAT('hist_', setting_key, '_', UNIX_TIMESTAMP(updated_at)) as id,
        setting_key,
        setting_value as new_value,
        '' as old_value,
        'system' as changed_by,
        updated_at as changed_at,
        'update' as operation,
        description
      FROM system_settings
      WHERE updated_at IS NOT NULL
      ORDER BY updated_at DESC
      LIMIT 5
    `);
    
    console.log(`完整查询结果数量: ${fullHistoryResults.length}`);
    if (fullHistoryResults.length > 0) {
      console.log('前2条完整记录:');
      fullHistoryResults.slice(0, 2).forEach((record, index) => {
        console.log(`  ${index + 1}. ID: ${record.id}`);
        console.log(`     键: ${record.setting_key}`);
        console.log(`     值: ${record.new_value}`);
        console.log(`     时间: ${record.changed_at}`);
        console.log(`     操作: ${record.operation}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error('详细信息:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

console.log('🚀 开始直接SQL测试...\n');
testDirectSQL(); 