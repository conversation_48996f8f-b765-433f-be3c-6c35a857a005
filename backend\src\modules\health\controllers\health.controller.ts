import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HttpHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
  HealthCheck,
} from '@nestjs/terminus';
import { DatabaseService } from '../../database/services/database.service';

/**
 * 健康检查控制器
 *
 * 提供系统健康状态检查的RESTful API接口
 * 包括基础健康检查、数据库检查、外部服务检查等
 *
 * @class HealthController
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private databaseService: DatabaseService,
  ) {}

  /**
   * 基础健康检查
   * GET /api/health
   */
  @Get()
  @ApiOperation({
    summary: '基础健康检查',
    description: '检查应用程序基本运行状态',
  })
  @ApiResponse({
    status: 200,
    description: '健康检查成功',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' },
      },
    },
  })
  @HealthCheck()
  check() {
    // 根据操作系统选择合适的磁盘检查路径
    const diskPath = process.platform === 'win32' ? 'C:\\' : '/';
    
    return this.health.check([
      // 内存检查：堆内存使用量小于150MB
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      
      // 磁盘检查：磁盘使用率小于90%
      () => this.disk.checkStorage('storage', { path: diskPath, thresholdPercent: 0.9 }),
    ]);
  }

  /**
   * 数据库健康检查
   * GET /api/health/db
   */
  @Get('db')
  @ApiOperation({
    summary: '数据库健康检查',
    description: '检查数据库连接状态',
  })
  @ApiResponse({
    status: 200,
    description: '数据库连接正常',
  })
  @ApiResponse({
    status: 503,
    description: '数据库连接异常',
  })
  @HealthCheck()
  async checkDatabase() {
    return this.health.check([
      async () => {
        try {
          const isConnected = await this.databaseService.testConnection();
          if (isConnected) {
            return {
              database: {
                status: 'up',
                message: '数据库连接正常',
                timestamp: new Date().toISOString(),
              },
            };
          } else {
            throw new Error('数据库连接失败');
          }
        } catch (error) {
          throw new Error(`数据库健康检查失败: ${error.message}`);
        }
      },
    ]);
  }

  /**
   * 数据库健康检查 (兼容路径)
   * GET /api/health/database
   */
  @Get('database')
  @ApiOperation({
    summary: '数据库健康检查 (兼容接口)',
    description: '检查数据库连接状态',
  })
  @ApiResponse({
    status: 200,
    description: '数据库连接正常',
  })
  @ApiResponse({
    status: 503,
    description: '数据库连接异常',
  })
  async checkDatabaseCompat() {
    try {
      const isConnected = await this.databaseService.testConnection();
      if (isConnected) {
        return {
          success: true,
          data: {
            status: 'up',
            message: '数据库连接正常',
            timestamp: new Date().toISOString(),
          },
          message: '数据库健康检查成功',
          timestamp: new Date().toISOString(),
        };
      } else {
        throw new Error('数据库连接失败');
      }
    } catch (error) {
      return {
        success: false,
        data: {
          status: 'down',
          message: `数据库连接失败: ${error.message}`,
          timestamp: new Date().toISOString(),
        },
        message: '数据库健康检查失败',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 外部服务健康检查
   * GET /api/health/external
   */
  @Get('external')
  @ApiOperation({
    summary: '外部服务健康检查',
    description: '检查依赖的外部服务状态',
  })
  @ApiResponse({
    status: 200,
    description: '外部服务正常',
  })
  @ApiResponse({
    status: 503,
    description: '外部服务异常',
  })
  @HealthCheck()
  checkExternalServices() {
    const checks = [];
    
    // OnlyOffice文档服务检查 - 根据实际环境变量结构
    let onlyOfficeUrl = process.env.ONLYOFFICE_SERVER_URL || process.env.ONLYOFFICE_DOCS_URL || 'http://*************';
    
    // 确保URL末尾没有多余的斜杠
    onlyOfficeUrl = onlyOfficeUrl.replace(/\/$/, '');
    const onlyOfficeHealthUrl = `${onlyOfficeUrl}/healthcheck`;
    
    console.log('[HealthCheck] OnlyOffice健康检查URL:', onlyOfficeHealthUrl);
    checks.push(
      () => this.http.pingCheck('onlyoffice-docs', onlyOfficeHealthUrl)
    );
    
    // FileNet服务检查 - 根据实际环境变量结构
    let filenetUrl;
    if (process.env.FILENET_URL) {
      // 如果有完整的FILENET_URL
      filenetUrl = process.env.FILENET_URL.replace(/\/$/, '');
    } else if (process.env.FILENET_HOST && process.env.FILENET_PORT) {
      // 如果是分开配置的HOST和PORT
      filenetUrl = `http://${process.env.FILENET_HOST}:${process.env.FILENET_PORT}`;
    } else {
      // 默认配置
      filenetUrl = 'http://*************:8090';
    }
    
    const filenetHealthUrl = `${filenetUrl}/common/health`;
    
    console.log('[HealthCheck] FileNet健康检查URL:', filenetHealthUrl);
    checks.push(
      () => this.http.pingCheck('filenet', filenetHealthUrl)
    );

    return this.health.check(checks);
  }

  /**
   * 详细系统信息
   * GET /api/health/info
   */
  @Get('info')
  @ApiOperation({
    summary: '系统信息',
    description: '获取详细的系统运行信息',
  })
  @ApiResponse({
    status: 200,
    description: '系统信息获取成功',
    schema: {
      type: 'object',
      properties: {
        application: {
          type: 'object',
          properties: {
            name: { type: 'string', example: 'OnlyOffice Integration API' },
            version: { type: 'string', example: '2.0.0' },
            environment: { type: 'string', example: 'development' },
            uptime: { type: 'number', example: 3600 },
          },
        },
        system: {
          type: 'object',
          properties: {
            platform: { type: 'string', example: 'linux' },
            arch: { type: 'string', example: 'x64' },
            nodeVersion: { type: 'string', example: 'v18.17.0' },
            memory: { type: 'object' },
          },
        },
        timestamp: { type: 'string', example: '2024-12-19T10:30:00Z' },
      },
    },
  })
  getSystemInfo() {
    const memoryUsage = process.memoryUsage();
    
    return {
      application: {
        name: 'OnlyOffice Integration API',
        version: process.env.npm_package_version || '2.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: Math.floor(process.uptime()),
        pid: process.pid,
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
          external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 就绪检查
   * GET /api/health/ready
   */
  @Get('ready')
  @ApiOperation({
    summary: '就绪检查',
    description: '检查应用程序是否已准备好接收请求',
  })
  @ApiResponse({
    status: 200,
    description: '应用程序已就绪',
  })
  @ApiResponse({
    status: 503,
    description: '应用程序未就绪',
  })
  getReadiness() {
    // 检查关键服务是否就绪
    const checks = {
      database: true, // TODO: 实际检查数据库连接
      config: !!process.env.NODE_ENV,
      onlyoffice: !!process.env.ONLYOFFICE_DOCS_URL,
    };

    const isReady = Object.values(checks).every(check => check);
    
    return {
      status: isReady ? 'ready' : 'not ready',
      checks,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 存活检查
   * GET /api/health/live
   */
  @Get('live')
  @ApiOperation({
    summary: '存活检查',
    description: '检查应用程序是否仍在运行',
  })
  @ApiResponse({
    status: 200,
    description: '应用程序正在运行',
  })
  getLiveness() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
    };
  }
} 