const axios = require('axios');

// 最新的JWT令牌（修复空格问题）
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NDkwMDMwMzYsImV4cCI6MTc0OTAwNjYzNiwiaXNzIjoib25seW9mZmljZS1hcGkiLCJhdWQiOiJvbmx5b2ZmaWNlLXVzZXJzIn0.IgWa9hOLnWBzSz_W2khJ4tWh8yGVj3NrHiyxNKSB54E';

async function testSwaggerAuth() {
  console.log('🔐 测试Swagger认证...');
  console.log('🎫 使用令牌:', token.substring(0, 50) + '...');
  
  try {
    // 测试用户管理API
    console.log('\n📋 测试 GET /api/users...');
    const response = await axios.get('http://localhost:3000/api/users', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ 用户API测试成功！');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 用户API测试失败:');
    console.error('错误类型:', error.constructor.name);
    
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.log('\n🔍 认证失败分析:');
        const errorData = error.response.data;
        if (errorData.error && errorData.error.error === 'No auth token') {
          console.log('- 问题: Swagger没有正确传递Bearer令牌');
          console.log('- 解决: 确保在Swagger页面正确设置了Authorization');
        } else {
          console.log('- 错误详情:', errorData.error);
        }
      }
    } else {
      console.error('网络错误:', error.message);
    }
  }
  
  // 测试其他需要认证的API
  try {
    console.log('\n📋 测试 GET /api/auth/me...');
    const authResponse = await axios.get('http://localhost:3000/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    console.log('✅ 认证API测试成功！');
    console.log('当前用户:', authResponse.data.data ? authResponse.data.data.username : '未知');
    
  } catch (authError) {
    console.error('❌ 认证API测试失败:', authError.response ? authError.response.status : authError.message);
  }
}

console.log('🎯 Swagger认证测试工具');
console.log('📖 使用说明:');
console.log('1. 确保服务器在 http://localhost:3000 运行');
console.log('2. 打开浏览器访问: http://localhost:3000/api-docs');
console.log('3. 点击右上角的"Authorize"按钮');
console.log('4. 输入令牌（无需Bearer前缀）:', token);
console.log('5. 点击"Authorize"完成认证');
console.log('6. 现在可以测试用户管理API了');

testSwaggerAuth(); 