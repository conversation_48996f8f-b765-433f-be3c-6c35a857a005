/**
 * 数据库初始化脚本
 * 用于创建数据库和用户
 */
const mysql = require('mysql2/promise');
const dbConfig = require('../config/database');

async function initializeDatabase() {
    // 连接到MySQL服务器，不指定数据库
    const connection = await mysql.createConnection({
        host: dbConfig.host,
        port: dbConfig.port,
        user: 'root',  // 需要有创建数据库和用户的权限
        password: process.env.MYSQL_ROOT_PASSWORD || '',
    });

    try {
        console.log('连接到MySQL服务器成功');

        // 创建数据库
        console.log(`创建数据库: ${dbConfig.database}`);
        await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

        // 检查用户是否存在
        const [users] = await connection.query(`SELECT User FROM mysql.user WHERE User = ?`, [dbConfig.user]);

        if (users.length === 0) {
            // 创建用户
            console.log(`创建用户: ${dbConfig.user}`);
            await connection.query(`CREATE USER '${dbConfig.user}'@'%' IDENTIFIED BY '${dbConfig.password}'`);

            // 授权
            console.log(`授权用户: ${dbConfig.user} 访问 ${dbConfig.database}`);
            await connection.query(`GRANT ALL PRIVILEGES ON ${dbConfig.database}.* TO '${dbConfig.user}'@'%'`);

            // 刷新权限
            await connection.query('FLUSH PRIVILEGES');
        } else {
            console.log(`用户 ${dbConfig.user} 已存在`);
        }

        console.log('数据库初始化完成');
    } catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

// 执行初始化
initializeDatabase()
    .then(() => {
        console.log('脚本执行成功');
        process.exit(0);
    })
    .catch((error) => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });