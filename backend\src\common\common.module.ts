import { Module } from '@nestjs/common';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { ResponseTransformInterceptor } from './interceptors/response-transform.interceptor';

/**
 * 通用模块
 * 
 * 提供全局使用的过滤器、拦截器、守卫等组件
 * 
 * @class CommonModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Module({
  providers: [
    GlobalExceptionFilter,
    LoggingInterceptor,
    ResponseTransformInterceptor,
  ],
  exports: [
    GlobalExceptionFilter,
    LoggingInterceptor,
    ResponseTransformInterceptor,
  ],
})
export class CommonModule {} 