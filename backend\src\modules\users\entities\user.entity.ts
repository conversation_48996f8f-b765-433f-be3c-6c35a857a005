export interface User {
  id: string;
  username: string;
  email?: string;
  password_hash: string;
  full_name?: string;
  phone?: string;
  avatar_url?: string;
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  role_id?: string;
  department?: string;
  position?: string;
  last_login_at?: Date;
  last_login_ip?: string;
  password_changed_at?: Date;
  failed_login_attempts: number;
  locked_until?: Date;
  email_verified: boolean;
  phone_verified: boolean;
  two_factor_enabled: boolean;
  created_by?: string;
  created_at: Date;
  updated_by?: string;
  updated_at: Date;
  deleted_at?: Date;
  
  // 关联数据 (从数据库查询中返回)
  role?: UserRole;
  role_name?: string;
  role_display_name?: string;
  role_permissions?: string[];
}

export interface UserRole {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
  is_system: boolean;
  is_active: boolean;
  sort_order: number;
  created_by?: string;
  created_at: Date;
  updated_by?: string;
  updated_at: Date;
}

export interface UserPermission {
  id: string;
  code: string;
  name: string;
  description?: string;
  module: string;
  resource?: string;
  action?: string;
  conditions?: Record<string, unknown>;
  is_active: boolean;
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_token: string;
  refresh_token?: string;
  device_type?: string;
  device_name?: string;
  user_agent?: string;
  ip_address?: string;
  location?: string;
  is_active: boolean;
  expires_at: Date;
  last_activity_at: Date;
  created_at: Date;
}

export interface AuditLog {
  id: string;
  user_id?: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  details?: Record<string, unknown>;
  ip_address?: string;
  user_agent?: string;
  status: 'success' | 'failed' | 'error';
  error_message?: string;
  execution_time?: number;
  created_at: Date;
}

export interface PasswordHistory {
  id: string;
  user_id: string;
  password_hash: string;
  created_at: Date;
}

// 用户详情视图（包含角色信息）
export interface UserDetail extends User {
  role_name?: string;
  role_display_name?: string;
  role_permissions?: string[];
}

// 分页查询结果
export interface UserListResult {
  data: UserDetail[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 用户统计信息
export interface UserStats {
  total_users: number;
  active_users: number;
  inactive_users: number;
  suspended_users: number;
  online_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
} 