import { Controller, Get, HttpCode, HttpStatus, Header } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiExcludeEndpoint } from '@nestjs/swagger';

/**
 * 应用根控制器
 * 
 * 处理应用根路径的请求，提供系统欢迎页面和导航信息
 * 此控制器绕过全局API前缀，直接处理根路径请求
 * 
 * @class AppController
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@ApiTags('系统信息')
@Controller()
export class AppController {
  
  /**
   * 根路径欢迎页面
   * 
   * @returns 系统欢迎信息和可用接口列表
   */
  @Get()
  @ApiOperation({ 
    summary: '系统欢迎页面',
    description: '获取系统基本信息、可用接口列表和系统状态'
  })
  @ApiResponse({ 
    status: 200, 
    description: '成功返回系统欢迎信息',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            title: { type: 'string', example: 'OnlyOffice集成系统API' },
            version: { type: 'string', example: '2.0.0' },
            description: { type: 'string', example: '基于NestJS的OnlyOffice文档管理和集成系统' },
            endpoints: { type: 'object' },
            links: { type: 'object' },
            environment: { type: 'string', example: 'development' },
            uptime: { type: 'number', example: 12345 },
            memory: { type: 'object' }
          }
        },
        requestId: { type: 'string' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  getWelcome() {
    return {
      success: true,
      data: {
        title: 'OnlyOffice集成系统API',
        version: '2.0.0',
        description: '基于NestJS的OnlyOffice文档管理和集成系统',
        author: 'OnlyOffice Team',
        timestamp: new Date().toISOString(),
        endpoints: {
          '系统健康检查': '/api/health',
          'API文档': '/api-docs', 
          '数据库健康检查': '/api/health/database',
          '外部服务检查': '/api/health/external',
          '系统信息': '/api/health/system',
          '就绪状态': '/api/health/readiness',
          '存活状态': '/api/health/liveness'
        },
        links: {
          'API文档界面': 'http://localhost:3000/api-docs',
          '健康检查': 'http://localhost:3000/api/health',
          'GitHub仓库': 'https://github.com/your-org/onlyoffice-integration'
        },
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime(),
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          unit: 'MB'
        }
      },
      requestId: `welcome_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * favicon.ico 处理
   * 
   * @returns 空响应，避免404错误
   */
  @Get('favicon.ico')
  @ApiExcludeEndpoint()
  @HttpCode(HttpStatus.NO_CONTENT)
  @Header('Content-Type', 'image/x-icon')
  getFavicon() {
    // 返回空响应，避免favicon 404错误
    return;
  }
} 