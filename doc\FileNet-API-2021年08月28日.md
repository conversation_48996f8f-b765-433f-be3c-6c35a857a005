﻿----------------------- Page 1-----------------------

      FileNet-API-2021年08⽉28 ⽇ 



      基本参数 



       1.  Host: 是上传位置&机器配置的区别 



          a.  测试环境 ：*************:8090 



          b.  正式环境 ：*************:8090 



       2.   folder ：具体上传位置 



          a.  测试环境 ：「{3076037A-0000-C417-AB3B-4B8A50F9EDED} 」 



          b.  正式环境 ：各域不同，后续单独给出并记录；（⽣产folder不易变动，建议写 

             死，并check是否正确） 



              i.  明源 ： 



                 1.  TEST: {6078817B-0000-C91A-BFF0-A3B7D84D1348} 



              ii.  CRM ： 



              iii.  400 ： 



             iv.  嘉扬 ： 



              v.  朗诗物业-财务共享系统 ：{B043517A-0000-CE15-95D9-64629AABE550} 



      接⼝定义 



         通⽤上传  /common/uploadFiles 



         通⽤下载 （通过DocID完成下载）/common/download 



         按照BusinessId下载 （ERP专⽤）/common/downloadByBusinessId 



         批量下载 （下载⽂件zip） /common/downloadBatch 



         通⽤直接下载 



      通⽤上传  [POST]/common/uploadFiles 



      ⽰例 ： 



FileNet-API-2021年08⽉28 ⽇                                                              1 


----------------------- Page 2-----------------------

         curl --location --request POST  '*************:8090/common/uploadFiles' \  

         --form  'docName=test.xml' \  

         --form  'docClass=only_for_test' \  

         --form  'file_md5=xxx' \  

         --form  'for_test_integer=1' \  

         --form  'for_test_date=2020-10-16 17:40' \  

         --form  'upload_time=2020-10-16 17:40' \  

         --form  'file=@/Users/<USER>/Desktop/IMG_1211.jpeg' \  

         --form  'folder={3095296C-0000-CF1B-8F8A-AC892927F153}' 



       ⼊参 ： 



        1.   docClass （String，必填项，每个项⽬固定值，⽤于后续参数校验合法性） 



        2.   folder   （String，必填项，标记⽂档的逻辑存储位置，固定值，由技术⽀持提供） 



        3.   docName (String, 必填项，⽂档名，标志当前⽂档的名字，可以给附件完整名字 （包括 



           后缀）) 



        4.  ... （动态参数信息） （这部分参数要求，随着docClass变动，在附录 ：动态参数  中 



           体现） 



       针对上传的DEMO ： 



           docClass:  SimpleDocument 



           folder:  {6078817B-0000-C91A-BFF0-A3B7D84D1348}  (各⾃服务会被指定，通常固定不变) 



           以下为动态参数 ： 



               因为SimpleDocument只需要两个参数 ：source_type  和  biz_tag 



           动态参数1 ：source_type  ：通常是系统名，表⽰当前系统⾝份 ；⽐如⽤户系统 ： 



            customer  （完全是各业务独⽴） 



           动态参数2 ：biz_tag ： 系统下的业务分类，实名  customer_certify 



         curl --location --request POST  'localhost:8090/common/uploadFiles' \  

         --form  'folder="{6078817B-0000-C91A-BFF0-A3B7D84D1348}"' \  

         --form  'docName="xxx.xls"' \  

         --form  'docClass="SimpleDocument"' \  

         --form  'source_type="CUSTOMER"' \  

         --form  'biz_tag="customer_certify"' \  

         --form  'file=@"/Users/<USER>/ Mysql表空间数据结构理解.png"' 



FileNet-API-2021年08⽉28 ⽇                                                                                2 


----------------------- Page 3-----------------------

      SimpleDocument上传  [POST] /common/simpleUpload 



      ⽰例 ： 



        curl --location --request POST  'localhost:8090/common/simpleUpload' \  

        --form  'folder="{6078817B-0000-C91A-BFF0-A3B7D84D1348}"' \  

        --form  'docName="xxx.xls"' \  

        --form  'docClass="SimpleDocument"' \  

        --form  'source_type="CUSTOMER"' \  

        --form  'biz_tag="customer_certify"' \  

        --form  'file=@"/Users/<USER>/ Mysql表空间数据结构理解.png"' 



      返回参数 ： 



        1.   code （200为正常，错误code会有附录说明） 



        2.   message （正确为SUCCESS ，错误为异常信息） 



       3.   data （具体返回信息） 



      ⽰例 ： 



       {"code":200,"message":"SUCCESS","data":"{70CD3075-0000-C018-8AC9-C9D8E18EF46E}"} 



      SimpleDocument删除⽂档  [POST] /common/deleteSimpleDocument 



      ⼊参 ： 



          docId  （可以不⽤带⼤括号） 



               「{xxxx}」 



               「xxxx」 



      返回 ： 



       {"code":200,"message":"SUCCESS","data":""} 



      通⽤下载 （通过DocID完成下载）[GET]/common/download 



      ⼊参 



FileNet-API-2021年08⽉28 ⽇                                                                        3 


----------------------- Page 4-----------------------

       1.   docId  (String ，必填项，去掉⼤括号部分的字符串，⽐如70CBD762-0000-CB1E-AD00- 



         30A195FD70CA ，也可以不去，但是请求时务必参数encoding⼀下） 



       2.   number   （int，⾮必选项，默认为0 ，描述需要下载document对象的第⼏个⽂件） 



      返回信息 ： 



         ⽂件流。 



      按照BusinessId下载 （ERP专⽤）[GET]/common/downloadByBusinessId 



      ⼊参 



       1.   businessId   （String，必填项 ； 



       2.  ⽆其他参数，因为跟ERP约定了⼀个businessId只传⼀个⽂件 ； 



      返回信息 ： 



         ⽂件流。 



      批量下载 （下载⽂件zip） [GET]/common/downloadBatch 



      ⼊参 ： 



       1.   data （String，必填项，内容举例 ：docId:62241363-4856-42FE-ABA1- 



         A1C9D33D60A5,businessId:0001131000000006SXBB,businessId:0001131000000006OF2Y ） 



      返回信息 ： 



         zip⽂件流。所需⽂件在zip内 



      获取⽂档缩略图 [POST] /web/getThumbnails 



      ⽬前仅有图⽚&pdf （会截取⾸图）⽀持缩略图 



      ⼊参 ： 



       {  

           "imgDataRequestModels":[  

               {  



FileNet-API-2021年08⽉28 ⽇                                                           4 


----------------------- Page 5-----------------------

                      "dataType":"", // docId 或者 businessId  

                      "value":""  

                  }  

              ]  

          } 



        返回 ： 



          {  

              "code":200,  

              "message":"SUCCESS",  

              "data":[  

                  {  

                      "index":0,  

                      "url":"xxx"// base64img  

                  },  

                  {  

                      "index":0,  

                      "url":"xxx"  

                  }  

              ]  

          } 



        动态参数 



             这⾥的参数，只显⽰⽂档部分参数，其余参数看接⼝定义 



         1.   + LandSeaDocument  表⽰需要加上LandSeaDocument 的所有参数 



         2.  Date是指可以转化为yyyy-MM-dd HH:mm  的date 类型 ； 



         3.  filenet-api服务不关⼼字段含义，字段业务含义不明，考虑看业务系统代码。 



         4.   docName 必须传哦 ！ 



          documents:  

            - name: fangjingping  

              params:  

                - fangjingping #String  

            - name: ICCCustomObject  

              params:  

                - ICCTitle #String  



FileNet-API-2021年08⽉28 ⽇                                                                                             5 


----------------------- Page 6-----------------------

           - name: only_for_test  

             params:  

               - upload_time #date  

               - file_md5 #String  

               - for_test_integer #int  

               - for_test_date #date  

           - name: ICCDocument  

             params:  

           - name: jiayang_filenet_document #嘉阳系统对接  

             params:  

               - fileMD5 #String  

               - fileRealName #String  

               - docName #String  

               - operateTime #Date  

               - operationManager #String  

               - fileRealContentType #String  

           - name: LandSeaDocument #⽂档⽗节点  

             params:  

               - DocumentTitile #String  

               - buildtime #Date  

               - builder #String  

               - business_app_name #String  

               - business_id #String  

               - keywords #String  

           - name: BPMBusinessDocument # BPM 系统⽂档属性 + LandSeaDocument  

             params:  

               - appId #String  

               - taskId #String  

               - businessKey #String  

               - instanceId #String  

           - name: ERPBusinessDocument # ERP 系统⽂档属性 + LandSeaDocument  

             params:  

               - otherData #String  

               - businessDocumentNo #String  

               - businessDocumentType #String  

               - systemModelName #String  

               - billNumber #String  

               - erpDef1 #String  

               - erpDef2 #String  

               - erpDef3 #String  

           - name: LandSeaDocApartmentGraph # +LandSeaDocument  

             params:  

               - realty_prj #String  

               - budgetpanel_id #String  

               - business_node #String  

               - author #String  

               - submit_time #Date  

               - del_executor #String  

           - name: LandSeaDocBidCalibration # +LandSeaDocument  

             params:  

               - purchasePlanCode #String  

               - purchasePlanName #String  

               - uploader #String  

           - name: LandSeaDocBidClarification #  +LandSeaDocument  



FileNet-API-2021年08⽉28 ⽇                                                                                   6 


----------------------- Page 7-----------------------

             params:  

               - purchasePlanCode #String  

               - purchasePlanName #String  

               - uploader #String  

           - name: LandSeaDocBiddingFile # 投标⽂件 +LandSeaDocument  

             params:  

               - purchasePlanCode #String  

               - purchasePlanName #String  

               - uploader #String  

               - bidCloseTime #Date  

               - bidOpenTime #Date  

           - name: LandSeaDocBiddingQA # +LandSeaDocument  

             params:  

               - purchasePlanCode #String  

               - purchasePlanName #String  

               - uploader #String  

           - name: LandSeaDocBidRespond #  +LandSeaDocument  

             params:  

               - purchasePlanCode #String  

               - purchasePlanName #String  

               - uploader #String  

               - organizationCode #String  

               - techStandardCode #String  

               - businessStandardCode #String  

               - threeVouchers #String  

           - name: LandSeaDocBPMAttachment # +LandSeaDocument  

             params:  

               - createPersonDept #String  

               - createPersonOrg #String  

           - name: LandSeaDocCallsysMessage # 400呼叫中⼼ +LandSeaDocument  

             params:  

               - src #String  

               - ext #String  

               - type #String  

               - url #String  

               - receptiontime #Date  

           - name: LandSeaDocCdro # +LandSeaDocument  

             params:  

               - calldate #Date  

               - billsec #int  

               - reception #String  

               - src #String  

               - dst #String  

               - clid #String  

           - name: LandSeaDocChangeRequest # +LandSeaDocument  

             params:  

               - realty_prj #String  

               - business_node #String  

               - obligee #String  

               - author #String  

               - submit_time #Date  

               - room_num #String  

           - name: LandSeaDocCompensation # +LandSeaDocument  

             params:  



FileNet-API-2021年08⽉28 ⽇                                                                                   7 


----------------------- Page 8-----------------------

               - realty_prj #String  

               - business_node #String  

               - task_id #String  

               - task_type #String  

               - compensation_company #String  

               - upload_person #String  

               - upload_time #Date  

               - payment_amount #String  

           - name: LandSeaDocComplainSubject # +LandSeaDocument  

             params:  

               - realty_prj #String  

               - business_node #String  

               - upload_person #String  

               - input_person #String  

               - subject #String  

               - doc_name #String  

               - doc_type #String  

               - attention_level #String  

               - occur_time #Date  

               - upload_time #Date  

           - name: LandSeaDocReceptionRecord # + LandSeaDocument  

             params:  

               - realty_prj #String  

               - reception_type #String  

               - reception_id #String  

               - complainant #String  

               - complain_object #String  

               - author #String  

               - proprietor #String  

               - accept_person #String  

               - business_node #String  

               - submit_time #Date  

           - name: LandSeaDocSalesBudgetDecomp # + LandSeaDocument  

             params:  

               - realty_prj #String  

               - business_node #String  

               - author #String  

               - submit_time #Date  

           - name: LandSeaDocTaskHandler # + LandSeaDocument  

             params:  

               - realty_prj #String  

               - business_node #String  

               - task_id #String  

               - doc_type #String  

               - accept_person #String  

               - follow_handler #String  

               - author #String  

               - task_level #String  

               - accept_time #Date  

               - reply_time #Date  

               - promise_handled_time #Date  

               - handled_limit_time #Date  

               - submit_time #Date  

           - name: LandSeaDocWebBidding #  + LandSeaDocument  



FileNet-API-2021年08⽉28 ⽇                                                                                   8 


----------------------- Page 9-----------------------

             params:  

               - firmBrandRecognition #String  

               - firmTaxRegistrationCode #String  

               - firmType #String  

               - firmFileCode #String  

               - firmWorkingCapital #String  

               - firmAssessScore #String  

               - firmRDAbility #String  

               - firmName #String  

               - firmSCS #String  

               - firmAddress #String  

               - organizationCode #String  

               - legalPersonName #String  

               - legalPersonId #String  

               - supplierScope #String  

               - supplyScope #String  

               - bidProductAnnualOutput #String  

               - firmBusinessLicenseCode #String  

               - threeVouchers #String  

           - name: SimpleDocument # 通⽤⽂档CLASS  

               params:  

                 - source_type #String  

                 - biz_tag #String  



FileNet-API-2021年08⽉28 ⽇                                                                                   9 

