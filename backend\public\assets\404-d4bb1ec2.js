import{d as l,u as d,q as s,c as i,j as t,s as o,h as f,i as a,_ as m}from"./index-5218909a.js";const b={class:"error-page"},x=l({__name:"404",setup(g){const _=d(),r=()=>{_.push("/")},c=()=>{_.go(-1)};return(v,e)=>{const n=s("a-button"),p=s("a-space"),u=s("a-result");return f(),i("div",b,[t(u,{status:"404",title:"404","sub-title":"抱歉，您访问的页面不存在"},{extra:o(()=>[t(p,null,{default:o(()=>[t(n,{type:"primary",onClick:r},{default:o(()=>e[0]||(e[0]=[a(" 返回首页 ")])),_:1,__:[0]}),t(n,{onClick:c},{default:o(()=>e[1]||(e[1]=[a(" 返回上页 ")])),_:1,__:[1]})]),_:1})]),_:1})])}}});const k=m(x,[["__scopeId","data-v-f400dbcb"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Error/404.vue"]]);export{k as default};
