const axios = require('axios');

async function testNestLogin() {
    try {
        console.log('🔄 测试NestJS登录API...');
        
        const response = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin123'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // 接受所有小于500的状态码
            }
        });
        
        console.log('📋 状态码:', response.status);
        console.log('📋 响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.status === 200) {
            console.log('✅ 登录成功！');
            console.log('🔑 Access Token:', response.data.data?.accessToken || response.data.accessToken);
        } else {
            console.log('❌ 登录失败');
            console.log('🔍 错误详情:', response.data);
        }
        
    } catch (error) {
        console.error('❌ 请求失败:', error.message);
        if (error.response) {
            console.error('📋 错误状态:', error.response.status);
            console.error('📋 错误数据:', error.response.data);
        }
    }
}

testNestLogin(); 