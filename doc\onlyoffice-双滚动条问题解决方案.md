# OnlyOffice编辑器双滚动条问题解决方案

## 问题描述

在集成OnlyOffice编辑器时，出现了双滚动条的问题：
- 外层容器有一个滚动条
- OnlyOffice编辑器内部又有一个滚动条
- 导致用户体验不佳，界面显得拥挤

## 解决方案

### 1. 布局架构优化

#### 1.1 路由配置优化
- 为编辑器页面添加 `fullscreen: true` 元标识
- 在 `BasicLayout` 中识别全屏页面并应用特殊样式

```typescript
// router/index.ts
{
  path: 'documents/editor/:id',
  name: 'DocumentEdit',
  component: () => import('@/pages/editor/EditorPage.vue'),
  meta: {
    title: '文档编辑',
    icon: 'edit',
    requiresAuth: true,
    hideInMenu: true,
    fullscreen: true, // 全屏页面标识
  },
}
```

#### 1.2 BasicLayout样式适配
- 为全屏页面移除默认的24px padding
- 隐藏底部页脚
- 设置正确的高度计算

```css
.fullscreen-content {
  padding: 0 !important;
  min-height: calc(100vh - 64px) !important;
  overflow: hidden !important;
}
```

### 2. 编辑器容器优化

#### 2.1 EditorPage高度设置
```css
.editor-page {
  height: calc(100vh - 64px); /* 只减去header高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
}
```

#### 2.2 EditorHeader固定高度
```css
.editor-header {
  height: 60px;
  min-height: 60px;
  max-height: 60px;
  flex-shrink: 0; /* 防止被压缩 */
}
```

#### 2.3 EditorContainer弹性布局
```css
.editor-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
```

### 3. OnlyOffice配置优化

#### 3.1 自定义配置
```typescript
customization: {
  autosave: true,
  forcesave: false,
  compactToolbar: false,
  toolbarNoTabs: false,
  zoom: 100,
  hideRightMenu: false,
  hideRulers: false,
  // 关键：禁用OnlyOffice内部滚动条
  showHorizontalScroll: false,
  showVerticalScroll: false,
  uiTheme: 'theme-light',
  integrationMode: 'embed',
}
```

#### 3.2 类型定义扩展
扩展了 `EditorCustomization` 接口，添加缺失的配置属性：
- `hideRulers`
- `showHorizontalScroll`
- `showVerticalScroll`
- `uiTheme`
- `integrationMode`
- `compactHeader`
- `about`

### 4. 全局CSS优化

#### 4.1 编辑器容器样式
```css
#editor-container {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

#editor-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  overflow: hidden !important;
}
```

#### 4.2 OnlyOffice内部元素优化
```css
/* 隐藏内部可能的滚动条 */
#editor-container .ws-canvas-outer,
#editor-container .ws-canvas-area,
#editor-container .ws-scrollable-area {
  overflow: hidden !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE 10+ */
}

#editor-container .ws-canvas-outer::-webkit-scrollbar,
#editor-container .ws-canvas-area::-webkit-scrollbar,
#editor-container .ws-scrollable-area::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari, Opera */
}
```

### 5. 响应式优化

#### 5.1 窗口大小变化监听
```typescript
const handleResize = (): void => {
  if (props.isReady) {
    nextTick(() => {
      const editorContainer = document.getElementById('editor-container')
      if (editorContainer) {
        const iframe = editorContainer.querySelector('iframe')
        if (iframe) {
          iframe.style.width = '100%'
          iframe.style.height = '100%'
        }
      }
    })
  }
}

// 添加监听器
window.addEventListener('resize', handleResize)
```

## 效果对比

### 修复前
- 页面有双滚动条
- 编辑器显示区域受限
- 用户体验不佳

### 修复后
- 只有OnlyOffice内部的文档滚动
- 编辑器占满整个可用空间
- 界面简洁，用户体验良好

## 注意事项

1. **类型安全**：确保TypeScript类型定义完整，避免运行时错误
2. **兼容性**：CSS样式使用了多种浏览器前缀确保兼容性
3. **性能**：添加了防抖和节流机制，避免频繁的DOM操作
4. **维护性**：代码结构清晰，注释完整，便于后续维护

## 相关文件

- `frontend/src/pages/editor/EditorPage.vue` - 编辑器主页面
- `frontend/src/pages/editor/components/EditorContainer.vue` - 编辑器容器
- `frontend/src/pages/editor/components/EditorHeader.vue` - 编辑器头部
- `frontend/src/pages/editor/composables/useEditor.ts` - 编辑器逻辑
- `frontend/src/components/Layout/BasicLayout.vue` - 基础布局
- `frontend/src/router/index.ts` - 路由配置
- `frontend/src/styles/index.css` - 全局样式
- `frontend/src/types/onlyoffice.types.ts` - 类型定义

---

**更新时间**: 2024-12-19  
**作者**: OnlyOffice Integration Team

## 新增功能：双模式编辑器打开方式

### 功能概述

系统现在支持两种OnlyOffice编辑器打开方式：

#### 1. 内嵌模式 (Embedded Mode)
- 在当前页面的框架内打开编辑器
- 保留原有的导航栏、页头页脚等界面元素
- 适合需要在系统内进行文档编辑的场景
- 路由: `/documents/editor/:id`

#### 2. 弹窗模式 (Popup Mode)
- 在新的浏览器窗口中打开编辑器
- 完全独立的编辑界面，不包含任何导航元素
- 支持自定义窗口大小和位置
- 适合专注编辑或需要并行操作的场景
- 路由: `/editor/fullscreen/:id`

### 使用方法

#### 通过工具函数使用

```typescript
import { openEmbeddedEditor, openPopupEditor, EditorOpenMode } from '@/utils/editor-utils'

// 内嵌模式打开
await openEmbeddedEditor('document-id')

// 弹窗模式打开（默认配置）
await openPopupEditor('document-id')

// 弹窗模式打开（自定义配置）
await openPopupEditor('document-id', {
  width: 1200,
  height: 800,
  resizable: true,
  scrollbars: false
})

// 统一接口打开
import { openEditor } from '@/utils/editor-utils'

await openEditor({
  documentId: 'document-id',
  mode: EditorOpenMode.POPUP,
  windowOptions: {
    width: 1600,
    height: 1000
  }
})
```

#### 通过组件使用

```vue
<template>
  <DocumentActions :document-id="documentId" />
</template>

<script setup>
import DocumentActions from '@/components/DocumentActions.vue'

const documentId = 'your-document-id'
</script>
```

### 技术实现

#### 1. 路由配置
- 添加了独立的全屏编辑器路由 `/editor/fullscreen/:id`
- 原有的内嵌编辑器路由保持不变 `/documents/editor/:id`

#### 2. 组件架构
- `FullscreenEditor.vue` - 独立的全屏编辑器页面
- `EditorPage.vue` - 原有的内嵌编辑器页面
- 共享 `EditorContainer`、`EditorHeader` 等子组件

#### 3. 工具函数
- `editor-utils.ts` - 提供统一的编辑器打开接口
- 支持多种窗口配置选项
- 自动计算窗口居中位置

#### 4. 用户体验优化
- 编辑器头部根据模式显示不同的返回按钮
- 弹窗模式显示"关闭窗口"按钮
- 内嵌模式显示"返回首页"链接
- 支持窗口关闭检测和状态提示

### 配置选项

弹窗模式支持以下窗口配置：

```typescript
interface WindowOptions {
  width?: number          // 窗口宽度，默认1200
  height?: number         // 窗口高度，默认800
  left?: number          // 窗口左边距，默认居中
  top?: number           // 窗口上边距，默认居中
  resizable?: boolean    // 是否可调整大小，默认true
  scrollbars?: boolean   // 是否显示滚动条，默认false
  menubar?: boolean      // 是否显示菜单栏，默认false
  toolbar?: boolean      // 是否显示工具栏，默认false
  location?: boolean     // 是否显示地址栏，默认false
  status?: boolean       // 是否显示状态栏，默认false
}
```

### 预设窗口大小

系统提供了几种预设的窗口大小：

- **小窗口**: 800x600
- **中窗口**: 1200x800（默认）
- **大窗口**: 1600x1000
- **全屏窗口**: 占满整个屏幕可用区域

### 浏览器兼容性

- 支持所有现代浏览器
- 自动处理弹窗拦截情况
- 提供友好的错误提示

### 安全考虑

- 弹窗模式继承原页面的认证状态
- 支持同源策略检查
- 窗口间通信安全隔离

---

**功能更新时间**: 2024-12-19  
**文档版本**: v1.1 