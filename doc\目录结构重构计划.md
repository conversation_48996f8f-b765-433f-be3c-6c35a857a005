# OnlyOffice Backend目录结构重构计划

## 📋 当前问题分析

### 1. Backend配置文件散乱问题
```
❌ 当前混乱的配置文件分布：
backend/src/config/
├── env.config.ts                    # 环境变量配置 ✅
├── app.config.ts                    # 应用配置 ✅  
├── hybrid-config.service.ts         # ❌ 应该在 modules/config/services/
├── hybrid-config.controller.ts      # ❌ 应该在 modules/config/controllers/
└── README.md                        # ✅

backend/src/modules/config/
├── config.module.ts                 # ✅
├── controllers/                     # ✅ 但缺少hybrid-config.controller.ts
├── services/                        # ✅ 但缺少hybrid-config.service.ts
├── interfaces/                      # ✅
└── dto/                            # ✅
```

### 2. 重要约束条件
- ✅ **根目录老项目文件完全不动**（routes/, middleware/, services/, views/, app.js, server.js等）
- ✅ **只重构backend/和frontend/目录内部结构**
- ✅ **保持系统正常运行，热更新不中断**

## 🎯 重构目标（仅限Backend/Frontend）

### 1. 清晰的Backend配置管理层次
```
✅ 重构后的backend配置结构：
backend/src/config/                   # 基础配置层（环境变量、应用配置）
├── env.config.ts                    # 环境变量管理
├── app.config.ts                    # 应用基础配置
└── README.md                        # 配置说明文档

backend/src/modules/config/           # 配置业务模块（数据库配置、JWT等）
├── config.module.ts                 # 模块定义
├── controllers/
│   ├── config-template.controller.ts
│   ├── jwt-config.controller.ts
│   ├── system-config.controller.ts
│   └── hybrid-config.controller.ts  # 🎯 移动到这里
├── services/
│   ├── config-template.service.ts
│   ├── jwt-config.service.ts
│   ├── system-config.service.ts
│   ├── config.service.ts
│   ├── onlyoffice-jwt.service.ts
│   └── hybrid-config.service.ts     # 🎯 移动到这里
├── interfaces/
├── dto/
└── config.validation.ts
```

### 2. Backend静态资源整理
```
✅ 当前backend目录：
backend/
├── src/                            # 源代码 ✅
├── dist/                           # 编译输出 ✅
├── public/                         # 静态资源 ✅
├── uploads/                        # 🎯 上传文件目录
├── tmp/                           # 🎯 临时文件目录
└── 其他配置文件                     # ✅

优化方案：
backend/
├── src/                            # 源代码
├── dist/                           # 编译输出  
├── public/                         # 静态资源
│   ├── uploads/                    # 🎯 移动到这里
│   ├── tmp/                       # 🎯 移动到这里
│   └── assets/                     # 其他静态资源
└── 其他配置文件
```

## 🚀 重构步骤

### 第一步：移动混合配置文件（不影响运行）
1. 将 `backend/src/config/hybrid-config.service.ts` 移动到 `backend/src/modules/config/services/`
2. 将 `backend/src/config/hybrid-config.controller.ts` 移动到 `backend/src/modules/config/controllers/`
3. 更新 `backend/src/modules/config/config.module.ts` 中的导入路径

### 第二步：整理Backend静态资源目录
1. 将 `backend/uploads/` 移动到 `backend/public/uploads/`
2. 将 `backend/tmp/` 移动到 `backend/public/tmp/`
3. 更新相关代码中的路径引用

### 第三步：验证系统功能
1. 确保热更新正常工作
2. 测试配置API接口
3. 验证文件上传下载功能

## 📋 重构后的Backend目录结构

```
backend/
├── src/
│   ├── config/                     # 基础配置层
│   │   ├── env.config.ts          # ✅ 环境变量管理
│   │   ├── app.config.ts          # ✅ 应用基础配置
│   │   └── README.md              # ✅ 配置说明
│   ├── modules/                    # 业务模块层
│   │   ├── config/                # 配置业务模块
│   │   │   ├── config.module.ts   # ✅ 模块定义
│   │   │   ├── controllers/       # 配置控制器
│   │   │   │   ├── config-template.controller.ts
│   │   │   │   ├── jwt-config.controller.ts
│   │   │   │   ├── system-config.controller.ts
│   │   │   │   └── hybrid-config.controller.ts  # 🎯 新位置
│   │   │   ├── services/          # 配置服务
│   │   │   │   ├── config-template.service.ts
│   │   │   │   ├── jwt-config.service.ts
│   │   │   │   ├── system-config.service.ts
│   │   │   │   ├── config.service.ts
│   │   │   │   ├── onlyoffice-jwt.service.ts
│   │   │   │   └── hybrid-config.service.ts     # 🎯 新位置
│   │   │   ├── interfaces/
│   │   │   ├── dto/
│   │   │   └── config.validation.ts
│   │   ├── auth/                  # 认证模块
│   │   ├── users/                 # 用户模块
│   │   ├── documents/             # 文档模块
│   │   ├── editor/                # 编辑器模块
│   │   ├── upload/                # 上传模块
│   │   ├── filenet/               # FileNet模块
│   │   ├── templates/             # 模板模块
│   │   ├── database/              # 数据库模块
│   │   └── health/                # 健康检查模块
│   ├── shared/                     # 共享组件
│   ├── common/                     # 通用工具
│   └── main.ts                    # 应用入口
├── public/                         # 静态资源目录
│   ├── uploads/                   # 🎯 上传文件（从根目录移动）
│   ├── tmp/                       # 🎯 临时文件（从根目录移动）
│   └── assets/                    # 其他静态资源
├── dist/                          # 编译输出
├── doc/                           # Backend文档
├── scripts/                       # Backend脚本
├── package.json                   # Backend依赖
├── tsconfig.json                  # TypeScript配置
├── nest-cli.json                  # NestJS配置
└── webpack.config.js              # Webpack配置
```

## 📋 Frontend目录结构规划（待后续整理）

```
frontend/
├── src/
│   ├── components/                # Vue组件
│   ├── views/                     # 页面视图
│   ├── router/                    # 路由配置
│   ├── store/                     # 状态管理
│   ├── api/                       # API接口
│   ├── utils/                     # 工具函数
│   ├── types/                     # TypeScript类型定义
│   ├── assets/                    # 静态资源
│   └── main.ts                    # 应用入口
├── public/                        # 公共静态资源
├── dist/                          # 构建输出
├── package.json                   # Frontend依赖
├── vite.config.ts                 # Vite配置
└── tsconfig.json                  # TypeScript配置
```

## ⚠️ 重构约束和注意事项

1. **绝对不动根目录文件**：routes/, middleware/, services/, views/, app.js, server.js等老项目文件完全保留
2. **渐进式重构**：每次只移动少量文件，确保系统正常运行
3. **热更新保护**：重构过程中保持开发服务器运行
4. **路径更新**：移动文件后立即更新所有相关的导入路径
5. **功能验证**：每个步骤完成后都要测试相关功能

## 🎯 预期收益

1. **清晰的模块化结构**：配置相关文件集中在config模块内
2. **符合NestJS最佳实践**：业务逻辑按模块组织
3. **易于维护**：相关文件集中管理，减少查找时间
4. **保持向后兼容**：不影响现有功能和老项目文件
5. **提高开发效率**：结构清晰，新功能开发更加便利

## 🚀 立即执行计划

接下来将按照以下顺序执行重构：
1. 移动hybrid-config.service.ts到正确位置
2. 移动hybrid-config.controller.ts到正确位置  
3. 更新config.module.ts的导入路径
4. 验证系统功能正常 