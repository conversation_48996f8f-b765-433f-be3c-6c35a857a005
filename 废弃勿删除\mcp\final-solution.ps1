# MCP MySQL 最终解决方案
Write-Host "========================================" -ForegroundColor Green
Write-Host "    MCP MySQL 最终解决方案" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "当前配置验证..." -ForegroundColor Yellow
Write-Host ""

# 1. 检查当前配置
Write-Host "1. 检查Cursor配置文件..." -ForegroundColor Cyan
if (Test-Path ".cursor\mcp.json") {
    $config = Get-Content ".cursor\mcp.json" | ConvertFrom-Json
    Write-Host "   ✓ 配置文件存在" -ForegroundColor Green
    Write-Host "   服务器名称: mysql" -ForegroundColor White
    Write-Host "   命令: $($config.mcpServers.mysql.command)" -ForegroundColor White
    Write-Host "   脚本路径: $($config.mcpServers.mysql.args[0])" -ForegroundColor White
    Write-Host "   MySQL主机: $($config.mcpServers.mysql.env.MYSQL_HOST)" -ForegroundColor White
    Write-Host "   MySQL数据库: $($config.mcpServers.mysql.env.MYSQL_DB)" -ForegroundColor White
} else {
    Write-Host "   ✗ 配置文件不存在" -ForegroundColor Red
}

# 2. 检查文件是否存在
Write-Host ""
Write-Host "2. 检查MCP服务器文件..." -ForegroundColor Cyan
$mcpPath = "C:\Users\<USER>\AppData\Roaming\npm\node_modules\@benborla29\mcp-server-mysql\dist\index.js"
if (Test-Path $mcpPath) {
    Write-Host "   ✓ MCP服务器文件存在" -ForegroundColor Green
} else {
    Write-Host "   ✗ MCP服务器文件不存在" -ForegroundColor Red
    Write-Host "   路径: $mcpPath" -ForegroundColor Yellow
}

# 3. 检查Node.js
Write-Host ""
Write-Host "3. 检查Node.js..." -ForegroundColor Cyan
$nodePath = "D:\Program Files\nodejs\node.exe"
if (Test-Path $nodePath) {
    Write-Host "   ✓ Node.js可执行文件存在" -ForegroundColor Green
} else {
    Write-Host "   ✗ Node.js可执行文件不存在" -ForegroundColor Red
    Write-Host "   路径: $nodePath" -ForegroundColor Yellow
}

# 4. 测试MySQL连接
Write-Host ""
Write-Host "4. 测试MySQL连接..." -ForegroundColor Cyan
$connection = Test-NetConnection -ComputerName "*************" -Port 3306 -WarningAction SilentlyContinue
if ($connection.TcpTestSucceeded) {
    Write-Host "   ✓ MySQL连接正常" -ForegroundColor Green
} else {
    Write-Host "   ✗ MySQL连接失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           使用说明" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "📋 步骤1: 重启Cursor" -ForegroundColor Yellow
Write-Host "   1. 完全关闭Cursor IDE" -ForegroundColor White
Write-Host "   2. 重新启动Cursor" -ForegroundColor White
Write-Host "   3. 打开您的项目" -ForegroundColor White
Write-Host ""

Write-Host "📋 步骤2: 测试MCP连接" -ForegroundColor Yellow
Write-Host "   在Cursor聊天中尝试以下命令:" -ForegroundColor White
Write-Host "   • @mysql" -ForegroundColor Cyan
Write-Host "   • 显示onlyfile数据库中的所有表" -ForegroundColor Cyan
Write-Host "   • @mysql 查询users表的结构" -ForegroundColor Cyan
Write-Host "   • 查询users表的前10条记录" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 步骤3: 检查MCP状态" -ForegroundColor Yellow
Write-Host "   在Cursor中:" -ForegroundColor White
Write-Host "   1. 按 Ctrl+Shift+P" -ForegroundColor Gray
Write-Host "   2. 搜索 'MCP'" -ForegroundColor Gray
Write-Host "   3. 查看MCP Tools面板" -ForegroundColor Gray
Write-Host "   4. 确认mysql服务显示为已启用" -ForegroundColor Gray
Write-Host ""

Write-Host "🔧 故障排除:" -ForegroundColor Yellow
Write-Host ""
Write-Host "如果@mysql不可用:" -ForegroundColor White
Write-Host "   1. 检查Cursor的输出面板是否有错误" -ForegroundColor Gray
Write-Host "   2. 查看MCP相关的日志" -ForegroundColor Gray
Write-Host "   3. 确认MySQL服务器正在运行" -ForegroundColor Gray
Write-Host "   4. 尝试备用配置（见下方）" -ForegroundColor Gray
Write-Host ""

Write-Host "🔄 备用配置:" -ForegroundColor Yellow
Write-Host ""
Write-Host "如果当前配置不工作，可以尝试以下配置:" -ForegroundColor White

# 创建备用配置
$alternativeConfig = @{
    mcpServers = @{
        mysql = @{
            command = "npx"
            args = @("-y", "@benborla29/mcp-server-mysql")
            env = @{
                MYSQL_HOST = "*************"
                MYSQL_PORT = "3306"
                MYSQL_USER = "onlyfile_user"
                MYSQL_PASS = "0nlyF!le`$ecure#123"
                MYSQL_DB = "onlyfile"
                ALLOW_INSERT_OPERATION = "true"
                ALLOW_UPDATE_OPERATION = "true"
                ALLOW_DELETE_OPERATION = "false"
                MYSQL_ENABLE_LOGGING = "true"
            }
        }
    }
}

$alternativeConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath ".cursor\mcp-alternative.json" -Encoding UTF8
Write-Host "   备用配置已保存到: .cursor\mcp-alternative.json" -ForegroundColor Cyan
Write-Host "   如需使用，请将其内容复制到 .cursor\mcp.json" -ForegroundColor Gray

Write-Host ""
Write-Host "📞 技术支持:" -ForegroundColor Yellow
Write-Host "   如果问题仍然存在，请提供以下信息:" -ForegroundColor White
Write-Host "   • Cursor版本" -ForegroundColor Gray
Write-Host "   • 错误日志（从Cursor输出面板）" -ForegroundColor Gray
Write-Host "   • MCP Tools面板的截图" -ForegroundColor Gray
Write-Host ""

Write-Host "🎉 预期结果:" -ForegroundColor Green
Write-Host "   成功配置后，您应该能够:" -ForegroundColor White
Write-Host "   • 在聊天中看到@mysql选项" -ForegroundColor Gray
Write-Host "   • 直接查询数据库" -ForegroundColor Gray
Write-Host "   • 获取表结构信息" -ForegroundColor Gray
Write-Host "   • 执行SELECT、INSERT、UPDATE查询" -ForegroundColor Gray
Write-Host ""

Write-Host "配置验证完成！请按照上述步骤操作。" -ForegroundColor Green
