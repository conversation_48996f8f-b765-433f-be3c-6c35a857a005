<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置模板系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-section h3 {
            color: #333;
            margin-top: 0;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background: #0056b3;
        }

        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }

        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        /* 示例链接样式 */
        .example-links {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .link-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .link-list li {
            margin-bottom: 15px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .link-list.deprecated li {
            border-left-color: #ffc107;
            background: #fff9e6;
        }

        .link-list li strong {
            display: block;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .link-list li a {
            color: #007bff;
            text-decoration: none;
            font-family: monospace;
            font-size: 14px;
            display: block;
            margin: 5px 0;
            padding: 4px 8px;
            background: #f1f3f4;
            border-radius: 4px;
            word-break: break-all;
        }

        .link-list li a:hover {
            background: #e9ecef;
            text-decoration: underline;
        }

        .link-list li .description {
            display: block;
            color: #6c757d;
            font-size: 13px;
            margin-top: 5px;
            font-style: italic;
        }

        .note-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .note-box h4 {
            color: #004085;
            margin-bottom: 10px;
        }

        .note-box ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .note-box li {
            margin-bottom: 8px;
            color: #004085;
        }

        .example-links h4 {
            color: #2c3e50;
            margin: 20px 0 10px 0;
            font-size: 16px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>配置模板系统测试</h1>
        <p>这个页面用于测试配置模板系统的各种功能</p>

        <div class="test-section">
            <h3>1. 获取所有配置模板</h3>
            <button onclick="testGetAllTemplates()">获取模板列表</button>
            <div id="result1" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取配置分组信息</h3>
            <button onclick="testGetConfigGroups()">获取配置分组</button>
            <div id="result2" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 获取特定模板详情</h3>
            <button onclick="testGetTemplate('default-edit')">获取默认模板</button>
            <button onclick="testGetTemplate('legal-edit')">获取法务模板</button>
            <button onclick="testGetTemplate('employee-readonly')">获取只读模板</button>
            <button onclick="testGetTemplate('simple-edit')">获取简化模板</button>
            <div id="result3" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试配置预览（需要文件ID）</h3>
            <input type="text" id="fileIdInput" placeholder="输入文件ID..." style="padding: 8px; width: 300px;">
            <button onclick="testConfigPreview()">测试配置预览</button>
            <div id="result4" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. 测试编辑器URL构建</h3>
            <p>测试不同配置模板的编辑器URL:</p>
            <div style="background: #e7f3ff; padding: 15px; border-radius: 6px; margin: 10px 0;">
                <p><strong>💡 提示：</strong>如果您在上方"测试配置预览"中输入了文件ID，点击下面的按钮将使用该ID生成URL。如果没有输入，将使用默认的测试ID。</p>
            </div>
            <button onclick="generateEditorUrls()">生成测试URL</button>
            <div id="result5" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 模板使用示例</h3>
            <p>以下是如何在编辑器URL中使用配置模板的示例：</p>

            <div class="note-box" style="margin-bottom: 20px;">
                <h4>💡 如何获取文件ID：</h4>
                <ul>
                    <li><strong>测试用途</strong>：可以使用 <code>sample-file-id-12345</code> 作为测试文件ID</li>
                    <li><strong>实际文档</strong>：在主页面文档列表中，每个文档都有一个唯一的UUID作为文件ID</li>
                    <li><strong>输入框测试</strong>：在上方"测试配置预览"部分输入文件ID后，"测试编辑器URL构建"会自动使用该ID</li>
                </ul>
            </div>

            <div class="example-links">
                <h4>🎯 推荐使用方式（模板ID）：</h4>
                <ul class="link-list">
                    <li>
                        <strong>default-edit (默认编辑版)：</strong>
                        <a href="/editor/sample-file-id-12345?template=default-edit" target="_blank">
                            /editor/sample-file-id-12345?template=default-edit
                        </a>
                        <span class="description">标准编辑功能，包含基本编辑工具</span>
                    </li>
                    <li>
                        <strong>legal-edit (法务编辑版)：</strong>
                        <a href="/editor/sample-file-id-12345?template=legal-edit" target="_blank">
                            /editor/sample-file-id-12345?template=legal-edit
                        </a>
                        <span class="description">完整功能，包含审阅和协作工具</span>
                    </li>
                    <li>
                        <strong>employee-readonly (员工只读版)：</strong>
                        <a href="/editor/sample-file-id-12345?template=employee-readonly" target="_blank">
                            /editor/sample-file-id-12345?template=employee-readonly
                        </a>
                        <span class="description">仅查看权限，适合文档分发</span>
                    </li>
                    <li>
                        <strong>simple-edit (功能简化版)：</strong>
                        <a href="/editor/sample-file-id-12345?template=simple-edit" target="_blank">
                            /editor/sample-file-id-12345?template=simple-edit
                        </a>
                        <span class="description">专注编辑，隐藏干扰功能</span>
                    </li>
                </ul>

                <h4>⚠️ 不推荐使用方式（中文名称，需要URL编码）：</h4>
                <ul class="link-list deprecated">
                    <li>
                        <strong>法务编辑版（中文）：</strong>
                        <a href="/editor/sample-file-id-12345?template=%E6%B3%95%E5%8A%A1%E7%BC%96%E8%BE%91%E7%89%88"
                            target="_blank">
                            /editor/sample-file-id-12345?template=法务编辑版
                        </a>
                        <span class="description">需要URL编码，不推荐在生产环境使用</span>
                    </li>
                </ul>

                <h4>🎛️ 带参数覆盖的示例：</h4>
                <ul class="link-list">
                    <li>
                        <strong>隐藏聊天功能：</strong>
                        <a href="/editor/sample-file-id-12345?template=default-edit&hideChat=true" target="_blank">
                            /editor/sample-file-id-12345?template=default-edit&hideChat=true
                        </a>
                    </li>
                    <li>
                        <strong>设置只读模式：</strong>
                        <a href="/editor/sample-file-id-12345?template=legal-edit&readonly=true" target="_blank">
                            /editor/sample-file-id-12345?template=legal-edit&readonly=true
                        </a>
                    </li>
                    <li>
                        <strong>多个参数组合：</strong>
                        <a href="/editor/sample-file-id-12345?template=simple-edit&hideComments=true&hideChat=true"
                            target="_blank">
                            /editor/sample-file-id-12345?template=simple-edit&hideComments=true&hideChat=true
                        </a>
                    </li>
                </ul>
            </div>

            <div class="note-box">
                <h4>💡 使用提示：</h4>
                <ul>
                    <li><strong>模板ID</strong>：推荐使用英文模板ID（如 default-edit），更稳定且不需要URL编码</li>
                    <li><strong>中文名称</strong>：虽然支持，但需要URL编码，容易出错</li>
                    <li><strong>参数覆盖</strong>：可以通过URL参数实时调整特定配置</li>
                    <li><strong>测试文件</strong>：sample-file-id-12345 是专门的测试文件ID</li>
                    <li><strong>动态测试</strong>：在上方输入实际文件ID后，点击"生成测试URL"可生成对应的编辑器链接</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function testGetAllTemplates() {
            const resultDiv = document.getElementById('result1');
            try {
                const response = await fetch('/api/config-templates');
                const data = await response.json();

                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        async function testGetConfigGroups() {
            const resultDiv = document.getElementById('result2');
            try {
                const response = await fetch('/api/config-templates/meta/groups');
                const data = await response.json();

                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        async function testGetTemplate(templateId) {
            const resultDiv = document.getElementById('result3');
            try {
                const response = await fetch(`/api/config-templates/${templateId}`);
                const data = await response.json();

                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        async function testConfigPreview() {
            const resultDiv = document.getElementById('result4');
            const fileId = document.getElementById('fileIdInput').value;

            if (!fileId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入文件ID';
                return;
            }

            try {
                const response = await fetch(`/api/config-templates/default-edit/preview`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ fileId: fileId })
                });
                const data = await response.json();

                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        function generateEditorUrls() {
            const resultDiv = document.getElementById('result5');
            const baseUrl = window.location.origin;

            // 从文件ID输入框获取用户输入的ID，如果为空则使用默认值
            const fileIdInput = document.getElementById('fileIdInput');
            const fileId = fileIdInput && fileIdInput.value.trim() ? fileIdInput.value.trim() : 'sample-file-id-12345';

            const urls = [
                `${baseUrl}/editor/${fileId}`,
                `${baseUrl}/editor/${fileId}?template=default-edit`,
                `${baseUrl}/editor/${fileId}?template=legal-edit`,
                `${baseUrl}/editor/${fileId}?template=employee-readonly`,
                `${baseUrl}/editor/${fileId}?template=simple-edit`,
                `${baseUrl}/editor/${fileId}?template=default-edit&hideChat=true`,
                `${baseUrl}/editor/${fileId}?template=legal-edit&readonly=true&hideComments=true`,
                `${baseUrl}/config-templates`
            ];

            resultDiv.className = 'result success';

            // 添加使用的文件ID说明
            const fileIdNote = fileIdInput && fileIdInput.value.trim()
                ? `使用您输入的文件ID: ${fileId}`
                : `使用默认测试文件ID: ${fileId}`;

            resultDiv.innerHTML = `📁 ${fileIdNote}\n\n` + urls.map(url =>
                `<a href="${url}" target="_blank">${url}</a>`
            ).join('\n\n');
        }

        // 页面加载时自动测试基本功能
        window.addEventListener('load', function () {
            setTimeout(() => {
                testGetAllTemplates();
            }, 1000);
        });
    </script>
</body>

</html>