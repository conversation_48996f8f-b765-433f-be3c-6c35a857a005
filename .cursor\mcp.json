{"mcpServers": {"mysql": {"command": "D:\\Program Files\\nodejs\\node.exe", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@benborla29\\mcp-server-mysql\\dist\\index.js"], "env": {"MYSQL_HOST": "*************", "MYSQL_PORT": "3306", "MYSQL_USER": "onlyfile_user", "MYSQL_PASS": "0nlyF!le$ecure#123", "MYSQL_DB": "onlyfile", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "false", "MYSQL_ENABLE_LOGGING": "true", "PATH": "D:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Windows\\System32;C:\\Windows", "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"}}}}