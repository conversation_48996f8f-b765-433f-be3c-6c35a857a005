# OnlyOffice集成系统 2.0

> 基于OnlyOffice的现代化文档管理系统，采用前后端分离架构  
> 🚀 **前端开发已完成95%+** | 🔄 **后端开发进行中** | 📋 **API集成待完成**

## 📋 项目概述

OnlyOffice集成系统是一个企业级文档管理平台，提供完整的文档编辑、存储、权限管理解决方案。系统采用现代化的技术栈，支持与IBM FileNet的无缝集成。

### 🎯 主要功能

- **文档管理**: 创建、编辑、预览、版本控制
- **在线编辑**: 集成OnlyOffice编辑器，支持Word、Excel、PowerPoint
- **模板系统**: 文档模板和配置模板管理
- **用户管理**: 多角色权限控制（管理员、编辑员、查看员）
- **文件存储**: 支持本地存储和FileNet企业内容管理
- **系统配置**: 灵活的配置管理和环境设置

## 🏗️ 系统架构

```
OnlyOffice集成系统/
├── frontend/          # Vue 3 + TypeScript 前端应用
├── backend/           # NestJS + TypeScript 后端API  
├── doc/              # 项目文档
└── 老项目/            # 历史版本代码（仅供参考）
```

### 技术栈

#### 前端技术栈
- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **UI库**: Ant Design Vue 4.0
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier

#### 后端技术栈  
- **框架**: NestJS
- **语言**: TypeScript
- **数据库**: MySQL 8.0
- **缓存**: Node-cache (可扩展Redis)
- **事件处理**: EventEmitter (可扩展RabbitMQ)
- **文档**: Swagger自动生成
- **测试**: Jest

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- OnlyOffice Document Server

### 安装步骤

1. **克隆项目**
```bash
git clone <项目地址>
cd OnlyOffice
```

2. **安装依赖**
```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖  
cd ../backend
npm install
```

3. **环境配置**
```bash
# 复制环境配置文件
cp .env.example .env

# 配置数据库连接、OnlyOffice服务器等参数
```

4. **启动服务**
```bash
# 启动后端服务 (端口: 3000)
cd backend
npm run start:dev

# 启动前端服务 (端口: 8080)  
cd frontend
npm run dev
```

5. **访问系统**
- 前端应用: http://localhost:8080
- 后端API: http://localhost:3000
- API文档: http://localhost:3000/api

### 默认账户

- 用户名: `admin`
- 密码: `admin123`

## 📁 项目结构

### 前端目录结构
```
frontend/
├── src/
│   ├── components/     # 公共组件
│   ├── pages/         # 页面组件
│   │   ├── Auth/      # 登录页面
│   │   ├── Dashboard/ # 仪表板
│   │   ├── Documents/ # 文档管理
│   │   ├── Templates/ # 模板管理
│   │   ├── Config/    # 系统配置
│   │   ├── Users/     # 用户管理
│   │   └── Error/     # 错误页面
│   ├── router/        # 路由配置
│   ├── services/      # API服务
│   ├── stores/        # Pinia状态管理
│   ├── styles/        # 全局样式
│   └── utils/         # 工具函数
├── public/            # 静态资源
└── dist/              # 构建输出
```

### 后端目录结构
```
backend/
├── src/
│   ├── controllers/   # 控制器
│   ├── services/      # 业务逻辑
│   ├── models/        # 数据模型
│   ├── middleware/    # 中间件
│   ├── config/        # 配置文件
│   ├── utils/         # 工具函数
│   └── types/         # 类型定义
├── test/              # 测试文件
└── dist/              # 构建输出
```

## 🎨 功能特性

### ✅ 已完成功能

#### 前端界面 (95%+)
- [x] 用户登录认证
- [x] 仪表板统计
- [x] 文档管理界面
- [x] 模板管理系统
- [x] 系统配置页面
- [x] 用户权限管理
- [x] 响应式设计
- [x] 路由守卫
- [x] API集成架构

#### 后端API (进行中)
- [x] 基础项目架构
- [x] 数据库连接
- [x] 用户认证模块
- [x] 控制器路由
- [ ] 文档CRUD操作
- [ ] 文件上传下载
- [ ] OnlyOffice集成
- [ ] FileNet连接

### 🔄 开发进度

- **前端开发**: ✅ 完成 (95%+)
- **后端API**: 🔄 进行中 (40%)
- **OnlyOffice集成**: ⏳ 计划中
- **FileNet集成**: ⏳ 计划中
- **系统测试**: ⏳ 计划中

## 🔧 开发指南

### 代码规范

项目严格遵循[代码规范和命名标准](doc/代码规范和命名标准.md)，包括:

- **文件命名**: kebab-case目录，PascalCase组件
- **变量命名**: camelCase变量，SCREAMING_SNAKE_CASE常量
- **函数命名**: camelCase + 动词开头
- **代码质量**: ESLint + Prettier + Jest

### 开发流程

1. 创建功能分支: `feature/功能名称`
2. 编写代码并通过ESLint检查
3. 编写单元测试
4. 提交代码: `feat: 功能描述`
5. 发起Pull Request
6. 代码审查通过后合并

### 配置管理

系统采用混合配置管理策略：
- **数据库配置**: 大部分配置存储在数据库中，支持运行时动态修改
- **环境变量**: 数据库连接等敏感配置通过环境变量管理
- **配置验证**: 提供配置检查和验证工具

```bash
# 检查配置状态
cd backend
npm run config:check

# 验证配置迁移
npm run config:verify
```

**配置管理API**:
- `GET /api/config/hybrid/check` - 配置检查报告
- `GET /api/config/hybrid/stats` - 配置统计信息
- `GET /api/config/hybrid/sources` - 配置来源信息
- `POST /api/config/hybrid/refresh-cache` - 刷新配置缓存

详细配置说明请参考：[环境配置说明.md](doc/环境配置说明.md)

## 📚 API文档

后端API采用RESTful设计，使用Swagger自动生成文档：

- 在线文档: http://localhost:3000/api
- 接口总数: 50+ 个API接口
- 认证方式: JWT Token
- 数据格式: JSON

### 主要API模块

- 用户认证: `/api/auth`
- 文档管理: `/api/documents`
- 模板管理: `/api/templates`
- 用户管理: `/api/users`
- 系统配置: `/api/config`

## 🔌 集成说明

### OnlyOffice Document Server

系统集成OnlyOffice编辑器，支持：
- Word文档 (.docx)
- Excel表格 (.xlsx)  
- PowerPoint演示 (.pptx)
- PDF查看

### IBM FileNet集成

支持企业内容管理：
- 文档存储到FileNet
- 版本控制
- 权限管理
- 审计跟踪

## 🧪 测试

### 前端测试
```bash
cd frontend
npm run test        # 运行单元测试
npm run test:e2e    # 运行端到端测试
```

### 后端测试
```bash
cd backend
npm run test        # 单元测试
npm run test:cov    # 测试覆盖率
```

## 📦 部署

### 构建生产版本
```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd backend  
npm run build
```

### Docker部署
```bash
# 使用Docker Compose
docker-compose up -d
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 发起Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [维护者信息]
- 邮箱: [邮箱地址]
- 项目地址: [Git仓库地址]

---

## 📈 项目状态

| 模块 | 状态 | 进度 | 说明 |
|------|------|------|------|
| 前端界面 | ✅ 完成 | 95% | 所有页面组件已完成 |
| 后端API | 🔄 开发中 | 95% | 基础架构已搭建 |
| 数据库 | 🔄 开发中 | 60% | 表结构基本完成 |
| OnlyOffice集成 | ⏳ 待开始 | 0% | 等待后端API完成 |
| FileNet集成 | ⏳ 待开始 | 50% | 等待后端API完成 |
| 系统测试 | ⏳ 待开始 | 0% | 等待功能开发完成 |

> **最后更新**: 2024年12月19日  
> **当前版本**: v2.0.0-dev 