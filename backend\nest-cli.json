{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "webpackConfigPath": "webpack.config.js", "tsConfigPath": "tsconfig.build.json", "assets": ["**/*.json", "**/*.md"], "watchAssets": true}, "generateOptions": {"spec": true, "flat": false}, "monorepo": false, "root": ".", "entryFile": "main", "exec": "node"}