<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 明亮经典风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f5f7fa;
      color: #2c3e50;
      line-height: 1.6;
    }

    .layout-container {
      display: flex;
      min-height: 100vh;
    }

    /* 左侧导航栏 */
    .sidebar {
      width: 260px;
      background: #ffffff;
      border-right: 1px solid #e8eaec;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 1000;
    }

    .logo-section {
      padding: 20px;
      border-bottom: 1px solid #e8eaec;
      text-align: center;
    }

    .logo {
      font-size: 24px;
      font-weight: 700;
      color: #1976d2;
      margin-bottom: 8px;
    }

    .logo-subtitle {
      font-size: 12px;
      color: #666;
    }

    .nav-menu {
      padding: 20px 0;
    }

    .nav-group {
      margin-bottom: 24px;
    }

    .nav-group-title {
      padding: 0 20px 8px;
      font-size: 12px;
      color: #999;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: #4a5568;
      text-decoration: none;
      transition: all 0.3s ease;
      border-left: 3px solid transparent;
    }

    .nav-item:hover, .nav-item.active {
      background: #f0f8ff;
      color: #1976d2;
      border-left-color: #1976d2;
    }

    .nav-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      font-size: 16px;
      text-align: center;
    }

    .nav-text {
      font-size: 14px;
      font-weight: 500;
    }

    .nav-badge {
      margin-left: auto;
      background: #ff4757;
      color: white;
      font-size: 11px;
      padding: 2px 6px;
      border-radius: 10px;
      min-width: 18px;
      text-align: center;
    }

    /* 主要内容区域 */
    .main-content {
      margin-left: 260px;
      flex: 1;
      background: #f5f7fa;
    }

    /* 顶部标题栏 */
    .header-bar {
      background: #ffffff;
      padding: 16px 24px;
      border-bottom: 1px solid #e8eaec;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      background: #f8f9fa;
      border-radius: 20px;
      border: 1px solid #e9ecef;
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 14px;
    }

    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: #2c3e50;
    }

    /* 内容区域 */
    .content-area {
      padding: 24px;
    }

    /* 系统状态卡片 */
    .status-overview {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 24px;
    }

    .status-card {
      background: #ffffff;
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #e8eaec;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .status-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .status-icon {
      width: 48px;
      height: 48px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .status-icon.documents { background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); color: #1976d2; }
    .status-icon.users { background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); color: #388e3c; }
    .status-icon.storage { background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%); color: #f57c00; }
    .status-icon.system { background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%); color: #c2185b; }

    .status-trend {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: 500;
    }

    .trend-up {
      background: #e8f5e8;
      color: #388e3c;
    }

    .trend-down {
      background: #ffebee;
      color: #d32f2f;
    }

    .status-value {
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .status-label {
      color: #7f8c8d;
      font-size: 14px;
      margin-bottom: 12px;
    }

    .status-details {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #95a5a6;
    }

    /* 主要功能区域 */
    .main-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
    }

    .content-card {
      background: #ffffff;
      border-radius: 12px;
      border: 1px solid #e8eaec;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: hidden;
    }

    .card-header {
      padding: 20px 24px;
      border-bottom: 1px solid #f0f1f2;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .card-action {
      color: #1976d2;
      font-size: 14px;
      cursor: pointer;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .card-action:hover {
      color: #1565c0;
    }

    /* 快捷操作 */
    .quick-actions {
      padding: 24px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
    }

    .action-item {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-item:hover {
      background: #e3f2fd;
      border-color: #1976d2;
      transform: translateY(-2px);
    }

    .action-icon {
      font-size: 32px;
      margin-bottom: 12px;
      display: block;
    }

    .action-label {
      color: #2c3e50;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .action-desc {
      color: #7f8c8d;
      font-size: 12px;
    }

    /* 文档列表 */
    .document-list {
      padding: 0 24px 24px;
    }

    .document-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #f0f1f2;
      transition: background 0.3s ease;
    }

    .document-item:hover {
      background: #f8f9fa;
      margin: 0 -24px;
      padding: 16px 24px;
    }

    .document-item:last-child {
      border-bottom: none;
    }

    .doc-icon {
      width: 44px;
      height: 44px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: white;
      font-weight: 600;
    }

    .doc-word { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); }
    .doc-excel { background: linear-gradient(135deg, #059669 0%, #047857 100%); }
    .doc-ppt { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
    .doc-pdf { background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%); }

    .doc-info {
      flex: 1;
    }

    .doc-name {
      font-size: 15px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .doc-meta {
      font-size: 13px;
      color: #7f8c8d;
    }

    .doc-actions {
      display: flex;
      gap: 8px;
    }

    .doc-btn {
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid;
    }

    .btn-primary {
      background: #1976d2;
      border-color: #1976d2;
      color: white;
    }

    .btn-primary:hover {
      background: #1565c0;
    }

    .btn-secondary {
      background: transparent;
      border-color: #e9ecef;
      color: #6c757d;
    }

    .btn-secondary:hover {
      background: #f8f9fa;
    }

    /* 侧边栏内容 */
    .sidebar-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* 用户信息 */
    .user-profile {
      padding: 24px;
      text-align: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .profile-avatar {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px;
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
      color: white;
    }

    .profile-name {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .profile-role {
      font-size: 14px;
      color: #7f8c8d;
      margin-bottom: 16px;
    }

    .profile-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }

    .stat-label {
      font-size: 12px;
      color: #7f8c8d;
    }

    /* 系统信息 */
    .system-info {
      padding: 20px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f1f2;
    }

    .info-item:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 14px;
      color: #7f8c8d;
    }

    .info-value {
      font-size: 14px;
      font-weight: 500;
      color: #2c3e50;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }

    .status-online { background: #28a745; }
    .status-warning { background: #ffc107; }
    .status-offline { background: #dc3545; }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar.open {
        transform: translateX(0);
      }
      
      .main-content {
        margin-left: 0;
      }
      
      .main-grid {
        grid-template-columns: 1fr;
      }
      
      .status-overview {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .actions-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .content-area {
        padding: 16px;
      }
      
      .status-overview {
        grid-template-columns: 1fr;
      }
      
      .actions-grid {
        grid-template-columns: 1fr;
      }
      
      .profile-stats {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="layout-container">
    <!-- 左侧导航栏 -->
    <nav class="sidebar">
      <div class="logo-section">
        <div class="logo">🏢 OnlyOffice</div>
        <div class="logo-subtitle">企业管理系统</div>
      </div>
      
      <div class="nav-menu">
        <div class="nav-group">
          <div class="nav-group-title">主要功能</div>
          <a href="#" class="nav-item active">
            <span class="nav-icon">🏠</span>
            <span class="nav-text">系统首页</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">📄</span>
            <span class="nav-text">文档管理</span>
            <span class="nav-badge">12</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">📋</span>
            <span class="nav-text">模板管理</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            <span class="nav-text">用户管理</span>
          </a>
        </div>
        
        <div class="nav-group">
          <div class="nav-group-title">系统配置</div>
          <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            <span class="nav-text">系统配置</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">🔐</span>
            <span class="nav-text">权限管理</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">🔔</span>
            <span class="nav-text">OnlyOffice配置</span>
          </a>
        </div>
        
        <div class="nav-group">
          <div class="nav-group-title">数据分析</div>
          <a href="#" class="nav-item">
            <span class="nav-icon">📊</span>
            <span class="nav-text">数据报表</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">📈</span>
            <span class="nav-text">统计分析</span>
          </a>
          <a href="#" class="nav-item">
            <span class="nav-icon">📋</span>
            <span class="nav-text">系统日志</span>
          </a>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 顶部标题栏 -->
      <div class="header-bar">
        <h1 class="page-title">📊 系统首页</h1>
        <div class="header-actions">
          <div class="user-info">
            <div class="user-avatar">管</div>
            <div class="user-name">系统管理员</div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 系统状态概览 -->
        <div class="status-overview">
          <div class="status-card">
            <div class="status-header">
              <div class="status-icon documents">📄</div>
              <div class="status-trend trend-up">↗ +15.3%</div>
            </div>
            <div class="status-value">1,247</div>
            <div class="status-label">文档总数</div>
            <div class="status-details">
              <span>本月新增: 156</span>
              <span>活跃: 892</span>
            </div>
          </div>

          <div class="status-card">
            <div class="status-header">
              <div class="status-icon users">👥</div>
              <div class="status-trend trend-up">↗ +8.7%</div>
            </div>
            <div class="status-value">89</div>
            <div class="status-label">活跃用户</div>
            <div class="status-details">
              <span>在线: 24</span>
              <span>本周: 67</span>
            </div>
          </div>

          <div class="status-card">
            <div class="status-header">
              <div class="status-icon storage">💾</div>
              <div class="status-trend trend-up">↗ +12.1%</div>
            </div>
            <div class="status-value">847GB</div>
            <div class="status-label">存储使用</div>
            <div class="status-details">
              <span>可用: 153GB</span>
              <span>使用率: 84.7%</span>
            </div>
          </div>

          <div class="status-card">
            <div class="status-header">
              <div class="status-icon system">❤️</div>
              <div class="status-trend trend-up">↗ +2.4%</div>
            </div>
            <div class="status-value">99.2%</div>
            <div class="status-label">系统可用性</div>
            <div class="status-details">
              <span>响应: 180ms</span>
              <span>故障: 0.1%</span>
            </div>
          </div>
        </div>

        <!-- 主要功能区域 -->
        <div class="main-grid">
          <!-- 左侧：快捷操作和文档列表 -->
          <div>
            <!-- 快捷操作 -->
            <div class="content-card">
              <div class="card-header">
                <h3 class="card-title">🚀 快捷操作</h3>
                <span class="card-action">自定义</span>
              </div>
              <div class="quick-actions">
                <div class="actions-grid">
                  <div class="action-item">
                    <span class="action-icon">📝</span>
                    <div class="action-label">创建文档</div>
                    <div class="action-desc">新建Word、Excel、PPT</div>
                  </div>
                  <div class="action-item">
                    <span class="action-icon">📤</span>
                    <div class="action-label">上传文件</div>
                    <div class="action-desc">批量上传本地文档</div>
                  </div>
                  <div class="action-item">
                    <span class="action-icon">📋</span>
                    <div class="action-label">模板管理</div>
                    <div class="action-desc">配置文档模板</div>
                  </div>
                  <div class="action-item">
                    <span class="action-icon">👥</span>
                    <div class="action-label">用户管理</div>
                    <div class="action-desc">管理系统用户</div>
                  </div>
                  <div class="action-item">
                    <span class="action-icon">📊</span>
                    <div class="action-label">数据报表</div>
                    <div class="action-desc">查看统计报告</div>
                  </div>
                  <div class="action-item">
                    <span class="action-icon">⚙️</span>
                    <div class="action-label">系统设置</div>
                    <div class="action-desc">配置系统参数</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 最近文档 -->
            <div class="content-card" style="margin-top: 20px;">
              <div class="card-header">
                <h3 class="card-title">📄 最近文档</h3>
                <span class="card-action">查看全部</span>
              </div>
              <div class="document-list">
                <div class="document-item">
                  <div class="doc-icon doc-word">W</div>
                  <div class="doc-info">
                    <div class="doc-name">项目需求分析文档.docx</div>
                    <div class="doc-meta">张三 · 2小时前 · 2.4MB</div>
                  </div>
                  <div class="doc-actions">
                    <button class="doc-btn btn-primary">编辑</button>
                    <button class="doc-btn btn-secondary">下载</button>
                  </div>
                </div>
                <div class="document-item">
                  <div class="doc-icon doc-excel">E</div>
                  <div class="doc-info">
                    <div class="doc-name">Q4财务报表.xlsx</div>
                    <div class="doc-meta">李四 · 1天前 · 1.8MB</div>
                  </div>
                  <div class="doc-actions">
                    <button class="doc-btn btn-primary">编辑</button>
                    <button class="doc-btn btn-secondary">下载</button>
                  </div>
                </div>
                <div class="document-item">
                  <div class="doc-icon doc-ppt">P</div>
                  <div class="doc-info">
                    <div class="doc-name">产品发布会演示.pptx</div>
                    <div class="doc-meta">王五 · 3天前 · 15.2MB</div>
                  </div>
                  <div class="doc-actions">
                    <button class="doc-btn btn-primary">编辑</button>
                    <button class="doc-btn btn-secondary">下载</button>
                  </div>
                </div>
                <div class="document-item">
                  <div class="doc-icon doc-pdf">P</div>
                  <div class="doc-info">
                    <div class="doc-name">用户操作手册.pdf</div>
                    <div class="doc-meta">赵六 · 1周前 · 8.9MB</div>
                  </div>
                  <div class="doc-actions">
                    <button class="doc-btn btn-secondary">预览</button>
                    <button class="doc-btn btn-secondary">下载</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧侧边栏 -->
          <div class="sidebar-content">
            <!-- 用户信息 -->
            <div class="content-card">
              <div class="user-profile">
                <div class="profile-avatar">管</div>
                <div class="profile-name">系统管理员</div>
                <div class="profile-role">Administrator</div>
                <div class="profile-stats">
                  <div class="stat-item">
                    <div class="stat-number">156</div>
                    <div class="stat-label">我的文档</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">24</div>
                    <div class="stat-label">协作项目</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">89</div>
                    <div class="stat-label">团队成员</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统信息 -->
            <div class="content-card">
              <div class="card-header">
                <h3 class="card-title">🔧 系统状态</h3>
                <span class="card-action">详情</span>
              </div>
              <div class="system-info">
                <div class="info-item">
                  <span class="info-label">数据库连接</span>
                  <span class="info-value">
                    <span class="status-dot status-online"></span>正常
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">OnlyOffice服务</span>
                  <span class="info-value">
                    <span class="status-dot status-online"></span>运行中
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">FileNet连接</span>
                  <span class="info-value">
                    <span class="status-dot status-warning"></span>警告
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">系统负载</span>
                  <span class="info-value">23%</span>
                </div>
                <div class="info-item">
                  <span class="info-label">内存使用</span>
                  <span class="info-value">67%</span>
                </div>
                <div class="info-item">
                  <span class="info-label">磁盘空间</span>
                  <span class="info-value">84%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // 导航菜单交互
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
        item.classList.add('active');
      });
    });

    // 快捷操作点击
    document.querySelectorAll('.action-item').forEach(item => {
      item.addEventListener('click', () => {
        const label = item.querySelector('.action-label').textContent;
        console.log('执行操作:', label);
      });
    });

    // 文档操作
    document.querySelectorAll('.doc-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('文档操作:', btn.textContent);
      });
    });

    // 移动端菜单切换
    function toggleSidebar() {
      const sidebar = document.querySelector('.sidebar');
      sidebar.classList.toggle('open');
    }

    // 响应式处理
    window.addEventListener('resize', () => {
      if (window.innerWidth > 1024) {
        document.querySelector('.sidebar').classList.remove('open');
      }
    });
  </script>
</body>
</html> 