<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice配置管理 - 暗色版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
            background: #0a0e1a;
            color: #e2e8f0;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1a202c;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a5568;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #718096;
        }

        .app-wrapper {
            min-height: 100vh;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1b3a 50%, #2d1b3d 100%);
        }

        /* 顶部栏 */
        .top-bar {
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid #2d3748;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .brand-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .brand-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #00d4ff, #ff00d4);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #0a0e1a;
            font-size: 16px;
        }

        .brand-text h1 {
            font-size: 20px;
            font-weight: 700;
            color: #f7fafc;
            margin-bottom: 2px;
        }

        .brand-text p {
            font-size: 12px;
            color: #a0aec0;
        }

        .top-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-family: inherit;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: #0a0e1a;
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 212, 255, 0.4);
        }

        .btn-ghost {
            background: transparent;
            color: #e2e8f0;
            border: 1px solid #4a5568;
        }

        .btn-ghost:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #718096;
        }

        .btn-icon {
            width: 36px;
            height: 36px;
            padding: 0;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 主要布局 */
        .main-container {
            display: grid;
            grid-template-columns: 360px 1fr;
            height: calc(100vh - 72px);
            overflow: hidden;
        }

        /* 左侧边栏 */
        .sidebar {
            background: rgba(26, 32, 44, 0.8);
            backdrop-filter: blur(20px);
            border-right: 1px solid #2d3748;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid #2d3748;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 700;
            color: #f7fafc;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-subtitle {
            font-size: 13px;
            color: #718096;
            margin-bottom: 20px;
        }

        .search-wrapper {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            background: rgba(45, 55, 72, 0.8);
            border: 1px solid #4a5568;
            border-radius: 8px;
            color: #e2e8f0;
            font-size: 13px;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #00d4ff;
            background: rgba(45, 55, 72, 1);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        }

        .search-input::placeholder {
            color: #718096;
        }

        .search-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-size: 14px;
        }

        .templates-container {
            flex: 1;
            overflow-y: auto;
            padding: 0 24px 24px;
        }

        .template-group {
            margin-top: 24px;
        }

        .group-label {
            font-size: 11px;
            font-weight: 600;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 12px;
            padding: 0 4px;
        }

        .template-item {
            background: rgba(45, 55, 72, 0.6);
            border: 1px solid transparent;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .template-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background: linear-gradient(45deg, #00d4ff, #ff00d4);
            border-radius: 2px;
            transition: height 0.3s ease;
        }

        .template-item:hover {
            background: rgba(45, 55, 72, 0.9);
            border-color: #4a5568;
            transform: translateX(4px);
        }

        .template-item:hover::before {
            height: 32px;
        }

        .template-item.active {
            background: rgba(0, 212, 255, 0.1);
            border-color: #00d4ff;
            transform: translateX(4px);
        }

        .template-item.active::before {
            height: 32px;
        }

        .template-name {
            font-size: 14px;
            font-weight: 600;
            color: #f7fafc;
            margin-bottom: 6px;
        }

        .template-desc {
            font-size: 12px;
            color: #a0aec0;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .template-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .template-badges {
            display: flex;
            gap: 6px;
        }

        .badge {
            padding: 2px 8px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-default {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .badge-active {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .badge-inactive {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
            border: 1px solid rgba(108, 117, 125, 0.3);
        }

        .template-meta {
            font-size: 11px;
            color: #718096;
        }

        /* 右侧主要内容 */
        .main-content {
            background: rgba(10, 14, 26, 0.8);
            backdrop-filter: blur(20px);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: rgba(26, 32, 44, 0.9);
            border-bottom: 1px solid #2d3748;
            padding: 24px 32px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .content-title-section h2 {
            font-size: 24px;
            font-weight: 700;
            color: #f7fafc;
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 14px;
            color: #a0aec0;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 12px;
        }

        .btn-secondary {
            background: rgba(74, 85, 104, 0.8);
            color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: rgba(74, 85, 104, 1);
        }

        /* 配置标签 */
        .config-tabs {
            background: rgba(26, 32, 44, 0.6);
            padding: 0 32px;
            display: flex;
            gap: 4px;
            border-bottom: 1px solid #2d3748;
        }

        .tab-btn {
            padding: 16px 20px;
            background: transparent;
            border: none;
            color: #718096;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            font-family: inherit;
            position: relative;
        }

        .tab-btn::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20%;
            right: 20%;
            height: 2px;
            background: linear-gradient(45deg, #00d4ff, #ff00d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .tab-btn:hover {
            color: #e2e8f0;
            background: rgba(45, 55, 72, 0.5);
        }

        .tab-btn.active {
            color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }

        .tab-btn.active::after {
            opacity: 1;
        }

        /* 配置内容区域 */
        .config-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .config-section {
            margin-bottom: 40px;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .section-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0a0e1a;
            font-size: 20px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #f7fafc;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
            gap: 24px;
        }

        .config-card {
            background: rgba(26, 32, 44, 0.8);
            border: 1px solid #2d3748;
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .config-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .config-card:hover {
            border-color: #4a5568;
            background: rgba(26, 32, 44, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        }

        .config-card:hover::before {
            opacity: 1;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #f7fafc;
            margin-bottom: 6px;
        }

        .card-desc {
            font-size: 13px;
            color: #a0aec0;
            line-height: 1.4;
        }

        .required-tag {
            background: rgba(255, 0, 212, 0.2);
            color: #ff00d4;
            border: 1px solid rgba(255, 0, 212, 0.3);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .switch-controls {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .switch-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: rgba(45, 55, 72, 0.6);
            border-radius: 8px;
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .switch-row:hover {
            background: rgba(45, 55, 72, 0.8);
            border-color: #4a5568;
        }

        .switch-label {
            font-size: 13px;
            font-weight: 500;
            color: #e2e8f0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .switch-icon {
            font-size: 14px;
            color: #718096;
        }

        .toggle-switch {
            width: 48px;
            height: 24px;
            background: #2d3748;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #4a5568;
        }

        .toggle-switch.active {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
        }

        .toggle-thumb {
            width: 20px;
            height: 20px;
            background: #f7fafc;
            border-radius: 50%;
            position: absolute;
            top: 1px;
            left: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active .toggle-thumb {
            transform: translateX(24px);
            background: #0a0e1a;
        }

        .config-status {
            margin-top: 16px;
            padding: 12px 16px;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-enabled {
            background: #00d4ff;
            box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }

        .status-disabled {
            background: #6c757d;
        }

        .status-text {
            font-size: 12px;
            color: #00d4ff;
            font-weight: 500;
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: #718096;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #a0aec0;
        }

        .empty-desc {
            font-size: 14px;
            max-width: 300px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
                height: auto;
            }

            .sidebar {
                order: 2;
                border-right: none;
                border-top: 1px solid #2d3748;
                height: auto;
                max-height: 400px;
            }

            .main-content {
                order: 1;
                height: auto;
                min-height: 60vh;
            }
        }

        @media (max-width: 768px) {
            .top-bar {
                padding: 12px 16px;
            }

            .config-content {
                padding: 20px;
            }

            .content-header {
                padding: 20px;
                flex-direction: column;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: stretch;
            }

            .btn {
                flex: 1;
                justify-content: center;
            }

            .config-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .template-item {
            animation: slideInLeft 0.3s ease;
        }

        .config-card {
            animation: slideInRight 0.3s ease;
        }

        /* 滚动提示 */
        .scroll-indicator {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 4px;
            height: 60px;
            background: rgba(0, 212, 255, 0.2);
            border-radius: 2px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .scroll-indicator.visible {
            opacity: 1;
        }

        .scroll-thumb {
            width: 100%;
            background: #00d4ff;
            border-radius: 2px;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 顶部栏 -->
        <header class="top-bar">
            <div class="brand-section">
                <div class="brand-logo">OO</div>
                <div class="brand-text">
                    <h1>OnlyOffice Config Terminal</h1>
                    <p>Advanced Configuration Management</p>
                </div>
            </div>
            <div class="top-actions">
                <button class="btn-icon" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="btn-icon" title="通知">
                    <i class="fas fa-bell"></i>
                </button>
                <a href="#" class="btn btn-ghost">
                    <i class="fas fa-upload"></i>
                    Import
                </a>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    New Template
                </a>
            </div>
        </header>

        <!-- 主要内容 -->
        <div class="main-container">
            <!-- 左侧边栏 -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h2 class="sidebar-title">
                        <i class="fas fa-layer-group"></i>
                        Configuration Templates
                    </h2>
                    <p class="sidebar-subtitle">Manage your OnlyOffice editor configurations</p>
                    <div class="search-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search templates...">
                    </div>
                </div>

                <div class="templates-container">
                    <div class="template-group">
                        <div class="group-label">Active Templates</div>
                        
                        <div class="template-item active">
                            <div class="template-name">default-edit.config</div>
                            <div class="template-desc">Standard editing configuration with full permissions and UI elements enabled</div>
                            <div class="template-footer">
                                <div class="template-badges">
                                    <span class="badge badge-default">default</span>
                                    <span class="badge badge-active">active</span>
                                </div>
                                <div class="template-meta">12-19</div>
                            </div>
                        </div>

                        <div class="template-item">
                            <div class="template-name">collab-review.config</div>
                            <div class="template-desc">Collaborative review setup with real-time editing and comment tracking</div>
                            <div class="template-footer">
                                <div class="template-badges">
                                    <span class="badge badge-active">active</span>
                                </div>
                                <div class="template-meta">12-18</div>
                            </div>
                        </div>

                        <div class="template-item">
                            <div class="template-name">readonly-view.config</div>
                            <div class="template-desc">Read-only configuration for document viewing and basic operations</div>
                            <div class="template-footer">
                                <div class="template-badges">
                                    <span class="badge badge-active">active</span>
                                </div>
                                <div class="template-meta">12-17</div>
                            </div>
                        </div>
                    </div>

                    <div class="template-group">
                        <div class="group-label">Archived Templates</div>
                        
                        <div class="template-item">
                            <div class="template-name">restricted-edit.config</div>
                            <div class="template-desc">Limited functionality template for external users with restricted access</div>
                            <div class="template-footer">
                                <div class="template-badges">
                                    <span class="badge badge-inactive">inactive</span>
                                </div>
                                <div class="template-meta">12-16</div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧主要内容 -->
            <main class="main-content">
                <div class="content-header">
                    <div class="content-title-section">
                        <h2>default-edit.config</h2>
                        <p class="content-subtitle">Configure document editor permissions, features and interface settings</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-secondary btn-sm">
                            <i class="fas fa-undo"></i>
                            Reset
                        </button>
                        <button class="btn btn-primary btn-sm">
                            <i class="fas fa-save"></i>
                            Apply Changes
                        </button>
                    </div>
                </div>

                <nav class="config-tabs">
                    <button class="tab-btn active">Permissions</button>
                    <button class="tab-btn">Interface</button>
                    <button class="tab-btn">Features</button>
                    <button class="tab-btn">Collaboration</button>
                    <button class="tab-btn">Advanced</button>
                </nav>

                <div class="config-content">
                    <div class="config-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="section-title">Document Permissions</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Edit Permission</div>
                                        <div class="card-desc">Control whether users can modify document content</div>
                                    </div>
                                    <span class="required-tag">Required</span>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-edit switch-icon"></i>
                                            Enable Editing
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Download Permission</div>
                                        <div class="card-desc">Control document download capabilities</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-download switch-icon"></i>
                                            Allow Download
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Print Permission</div>
                                        <div class="card-desc">Control document printing functionality</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-print switch-icon"></i>
                                            Allow Printing
                                        </div>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye-slash switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-disabled"></div>
                                    <div class="status-text">Feature disabled and hidden</div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Comment Permission</div>
                                        <div class="card-desc">Enable commenting and review features</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-comment switch-icon"></i>
                                            Allow Comments
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Review Permission</div>
                                        <div class="card-desc">Control review mode and track changes</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-clipboard-check switch-icon"></i>
                                            Enable Review
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Form Filling</div>
                                        <div class="card-desc">Allow form completion and submission</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-wpforms switch-icon"></i>
                                            Enable Forms
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <h3 class="section-title">Interface Customization</h3>
                        </div>

                        <div class="config-grid">
                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Toolbar Display</div>
                                        <div class="card-desc">Control main toolbar visibility and behavior</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-tools switch-icon"></i>
                                            Show Toolbar
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>

                            <div class="config-card">
                                <div class="card-header">
                                    <div>
                                        <div class="card-title">Status Bar</div>
                                        <div class="card-desc">Control bottom status bar display</div>
                                    </div>
                                </div>
                                <div class="switch-controls">
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-info-circle switch-icon"></i>
                                            Show Status Bar
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                    <div class="switch-row">
                                        <div class="switch-label">
                                            <i class="fas fa-eye switch-icon"></i>
                                            Show in UI
                                        </div>
                                        <div class="toggle-switch active">
                                            <div class="toggle-thumb"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="config-status">
                                    <div class="status-indicator status-enabled"></div>
                                    <div class="status-text">Feature enabled and visible</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="scroll-indicator">
                    <div class="scroll-thumb"></div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模板选择
            const templateItems = document.querySelectorAll('.template-item');
            templateItems.forEach(item => {
                item.addEventListener('click', function() {
                    templateItems.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 标签页切换
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabBtns.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 开关切换
            const toggleSwitches = document.querySelectorAll('.toggle-switch');
            toggleSwitches.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    
                    // 更新状态显示
                    const card = this.closest('.config-card');
                    const statusText = card.querySelector('.status-text');
                    const statusIndicator = card.querySelector('.status-indicator');
                    const switches = card.querySelectorAll('.toggle-switch');
                    
                    const enableSwitch = switches[0];
                    const visibilitySwitch = switches[1];
                    
                    if (enableSwitch.classList.contains('active') && visibilitySwitch.classList.contains('active')) {
                        statusText.textContent = 'Feature enabled and visible';
                        statusIndicator.className = 'status-indicator status-enabled';
                    } else if (enableSwitch.classList.contains('active')) {
                        statusText.textContent = 'Feature enabled but hidden';
                        statusIndicator.className = 'status-indicator status-enabled';
                    } else if (visibilitySwitch.classList.contains('active')) {
                        statusText.textContent = 'Feature disabled but visible';
                        statusIndicator.className = 'status-indicator status-disabled';
                    } else {
                        statusText.textContent = 'Feature disabled and hidden';
                        statusIndicator.className = 'status-indicator status-disabled';
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                templateItems.forEach(item => {
                    const name = item.querySelector('.template-name').textContent.toLowerCase();
                    const desc = item.querySelector('.template-desc').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || desc.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });

            // 滚动指示器
            const configContent = document.querySelector('.config-content');
            const scrollIndicator = document.querySelector('.scroll-indicator');
            const scrollThumb = document.querySelector('.scroll-thumb');

            configContent.addEventListener('scroll', function() {
                const scrollPercentage = this.scrollTop / (this.scrollHeight - this.clientHeight);
                const thumbHeight = Math.max(20, 60 * (this.clientHeight / this.scrollHeight));
                const thumbPosition = (60 - thumbHeight) * scrollPercentage;
                
                scrollThumb.style.height = thumbHeight + 'px';
                scrollThumb.style.transform = `translateY(${thumbPosition}px)`;
                
                if (this.scrollHeight > this.clientHeight) {
                    scrollIndicator.classList.add('visible');
                } else {
                    scrollIndicator.classList.remove('visible');
                }
            });

            // 触发初始滚动检查
            configContent.dispatchEvent(new Event('scroll'));
        });
    </script>
</body>
</html> 