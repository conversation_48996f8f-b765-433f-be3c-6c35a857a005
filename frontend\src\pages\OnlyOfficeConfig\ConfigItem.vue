<template>
  <div class="config-card-compact">
    <div class="config-item-header-compact">
      <div class="config-item-info">
        <div class="config-item-title-compact">
          {{ label }}
          <span v-if="isRequired" class="required-indicator-compact">必需</span>
        </div>
        <div class="config-item-desc-compact">{{ description }}</div>
      </div>
      <div class="config-status-compact">
        <a-tooltip v-if="!isEnabled" title="此配置项已禁用，不会出现在最终配置中">
          <eye-invisible-outlined class="status-icon status-icon--disabled" />
        </a-tooltip>
        <a-tooltip v-else-if="isRequired" title="此配置项为必需项">
          <exclamation-circle-outlined class="status-icon status-icon--required" />
        </a-tooltip>
        <a-tooltip v-else title="此配置项已启用">
          <eye-outlined class="status-icon status-icon--enabled" />
        </a-tooltip>
      </div>
    </div>

    <div class="switch-controls-compact">
      <div class="switch-row-compact">
        <span class="switch-label-compact">启用选项</span>
        <div
          :class="['toggle-switch-compact', { active: isEnabled }]"
          @click="handleEnabledChange(!isEnabled)"
        >
          <div class="toggle-thumb-compact"></div>
        </div>
      </div>
      <div class="switch-row-compact" v-if="valueType === 'boolean'">
        <span class="switch-label-compact" :class="{ 'disabled-label': !isEnabled }"
          >允许{{ getPermissionLabel(label) }}</span
        >
        <div
          :class="[
            'toggle-switch-compact',
            { active: computedValue && isEnabled, disabled: !isEnabled },
          ]"
          @click="isEnabled && handleValueChange(!computedValue)"
        >
          <div class="toggle-thumb-compact"></div>
        </div>
      </div>
    </div>

    <!-- 非布尔类型的控制器 -->
    <div
      v-if="valueType !== 'boolean' && isEnabled"
      class="config-control-compact"
      :class="{ 'control-disabled': !isEnabled }"
    >
      <!-- 字符串类型 -->
      <a-input
        v-if="valueType === 'string'"
        :value="computedValue"
        size="small"
        @input="(e: Event) => handleValueChange((e.target as HTMLInputElement).value)"
        :placeholder="'请输入' + label"
      />

      <!-- 数字类型 -->
      <a-input-number
        v-else-if="valueType === 'number'"
        :value="computedValue"
        size="small"
        style="width: 100%"
        @change="handleValueChange"
        :placeholder="'请输入' + label"
      />

      <!-- 选择器类型 -->
      <a-select
        v-else-if="valueType === 'select' && options"
        :value="computedValue"
        size="small"
        style="width: 100%"
        @change="handleValueChange"
        :placeholder="'请选择' + label"
      >
        <a-select-option
          v-for="option in options"
          :key="String(option.value)"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </div>

    <div class="config-status-info-compact">
      <div class="status-text-compact">
        <div
          :class="['status-icon-compact', isEnabled ? 'status-enabled' : 'status-disabled']"
        ></div>
        {{ getStatusText() }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EyeOutlined, EyeInvisibleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'

interface OptionItem {
  label: string
  value: string | number | boolean
}

interface Props {
  label: string
  description: string
  configKey: string
  configValue: unknown
  isEnabled: boolean
  isRequired: boolean
  valueType: 'boolean' | 'string' | 'number' | 'select'
  options?: OptionItem[]
}

interface Emits {
  (e: 'update-value', value: unknown): void
  (e: 'update-enabled', enabled: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性：处理配置值的类型转换
const computedValue = computed(() => {
  if (!props.isEnabled) {
    return undefined
  }

  const value = props.configValue

  switch (props.valueType) {
    case 'boolean':
      return value === true || value === 'true'
    case 'number':
      return typeof value === 'number' ? value : Number(value) || 0
    case 'string':
    case 'select':
      return String(value || '')
    default:
      return value
  }
})

// 处理值变更
const handleValueChange = (newValue: unknown) => {
  if (!props.isEnabled) return

  let processedValue = newValue

  // 根据类型处理值
  switch (props.valueType) {
    case 'boolean':
      processedValue = Boolean(newValue)
      break
    case 'number':
      processedValue = Number(newValue) || 0
      break
    case 'string':
    case 'select':
      processedValue = String(newValue || '')
      break
  }

  emit('update-value', processedValue)
}

// 处理启用状态变更
const handleEnabledChange = (enabled: boolean) => {
  emit('update-enabled', enabled)
}

// 获取权限标签
const getPermissionLabel = (label: string): string => {
  return label.replace('权限', '').replace('设置', '').replace('配置', '')
}

// 获取状态文本
const getStatusText = (): string => {
  if (!props.isEnabled) {
    return '功能已禁用'
  }

  if (props.valueType === 'boolean') {
    if (computedValue.value) {
      return '功能已启用'
    } else {
      return '功能已关闭'
    }
  }

  return '功能已启用'
}
</script>

<style scoped>
.config-card-compact {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
  animation: slideInCompact 0.2s ease;
}

.config-card-compact:hover {
  border-color: #cbd5e0;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.config-item-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.config-item-info {
  flex: 1;
  min-width: 0;
}

.config-item-title-compact {
  font-size: 13px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.config-item-desc-compact {
  font-size: 10px;
  color: #718096;
  line-height: 1.3;
}

.required-indicator-compact {
  background: #f56565;
  color: white;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 8px;
  font-weight: 600;
  text-transform: uppercase;
}

.config-status-compact {
  margin-left: 8px;
  flex-shrink: 0;
}

.status-icon {
  font-size: 16px;
  cursor: help;
}

.status-icon--enabled {
  color: #52c41a;
}

.status-icon--disabled {
  color: #d9d9d9;
}

.status-icon--required {
  color: #faad14;
}

.switch-controls-compact {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 8px;
}

.switch-row-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.switch-label-compact {
  font-size: 11px;
  font-weight: 500;
  color: #4a5568;
}

.toggle-switch-compact {
  position: relative;
  width: 32px;
  height: 18px;
  background: #e2e8f0;
  border-radius: 9px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-switch-compact.active {
  background: #1890ff;
}

.toggle-thumb-compact {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch-compact.active .toggle-thumb-compact {
  transform: translateX(14px);
}

.toggle-switch-compact.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.disabled-label {
  opacity: 0.5;
  color: #a0aec0 !important;
}

.config-control-compact {
  margin-bottom: 8px;
}

.control-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.config-status-info-compact {
  margin-top: 8px;
  padding: 6px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 4px;
  border-left: 2px solid #1890ff;
}

.status-text-compact {
  font-size: 9px;
  color: #1890ff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon-compact {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-enabled {
  background: #48bb78;
}

.status-disabled {
  background: #cbd5e0;
}

/* 加载动画 */
@keyframes slideInCompact {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-item-header-compact {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .config-status-compact {
    align-self: flex-start;
    margin-left: 0;
  }
}
</style>
