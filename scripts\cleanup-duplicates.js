/**
 * 清理重复文件脚本
 * 用于清理数据库中的逻辑重复记录以及文件系统中孤立的物理文件
 */
const fs = require('fs-extra');
const path = require('path');
const db = require('../services/database');
const config = require('../config');

async function cleanupDuplicates() {
    try {
        console.log('开始清理重复文件...');

        const connected = await db.testConnection();
        if (!connected) {
            console.error('无法连接到数据库，清理操作取消');
            process.exit(1);
        }

        const uploadDir = config.storage.uploadDir;

        // --- Part 1: Deduplicate Database Entries ---
        console.log('第1部分：数据库记录去重...');
        const duplicateGroups = await db.query(`
            SELECT 
                original_name, 
                file_size, 
                GROUP_CONCAT(id ORDER BY created_at ASC) as ids_csv,
                COUNT(*) as count
            FROM files
            WHERE is_deleted = FALSE
            GROUP BY original_name, file_size
            HAVING COUNT(*) > 1;
        `);

        let totalDbDuplicatesSoftDeleted = 0;

        if (duplicateGroups.length === 0) {
            console.log('数据库中未发现逻辑上的重复文件组。');
        } else {
            console.log(`发现 ${duplicateGroups.length} 组逻辑上的重复文件。正在处理...`);
            for (const group of duplicateGroups) {
                console.log(`\n  处理组: 原始文件名="${group.original_name}", 文件大小:${group.file_size}, 包含 ${group.count} 个记录`);
                const idsInGroup = group.ids_csv.split(',');
                console.log(`    组内所有 IDs: ${idsInGroup.join(', ')}`);

                let masterRecord = null;
                const viableRecords = [];

                for (const id of idsInGroup) {
                    const record = await db.queryOne(
                        'SELECT id, storage_name, created_at FROM files WHERE id = ? AND is_deleted = FALSE',
                        [id]
                    );
                    if (record && record.storage_name) {
                        const filePath = path.join(uploadDir, record.storage_name);
                        if (await fs.pathExists(filePath)) {
                            viableRecords.push(record);
                            console.log(`      可行的记录: ID=${record.id}, Storage=${record.storage_name}, Created=${record.created_at}, 物理文件存在`);
                        } else {
                            console.warn(`      记录 ID ${id} (存储名: ${record.storage_name}) 对应的物理文件未找到。`);
                        }
                    } else if (record) {
                        console.warn(`      记录 ID ${id} 的 storage_name 为空或无效。`);
                    } else {
                        console.warn(`      记录 ID ${id} 在数据库中未找到或已被删除。`);
                    }
                }

                if (viableRecords.length === 0) {
                    console.error(`    错误：在 "${group.original_name}" 组中未找到任何具有有效物理文件的记录。无法合并，跳过此组。`);
                    continue;
                }

                viableRecords.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
                masterRecord = viableRecords[0]; // 选择创建时间最早且物理文件存在的记录

                if (viableRecords.length === 1 && idsInGroup.length > 1) {
                    console.warn(`    警告：在 "${group.original_name}" 组中，尽管原始记录多于一个 (${idsInGroup.length}个)，但只有一个记录 (${masterRecord.id}) 有关联的物理文件。将此作为主记录。`);
                } else {
                    console.log(`    从 ${viableRecords.length} 个可行记录中选定主记录 (创建最早且物理文件存在): ID=${masterRecord.id}, Storage=${masterRecord.storage_name}`);
                }

                const idsToSoftDeleteCandidate = idsInGroup.filter(id => id !== masterRecord.id);
                console.log(`    候选软删除的 IDs (排除主记录): ${idsToSoftDeleteCandidate.join(', ') || '无'}`);
                console.log(`    主记录 ID: ${masterRecord.id}`);

                if (idsToSoftDeleteCandidate.length > 0) {
                    await db.transaction(async (connection) => {
                        const placeholders = idsToSoftDeleteCandidate.map(() => '?').join(',');
                        const sqlSelectExistingCandidates = `SELECT id FROM files WHERE id IN (${placeholders}) AND is_deleted = FALSE AND id != ?`;
                        const paramsSelectExistingCandidates = [...idsToSoftDeleteCandidate, masterRecord.id];

                        console.log("      Executing SQL to find existing candidates for deletion:", sqlSelectExistingCandidates);
                        console.log("      With params:", paramsSelectExistingCandidates);

                        const actualIdsToSoftDeleteResult = await connection.query(sqlSelectExistingCandidates, paramsSelectExistingCandidates);
                        const actualIdsToSoftDelete = (actualIdsToSoftDeleteResult[0] || []).map(r => r.id);

                        console.log(`      查询后，实际将要软删除的 files 记录 IDs: ${actualIdsToSoftDelete.join(', ') || '无'}`);

                        if (actualIdsToSoftDelete.length > 0) {
                            console.log(`      步骤1: 删除 ${actualIdsToSoftDelete.length} 个重复 files 记录对应的 file_versions 条目 (file_ids: ${actualIdsToSoftDelete.join(', ')})`);
                            await connection.query(
                                'DELETE FROM file_versions WHERE file_id IN (?)',
                                [actualIdsToSoftDelete]
                            );

                            console.log(`      步骤2: 软删除 ${actualIdsToSoftDelete.length} 个重复的 files 记录 (IDs: ${actualIdsToSoftDelete.join(', ')})`);
                            const updateResult = await connection.query(
                                'UPDATE files SET is_deleted = TRUE WHERE id IN (?)',
                                [actualIdsToSoftDelete]
                            );
                            totalDbDuplicatesSoftDeleted += updateResult.affectedRows || 0;
                        } else {
                            console.log(`      在组 "${group.original_name}" 中没有需要软删除的额外 files 记录 (查询后确认)。`);
                        }
                    });
                } else {
                    console.log(`    在组 "${group.original_name}" 中，主记录之外没有其他 files 记录需要处理 (候选列表为空)。`);
                }
            }
            console.log(`\n数据库记录去重完成。共软删除了 ${totalDbDuplicatesSoftDeleted} 条重复记录。`);
        }

        // --- Part 2: Cleanup Orphaned Physical Files ---
        console.log('\n第2部分：物理文件清理...');
        const physicalFilesInUploadDir = await fs.readdir(uploadDir);
        console.log(`上传目录中共有 ${physicalFilesInUploadDir.length} 个物理文件。`);

        const activeStorageNamesQuery = await db.query(`
            SELECT T.storage_name 
            FROM (
                SELECT storage_name FROM files WHERE is_deleted = FALSE
                UNION
                SELECT fv.storage_name FROM file_versions fv
                INNER JOIN files f ON fv.file_id = f.id
                WHERE f.is_deleted = FALSE
            ) AS T 
            WHERE T.storage_name IS NOT NULL AND T.storage_name != '';
        `);

        const activeDbStorageNames = new Set(activeStorageNamesQuery.map(file => file.storage_name));
        console.log(`数据库中记录的活动存储名 (需要保留的物理文件) 共 ${activeDbStorageNames.size} 个。`);

        const orphanedPhysicalFiles = physicalFilesInUploadDir.filter(file => !activeDbStorageNames.has(file));
        console.log(`发现 ${orphanedPhysicalFiles.length} 个孤立的物理文件需要清理。`);

        if (orphanedPhysicalFiles.length === 0) {
            console.log('没有需要清理的孤立物理文件。');
            return totalDbDuplicatesSoftDeleted;
        }

        const backupDir = path.join(process.cwd(), 'backup_orphaned_files', Date.now().toString());
        await fs.ensureDir(backupDir);
        console.log(`创建备份目录: ${backupDir} 用于存放孤立文件。`);

        let movedPhysicalFileCount = 0;
        for (const file of orphanedPhysicalFiles) {
            try {
                const srcPath = path.join(uploadDir, file);
                if (await fs.pathExists(srcPath)) {
                    const destPath = path.join(backupDir, file);
                    await fs.move(srcPath, destPath);
                    console.log(`  已移动孤立文件: ${file} 到备份目录。`);
                    movedPhysicalFileCount++;
                }
            } catch (error) {
                console.error(`  移动孤立文件失败: ${file}`, error);
            }
        }

        console.log(`物理文件清理完成。共移动了 ${movedPhysicalFileCount} 个孤立文件到备份目录。`);
        return totalDbDuplicatesSoftDeleted + movedPhysicalFileCount;

    } catch (error) {
        console.error('清理重复文件过程中发生严重错误:', error);
        throw error;
    }
}

cleanupDuplicates()
    .then((count) => {
        console.log(`\n清理脚本执行完毕。共处理 (软删除或移动) 了约 ${count} 项内容。`);
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n清理脚本执行失败:', error);
        process.exit(1);
    }); 