# OnlyOffice集成系统 - 环境配置说明

> **更新时间**: 2024年12月19日  
> **状态**: ✅ 已自动从现有配置填充完成

## 🎯 配置来源分析

### ✅ **已自动填充的配置**

#### 1. **数据库配置** (来自 `config/database.js`)
```env
DB_HOST=*************          # MySQL服务器地址
DB_PORT=3306                   # MySQL端口
DB_NAME=onlyfile               # 数据库名称
DB_USER=onlyfile_user          # 数据库用户名
DB_PASSWORD=0nlyF!le$ecure#123 # 数据库密码
```

#### 2. **OnlyOffice服务器配置** (来自 `config/default.js`)
```env
ONLYOFFICE_SERVER_URL=http://*************/
ONLYOFFICE_DOCUMENT_SERVER_URL=http://*************/
```

#### 3. **应用服务器配置** (来自 `config/default.js`)
```env
PORT=3000                      # 后端API端口
SERVER_HOST=*************      # Node.js服务器地址
CALLBACK_URL=http://*************:3000/api/editor/callback
```

#### 4. **JWT认证配置** (来自现有配置)
```env
JWT_SECRET=R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV  # 已有的JWT密钥
JWT_EXPIRES_IN=24h                          # 令牌24小时有效期
```

#### 5. **FileNet企业集成** (来自 `config/index.js`)
```env
FILENET_URL=http://*************:8090
FILENET_OBJECT_STORE={2FFE1C9C-3EF4-4467-808D-99F85F42531F}
FILENET_DEFAULT_DOC_CLASS=SimpleDocument
FILENET_DEFAULT_SOURCE_TYPE=MaxOffice
FILENET_DEFAULT_BIZ_TAG=office_file
```

#### 6. **文件存储配置** (来自 `config/default.js`)
```env
UPLOAD_PATH=./uploads          # 文件上传目录
TMP_PATH=./tmp                 # 临时文件目录  
MAX_FILE_SIZE=50MB             # 文件大小限制 (从原配置)
```

## ⚠️ **需要手动确认的配置**

### 1. **OnlyOffice密钥**
```env
ONLYOFFICE_SECRET_KEY=your-onlyoffice-secret-key
```
**说明**: 如果您的OnlyOffice服务器启用了JWT验证，需要填入实际密钥

### 2. **FileNet认证信息**
```env
FILENET_USERNAME=your-filenet-username
FILENET_PASSWORD=your-filenet-password
```
**说明**: 需要您提供FileNet系统的用户名和密码

## 🔧 **网络配置检查**

### 当前网络拓扑：
```yaml
开发机器:
  - Node.js服务: *************:3000
  - Vue前端: localhost:8080 (开发)

外部服务:
  - OnlyOffice服务器: *************
  - MySQL数据库: *************:3306
  - FileNet服务器: *************:8090
```

### 网络连通性验证：
```bash
# 验证数据库连接
mysql -h ************* -P 3306 -u onlyfile_user -p

# 验证OnlyOffice服务
curl http://*************/

# 验证FileNet服务
curl http://*************:8090/
```

## 🚀 **配置优势**

### ✅ **保持业务连续性**
- 所有现有的数据库连接保持不变
- OnlyOffice集成配置完全兼容
- FileNet企业系统无缝对接

### ✅ **开发环境优化**
- 前端开发服务器(8080) + 后端API(3000)
- 自动代理配置，无跨域问题
- 热重载支持，开发效率高

### ✅ **安全配置**
- JWT密钥从现有系统迁移
- 数据库密码和配置保持安全
- CORS配置支持多环境

## 📋 **下一步验证**

### 1. **数据库连接测试**
```bash
# 在backend目录下创建测试脚本
npm run test:db
```

### 2. **OnlyOffice服务测试**
```bash
# 测试文档服务器连接
curl http://*************/
```

### 3. **启动开发环境**
```bash
# 在根目录执行
npm run dev
```

预期结果：
- 后端: http://localhost:3000 ✅
- 前端: http://localhost:8080 ✅
- API代理: http://localhost:8080/api/* → http://localhost:3000/api/* ✅

## 🔄 **配置迁移状态**

| 配置项 | 原始位置 | 新位置 | 状态 |
|--------|----------|--------|------|
| 数据库配置 | `config/database.js` | `.env` | ✅ 已迁移 |
| OnlyOffice配置 | `config/default.js` | `.env` | ✅ 已迁移 |
| JWT配置 | `config/default.js` | `.env` | ✅ 已迁移 |
| FileNet配置 | `config/index.js` | `.env` | ✅ 已迁移 |
| 服务器配置 | `config/default.js` | `.env` | ✅ 已迁移 |

## ❓ **常见问题**

### Q: 为什么有些IP地址不是localhost？
**A**: 您的系统部署在内网环境中，使用实际的内网IP地址进行服务间通信，这是正确的配置。

### Q: FileNet的用户名密码从哪里获取？
**A**: 需要联系您的系统管理员获取FileNet系统的访问凭据。

### Q: 可以修改端口配置吗？
**A**: 可以，但需要同时修改前端的代理配置(`frontend/vite.config.ts`)和现有应用的回调URL配置。

准备好测试环境了吗？我可以帮您：
1. 创建数据库连接测试脚本
2. 验证OnlyOffice服务连接
3. 创建第一个新架构的API接口 