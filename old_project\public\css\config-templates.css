/* 配置模板管理页面样式 */
.template-card {
    transition: transform 0.2s;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.config-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.config-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.config-group-title {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.config-group-toggle {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
}

.config-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: white;
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.2s;
}

.config-item:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.config-item.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.config-item-main {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
}

.config-item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 200px;
}

.config-item-label {
    font-weight: 500;
    color: #495057;
    min-width: 120px;
}

.config-item-description {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.config-value-input {
    flex: 1;
    min-width: 150px;
}

.config-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
}

.config-toggle input[type="checkbox"] {
    transform: scale(1.1);
}

.config-toggle label {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
    cursor: pointer;
}

.config-status-badges {
    display: flex;
    gap: 5px;
}

.status-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.enabled {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.disabled {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.required {
    background-color: #fff3cd;
    color: #856404;
}

.template-preview {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    background: #f8f9fa;
}

.template-actions {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 20px 0;
    border-top: 1px solid #dee2e6;
    margin-top: 30px;
}

.btn-group-custom {
    display: flex;
    gap: 10px;
    justify-content: space-between;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.alert-custom {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .config-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .config-item-main {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .config-item-controls {
        min-width: auto;
        justify-content: space-between;
    }

    .btn-group-custom {
        flex-direction: column;
    }
}