# 简单的MCP测试脚本
Write-Host "简单MCP测试" -ForegroundColor Green
Write-Host "============" -ForegroundColor Green

# 设置环境变量
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "onlyfile_user"
$env:MYSQL_PASS = "0nlyF!le`$ecure#123"
$env:MYSQL_DB = "onlyfile"
$env:MYSQL_ENABLE_LOGGING = "true"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"

Write-Host "环境变量已设置" -ForegroundColor Yellow

# 方法1: 检查MCP服务器是否能响应help
Write-Host ""
Write-Host "方法1: 检查help参数..." -ForegroundColor Yellow
try {
    $helpOutput = npx @benborla29/mcp-server-mysql --help 2>&1
    Write-Host "Help输出: $helpOutput" -ForegroundColor Cyan
}
catch {
    Write-Host "Help失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 方法2: 检查版本
Write-Host ""
Write-Host "方法2: 检查版本..." -ForegroundColor Yellow
try {
    $versionOutput = npx @benborla29/mcp-server-mysql --version 2>&1
    Write-Host "Version输出: $versionOutput" -ForegroundColor Cyan
}
catch {
    Write-Host "Version失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 方法3: 尝试stdio模式
Write-Host ""
Write-Host "方法3: 尝试stdio模式..." -ForegroundColor Yellow
try {
    # 创建一个临时的输入文件
    $initMessage = @'
{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"roots":{"listChanged":true},"sampling":{}},"clientInfo":{"name":"test-client","version":"1.0.0"}}}
'@
    
    $initMessage | Out-File -FilePath "temp-init.json" -Encoding UTF8 -NoNewline
    
    Write-Host "发送初始化消息..." -ForegroundColor Cyan
    $output = Get-Content "temp-init.json" | npx @benborla29/mcp-server-mysql 2>&1
    Write-Host "Stdio输出: $output" -ForegroundColor White
    
    # 清理临时文件
    Remove-Item "temp-init.json" -Force -ErrorAction SilentlyContinue
}
catch {
    Write-Host "Stdio失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 方法4: 检查是否是远程模式问题
Write-Host ""
Write-Host "方法4: 尝试远程模式..." -ForegroundColor Yellow
$env:IS_REMOTE_MCP = "true"
$env:PORT = "3000"
$env:REMOTE_SECRET_KEY = "test-secret-key"

try {
    Write-Host "启动远程模式MCP服务器..." -ForegroundColor Cyan
    $job = Start-Job -ScriptBlock {
        $env:MYSQL_HOST = "*************"
        $env:MYSQL_PORT = "3306"
        $env:MYSQL_USER = "onlyfile_user"
        $env:MYSQL_PASS = "0nlyF!le`$ecure#123"
        $env:MYSQL_DB = "onlyfile"
        $env:IS_REMOTE_MCP = "true"
        $env:PORT = "3000"
        $env:REMOTE_SECRET_KEY = "test-secret-key"
        
        npx @benborla29/mcp-server-mysql 2>&1
    }
    
    Start-Sleep -Seconds 3
    
    # 检查端口是否开放
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port 3000 -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✓ 远程模式启动成功，端口3000已开放" -ForegroundColor Green
            
            # 尝试HTTP请求
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:3000/mcp" -Method GET -TimeoutSec 5
                Write-Host "HTTP响应: $($response.StatusCode)" -ForegroundColor Green
            }
            catch {
                Write-Host "HTTP请求失败: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "✗ 远程模式启动失败，端口3000未开放" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "端口测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 停止作业
    Stop-Job -Job $job -ErrorAction SilentlyContinue
    Remove-Job -Job $job -Force -ErrorAction SilentlyContinue
}
catch {
    Write-Host "远程模式失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "建议:" -ForegroundColor Yellow
Write-Host "1. MCP服务器可能需要通过Cursor IDE来启动" -ForegroundColor White
Write-Host "2. 尝试重启Cursor并检查MCP连接状态" -ForegroundColor White
Write-Host "3. 检查Cursor的MCP日志文件" -ForegroundColor White
