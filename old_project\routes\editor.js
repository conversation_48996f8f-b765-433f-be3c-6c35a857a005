/**
 * 编辑器路由
 */
const express = require('express');
const router = express.Router();
const documentService = require('../services/document');
const jwtService = require('../services/jwt');
const fileStorage = require('../services/fileStorage');
const configTemplateService = require('../services/configTemplateService');
const { apiError } = require('../middleware/error');
const fs = require('fs-extra');
const path = require('path');
const config = require('../config');
const axios = require('axios');
const db = require('../services/database'); // 引入db模块
const jwt = require('jsonwebtoken');

// JWT密钥（应该与OnlyOffice配置一致）
const JWT_SECRET = config.jwt.secret;

// UUID验证函数
function isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}

// 根据文件扩展名获取文档类型
function getDocumentType(fileExtension) {
    const ext = fileExtension.toLowerCase();
    if (['.doc', '.docx', '.odt', '.rtf', '.txt'].includes(ext)) {
        return 'word';
    } else if (['.xls', '.xlsx', '.ods', '.csv'].includes(ext)) {
        return 'cell';
    } else if (['.ppt', '.pptx', '.odp'].includes(ext)) {
        return 'slide';
    } else if (['.pdf'].includes(ext)) {
        return 'pdf';
    }
    return 'word'; // 默认类型
}

// 添加documentServer引用，可以从config中获取
const documentServer = {
    getApiUrl: () => {
        return `${config.documentServer.url}/web-apps/apps/api/documents/api.js`;
    }
};

// 移动getEditorConfig函数到顶部，供所有路由使用
async function getEditorConfig(document, req) {
    // 从现有的标准编辑器逻辑中提取配置生成功能
    return await documentService.getDocumentConfig(document.id);
}

// 获取令牌函数
function getEditorToken(config) {
    return jwtService.generateJWT(config);
}

// 编辑文档
router.get('/:id', async (req, res, next) => {
    try {
        const fileId = req.params.id;
        const templateName = req.query.template; // 从查询参数获取模板名称
        const overrides = {}; // 可以从查询参数中提取配置覆盖

        // 提取URL参数中的配置覆盖
        if (req.query.hideChat === 'true') overrides['customization.chat'] = false;
        if (req.query.hideComments === 'true') overrides['customization.comments'] = false;
        if (req.query.readonly === 'true') overrides['permissions.edit'] = false;

        // 从数据库获取文件信息
        const fileInfo = await fileStorage.getFileById(fileId);
        if (!fileInfo) {
            console.error('找不到对应的文件ID:', fileId);
            return next(apiError('找不到该文件，请检查文件ID或重新上传文件。', 404));
        }

        const filePath = path.join(config.storage.uploadDir, fileInfo.storage_name);
        if (!fs.existsSync(filePath)) {
            console.error('文件不存在:', filePath);
            return next(apiError('文件不存在，请检查文件名或重新上传', 404));
        }

        // 使用配置模板服务生成文档配置
        let docConfig;
        if (templateName) {
            // 使用指定的配置模板
            docConfig = await configTemplateService.buildEditorConfig(templateName, fileId, overrides);
        } else {
            // 使用默认模板
            docConfig = await configTemplateService.buildEditorConfig(null, fileId, overrides);
        }

        // 生成JWT令牌
        const token = jwtService.generateJWT(docConfig);

        res.render('editor', {
            docTitle: fileInfo.original_name,
            docServerUrl: config.documentServer.url,
            apiUrl: `${config.documentServer.url}/web-apps/apps/api/documents/api.js`,
            token: token,
            config: docConfig
        });
    } catch (error) {
        next(apiError('编辑文档失败', 500, error));
    }
});

// 文档保存回调
router.post('/callback/:fileId?', async (req, res, next) => {
    const fileIdFromUrl = req.params.fileId;
    if (fileIdFromUrl) {
        console.log('从URL路径中获取到文件ID:', fileIdFromUrl);
    }
    console.log('接收到文档保存回调:', JSON.stringify(req.body));

    try {
        // 验证JWT令牌
        const token = req.headers.authorization || '';
        let isValidToken = false;

        if (token) {
            const tokenParts = token.split(' ');
            if (tokenParts.length === 2 && tokenParts[0] === 'Bearer') {
                const decoded = jwtService.verifyJWT(tokenParts[1]);
                isValidToken = !!decoded;
            }
        }

        if (!isValidToken && process.env.NODE_ENV === 'production') {
            console.warn('回调验证失败: 无效的JWT令牌');
            return res.status(401).json({ error: 1, message: '无效的令牌' });
        }

        // 尽早返回响应，避免OnlyOffice超时
        res.json({ error: 0 });

        // 在后台处理回调，不影响响应速度
        setTimeout(async () => {
            try {
                const result = await documentService.handleCallback(req.body);
                console.log('文档保存处理结果:', result ? '成功' : '失败');
            } catch (error) {
                console.error('后台处理回调失败:', error);
            }
        }, 10);

    } catch (error) {
        console.error('回调处理失败:', error);
        res.status(500).json({
            error: 1,
            message: '回调处理失败',
            errorDetails: error.message
        });
    }
});

// 检查文档保存状态
router.get('/check-save-status/:fileId', async (req, res, next) => {
    try {
        const internalDbId = req.params.fileId;
        console.log('检查文档保存状态 (filenet_documents)，内部DB ID:', internalDbId);

        // 校验传入的ID是否为有效的UUID格式 (如果你的内部ID都是UUID)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(internalDbId)) {
            console.warn('无效的内部DB ID格式:', internalDbId);
            return res.status(400).json({
                success: false,
                status: 'error',
                message: '无效的文档ID格式'
            });
        }

        // 直接从 filenet_documents 表获取文件信息
        const fileInfo = await db.queryOne(`
            SELECT id, original_name, version, updated_at, fn_doc_id 
            FROM filenet_documents 
            WHERE id = ? AND is_deleted = FALSE
        `, [internalDbId]);

        if (!fileInfo) {
            return res.status(404).json({
                success: false,
                status: 'error',
                message: '找不到对应的文档记录 (filenet_documents)'
            });
        }

        // 比较前端可能知道的旧版本号或旧的更新时间，或者简单地返回最新状态
        // 这里我们简单返回当前数据库中的状态信息
        // 前端可以根据这些信息判断是否有更新

        const nowTime = new Date();
        const fileUpdateTime = new Date(fileInfo.updated_at);
        const diffMinutes = (nowTime.getTime() - fileUpdateTime.getTime()) / (1000 * 60);

        // 如果文件是最近1分钟内修改的，认为是"最近已保存"
        // 这个阈值可以调整，或者前端直接比较版本号
        const isRecentlySaved = diffMinutes < 1;

        res.json({
            success: true,
            status: isRecentlySaved ? 'saved' : 'unknown', // 或者直接返回 'latest_info'
            message: isRecentlySaved ? '文档状态已更新' : '文档当前状态信息已获取',
            fileName: fileInfo.original_name,
            version: fileInfo.version,
            lastModifiedDb: fileInfo.updated_at, // 数据库中的更新时间
            currentFnDocId: fileInfo.fn_doc_id, // 返回当前的FileNet ID，前端可以更新
            timeSinceLastSaveDb: `${Math.round(diffMinutes * 10) / 10}分钟前 (数据库记录)`
        });

    } catch (error) {
        console.error('检查保存状态失败 (filenet_documents):', error);
        res.status(500).json({
            success: false,
            status: 'error',
            message: '检查保存状态时发生服务器内部错误',
            error: error.message
        });
    }
});

// 触发强制保存
router.post('/force-save/:fileId', async (req, res, next) => {
    try {
        const fileId = req.params.fileId;
        const { documentKey } = req.body;

        if (!documentKey) {
            console.warn('Force save request for fileId ', fileId, ' is missing documentKey.');
            return res.status(400).json({
                success: false,
                message: '请求体中缺少 documentKey。请确保客户端传递了当前编辑器会话的文档密钥。'
            });
        }

        console.log(`接收到强制保存请求，文件ID: ${fileId}, 文档密钥: ${documentKey}`);

        const baseUrl = config.documentServer.url.endsWith('/')
            ? config.documentServer.url
            : `${config.documentServer.url}/`;
        const commandServiceUrl = `${baseUrl}coauthoring/CommandService.ashx`;

        const commandPayload = {
            c: 'forcesave',
            key: documentKey,
            forcesavetype: 0
        };

        console.log(`向 OnlyOffice Command Service (${commandServiceUrl}) 发送命令:`, JSON.stringify(commandPayload));

        try {
            const ooResponse = await axios.post(commandServiceUrl, commandPayload, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout: 15000
            });

            console.log('OnlyOffice Command Service 响应状态:', ooResponse.status);
            console.log('OnlyOffice Command Service 响应数据:', ooResponse.data);

            if (ooResponse.data && ooResponse.data.error === 0) {
                res.json({
                    success: true,
                    message: '强制保存命令已成功发送至 OnlyOffice。请稍后检查文件是否已通过回调更新。'
                });
            } else if (ooResponse.data && typeof ooResponse.data.error !== 'undefined') {
                const errorMessage = ooResponse.data.message || 'OnlyOffice 未提供具体的错误信息。';
                console.error(`OnlyOffice Command Service 返回错误: code ${ooResponse.data.error}, message: ${errorMessage}`);
                res.status(502).json({
                    success: false,
                    message: `OnlyOffice 处理强制保存命令失败: ${errorMessage} (错误码: ${ooResponse.data.error})`
                });
            } else {
                console.warn('OnlyOffice Command Service 返回了非预期的响应格式:', ooResponse.data);
                res.status(502).json({
                    success: false,
                    message: '强制保存命令已发送，但从 OnlyOffice 收到了非预期的响应。'
                });
            }
        } catch (cmdError) {
            console.error('发送强制保存命令至 OnlyOffice Document Server 时发生错误:', cmdError.message);
            if (cmdError.response) {
                console.error('OnlyOffice Command Service 错误响应状态:', cmdError.response.status);
                console.error('OnlyOffice Command Service 错误响应数据:', cmdError.response.data);
                const errorDetail = cmdError.response.data && cmdError.response.data.message
                    ? cmdError.response.data.message
                    : (cmdError.response.data || '无详细信息');
                res.status(502).json({
                    success: false,
                    message: `无法向 OnlyOffice 发送强制保存命令: 服务器响应错误状态 ${cmdError.response.status}。详情: ${JSON.stringify(errorDetail)}`
                });
            } else if (cmdError.request) {
                console.error('OnlyOffice Command Service 未响应:', cmdError.code);
                res.status(504).json({
                    success: false,
                    message: '无法向 OnlyOffice 发送强制保存命令: OnlyOffice 服务器无响应或超时。'
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: '向 OnlyOffice 发送强制保存命令前发生内部错误。'
                });
            }
        }
    } catch (error) {
        console.error('强制保存接口 (/force-save/:fileId) 内部错误:', error);
        res.status(500).json({
            success: false,
            message: '处理强制保存请求时发生内部服务器错误。'
        });
    }
});

// 测试文件保存功能
router.get('/test-save/:fileId', async (req, res, next) => {
    try {
        const fileId = req.params.fileId;
        console.log('测试文件保存功能，文件ID:', fileId);

        // 构造一个模拟的回调数据
        const testUrl = `http://*************/cache/files/data/${fileId}-${Date.now()}_4943/output.docx/output.docx?md5=j4WFxJ1cUTrcErq0xwvUdA&expires=1746675109&shardkey=${fileId}-${Date.now()}&filename=output.docx`;

        const callbackData = {
            key: `${fileId}-${Date.now()}`,
            status: 2,
            url: testUrl,
            changesurl: testUrl.replace('output.docx', 'changes.zip'),
            history: { serverVersion: '8.3.3', changes: [{}] },
            users: ['user-1'],
            actions: [{ type: 0, userid: 'user-1' }],
            lastsave: new Date().toISOString(),
            notmodified: false,
            filetype: 'docx'
        };

        console.log('模拟回调数据:', callbackData);

        // 调用回调处理函数
        const result = await documentService.handleCallback(callbackData);

        if (result) {
            res.json({ success: true, message: '测试保存成功！' });
        } else {
            res.status(500).json({ success: false, message: '测试保存失败！' });
        }
    } catch (error) {
        next(error);
    }
});

/**
 * 支持从URL参数编辑FileNet文档
 */
router.get('/', async (req, res, next) => {
    try {
        const fnDocId = req.query.fn_doc_id;

        if (!fnDocId) {
            return next(apiError('缺少必要的文档ID参数', 400));
        }

        // 获取文档配置
        const docConfig = await documentService.getFileNetDocumentConfig(fnDocId);

        // 生成JWT令牌
        const token = jwtService.generateJWT(docConfig);

        res.render('editor', {
            docTitle: docConfig.document.title,
            docServerUrl: config.documentServer.url,
            apiUrl: `${config.documentServer.url}/web-apps/apps/api/documents/api.js`,
            token: token,
            config: docConfig
        });
    } catch (error) {
        next(apiError('编辑FileNet文档失败', 500, error));
    }
});

/**
 * 支持通过路径编辑FileNet文档
 */
router.get('/filenet/:fnDocId', async (req, res, next) => {
    try {
        const fnDocId = req.params.fnDocId;

        console.log('请求通过路径编辑FileNet文档:', fnDocId);

        // 获取文档配置
        const docConfig = await documentService.getFileNetDocumentConfig(fnDocId);

        // 生成JWT令牌
        const token = jwtService.generateJWT(docConfig);

        res.render('editor', {
            docTitle: docConfig.document.title,
            docServerUrl: config.documentServer.url,
            apiUrl: `${config.documentServer.url}/web-apps/apps/api/documents/api.js`,
            token: token,
            config: docConfig
        });
    } catch (error) {
        next(apiError('编辑FileNet文档失败', 500, error));
    }
});

/**
 * 添加新的API端点：获取编辑器配置 - 修正路径问题
 */
router.get('/config/:id', async (req, res) => {
    try {
        const fileId = req.params.id;
        const templateName = req.query.template; // 从查询参数获取模板名称
        const overrides = {}; // 可以从查询参数中提取配置覆盖

        // 提取URL参数中的配置覆盖
        if (req.query.hideChat === 'true') overrides['customization.chat'] = false;
        if (req.query.hideComments === 'true') overrides['customization.comments'] = false;
        if (req.query.readonly === 'true') overrides['permissions.edit'] = false;

        // 验证UUID格式
        if (!isValidUUID(fileId)) {
            return res.status(400).json({
                success: false,
                message: '无效的文件ID格式'
            });
        }

        console.log('获取编辑器配置，文件ID:', fileId);
        console.log('使用配置模板:', templateName || '默认');
        console.log('配置覆盖:', overrides);

        // 使用配置模板服务生成文档配置 (API调用)
        let docConfig;
        if (templateName) {
            // 使用指定的配置模板
            docConfig = await configTemplateService.buildEditorConfig(templateName, fileId, overrides, true); // 添加API标识
        } else {
            // 使用默认模板
            docConfig = await configTemplateService.buildEditorConfig(null, fileId, overrides, true); // 添加API标识
        }

        // 生成JWT令牌
        const token = jwtService.generateJWT(docConfig);

        // 返回完整配置，添加token
        const config = {
            ...docConfig,
            token: token
        };

        res.json(config);

    } catch (error) {
        console.error('获取编辑器配置错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误: ' + error.message
        });
    }
});

module.exports = router; 