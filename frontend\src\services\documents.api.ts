import { ApiService, api } from './api'
import type { DocumentInfo, OnlyOfficeConfigResponse, ApiResponse } from '@/types/api.types'

export interface CreateDocumentDto {
  title: string
  type: 'word' | 'excel' | 'powerpoint' | 'pdf'
  category?: string
  isPublic?: boolean
  tags?: string[]
  description?: string
}

export interface UpdateDocumentDto {
  name?: string // 修改为name而不是title，与后端保持一致
}

export interface DocumentListQueryParams {
  page?: number
  limit?: number
  search?: string
  extension?: string // 后端使用extension而不是type
  status?: string
  category?: string
  createdBy?: string
  startDate?: string
  endDate?: string
  tags?: string[]
}

export interface DocumentListResponse {
  data: DocumentInfo[]
  total: number
  pagination?: {
    current: number
    pageSize: number
    total: number
  }
}

export interface OnlyOfficeCallbackData {
  key: string
  status: number
  url?: string
  changesurl?: string
  history?: unknown
  users?: string[]
  actions?: unknown[]
  lastsave?: string
  notmodified?: boolean
}

/**
 * 文档管理API服务
 */
export class DocumentsApiService {
  /**
   * 获取文档列表
   */
  static async getDocuments(params?: DocumentListQueryParams): Promise<DocumentListResponse> {
    // 直接调用api实例获取完整响应
    const response = await api.get<ApiResponse<DocumentInfo[]>>('/documents', { params })

    // 返回包含分页信息的完整响应
    return {
      data: response.data.data || [],
      total: response.data.total || 0,
      pagination: response.data.pagination,
    }
  }

  /**
   * 获取文档详情
   */
  static async getDocumentById(id: string): Promise<DocumentInfo> {
    return ApiService.get<DocumentInfo>(`/documents/${id}`)
  }

  /**
   * 创建文档记录
   */
  static async createDocument(data: CreateDocumentDto): Promise<DocumentInfo> {
    return ApiService.post<DocumentInfo>('/documents', data)
  }

  /**
   * 更新文档信息（主要是名称）
   */
  static async updateDocument(id: string, data: UpdateDocumentDto): Promise<DocumentInfo> {
    return ApiService.put<DocumentInfo>(`/documents/${id}/update-info`, data)
  }

  /**
   * 更新文档名称
   */
  static async updateDocumentName(id: string, name: string): Promise<DocumentInfo> {
    return ApiService.put<DocumentInfo>(`/documents/${id}/update-info`, { name })
  }

  /**
   * 删除文档（逻辑删除）
   */
  static async deleteDocument(id: string): Promise<boolean> {
    const response = await ApiService.delete<{ success: boolean }>(`/documents/${id}`)
    return response.success
  }

  /**
   * 获取文档OnlyOffice配置
   */
  static async getDocumentConfig(id: string): Promise<OnlyOfficeConfigResponse> {
    return ApiService.get<OnlyOfficeConfigResponse>(`/documents/${id}/config`)
  }

  /**
   * 下载文档
   */
  static async downloadDocument(id: string, filename?: string): Promise<void> {
    return ApiService.download(`/documents/${id}/download`, filename)
  }

  /**
   * OnlyOffice编辑器回调处理
   */
  static async handleCallback(data: OnlyOfficeCallbackData): Promise<{ error: number }> {
    return ApiService.post<{ error: number }>('/documents/callback', data)
  }

  /**
   * 搜索文档
   */
  static async searchDocuments(params: {
    keyword: string
    type?: string
    category?: string
    limit?: number
  }): Promise<DocumentInfo[]> {
    return ApiService.get<DocumentInfo[]>('/documents/search', { params })
  }

  /**
   * 获取文档统计信息
   */
  static async getDocumentStats(): Promise<{
    total: number
    published: number
    draft: number
    archived: number
    byType: Record<string, number>
    recentDocuments: DocumentInfo[]
  }> {
    return ApiService.get('/documents/stats')
  }

  /**
   * 批量删除文档
   */
  static async batchDeleteDocuments(ids: string[]): Promise<void> {
    return ApiService.post<void>('/documents/batch-delete', { ids })
  }

  /**
   * 文档分享
   */
  static async shareDocument(
    id: string,
    data: {
      shareType: 'public' | 'private'
      expiresAt?: string
      permissions: ('read' | 'write' | 'download')[]
    }
  ): Promise<{ shareUrl: string }> {
    return ApiService.post<{ shareUrl: string }>(`/documents/${id}/share`, data)
  }
}

export default DocumentsApiService
