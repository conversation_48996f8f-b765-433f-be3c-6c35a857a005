# 重复文档数据清理脚本说明

## 问题背景

由于OnlyOffice编辑器回调处理的bug，在编辑保存文档时会错误地创建新的主表记录而不是仅创建版本记录。这导致了大量重复的文档记录。

### 问题表现
- 同一个文件名在 `filenet_documents` 主表中有多个记录
- 每次编辑保存都会创建新的主表记录
- 版本管理混乱，应该是版本递增的文档变成了多个独立记录

## 解决方案

已修复OnlyOffice回调处理的代码问题，现在需要清理历史的重复数据。

## 脚本功能

### 1. 分析脚本 - `analyze-duplicate-documents.js`
用于分析当前数据库中的重复文档情况。

```bash
# 运行分析
cd backend
node scripts/analyze-duplicate-documents.js
```

**输出内容：**
- 重复文档的详细列表
- 每个重复文档的创建时间、版本号、创建者信息
- OnlyOffice系统创建的重复记录统计
- 版本表中的相关记录检查
- 生成分析报告 `duplicate-analysis-report.json`

### 2. 清理脚本 - `cleanup-duplicate-documents.js`
用于实际清理重复的文档数据。

```bash
# 预览模式（不修改数据，只显示将要执行的操作）
cd backend
node scripts/cleanup-duplicate-documents.js

# 实际执行模式（会修改数据库）
cd backend
node scripts/cleanup-duplicate-documents.js --execute
```

**清理策略：**
1. 对于每个重复的文件名，保留最早创建的记录作为主记录
2. 将其他重复记录的版本信息合并到版本表中
3. 将重复的主表记录标记为已删除（`is_deleted = 1`）
4. 更新主记录的版本号为最高版本号

### 3. 验证脚本 - `verify-cleanup.js`
用于验证清理操作的结果。

```bash
# 验证清理结果
cd backend
node scripts/verify-cleanup.js
```

**验证内容：**
- 检查是否还有重复的文档记录
- 验证版本表的完整性
- 检查文档版本号的正确性
- 特定问题文件的状态检查
- 总体统计信息

## 使用步骤

### 步骤 1: 备份数据库
```bash
# 强烈建议在清理前备份数据库
mysqldump -u root -p onlyfile > backup-before-cleanup.sql
```

### 步骤 2: 分析重复数据
```bash
cd backend
node scripts/analyze-duplicate-documents.js
```
查看分析结果，了解重复数据的情况。

### 步骤 3: 预览清理操作
```bash
cd backend
node scripts/cleanup-duplicate-documents.js
```
查看将要执行的清理操作，确认无误。

### 步骤 4: 执行清理（可选）
```bash
cd backend
node scripts/cleanup-duplicate-documents.js --execute
```
⚠️ **注意：这会实际修改数据库数据！**

### 步骤 5: 验证结果
```bash
cd backend
node scripts/verify-cleanup.js
```
验证清理操作是否成功。

## 安全措施

1. **数据备份**：执行前务必备份数据库
2. **预览模式**：先运行预览模式查看操作计划
3. **事务保护**：使用数据库事务，出错时自动回滚
4. **软删除**：重复记录标记为删除而不是物理删除
5. **详细日志**：记录所有操作过程和结果

## 示例输出

### 分析脚本输出示例
```
🔍 开始分析重复文档数据...

=== 1. 按文件名分组统计重复记录 ===
发现 3 个重复的文件名：

1. "poems_answer.docx"
   重复数量: 15 个记录
   首次创建: 2025-07-21 02:06:03
   最后创建: 2025-07-22 06:38:49
   版本号: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,18
   创建者: system,onlyoffice-system,onlyoffice-system...

📊 统计结果:
- 重复文件数量: 3
- 需要清理的记录数: 25
```

### 清理脚本输出示例
```
🧹 开始清理重复文档数据...
模式: 预览模式 (不实际修改数据)

📄 文件: "poems_answer.docx"
   重复数量: 15
   保留记录: e6064cc5-e47f-4225-b6af-d6973aa485ab (2025-07-21 02:06:03)
   删除记录: 14 个
   最高版本: 18

📊 清理统计:
- 需要处理的文件: 3 个
- 需要删除的记录: 25 个
- 需要更新的主记录: 3 个
```

## 注意事项

1. **数据库连接**：脚本使用环境变量或默认值连接数据库
2. **权限要求**：需要对数据库有读写权限
3. **执行时间**：清理大量数据可能需要一些时间
4. **服务影响**：建议在低峰期执行，避免影响业务
5. **结果验证**：清理后务必验证系统功能正常

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库是否运行
   - 确认连接参数正确

2. **权限不足**
   - 确保数据库用户有足够权限
   - 检查表的读写权限

3. **脚本执行失败**
   - 查看错误日志
   - 检查数据库完整性
   - 恢复备份重新尝试

### 恢复方法
如果清理过程中出现问题：
```bash
# 恢复备份
mysql -u root -p onlyfile < backup-before-cleanup.sql
```

## 相关文件

- `/backend/src/modules/documents/services/document.service.ts` - 已修复的OnlyOffice回调处理
- `/backend/src/modules/filenet/services/filenet.service.ts` - 新增的仅上传FileNet方法
- `duplicate-analysis-report.json` - 分析报告
- `cleanup-plan.json` - 清理计划
- `cleanup-result-*.json` - 清理结果 