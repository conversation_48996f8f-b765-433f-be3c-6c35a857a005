const mysql = require('mysql2/promise');

/**
 * 验证数据清理结果脚本
 */
async function verifyCleanupResults() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'onlyfile'
    });

    console.log('🔍 验证数据清理结果...\n');

    // 1. 检查是否还有重复的文档
    console.log('=== 1. 检查剩余重复文档 ===');
    const [stillDuplicates] = await connection.execute(`
      SELECT 
        original_name,
        COUNT(*) as count
      FROM filenet_documents 
      WHERE is_deleted = 0
      GROUP BY original_name 
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `);

    if (stillDuplicates.length === 0) {
      console.log('✅ 没有发现重复的文档记录');
    } else {
      console.log(`❌ 仍然存在 ${stillDuplicates.length} 个重复的文档：`);
      stillDuplicates.forEach((dup, i) => {
        console.log(`${i + 1}. "${dup.original_name}" - ${dup.count} 个重复`);
      });
    }

    // 2. 检查版本表的完整性
    console.log('\n=== 2. 检查版本表完整性 ===');
    const [orphanVersions] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM filenet_document_versions fv
      LEFT JOIN filenet_documents fd ON fv.doc_id = fd.id
      WHERE fd.id IS NULL OR fd.is_deleted = 1
    `);

    if (orphanVersions[0].count === 0) {
      console.log('✅ 版本表中没有孤立记录');
    } else {
      console.log(`❌ 版本表中有 ${orphanVersions[0].count} 个孤立记录`);
    }

    // 3. 检查已删除的记录数量
    console.log('\n=== 3. 检查已删除记录统计 ===');
    const [deletedStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_deleted,
        COUNT(DISTINCT original_name) as unique_files_deleted
      FROM filenet_documents 
      WHERE is_deleted = 1
    `);

    console.log(`标记删除的记录数: ${deletedStats[0].total_deleted}`);
    console.log(`涉及的唯一文件数: ${deletedStats[0].unique_files_deleted}`);

    // 4. 检查活跃文档的版本号是否正确
    console.log('\n=== 4. 检查文档版本号正确性 ===');
    const [versionCheck] = await connection.execute(`
      SELECT 
        fd.id,
        fd.original_name,
        fd.version as main_version,
        COALESCE(MAX(fv.version), 0) as max_version_in_versions_table
      FROM filenet_documents fd
      LEFT JOIN filenet_document_versions fv ON fd.id = fv.doc_id
      WHERE fd.is_deleted = 0
      GROUP BY fd.id, fd.original_name, fd.version
      HAVING fd.version != COALESCE(MAX(fv.version), 1)
      LIMIT 10
    `);

    if (versionCheck.length === 0) {
      console.log('✅ 所有文档的版本号都正确');
    } else {
      console.log(`❌ 发现 ${versionCheck.length} 个文档的版本号不一致：`);
      versionCheck.forEach((doc, i) => {
        console.log(`${i + 1}. "${doc.original_name}"`);
        console.log(`   主表版本: ${doc.main_version}, 版本表最大版本: ${doc.max_version_in_versions_table}`);
      });
    }

    // 5. 总体统计
    console.log('\n=== 5. 总体统计 ===');
    const [totalStats] = await connection.execute(`
      SELECT 
        (SELECT COUNT(*) FROM filenet_documents WHERE is_deleted = 0) as active_documents,
        (SELECT COUNT(*) FROM filenet_documents WHERE is_deleted = 1) as deleted_documents,
        (SELECT COUNT(*) FROM filenet_document_versions) as total_versions,
        (SELECT COUNT(DISTINCT original_name) FROM filenet_documents WHERE is_deleted = 0) as unique_files
    `);

    const stats = totalStats[0];
    console.log(`活跃文档数: ${stats.active_documents}`);
    console.log(`已删除文档数: ${stats.deleted_documents}`);
    console.log(`版本记录总数: ${stats.total_versions}`);
    console.log(`唯一文件数: ${stats.unique_files}`);

    // 6. 检查特定问题文件的状态
    console.log('\n=== 6. 检查特定问题文件状态 ===');
    const problemFiles = ['poems_answer.docx', '张艺晗数学错题集(四年级下) .docx'];
    
    for (const fileName of problemFiles) {
      console.log(`\n文件: "${fileName}"`);
      
      const [fileStats] = await connection.execute(`
        SELECT 
          COUNT(*) as total_records,
          SUM(CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END) as active_records,
          SUM(CASE WHEN is_deleted = 1 THEN 1 ELSE 0 END) as deleted_records
        FROM filenet_documents 
        WHERE original_name = ?
      `, [fileName]);

      const [versionStats] = await connection.execute(`
        SELECT COUNT(*) as version_count
        FROM filenet_document_versions fv
        JOIN filenet_documents fd ON fv.doc_id = fd.id
        WHERE fd.original_name = ? AND fd.is_deleted = 0
      `, [fileName]);

      if (fileStats[0]) {
        console.log(`  总记录数: ${fileStats[0].total_records}`);
        console.log(`  活跃记录: ${fileStats[0].active_records}`);
        console.log(`  已删除: ${fileStats[0].deleted_records}`);
        console.log(`  版本数: ${versionStats[0].version_count}`);
        
        if (fileStats[0].active_records === 1) {
          console.log('  ✅ 状态正常');
        } else if (fileStats[0].active_records > 1) {
          console.log('  ❌ 仍有重复记录');
        } else {
          console.log('  ⚠️  没有活跃记录');
        }
      }
    }

    console.log('\n✅ 验证完成');

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行验证
if (require.main === module) {
  verifyCleanupResults()
    .then(() => {
      console.log('\n🎉 数据验证完成');
    })
    .catch((error) => {
      console.error('❌ 验证失败:', error);
      process.exit(1);
    });
}

module.exports = { verifyCleanupResults }; 