import { ConfigModuleOptions } from '@nestjs/config';
import envConfig from './env.config';

/**
 * NestJS应用配置
 * 基于统一的环境变量配置管理
 */

export const configModuleOptions: ConfigModuleOptions = {
  isGlobal: true,
  cache: true,
  // 不使用 NestJS 的 .env 自动加载，因为我们已经在 env.config.ts 中处理了
  ignoreEnvFile: true,
  // 提供配置值
  load: [() => envConfig],
};

// 导出配置供其他模块使用
export { envConfig };
export default envConfig; 