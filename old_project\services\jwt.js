/**
 * JWT服务
 * 用于生成和验证JWT令牌
 */
const jwt = require('jsonwebtoken');
const config = require('../config');

/**
 * 生成JWT令牌
 * @param {Object} payload JWT载荷
 * @returns {string} JWT令牌
 */
function generateJWT(payload) {
    // 确保配置符合OnlyOffice要求
    const jwtPayload = {
        ...payload,
        iss: `http://${config.server.host}:${config.server.port}`,
        aud: config.documentServer.url,
        exp: Math.floor(Date.now() / 1000) + config.jwt.expiresIn,
        nbf: Math.floor(Date.now() / 1000) - 60, // 令牌生效时间比当前时间提前60秒
        iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(jwtPayload, config.jwt.secret, {
        header: config.jwt.header
    });
}

/**
 * 验证JWT令牌
 * @param {string} token JWT令牌
 * @returns {Object|null} 验证通过返回解码后的载荷，失败返回null
 */
function verifyJWT(token) {
    try {
        return jwt.verify(token, config.jwt.secret);
    } catch (error) {
        console.error('JWT验证失败:', error.message);
        return null;
    }
}

module.exports = {
    generateJWT,
    verifyJWT
}; 