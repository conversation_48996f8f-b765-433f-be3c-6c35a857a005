/**
 * JWT验证测试脚本
 * 验证我们生成的JWT token是否能被正确验证
 */

const jwt = require('jsonwebtoken');

async function testJwtVerification() {
  console.log('=== JWT验证测试 ===');
  
  // 从API获取最新的JWT token
  try {
    const fileId = '286bf7f3-1eca-4bc1-9c4f-f92767cd74f9';
    const response = await fetch(`http://localhost:3000/api/editor/${fileId}/config`);
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }
    
    const result = await response.json();
    const token = result.data?.token;
    
    if (!token) {
      throw new Error('API响应中没有JWT token');
    }
    
    console.log('✅ 从API获取JWT token成功');
    console.log('Token长度:', token.length);
    
    // 使用我们的密钥验证token
    const secret = 'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV';
    
    try {
      const decoded = jwt.verify(token, secret);
      console.log('✅ JWT验证成功!');
      console.log('解码后的载荷:');
      console.log('- 签发者 (iss):', decoded.iss);
      console.log('- 接收者 (aud):', decoded.aud);
      console.log('- 签发时间 (iat):', new Date(decoded.iat * 1000).toLocaleString());
      console.log('- 过期时间 (exp):', new Date(decoded.exp * 1000).toLocaleString());
      console.log('- 生效时间 (nbf):', new Date(decoded.nbf * 1000).toLocaleString());
      console.log('- 文档标题:', decoded.document?.title);
      console.log('- 文档URL:', decoded.document?.url);
      
      // 检查token是否过期
      const now = Math.floor(Date.now() / 1000);
      if (decoded.exp < now) {
        console.log('⚠️ JWT token已过期');
      } else {
        console.log('✅ JWT token未过期，剩余时间:', Math.floor((decoded.exp - now) / 60), '分钟');
      }
      
      // 检查token是否已生效
      if (decoded.nbf > now) {
        console.log('⚠️ JWT token尚未生效');
      } else {
        console.log('✅ JWT token已生效');
      }
      
      return true;
    } catch (verifyError) {
      console.error('❌ JWT验证失败:', verifyError.message);
      
      // 尝试解码token header和payload（不验证签名）
      try {
        const parts = token.split('.');
        const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        
        console.log('🔍 Token结构分析:');
        console.log('Header:', header);
        console.log('算法:', header.alg);
        console.log('类型:', header.typ);
        console.log('签发者:', payload.iss);
        console.log('接收者:', payload.aud);
        
        // 检查密钥是否匹配（通过重新签名验证）
        console.log('🔍 尝试使用不同密钥...');
        
        // 一些可能的密钥变体
        const possibleSecrets = [
          'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV',
          'OnlyOffice-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV',
          'R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV-OnlyOffice',
          'default-onlyoffice-secret',
          ''
        ];
        
        for (const testSecret of possibleSecrets) {
          try {
            jwt.verify(token, testSecret);
            console.log(`✅ 找到匹配的密钥: "${testSecret}"`);
            return testSecret;
          } catch (e) {
            // 继续尝试下一个密钥
          }
        }
        
        console.log('❌ 未找到匹配的密钥');
        
      } catch (parseError) {
        console.error('❌ Token解析失败:', parseError.message);
      }
      
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 运行测试
testJwtVerification().then(result => {
  console.log('');
  console.log('🎉 测试完成!');
  console.log('结果:', result === true ? '验证成功' : result || '验证失败');
}); 