<template>
  <Transition name="notification">
    <div v-if="notification.show" class="notification-panel" :class="getNotificationClass">
      <div class="notification-content">
        <span class="notification-icon">{{ getNotificationIcon }}</span>
        <span class="notification-message">{{ notification.message }}</span>
        <button class="notification-close" @click="$emit('hide')" aria-label="关闭通知">×</button>
      </div>

      <!-- 进度条 -->
      <div
        v-if="notification.duration > 0"
        class="notification-progress"
        :style="{ animationDuration: `${notification.duration}ms` }"
      ></div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NotificationInfo } from '../composables/useNotification'

/**
 * 通知面板组件
 *
 * @description 显示应用内通知消息
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

interface Props {
  /** 通知信息 */
  notification: NotificationInfo
}

interface Emits {
  /** 隐藏通知事件 */
  (e: 'hide'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

/**
 * 获取通知图标
 */
const getNotificationIcon = computed(() => {
  switch (props.notification.type) {
    case 'success':
      return '✓'
    case 'error':
      return '✗'
    case 'warning':
      return '⚠'
    case 'info':
    default:
      return 'ℹ'
  }
})

/**
 * 获取通知CSS类名
 */
const getNotificationClass = computed(() => {
  return `notification-${props.notification.type}`
})
</script>

<style scoped>
.notification-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  min-width: 300px;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  overflow: hidden;
  border-left: 4px solid #3498db;
}

.notification-info {
  border-left-color: #3498db;
}

.notification-success {
  border-left-color: #2ecc71;
}

.notification-warning {
  border-left-color: #f39c12;
}

.notification-error {
  border-left-color: #e74c3c;
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
}

.notification-icon {
  font-size: 18px;
  font-weight: bold;
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.notification-info .notification-icon {
  color: #3498db;
}

.notification-success .notification-icon {
  color: #2ecc71;
}

.notification-warning .notification-icon {
  color: #f39c12;
}

.notification-error .notification-icon {
  color: #e74c3c;
}

.notification-message {
  flex: 1;
  color: #2c3e50;
  font-size: 14px;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: #7f8c8d;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background-color: #ecf0f1;
  color: #2c3e50;
}

.notification-progress {
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  animation: progress-countdown linear forwards;
}

.notification-info .notification-progress {
  background: linear-gradient(90deg, #3498db 0%, rgba(52, 152, 219, 0.3) 100%);
}

.notification-success .notification-progress {
  background: linear-gradient(90deg, #2ecc71 0%, rgba(46, 204, 113, 0.3) 100%);
}

.notification-warning .notification-progress {
  background: linear-gradient(90deg, #f39c12 0%, rgba(243, 156, 18, 0.3) 100%);
}

.notification-error .notification-progress {
  background: linear-gradient(90deg, #e74c3c 0%, rgba(231, 76, 60, 0.3) 100%);
}

/* 过渡动画 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

@keyframes progress-countdown {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}
</style>
