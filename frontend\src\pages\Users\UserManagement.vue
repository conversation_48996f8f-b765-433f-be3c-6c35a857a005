<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>用户管理</h1>
        <p>管理系统用户和权限</p>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><plus-outlined /></template>
          添加用户
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="用户总数"
              :value="userStats.totalUsers"
              :precision="0"
              style="color: #1890ff"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="活跃用户"
              :value="userStats.activeUsers"
              :precision="0"
              style="color: #52c41a"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="管理员"
              :value="userStats.adminUsers"
              :precision="0"
              style="color: #faad14"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="本月新增"
              :value="userStats.newUsersThisMonth"
              :precision="0"
              style="color: #722ed1"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input-search
            v-model:value="searchParams.search"
            placeholder="搜索用户名、邮箱或姓名"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchParams.role"
            placeholder="角色筛选"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="editor">编辑者</a-select-option>
            <a-select-option value="viewer">查看者</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchParams.status"
            placeholder="状态筛选"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
            <a-select-option value="locked">锁定</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchParams.department"
            placeholder="部门筛选"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option value="IT">IT部门</a-select-option>
            <a-select-option value="HR">人力资源</a-select-option>
            <a-select-option value="Finance">财务部</a-select-option>
            <a-select-option value="Marketing">市场部</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-button @click="resetSearch">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 用户列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="userData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        size="middle"
      >
        <!-- 头像列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :src="record.avatar" size="small">
              {{ record.username?.[0]?.toUpperCase() }}
            </a-avatar>
          </template>

          <!-- 角色列 -->
          <template v-else-if="column.key === 'role'">
            <a-tag :color="getRoleColor(record.role)">
              {{ getRoleText(record.role) }}
            </a-tag>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 最后登录时间 -->
          <template v-else-if="column.key === 'lastLoginAt'">
            <span v-if="record.lastLoginAt">
              {{ dayjs(record.lastLoginAt).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else class="text-muted">从未登录</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="handleDelete(record)"
                :disabled="record.id === authStore.userInfo?.id"
              >
                删除
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleMenuClick($event, record)">
                    <a-menu-item key="resetPassword">重置密码</a-menu-item>
                    <a-menu-item key="viewDetails">查看详情</a-menu-item>
                    <a-menu-item :key="record.status === 'active' ? 'deactivate' : 'activate'">
                      {{ record.status === 'active' ? '禁用' : '启用' }}
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small"> 更多 <down-outlined /> </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑用户模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑用户' : '创建用户'"
      :width="600"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input
                v-model:value="formData.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="姓名" name="fullName">
              <a-input v-model:value="formData.fullName" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="角色" name="role">
              <a-select v-model:value="formData.role" placeholder="请选择角色">
                <a-select-option value="admin">管理员</a-select-option>
                <a-select-option value="editor">编辑者</a-select-option>
                <a-select-option value="viewer">查看者</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="部门" name="department">
              <a-select v-model:value="formData.department" placeholder="请选择部门" allow-clear>
                <a-select-option value="IT">IT部门</a-select-option>
                <a-select-option value="HR">人力资源</a-select-option>
                <a-select-option value="Finance">财务部</a-select-option>
                <a-select-option value="Marketing">市场部</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item v-if="!isEdit" label="密码" name="password">
          <a-input-password v-model:value="formData.password" placeholder="请输入密码" />
        </a-form-item>

        <a-form-item v-if="isEdit" label="状态" name="status">
          <a-select v-model:value="formData.status" placeholder="请选择状态">
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
            <a-select-option value="locked">锁定</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, DownOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { useAuthStore } from '@/stores/auth'
import { UsersApiService } from '@/services/users.api'
import type { UserInfo } from '@/types/api.types'
import type { UserListResponse } from '@/services/users.api'

// 定义本地接口类型
interface CreateUserDto {
  username: string
  email: string
  fullName: string
  phone?: string
  role: 'admin' | 'editor' | 'viewer'
  password: string
  department?: string
}

interface UpdateUserDto {
  username?: string
  email?: string
  fullName?: string
  phone?: string
  role?: 'admin' | 'editor' | 'viewer'
  department?: string
  status?: 'active' | 'inactive' | 'locked'
}

interface UserListQueryParams {
  current: number
  pageSize: number
  search?: string
  role?: string
  status?: 'active' | 'inactive' | 'suspended' | 'deleted'
  department?: string
}

interface UserStats {
  totalUsers: number
  activeUsers: number
  adminUsers: number
  editorUsers: number
  viewerUsers: number
  newUsersThisMonth: number
}

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 用户数据
const userData = ref<UserInfo[]>([])
const userStats = ref<UserStats>({
  totalUsers: 0,
  activeUsers: 0,
  adminUsers: 0,
  editorUsers: 0,
  viewerUsers: 0,
  newUsersThisMonth: 0,
})

// 搜索参数
const searchParams = reactive<UserListQueryParams>({
  current: 1,
  pageSize: 10,
  search: '',
  role: undefined,
  status: undefined,
  department: undefined,
})

// 分页配置
const pagination = computed(() => ({
  current: searchParams.current,
  pageSize: searchParams.pageSize,
  total: userData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
}))

// 表单数据
const formData = reactive<CreateUserDto & { status?: string }>({
  username: '',
  email: '',
  fullName: '',
  phone: '',
  role: 'viewer',
  password: '',
  department: '',
  status: 'active',
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' },
  ],
  fullName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' },
  ],
}

// 表格列配置
const columns = [
  {
    title: '头像',
    dataIndex: 'avatar',
    key: 'avatar',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120,
  },
  {
    title: '姓名',
    dataIndex: 'fullName',
    key: 'fullName',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200,
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    width: 100,
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '最后登录',
    dataIndex: 'lastLoginAt',
    key: 'lastLoginAt',
    width: 150,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
  },
]

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await UsersApiService.getUsers(searchParams)
    // API返回的是{data: [], total: number}格式
    if (response && typeof response === 'object' && 'data' in response) {
      userData.value = (response as UserListResponse).data || []
      // 更新分页信息
      searchParams.current = (response as UserListResponse).page || 1
    } else {
      // 如果是数组格式的响应，直接使用
      userData.value = Array.isArray(response) ? response : []
    }
  } catch (error) {
    message.error('获取用户列表失败')
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取用户统计
const fetchUserStats = async () => {
  try {
    const stats = await UsersApiService.getUserStats()
    userStats.value = stats
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  searchParams.current = 1
  fetchUsers()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    current: 1,
    pageSize: 10,
    search: '',
    role: undefined,
    status: undefined,
    department: undefined,
  })
  fetchUsers()
}

// 表格变化处理
const handleTableChange = (pag: { current: number; pageSize: number }) => {
  searchParams.current = pag.current
  searchParams.pageSize = pag.pageSize
  fetchUsers()
}

// 显示创建模态框
const showCreateModal = () => {
  isEdit.value = false
  modalVisible.value = true
  resetFormData()
}

// 编辑用户
const handleEdit = (record: UserInfo) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, {
    username: record.username,
    email: record.email,
    fullName: record.fullName,
    phone: record.phone,
    role: record.role,
    department: record.department,
    status: record.status,
  })
}

// 删除用户
const handleDelete = (record: UserInfo) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户"${record.fullName}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await UsersApiService.deleteUser(record.id)
        message.success('删除成功')
        fetchUsers()
      } catch (error) {
        message.error('删除失败')
      }
    },
  })
}

// 菜单点击处理
const handleMenuClick = (menuInfo: { key: string }, record: UserInfo) => {
  handleMenuAction(menuInfo.key, record)
}

// 菜单操作处理
const handleMenuAction = async (key: string, record: UserInfo) => {
  switch (key) {
    case 'resetPassword': {
      // 重置密码逻辑
      Modal.confirm({
        title: '重置密码',
        content: `确定要重置用户"${record.fullName}"的密码吗？`,
        onOk: async () => {
          try {
            await UsersApiService.resetPassword({
              userId: record.id,
              newPassword: '123456', // 默认密码
            })
            message.success('密码重置成功，新密码为：123456')
          } catch (error) {
            message.error('密码重置失败')
          }
        },
      })
      break
    }
    case 'viewDetails': {
      // 查看详情逻辑
      break
    }
    case 'activate':
    case 'deactivate': {
      // 状态切换逻辑
      const newStatus = key === 'activate' ? 'active' : 'inactive'
      try {
        await UsersApiService.updateUser(record.id, { status: newStatus })
        message.success(`用户${newStatus === 'active' ? '启用' : '禁用'}成功`)
        fetchUsers()
      } catch (error) {
        message.error('状态更新失败')
      }
      break
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEdit.value) {
      // 编辑用户
      const updateData: UpdateUserDto = {
        email: formData.email,
        fullName: formData.fullName,
        phone: formData.phone,
        role: formData.role,
        department: formData.department,
        status: formData.status as 'active' | 'inactive' | 'locked',
      }
      await UsersApiService.updateUser(formData.username, updateData)
      message.success('用户更新成功')
    } else {
      // 创建用户
      const createData: CreateUserDto = {
        username: formData.username,
        email: formData.email,
        fullName: formData.fullName,
        phone: formData.phone,
        role: formData.role,
        password: formData.password,
        department: formData.department,
      }
      await UsersApiService.createUser(createData)
      message.success('用户创建成功')
    }

    modalVisible.value = false
    fetchUsers()
    fetchUserStats()
  } catch (error) {
    console.error('表单提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 取消模态框
const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    username: '',
    email: '',
    fullName: '',
    phone: '',
    role: 'viewer',
    password: '',
    department: '',
    status: 'active',
  })
  formRef.value?.resetFields()
}

// 工具函数
const getRoleColor = (role: string) => {
  const colors = {
    admin: 'red',
    editor: 'blue',
    viewer: 'green',
  }
  return colors[role as keyof typeof colors] || 'default'
}

const getRoleText = (role: string) => {
  const texts = {
    admin: '管理员',
    editor: '编辑者',
    viewer: '查看者',
  }
  return texts[role as keyof typeof texts] || role
}

const getStatusColor = (status: string) => {
  const colors = {
    active: 'success',
    inactive: 'warning',
    locked: 'error',
  }
  return colors[status as keyof typeof colors] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    active: '活跃',
    inactive: '禁用',
    locked: '锁定',
  }
  return texts[status as keyof typeof texts] || status
}

// 初始化
onMounted(() => {
  fetchUsers()
  fetchUserStats()
})
</script>

<style scoped>
.user-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 500;
}

.header-left p {
  margin: 0;
  color: #666;
}

.stats-cards {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 24px;
}

.text-muted {
  color: #999;
}
</style>
