<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    mode="inline"
    theme="light"
    :inline-collapsed="collapsed"
    @click="handleMenuClick"
  >
    <template v-for="item in filteredMenuItems" :key="item.key">
      <!-- 菜单分组 -->
      <a-menu-item-group v-if="item.type === 'group'" :title="item.title">
        <template v-for="child in item.children" :key="child.key">
          <a-menu-item v-if="hasMenuPermission(child)" :key="child.key">
            <template #icon>
              <component :is="child.icon" />
            </template>
            <span>{{ child.title }}</span>
            <a-badge v-if="child.badge" :count="child.badge" :offset="[8, 0]" />
          </a-menu-item>
        </template>
      </a-menu-item-group>

      <!-- 普通菜单项 -->
      <a-menu-item v-else-if="hasMenuPermission(item)" :key="item.key">
        <template #icon>
          <component :is="item.icon" />
        </template>
        <span>{{ item.title }}</span>
        <a-badge v-if="item.badge" :count="item.badge" :offset="[8, 0]" />
      </a-menu-item>
    </template>
  </a-menu>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  DashboardOutlined,
  FileTextOutlined,
  SnippetsOutlined,
  SettingOutlined,
  UserOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons-vue'
import { usePermissions, PERMISSIONS } from '@/composables/usePermissions'

/**
 * 菜单项类型定义
 */
interface MenuItemType {
  key: string
  title: string
  icon?: typeof DashboardOutlined
  path?: string
  permission?: string | string[]
  badge?: number
  type?: 'group' | 'item'
  children?: MenuItemType[]
}

// Props
interface Props {
  collapsed?: boolean
}

defineProps<Props>()

// Composables
const router = useRouter()
const route = useRoute()
const { hasPermission, permissionState } = usePermissions()

// 响应式数据
const selectedKeys = ref<string[]>([])

// 完整菜单配置
const menuItems: MenuItemType[] = [
  {
    key: 'main',
    title: '主要功能',
    type: 'group',
    children: [
      {
        key: 'dashboard',
        title: '系统首页',
        icon: DashboardOutlined,
        path: '/dashboard',
      },
      {
        key: 'documents',
        title: '文档管理',
        icon: FileTextOutlined,
        path: '/documents',
        permission: PERMISSIONS.DOCUMENTS.READ,
        badge: 12, // 示例：12个待处理文档
      },
      {
        key: 'templates',
        title: '文档模板管理',
        icon: SnippetsOutlined,
        path: '/templates',
        permission: PERMISSIONS.TEMPLATES.READ,
      },
      {
        key: 'onlyoffice-templates',
        title: 'OnlyOffice模板管理',
        icon: SettingOutlined,
        path: '/onlyoffice-templates',
        permission: PERMISSIONS.TEMPLATES.READ,
      },
    ],
  },
  {
    key: 'system',
    title: '系统配置',
    type: 'group',
    children: [
      {
        key: 'users',
        title: '用户管理',
        icon: UserOutlined,
        path: '/users',
        permission: PERMISSIONS.USERS.READ,
      },
      {
        key: 'permissions',
        title: '权限管理',
        icon: SafetyCertificateOutlined,
        path: '/permissions',
        permission: [PERMISSIONS.PERMISSIONS.READ, PERMISSIONS.ROLES.READ],
      },
      {
        key: 'system-config',
        title: '系统配置管理',
        icon: SettingOutlined,
        path: '/system-config',
        permission: [PERMISSIONS.SYSTEM.READ, PERMISSIONS.CONFIG.READ],
      },
    ],
  },
]

/**
 * 检查菜单项权限
 * @param item 菜单项
 * @returns 是否有权限显示
 */
const hasMenuPermission = (item: MenuItemType): boolean => {
  // 没有权限要求的菜单项默认显示
  if (!item.permission) {
    return true
  }

  // 检查权限
  return hasPermission(item.permission)
}

/**
 * 过滤后的菜单项（根据权限）
 */
const filteredMenuItems = computed(() => {
  return menuItems
    .map(group => ({
      ...group,
      children: group.children?.filter(hasMenuPermission) || [],
    }))
    .filter(group => group.type !== 'group' || (group.children && group.children.length > 0))
})

/**
 * 处理菜单点击事件
 * @param param 菜单点击参数
 */
const handleMenuClick = ({ key }: { key: string }) => {
  console.log('🔍 [权限菜单] 菜单点击:', key)

  // 查找对应的菜单项
  const findMenuItem = (items: MenuItemType[], targetKey: string): MenuItemType | null => {
    for (const item of items) {
      if (item.key === targetKey) {
        return item
      }
      if (item.children) {
        const found = findMenuItem(item.children, targetKey)
        if (found) return found
      }
    }
    return null
  }

  const menuItem = findMenuItem(menuItems, key)
  if (menuItem?.path) {
    console.log('🔍 [权限菜单] 导航到:', menuItem.path)
    router.push(menuItem.path)
  }
}

/**
 * 根据当前路由设置选中的菜单项
 */
const updateSelectedKeys = () => {
  const currentPath = route.path

  // 查找匹配当前路径的菜单项
  const findKeyByPath = (items: MenuItemType[], path: string): string | null => {
    for (const item of items) {
      if (item.path === path) {
        return item.key
      }
      if (item.children) {
        const found = findKeyByPath(item.children, path)
        if (found) return found
      }
    }
    return null
  }

  const key = findKeyByPath(menuItems, currentPath)
  if (key) {
    selectedKeys.value = [key]
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    updateSelectedKeys()
  },
  { immediate: true }
)

// 监听权限状态变化，重新计算菜单
watch(
  () => permissionState.value,
  () => {
    console.log('🔐 [权限菜单] 权限状态更新，重新计算菜单')
    updateSelectedKeys()
  },
  { deep: true }
)
</script>

<style scoped>
.ant-menu {
  border-right: none;
}

.ant-menu-item {
  display: flex;
  align-items: center;
  margin: 4px 0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-menu-item:hover {
  background: #f0f9ff;
  color: #1890ff;
}

.ant-menu-item-selected {
  background: #e6f7ff !important;
  color: #1890ff !important;
  border-right: 3px solid #1890ff;
}

.ant-menu-item-group-title {
  font-size: 12px;
  color: #8c8c8c;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 16px 0 8px 0;
  padding: 0 16px;
}

.ant-badge {
  margin-left: auto;
}

/* 收缩状态下的样式 */
.ant-menu-inline-collapsed .ant-menu-item {
  padding: 0 calc(50% - 16px / 2);
}

.ant-menu-inline-collapsed .ant-badge {
  display: none;
}
</style>
