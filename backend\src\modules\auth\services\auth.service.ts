import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../../users/services/user.service';
import * as bcrypt from 'bcrypt';

/**
 * 用户JWT载荷接口
 */
export interface UserJwtPayload {
  sub: string;        // JWT标准字段：subject (用户ID)
  username: string;
  role: string;
  type: string;       // 令牌类型 ('access' 或 'refresh')
  iss?: string;       // JWT标准字段：issuer (签发者)
  aud?: string;       // JWT标准字段：audience (受众)  
  iat?: number;       // JWT签发时间 (issued at)
  exp?: number;       // JWT过期时间 (expiration time)
  nbf?: number;       // JWT生效时间 (not before)
}

/**
 * 刷新令牌载荷接口
 */
export interface RefreshTokenPayload {
  userId: string;
  type: 'refresh';
}

/**
 * 认证响应接口
 */
export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: {
    id: string;
    username: string;
    role: string;
    full_name?: string;
    email?: string;
  };
}

/**
 * 简化的用户信息接口（用于认证）
 */
export interface AuthUser {
  id: string;
  username: string;
  password_hash: string;
  role_name?: string;
  status: string;
  full_name?: string;
  email?: string;
  failed_login_attempts: number;
  locked_until?: Date;
}

/**
 * 认证服务
 * 
 * 处理用户认证相关的业务逻辑，包括登录验证、令牌生成等
 * 
 * @class AuthService
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.1.0 (集成真实用户验证)
 */
@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
  ) {}

  /**
   * 用户登录验证
   */
  async login(username: string, password: string, ipAddress?: string): Promise<AuthResponse> {
    // 验证用户凭据
    const user = await this.validateUserCredentials(username, password);
    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 检查用户状态
    if (user.status !== 'active') {
      throw new UnauthorizedException('用户账户已被禁用');
    }

    // 检查账户是否被锁定
    if (user.locked_until && new Date() < user.locked_until) {
      throw new UnauthorizedException('账户已被锁定，请稍后再试');
    }

    // 更新用户登录信息
    await this.userService.updateLoginInfo(user.id, ipAddress);

    // 生成认证响应
    return await this.generateAuthResponse(user);
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    // 验证刷新令牌并获取用户
    const user = await this.validateAndGetUserFromRefreshToken(refreshToken);
    if (!user) {
      throw new UnauthorizedException('无效的刷新令牌或用户不存在');
    }

    // 生成新的认证响应
    return await this.generateAuthResponse(user);
  }

  /**
   * 验证JWT令牌
   */
  async validateToken(token: string): Promise<UserJwtPayload | null> {
    try {
      const payload = this.jwtService.verify(token) as UserJwtPayload;
      return payload;
    } catch (error) {
      console.warn('[AuthService] 令牌验证失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * 获取用户信息
   */
  async getCurrentUser(userId: string): Promise<Record<string, unknown> | null> {
    try {
      const user = await this.userService.findById(userId);
      if (!user) {
        return null;
      }

      // 过滤敏感信息
      const { password_hash: _password_hash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('[AuthService] 获取用户信息失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * 验证用户凭据
   * @private
   */
  private async validateUserCredentials(username: string, password: string): Promise<AuthUser | null> {
    try {
      console.log('[AuthService] 开始验证用户凭据:', { username });
      
      // 从数据库查找用户
      const user = await this.userService.findByUsername(username);
      if (!user) {
        console.log('[AuthService] 用户不存在:', username);
        return null;
      }

      console.log('[AuthService] 找到用户:', {
        id: user.id,
        username: user.username,
        status: user.status,
        role_name: user.role_name,
        password_hash_preview: user.password_hash?.substring(0, 20) + '...'
      });

      // 验证密码
      console.log('[AuthService] 开始验证密码...');
      console.log('[AuthService] 明文密码:', password);
      console.log('[AuthService] 密码长度:', password.length);
      console.log('[AuthService] 完整密码哈希:', user.password_hash);
      console.log('[AuthService] 密码哈希长度:', user.password_hash.length);
      console.log('[AuthService] 密码哈希格式验证:', /^\$2[abxy]?\$\d+\$/.test(user.password_hash));
      
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      console.log('[AuthService] 密码验证结果:', isPasswordValid);
      
      // 测试：手动生成这个密码的哈希看看
      const testHash = await bcrypt.hash(password, 10);
      console.log('[AuthService] 使用相同密码生成的新哈希:', testHash);
      const testCompare = await bcrypt.compare(password, testHash);
      console.log('[AuthService] 新哈希验证结果:', testCompare);
      
      if (!isPasswordValid) {
        console.log('[AuthService] 密码验证失败，增加失败登录次数');
        // 增加失败登录次数
        await this.userService.incrementFailedLoginAttempts(user.id);
        return null;
      }

      console.log('[AuthService] 用户凭据验证成功');
      return {
        id: user.id,
        username: user.username,
        password_hash: user.password_hash,
        role_name: user.role_name || 'user',
        status: user.status,
        full_name: user.full_name,
        email: user.email,
        failed_login_attempts: user.failed_login_attempts,
        locked_until: user.locked_until,
      };
    } catch (error) {
      console.error('[AuthService] 验证用户凭据失败:', error);
      return null;
    }
  }

  /**
   * 生成认证响应
   * @private
   */
  private async generateAuthResponse(user: AuthUser): Promise<AuthResponse> {
    // 生成JWT令牌 - 移除与JWT配置冲突的字段
    const tokenPayload = {
      sub: user.id,          // JWT标准字段：subject (用户ID)
      username: user.username,
      role: user.role_name || 'user',
      type: 'access',        // 令牌类型，用于区分访问令牌和刷新令牌
      // 注意：不再在payload中设置iss和aud，让JWT配置来处理
    };

    const accessToken = this.jwtService.sign(tokenPayload);
    const refreshToken = this.generateRefreshToken(user.id);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: 24 * 60 * 60, // 24小时
      user: {
        id: user.id,
        username: user.username,
        role: user.role_name || 'user',
        full_name: user.full_name,
        email: user.email,
      },
    };
  }

  /**
   * 生成刷新令牌
   * @private
   */
  private generateRefreshToken(userId: string): string {
    const payload: RefreshTokenPayload = {
      userId,
      type: 'refresh',
    };

    return this.jwtService.sign(payload, {
      expiresIn: '7d', // 7天有效期
      secret: this.configService.get<string>('JWT_REFRESH_SECRET', 'default-refresh-secret'),
    });
  }

  /**
   * 验证刷新令牌并获取用户信息
   * @private
   */
  private async validateAndGetUserFromRefreshToken(refreshToken: string): Promise<AuthUser | null> {
    try {
      // 验证刷新令牌
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET', 'default-refresh-secret'),
      }) as RefreshTokenPayload;

      if (payload.type !== 'refresh') {
        return null;
      }

      // 获取用户信息
      const user = await this.userService.findById(payload.userId);
      if (!user || user.status !== 'active') {
        return null;
      }

      return {
        id: user.id,
        username: user.username,
        password_hash: user.password_hash,
        role_name: user.role_name || 'user',
        status: user.status,
        full_name: user.full_name,
        email: user.email,
        failed_login_attempts: user.failed_login_attempts,
        locked_until: user.locked_until,
      };
    } catch (error) {
      console.error('[AuthService] 验证刷新令牌失败:', error);
      return null;
    }
  }
} 