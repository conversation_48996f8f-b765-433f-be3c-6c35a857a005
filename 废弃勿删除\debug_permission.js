const http = require('http');

async function callPermissionAPI() {
  console.log('📞 开始调用权限API...');
  console.log('时间:', new Date().toISOString());
  console.log('目标URL: http://*************:3000/api/permissions/user/my');
  console.log('===================================');

  // 使用有效的token调用权限API
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyLWFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiIsImlhdCI6MTczNTcxMzY5NCwiZXhwIjoxNzM1NzE3Mjk0fQ.PEVfMvQ2KYcN5hpGVnOO9gMBqq8HNqMtpJWYxYdZRXg';
  
  const permissionOptions = {
    hostname: '*************',
    port: 3000,
    path: '/api/permissions/user/my',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    console.log('🔄 发送权限API请求...');
    
    const permissionReq = http.request(permissionOptions, (permissionRes) => {
      let permissionBody = '';
      
      permissionRes.on('data', (chunk) => {
        permissionBody += chunk;
      });
      
      permissionRes.on('end', () => {
        try {
          const permissionResult = JSON.parse(permissionBody);
          
          console.log('🎯 权限API调用完成！');
          console.log('状态码:', permissionRes.statusCode);
          console.log('响应数据:');
          console.log(JSON.stringify(permissionResult, null, 2));
          console.log('===================================');
          console.log('⚠️ 现在请立即检查后端控制台！');
          console.log('应该可以看到以下调试信息:');
          console.log('- PermissionController getCurrentUserPermissions 被调用');
          console.log('- PermissionService getUserPermissions 开始执行');
          console.log('- SQL查询和结果日志');
          console.log('===================================');
          
          resolve(permissionResult);
        } catch (err) {
          console.error('❌ 解析权限API响应失败:', err.message);
          reject(err);
        }
      });
    });

    permissionReq.on('error', (err) => {
      console.error('❌ 权限API请求失败:', err.message);
      reject(err);
    });

    permissionReq.end();
  });
}

// 执行调用
callPermissionAPI()
  .then((result) => {
    console.log('✅ API调用成功完成');
    console.log('请查看后端控制台输出！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ API调用失败:', error.message);
    process.exit(1);
  }); 