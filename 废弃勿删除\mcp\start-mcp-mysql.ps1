# MCP MySQL Server 启动脚本
# 作者: AI Assistant
# 描述: 启动MCP MySQL服务器的便捷脚本

param(
  [string]$ConfigFile = "mcp-mysql-config.env",
  [switch]$Help
)

if ($Help) {
  Write-Host "MCP MySQL Server 启动脚本" -ForegroundColor Green
  Write-Host ""
  Write-Host "用法: .\start-mcp-mysql.ps1 [-ConfigFile <配置文件路径>] [-Help]" -ForegroundColor Yellow
  Write-Host ""
  Write-Host "参数:"
  Write-Host "  -ConfigFile   环境配置文件路径 (默认: mcp-mysql-config.env)"
  Write-Host "  -Help         显示此帮助信息"
  Write-Host ""
  Write-Host "示例:"
  Write-Host "  .\start-mcp-mysql.ps1"
  Write-Host "  .\start-mcp-mysql.ps1 -ConfigFile custom-config.env"
  exit 0
}

Write-Host "正在启动 MCP MySQL Server..." -ForegroundColor Green

# 检查配置文件是否存在
if (-not (Test-Path $ConfigFile)) {
  Write-Host "错误: 配置文件 '$ConfigFile' 不存在" -ForegroundColor Red
  Write-Host "请确保配置文件存在，或使用 -ConfigFile 参数指定正确的路径" -ForegroundColor Yellow
  exit 1
}

# 读取环境变量
Write-Host "正在加载配置文件: $ConfigFile" -ForegroundColor Yellow
Get-Content $ConfigFile | ForEach-Object {
  if ($_ -match '^([^#].+?)=(.*)$') {
    $name = $matches[1].Trim()
    $value = $matches[2].Trim()
    Write-Host "  设置环境变量: $name" -ForegroundColor Cyan
    [Environment]::SetEnvironmentVariable($name, $value, "Process")
  }
}

Write-Host ""
Write-Host "配置信息:" -ForegroundColor Green
Write-Host "  MySQL主机: $env:MYSQL_HOST" -ForegroundColor White
Write-Host "  MySQL端口: $env:MYSQL_PORT" -ForegroundColor White
Write-Host "  MySQL用户: $env:MYSQL_USER" -ForegroundColor White
Write-Host "  MySQL数据库: $env:MYSQL_DB" -ForegroundColor White
Write-Host "  远程模式: $env:IS_REMOTE_MCP" -ForegroundColor White
Write-Host "  服务端口: $env:PORT" -ForegroundColor White
Write-Host ""

# 检查Node.js是否可用
try {
  $nodeVersion = node --version
  Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
}
catch {
  Write-Host "错误: Node.js未找到或无法执行" -ForegroundColor Red
  Write-Host "请确保Node.js已正确安装并在PATH中" -ForegroundColor Yellow
  exit 1
}

# 启动MCP服务器
Write-Host "正在启动MCP MySQL服务器..." -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

try {
  npx @benborla29/mcp-server-mysql
}
catch {
  Write-Host "错误: MCP服务器启动失败" -ForegroundColor Red
  Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Yellow
  exit 1
} 