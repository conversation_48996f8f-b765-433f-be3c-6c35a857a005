<!-- 用户权限编辑器组件 -->
<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="600px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="loading" class="text-center py-8">
      <a-spin size="large" />
      <div class="mt-4 text-gray-500">加载用户数据中...</div>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <a-result status="error" :title="error" />
    </div>

    <div v-else class="space-y-6">
      <!-- 用户基本信息 -->
      <a-card size="small" title="用户信息">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="用户名">
            {{ user?.username || '' }}
          </a-descriptions-item>
          <a-descriptions-item label="姓名">
            {{ user?.full_name || '' }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱">
            {{ user?.email || '' }}
          </a-descriptions-item>
          <a-descriptions-item label="部门">
            {{ user?.department || '未设置' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 角色分配 -->
      <a-card size="small" title="角色分配">
        <div style="margin-bottom: 16px">
          <label style="display: block; margin-bottom: 8px; font-weight: 500">选择角色：</label>
          <a-select
            v-model:value="selectedRoleId"
            :loading="rolesLoading"
            placeholder="请选择角色"
            style="width: 100%"
            allow-clear
          >
            <a-select-option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.displayName }}
              <a-tag
                :color="role.is_active ? 'green' : 'red'"
                size="small"
                style="margin-left: 8px"
              >
                {{ role.is_active ? '启用' : '禁用' }}
              </a-tag>
            </a-select-option>
          </a-select>
        </div>

        <div v-if="selectedRole" style="padding: 12px; background: #f5f5f5; border-radius: 6px">
          <div style="font-size: 12px; color: #666">
            <strong>角色说明：</strong>{{ selectedRole.description || '无说明' }}
          </div>
        </div>
      </a-card>

      <!-- 账户状态 -->
      <a-card size="small" title="账户状态">
        <div style="margin-bottom: 16px">
          <label style="display: block; margin-bottom: 8px; font-weight: 500">账户状态：</label>
          <a-switch v-model:checked="isActive" checked-children="启用" un-checked-children="禁用" />
        </div>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { getUserById, getRoles, updateUser, type User, type Role } from '@/api/permissions'

// 类型定义
interface ApiError {
  message: string
  response?: {
    data?: {
      message: string
    }
  }
}

interface UpdateUserData {
  role_id?: string
  status: 'active' | 'inactive'
}

// Props
interface Props {
  userId?: string
  open?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
})

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  success: []
}>()

// State
const loading = ref(false)
const rolesLoading = ref(false)
const error = ref('')
const user = ref<User | null>(null)
const roles = ref<Role[]>([])
const selectedRoleId = ref<string | null>(null)
const isActive = ref<boolean>(true)

// Computed
const visible = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const modalTitle = computed(() => {
  return `编辑用户权限 - ${user.value?.full_name || user.value?.username || ''}`
})

const selectedRole = computed(() => {
  return roles.value.find(role => role.id === selectedRoleId.value) || null
})

// Methods
const loadUserData = async () => {
  if (!props.userId) return

  loading.value = true
  error.value = ''

  try {
    console.log('🔍 [用户权限编辑] 开始获取用户详情, userId:', props.userId)
    const response = await getUserById(props.userId)
    console.log('✅ [用户权限编辑] 用户详情API响应:', response)

    if (response?.data) {
      user.value = response.data
      selectedRoleId.value = response.data.role_id || null
      isActive.value = response.data.status === 'active'
      console.log('✅ [用户权限编辑] 用户数据设置完成:', {
        userId: props.userId,
        userData: user.value,
        selectedRole: selectedRoleId.value,
        isActive: isActive.value,
      })
    } else {
      error.value = '获取用户详情失败：数据为空'
      console.error('❌ [用户权限编辑] 用户详情数据为空')
    }
  } catch (err: unknown) {
    const apiError = err as ApiError
    error.value = `获取用户详情失败: ${apiError.message}`
    console.error('❌ [用户权限编辑] 获取用户详情失败:', err)
  } finally {
    loading.value = false
  }
}

const loadRoles = async () => {
  rolesLoading.value = true

  try {
    console.log('🔍 [用户权限编辑] 开始加载角色列表')
    const response = await getRoles()
    roles.value = response.data || []
    console.log('✅ [用户权限编辑] 角色列表加载成功:', roles.value.length, '个角色')
  } catch (err: unknown) {
    console.error('❌ [用户权限编辑] 加载角色列表失败:', err)
    message.error('加载角色列表失败')
  } finally {
    rolesLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!props.userId || !user.value) {
    console.error('❌ [用户权限编辑] 用户数据为空，无法提交')
    return
  }

  try {
    loading.value = true
    console.log('📤 [用户权限编辑] 开始提交用户更新')

    const updateData: UpdateUserData = {
      role_id: selectedRoleId.value || undefined,
      status: isActive.value ? 'active' : 'inactive',
    }

    console.log('📤 [用户权限编辑] 准备提交的数据:', {
      userId: user.value.id,
      updateData,
    })

    const response = await updateUser(user.value.id, updateData)
    console.log('✅ [用户权限编辑] 用户更新成功:', response)

    message.success('用户权限更新成功')
    emit('success')
    handleCancel()
  } catch (err: unknown) {
    console.error('❌ [用户权限编辑] 用户更新失败:', err)
    const apiError = err as ApiError
    const errorMsg = apiError?.response?.data?.message || apiError?.message || '更新失败'
    message.error(`用户权限更新失败: ${errorMsg}`)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

// 重置数据
const resetData = () => {
  user.value = null
  roles.value = []
  selectedRoleId.value = null
  isActive.value = true
  error.value = ''
  loading.value = false
  rolesLoading.value = false
}

// Watch
watch(
  () => props.open,
  newValue => {
    if (newValue && props.userId) {
      console.log('🚀 [用户权限编辑] 对话框打开，开始加载数据，userId:', props.userId)
      loadUserData()
      loadRoles()
    } else if (!newValue) {
      // 对话框关闭时重置数据
      resetData()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.space-y-6 > * + * {
  margin-top: 24px;
}
</style>
