/**
 * 数据库表结构类型定义
 * 基于实际的MySQL表结构定义
 * 
 * <AUTHOR> Team
 * @since 2024-12-19
 */

/**
 * 基础查询结果接口
 */
export interface CountResult {
  count: number;
}

export interface TotalResult {
  total: number;
}

/**
 * 用户表结构
 */
export interface UserRow {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  full_name: string;
  phone?: string;
  avatar_url?: string;
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  role_id: string;
  department?: string;
  position?: string;
  last_login_at: Date | null;
  last_login_ip?: string;
  password_changed_at: Date;
  failed_login_attempts: number;
  locked_until: Date | null;
  email_verified: boolean;
  phone_verified: boolean;
  two_factor_enabled: boolean;
  created_by?: string;
  created_at: Date;
  updated_by?: string;
  updated_at: Date;
  deleted_at: Date | null;
}

/**
 * 用户角色表结构
 */
export interface UserRoleRow {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string; // JSON字符串
  is_system: boolean;
  is_active: boolean;
  sort_order: number;
  created_by?: string;
  created_at: Date;
  updated_by?: string;
  updated_at: Date;
}

/**
 * 用户权限表结构
 */
export interface UserPermissionRow {
  id: string;
  code: string;
  name: string;
  description?: string;
  module: string;
  resource?: string;
  action?: string;
  conditions?: string; // JSON字符串
  is_active: boolean;
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * 用户连接查询结果（带角色信息）
 */
export interface UserWithRoleRow extends UserRow {
  role_name: string;
  role_display_name: string;
  role_permissions: string; // JSON字符串
  role_color?: string;
}

/**
 * 用户统计查询结果
 */
export interface UserStatsRow {
  total_users: number;
  active_users: number;
  inactive_users: number;
  suspended_users: number;
  online_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
}

/**
 * 文档表结构
 */
export interface FilenetDocumentRow {
  id: string;
  fn_doc_id: string;
  original_name: string;
  file_size?: number;
  mime_type?: string;
  extension?: string;
  version: number;
  file_hash?: string;
  created_by?: string;
  last_modified_by?: string;
  template_id?: string;
  uploaded_at: Date;
  created_at: Date;
  updated_at: Date;
  is_deleted: boolean;
}

/**
 * 文档版本表结构
 */
export interface FilenetDocumentVersionRow {
  id: string;
  doc_id: string;
  fn_doc_id: string;
  version: number;
  file_hash?: string;
  modified_by?: string;
  modified_at: Date;
  file_size?: number;
  comment?: string;
}

/**
 * 模板分类表结构
 */
export interface TemplateCategoryRow {
  id: string;
  name: string;
  parent_id?: string;
  description?: string;
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * 模板表结构
 */
export interface TemplateRow {
  id: string;
  name: string;
  category_id?: string;
  source_doc_id?: string; // 重命名：引用的源文档ID（当source_type=reference时使用）
  fn_doc_id?: string; // 新增：模板的FileNet文档ID
  source_type?: 'upload' | 'create' | 'reference'; // 新增：模板来源类型
  description?: string;
  current_version: number;
  created_by?: string;
  created_at: Date;
  updated_by?: string;
  updated_at: Date;
  status: 'enabled' | 'disabled';
  is_deleted: boolean;
}

/**
 * 系统配置表结构
 */
export interface SystemSettingRow {
  setting_key: string;
  setting_value: string;
  description?: string;
  setting_type?: string;
  is_encrypted?: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * 密码历史表结构
 */
export interface PasswordHistoryRow {
  id: string;
  user_id: string;
  password_hash: string;
  created_at: Date;
}

/**
 * 审计日志表结构
 */
export interface AuditLogRow {
  id: string;
  user_id?: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  details?: string; // JSON字符串
  ip_address?: string;
  user_agent?: string;
  status: 'success' | 'failed' | 'error';
  error_message?: string;
  execution_time?: number;
  created_at: Date;
}

/**
 * 用户会话表结构
 */
export interface UserSessionRow {
  id: string;
  user_id: string;
  session_token: string;
  refresh_token?: string;
  device_type?: string;
  device_name?: string;
  user_agent?: string;
  ip_address?: string;
  location?: string;
  is_active: boolean;
  expires_at: Date;
  last_activity_at: Date;
  created_at: Date;
}

/**
 * 数据库查询结果联合类型
 */
export type DatabaseRow = 
  | UserRow 
  | UserRoleRow 
  | UserPermissionRow 
  | UserWithRoleRow 
  | UserStatsRow
  | FilenetDocumentRow 
  | FilenetDocumentVersionRow 
  | TemplateCategoryRow 
  | TemplateRow 
  | SystemSettingRow 
  | PasswordHistoryRow 
  | AuditLogRow 
  | UserSessionRow
  | CountResult 
  | TotalResult
  | Record<string, unknown>; 