import { IsOptional, IsString, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OnlyOfficeConfig } from '../interfaces/editor-config.interface';

/**
 * 编辑器配置查询参数DTO
 * 
 * @description 获取OnlyOffice编辑器配置时的查询参数
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */
export class EditorConfigQueryDto {
  @ApiPropertyOptional({
    description: '配置模板名称',
    example: 'default',
  })
  @IsOptional()
  @IsString()
  template?: string;

  @ApiPropertyOptional({
    description: '是否隐藏聊天功能',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  hideChat?: boolean;

  @ApiPropertyOptional({
    description: '是否隐藏评论功能',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  hideComments?: boolean;

  @ApiPropertyOptional({
    description: '是否设置为只读模式',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  readonly?: boolean;

  @ApiPropertyOptional({
    description: '用户ID（可选，用于自定义用户信息）',
    example: 'user-123',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: '用户名称（可选，用于自定义用户信息）',
    example: '张三',
  })
  @IsOptional()
  @IsString()
  userName?: string;
}

/**
 * 编辑器配置响应DTO
 */
export class EditorConfigResponseDto implements OnlyOfficeConfig {
  @ApiProperty({
    description: '文档配置',
    example: {
      fileType: 'docx',
      key: 'doc-123-1640123456789',
      title: '测试文档.docx',
      url: 'http://localhost:3000/api/documents/123',
      permissions: {
        edit: true,
        download: true,
        review: true,
        comment: true,
        fillForms: true,
      },
      dbId: '123e4567-e89b-12d3-a456-426614174000',
    },
  })
  document: {
    fileType: string;
    key: string;
    title: string;
    url: string;
    permissions: {
      edit?: boolean;
      download?: boolean;
      review?: boolean;
      comment?: boolean;
      fillForms?: boolean;
      modifyFilter?: boolean;
      modifyContentControl?: boolean;
    };
    dbId?: string;
  };

  @ApiProperty({
    description: '文档类型',
    enum: ['word', 'cell', 'slide', 'pdf'],
    example: 'word',
  })
  documentType: 'word' | 'cell' | 'slide' | 'pdf';

  @ApiProperty({
    description: '编辑器配置',
    example: {
      callbackUrl: 'http://localhost:3000/api/editor/callback',
      lang: 'zh',
      mode: 'edit',
      customization: {
        chat: false,
        comments: true,
        help: true,
        about: true,
        feedback: false,
        forcesave: true,
        review: true,
        toolbarNoTabs: false,
        toolbarHideFileName: false,
      },
      user: {
        id: 'user-1',
        name: '默认用户',
      },
      coEditing: {
        mode: 'fast',
        change: true,
      },
    },
  })
  editorConfig: {
    callbackUrl: string;
    lang: string;
    mode: 'edit' | 'view';
    customization: {
      chat?: boolean;
      comments?: boolean;
      help?: boolean;
      about?: boolean;
      feedback?: boolean;
      forcesave?: boolean;
      review?: boolean;
      toolbarNoTabs?: boolean;
      toolbarHideFileName?: boolean;
    };
    user: {
      id: string;
      name: string;
      group?: string;
      image?: string;
    };
    coEditing: {
      mode: 'fast' | 'strict';
      change: boolean;
    };
  };

  @ApiPropertyOptional({
    description: 'JWT认证令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token?: string;

  @ApiPropertyOptional({
    description: 'OnlyOffice API脚本URL',
    example: 'http://localhost/web-apps/apps/api/documents/api.js',
  })
  apiUrl?: string;
}

/**
 * 编辑器页面渲染数据DTO
 */
export class EditorPageDataDto {
  @ApiProperty({
    description: '文档标题',
    example: '测试文档.docx',
  })
  docTitle: string;

  @ApiProperty({
    description: 'OnlyOffice Document Server URL',
    example: 'http://localhost:8080',
  })
  docServerUrl: string;

  @ApiProperty({
    description: 'OnlyOffice API JS URL',
    example: 'http://localhost:8080/web-apps/apps/api/documents/api.js',
  })
  apiUrl: string;

  @ApiProperty({
    description: 'JWT认证令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token: string;

  @ApiProperty({
    description: 'OnlyOffice编辑器配置',
    type: EditorConfigResponseDto,
  })
  config: EditorConfigResponseDto;
} 