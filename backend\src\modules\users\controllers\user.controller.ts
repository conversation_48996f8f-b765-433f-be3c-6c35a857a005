import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UserService } from '../services/user.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import {
  CreateUserDto,
  UpdateUserDto,
  ChangePasswordDto,
  ResetPasswordDto,
  UserQueryDto,
  UserResponseDto,
  UserDetailResponseDto,
  UserListResponseDto,
  UserStatsResponseDto,
  UserPermissionQueryDto,
  UserListApiResponse,
  UserDetailApiResponse,
  UserApiResponse,
  UserStatsApiResponse,
} from '../dto/user.dto';

/**
 * 基础API响应接口
 */
interface BaseApiResponse {
  success: boolean;
  message: string;
  error?: string;
}

/**
 * 重置密码响应接口
 */
interface ResetPasswordResponse extends BaseApiResponse {}

/**
 * 用户详情响应接口
 */
interface UserDetailResponse extends BaseApiResponse {
  data?: Record<string, unknown>;
}

/**
 * 用户权限响应接口
 */
interface UserPermissionsResponse extends BaseApiResponse {
  data?: {
    data: Array<{
      id: string;
      username: string;
      email: string;
      fullName: string;
      roles: Array<{
        id: string;
        name: string;
        displayName: string;
        color: string;
      }>;
      permissions: string[];
      lastLoginAt: Date | null;
    }>;
    total: number;
    page: number;
    pageSize: number;
  };
}

/**
 * 用户当前权限响应接口
 */
interface CurrentUserPermissionsResponse extends BaseApiResponse {
  data?: {
    user: {
      id: string;
      username: string;
      email: string;
      fullName: string;
      lastLoginAt: Date | null;
      roles: Array<{
        id: string;
        name: string;
        displayName: string;
        color: string;
      }>;
      permissions: string[];
    };
    permissions: string[];
    roles: Array<{
      id: string;
      name: string;
      displayName: string;
      color: string;
    }>;
    summary?: {
      userId: string;
      username: string;
      email: string;
      rolesCount: number;
      permissionsCount: number;
      isAdminWithEmptyPermissions: boolean;
      appliedAdminFix: boolean;
      timestamp: string;
    };
  };
}

@ApiTags('用户管理')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiResponse({
    status: 200,
    description: '获取用户列表成功',
    type: UserListResponseDto,
  })
  async getUsers(@Query() query: UserQueryDto): Promise<UserListApiResponse> {
    try {
      const result = await this.userService.findMany(query);
      
      // 过滤敏感信息
      const filteredData = result.data.map(user => {
        const { password_hash: _, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      return {
        success: true,
        message: '获取用户列表成功',
        data: {
          ...result,
          data: filteredData,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: '获取用户列表失败',
        error: error.message,
      };
    }
  }

  @Get('stats')
  @ApiOperation({ summary: '获取用户统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取用户统计信息成功',
    type: UserStatsResponseDto,
  })
  async getUserStats(): Promise<UserStatsApiResponse> {
    try {
      const stats = await this.userService.getUserStats();
      return {
        success: true,
        message: '获取用户统计信息成功',
        data: stats,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取用户统计信息失败',
        error: error.message,
      };
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiResponse({
    status: 200,
    description: '获取用户详情成功',
    type: UserDetailResponseDto,
  })
  async getUserById(@Param('id') id: string): Promise<UserDetailApiResponse> {
    try {
      const user = await this.userService.findById(id);
      if (!user) {
        return {
          success: false,
          message: '用户不存在',
        };
      }

      // 过滤敏感信息
      const { password_hash: _, ...userWithoutPassword } = user;

      return {
        success: true,
        message: '获取用户详情成功',
        data: userWithoutPassword,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取用户详情失败',
        error: error.message,
      };
    }
  }

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({
    status: 201,
    description: '创建用户成功',
    type: UserResponseDto,
  })
  async createUser(@Body() createUserDto: CreateUserDto, @Request() req): Promise<UserApiResponse> {
    try {
      const currentUserId = req.user?.sub;
      const user = await this.userService.createUser(createUserDto, currentUserId);

      // 过滤敏感信息
      const { password_hash: _password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        message: '创建用户成功',
        data: userWithoutPassword,
      };
    } catch (error) {
      return {
        success: false,
        message: '创建用户失败',
        error: error.message,
      };
    }
  }

  @Put(':id')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiResponse({
    status: 200,
    description: '更新用户信息成功',
    type: UserResponseDto,
  })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req,
  ): Promise<UserApiResponse> {
    try {
      const currentUserId = req.user?.sub;
      const user = await this.userService.updateUser(id, updateUserDto, currentUserId);

      // 过滤敏感信息
      const { password_hash: _password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        message: '更新用户信息成功',
        data: userWithoutPassword,
      };
    } catch (error) {
      return {
        success: false,
        message: '更新用户信息失败',
        error: error.message,
      };
    }
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({
    status: 204,
    description: '删除用户成功',
  })
  async deleteUser(@Param('id') id: string, @Request() req): Promise<UserApiResponse> {
    try {
      const currentUserId = req.user?.sub;
      await this.userService.deleteUser(id, currentUserId);

      return {
        success: true,
        message: '删除用户成功',
      };
    } catch (error) {
      return {
        success: false,
        message: '删除用户失败',
        error: error.message,
      };
    }
  }

  @Post(':id/change-password')
  @ApiOperation({ summary: '修改用户密码' })
  @ApiResponse({
    status: 200,
    description: '修改密码成功',
  })
  async changePassword(
    @Param('id') id: string,
    @Body() changePasswordDto: ChangePasswordDto,
    @Request() req,
  ): Promise<UserApiResponse> {
    try {
      const currentUserId = req.user?.sub;
      
      // 只能修改自己的密码，除非是管理员
      if (currentUserId !== id) {
        // 这里可以添加权限检查逻辑
        return {
          success: false,
          message: '无权限修改其他用户的密码',
        };
      }

      await this.userService.changePassword(id, changePasswordDto);

      return {
        success: true,
        message: '修改密码成功',
      };
    } catch (error) {
      return {
        success: false,
        message: '修改密码失败',
        error: error.message,
      };
    }
  }

  @Post(':id/reset-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '重置用户密码（管理员功能）' })
  @ApiResponse({
    status: 200,
    description: '重置密码成功',
    type: UserResponseDto,
  })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
    @Request() req,
  ): Promise<ResetPasswordResponse> {
    try {
      const currentUserId = req.user?.sub;
      
      // 这里可以添加管理员权限检查逻辑
      await this.userService.resetPassword(resetPasswordDto, currentUserId);

      return {
        success: true,
        message: '重置密码成功',
      };
    } catch (error) {
      return {
        success: false,
        message: '重置密码失败',
        error: error.message,
      };
    }
  }

  @Get('profile/me')
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '获取当前用户信息成功',
    type: UserResponseDto,
  })
  async getCurrentUser(@Request() req): Promise<UserDetailResponse> {
    try {
      const userId = req.user?.sub;
      if (!userId) {
        return {
          success: false,
          message: '用户未登录',
        };
      }

      const user = await this.userService.findById(userId);
      if (!user) {
        return {
          success: false,
          message: '用户不存在',
        };
      }

      // 过滤敏感信息
      const { password_hash: _password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        message: '获取当前用户信息成功',
        data: userWithoutPassword,
      };
    } catch (error) {
      return {
        success: false,
        message: '获取当前用户信息失败',
        error: error.message,
      };
    }
  }

  @Put('profile/me')
  @ApiOperation({ summary: '更新当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '更新当前用户信息成功',
    type: UserResponseDto,
  })
  async updateCurrentUser(
    @Body() updateUserDto: UpdateUserDto,
    @Request() req,
  ): Promise<UserDetailResponse> {
    try {
      const userId = req.user?.sub;
      if (!userId) {
        return {
          success: false,
          message: '用户未登录',
        };
      }

      // 普通用户不能修改某些字段（如角色、状态等）
      const allowedFields = ['full_name', 'phone', 'avatar_url', 'department', 'position'];
      const filteredDto: Record<string, unknown> = {};
      allowedFields.forEach(field => {
        if (updateUserDto[field] !== undefined) {
          filteredDto[field] = updateUserDto[field];
        }
      });

      const user = await this.userService.updateUser(userId, filteredDto, userId);

      // 过滤敏感信息
      const { password_hash: _password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        message: '更新当前用户信息成功',
        data: userWithoutPassword,
      };
    } catch (error) {
      return {
        success: false,
        message: '更新当前用户信息失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取用户权限列表
   */
  @Get('permissions')
  @ApiOperation({ summary: '获取用户权限列表' })
  @ApiResponse({
    status: 200,
    description: '获取用户权限列表成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  username: { type: 'string' },
                  email: { type: 'string' },
                  fullName: { type: 'string' },
                  roles: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        name: { type: 'string' },
                        displayName: { type: 'string' },
                        color: { type: 'string' },
                      },
                    },
                  },
                  permissions: { type: 'array', items: { type: 'string' } },
                  lastLoginAt: { type: 'string' },
                },
              },
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' },
          },
        },
      },
    },
  })
  async getUserPermissions(@Query() query: UserPermissionQueryDto, @Request() req): Promise<UserPermissionsResponse> {
    try {
      console.log('🔍 [UserController] getUserPermissions 开始:', {
        query,
        user: req.user,
        headers: {
          authorization: req.headers?.authorization ? 'Bearer ***' : undefined,
          'user-agent': req.headers?.['user-agent'],
        },
        timestamp: new Date().toISOString(),
      });

      const result = await this.userService.getUserPermissions(query);
      
      console.log('✅ [UserController] getUserPermissions 成功:', {
        resultCount: result.data.length,
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
      });

      return {
        success: true,
        message: '获取用户权限列表成功',
        data: result,
      };
    } catch (error) {
      console.error('❌ [UserController] getUserPermissions 失败:', {
        error: error.message,
        stack: error.stack,
        query,
        user: req.user,
        timestamp: new Date().toISOString(),
      });

      return {
        success: false,
        message: '获取用户权限列表失败',
        error: error.message,
      };
    }
  }

  /**
   * 获取当前用户的权限信息
   */
  @Get('permissions/me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户权限信息' })
  @ApiResponse({
    status: 200,
    description: '获取当前用户权限信息成功',
  })
  async getCurrentUserPermissions(@Request() req): Promise<CurrentUserPermissionsResponse> {
    try {
      const userId = req.user?.sub;
      if (!userId) {
        return {
          success: false,
          message: '用户未登录',
        };
      }

      console.log('🔍 [UserController] getCurrentUserPermissions 开始:', {
        userId,
        userFromToken: req.user,
        headers: {
          authorization: req.headers?.authorization ? 'Bearer ***' : undefined,
        },
        timestamp: new Date().toISOString(),
      });

      const result = await this.userService.getCurrentUserPermissions(userId);
      
      console.log('✅ [UserController] getCurrentUserPermissions 成功:', {
        userId,
        userInfo: {
          id: result.user.id,
          username: result.user.username,
          email: result.user.email,
        },
        rolesCount: result.roles.length,
        permissionsCount: result.permissions.length,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        message: '获取当前用户权限信息成功',
        data: {
          ...result,
          summary: {
            userId: result.user.id,
            username: result.user.username,
            email: result.user.email,
            rolesCount: result.roles.length,
            permissionsCount: result.permissions.length,
            isAdminWithEmptyPermissions: result.user.username === 'admin' && result.permissions.length === 0,
            appliedAdminFix: result.user.username === 'admin' && result.permissions.length === 0,
            timestamp: new Date().toISOString(),
          },
        },
      };
    } catch (error) {
      console.error('❌ [UserController] getCurrentUserPermissions 失败:', {
        userId: req.user?.sub,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      return {
        success: false,
        message: '获取当前用户权限信息失败',
        error: error.message,
      };
    }
  }
} 