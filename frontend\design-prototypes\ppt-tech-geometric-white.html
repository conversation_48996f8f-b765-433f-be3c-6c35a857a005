<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        :root {
            --geo-white: #ffffff;
            --geo-gray-50: #f9fafb;
            --geo-gray-200: #e5e7eb;
            --geo-gray-400: #9ca3af;
            --geo-gray-700: #374151;
            --geo-gray-900: #111827;
            --geo-blue: #2563eb;
            --geo-indigo: #4f46e5;
            --geo-purple: #7c3aed;
            --geo-shadow: rgba(17, 24, 39, 0.1);
        }

        html {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--geo-gray-50);
        }

        body {
            width: 1280px;
            height: 720px;
            margin: 0;
            padding: 0;
            position: relative;
            overflow: hidden;
            background: var(--geo-white);
            font-family: 'Inter', 'SF Pro Display', sans-serif;
            flex-shrink: 0;
        }
        
        .slide-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: var(--geo-gray-900);
            position: relative;
            background: var(--geo-white);
        }
        
        /* 几何背景装饰 */
        .geometric-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .hex-pattern {
            position: absolute;
            top: -50px;
            right: -50px;
            width: 300px;
            height: 300px;
            opacity: 0.03;
            background-image: 
                radial-gradient(circle at 50% 50%, var(--geo-blue) 2px, transparent 2px);
            background-size: 30px 26px;
            background-position: 0 0, 15px 13px;
            transform: rotate(30deg);
        }
        
        .triangle-accent {
            position: absolute;
            bottom: 80px;
            left: 60px;
            width: 0;
            height: 0;
            border-left: 40px solid transparent;
            border-right: 40px solid transparent;
            border-bottom: 70px solid var(--geo-blue);
            opacity: 0.1;
        }
        
        .slide-header {
            padding: 70px 80px 30px 80px;
            position: relative;
            z-index: 1;
            background: linear-gradient(135deg, var(--geo-white) 0%, var(--geo-gray-50) 100%);
            border-bottom: 1px solid var(--geo-gray-200);
        }
        
        .slide-title {
            font-size: clamp(2.8rem, 4vw, 4.5rem);
            font-weight: 700;
            color: var(--geo-gray-900);
            margin: 0;
            line-height: 1.1;
            letter-spacing: -0.02em;
            position: relative;
        }
        
        .slide-title::before {
            content: "";
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 60%;
            background: linear-gradient(180deg, var(--geo-blue), var(--geo-purple));
            border-radius: 3px;
        }
        
        .slide-content {
            flex: 1;
            padding: 40px 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .content-main {
            font-size: clamp(1.3rem, 2.5vw, 1.9rem);
            line-height: 1.7;
            color: var(--geo-gray-700);
            font-weight: 400;
        }
        
        /* 几何风格列表 */
        .content-points {
            list-style: none;
            padding: 0;
            margin: 35px 0 0 0;
        }
        
        .content-points li {
            margin-bottom: 28px;
            padding-left: 60px;
            position: relative;
            font-size: 1.5rem;
            color: var(--geo-gray-900);
            font-weight: 500;
        }
        
        .content-points li:before {
            content: "";
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(45deg);
            width: 8px;
            height: 8px;
            background: var(--geo-blue);
            border-radius: 1px;
        }
        
        .content-points li:after {
            content: "";
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 10px solid var(--geo-indigo);
            opacity: 0.2;
        }
        
        /* 几何数据卡片 */
        .geo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin: 35px 0;
        }
        
        .geo-card {
            background: var(--geo-white);
            border: 2px solid var(--geo-gray-200);
            padding: 35px 25px;
            text-align: center;
            position: relative;
            transition: all 0.4s ease;
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
        }
        
        .geo-card::before {
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, var(--geo-blue), var(--geo-purple));
            z-index: -1;
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
            opacity: 0;
            transition: opacity 0.4s ease;
        }
        
        .geo-card:hover::before {
            opacity: 1;
        }
        
        .geo-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px var(--geo-shadow);
        }
        
        .geo-number {
            font-size: 3.2rem;
            font-weight: 800;
            color: var(--geo-blue);
            display: block;
            margin-bottom: 12px;
            letter-spacing: -0.02em;
        }
        
        .geo-label {
            font-size: 1rem;
            color: var(--geo-gray-700);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .slide-footer {
            position: absolute;
            bottom: 35px;
            right: 80px;
            font-size: 16px;
            color: var(--geo-gray-400);
            font-weight: 500;
            z-index: 1;
        }

        /* 装饰性几何元素 */
        .geo-decoration {
            position: absolute;
            pointer-events: none;
        }
        
        .geo-decoration.hexagon {
            top: 100px;
            right: 100px;
            width: 60px;
            height: 35px;
            background: var(--geo-indigo);
            opacity: 0.1;
            position: relative;
        }
        
        .geo-decoration.hexagon:before,
        .geo-decoration.hexagon:after {
            content: "";
            position: absolute;
            width: 0;
            border-left: 30px solid transparent;
            border-right: 30px solid transparent;
        }
        
        .geo-decoration.hexagon:before {
            bottom: 100%;
            border-bottom: 17px solid var(--geo-indigo);
        }
        
        .geo-decoration.hexagon:after {
            top: 100%;
            border-top: 17px solid var(--geo-indigo);
        }
        
        .geo-decoration.diamond {
            bottom: 120px;
            left: 100px;
            width: 30px;
            height: 30px;
            background: var(--geo-purple);
            transform: rotate(45deg);
            opacity: 0.15;
        }
        
        @media (max-width: 1280px) {
            body {
                width: 100vw;
                height: 56.25vw;
                max-height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="geometric-bg">
            <div class="hex-pattern"></div>
            <div class="triangle-accent"></div>
        </div>
        
        <div class="geo-decoration hexagon"></div>
        <div class="geo-decoration diamond"></div>
        
        <header class="slide-header">
            <h1 class="slide-title">{{ main_heading }}</h1>
        </header>
        
        <main class="slide-content">
            <div class="content-main">
                {{ page_content }}

                <!--
                <ul class="content-points">
                    <li>量子计算驱动的下一代处理器</li>
                    <li>分布式边缘计算网络架构</li>
                    <li>自适应机器学习算法引擎</li>
                    <li>区块链安全验证机制</li>
                </ul>
                -->

                <!--
                <div class="geo-grid">
                    <div class="geo-card">
                        <span class="geo-number">128</span>
                        <span class="geo-label">处理核心</span>
                    </div>
                    <div class="geo-card">
                        <span class="geo-number">5.8</span>
                        <span class="geo-label">TFLOPS</span>
                    </div>
                    <div class="geo-card">
                        <span class="geo-number">12GB</span>
                        <span class="geo-label">高速缓存</span>
                    </div>
                </div>
                -->
            </div>
        </main>
        
        <footer class="slide-footer">
            {{ current_page_number }} / {{ total_page_count }}
        </footer>
    </div>
</body>
</html> 