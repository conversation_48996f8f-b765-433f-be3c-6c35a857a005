/**
 * OnlyOffice文档服务器集成应用
 */
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const config = require('./config');
const routes = require('./routes');
const filenetRoutes = require('./routes/filenetRoutes');
const { errorHandler, notFoundHandler } = require('./middleware/error');

// 初始化Express应用
const app = express();

// 确保上传目录和临时目录存在
fs.ensureDirSync(config.storage.uploadDir);
fs.ensureDirSync(config.storage.tmpDir);

// 配置中间件
app.use(cors(config.cors));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static('public'));

// 处理 /favicon.ico 请求，避免404错误
app.get('/favicon.ico', (req, res) => res.status(204).end());

// 处理所有路由的OPTIONS请求
app.options('*', cors(config.cors));

// 配置EJS模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 注册路由
app.use('/', routes);
app.use('/api/filenet', filenetRoutes);
app.use('/api/config-templates', require('./routes/config-templates'));
app.use('/debug', require('./routes/debug'));
app.use('/', require('./routes/config'));

// 404处理
app.use(notFoundHandler);

// 错误处理中间件
app.use(errorHandler);

module.exports = app; 