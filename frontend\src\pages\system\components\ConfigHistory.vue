<template>
  <div class="config-history">
    <a-timeline>
      <a-timeline-item
        v-for="item in history"
        :key="item.id || item.setting_key"
        :color="getTimelineColor(item.operation)"
      >
        <div class="history-item">
          <div class="history-header">
            <span class="history-key">{{ item.setting_key }}</span>
            <span class="history-time">{{ formatTime(item.changed_at) }}</span>
          </div>
          <div class="history-changes">
            <div class="change-item">
              <span class="change-label">原值:</span>
              <span class="change-value old">{{ item.old_value || '(空)' }}</span>
            </div>
            <div class="change-item">
              <span class="change-label">新值:</span>
              <span class="change-value new">{{ item.new_value || '(空)' }}</span>
            </div>
          </div>
          <div class="history-footer">
            <span class="history-user">{{ item.changed_by }}</span>
            <a-tag :color="getOperationColor(item.operation)">
              {{ getOperationText(item.operation) }}
            </a-tag>
          </div>
        </div>
      </a-timeline-item>
    </a-timeline>

    <div v-if="!history || history.length === 0" class="empty-history">
      <a-empty description="暂无变更记录" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface HistoryItem {
  id?: string
  setting_key: string
  old_value: string
  new_value: string
  changed_by: string
  changed_at: string
  operation: 'create' | 'update' | 'delete'
}

interface Props {
  history: HistoryItem[]
}

defineProps<Props>()

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

const getTimelineColor = (operation: string) => {
  switch (operation) {
    case 'create':
      return 'green'
    case 'update':
      return 'blue'
    case 'delete':
      return 'red'
    default:
      return 'gray'
  }
}

const getOperationColor = (operation: string) => {
  switch (operation) {
    case 'create':
      return 'success'
    case 'update':
      return 'processing'
    case 'delete':
      return 'error'
    default:
      return 'default'
  }
}

const getOperationText = (operation: string) => {
  switch (operation) {
    case 'create':
      return '创建'
    case 'update':
      return '更新'
    case 'delete':
      return '删除'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.config-history {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  margin-left: 16px;
  padding: 8px 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-key {
  font-family: monospace;
  font-weight: 500;
  color: #1890ff;
}

.history-time {
  font-size: 12px;
  color: #999;
}

.history-changes {
  margin-bottom: 8px;
}

.change-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.change-label {
  width: 50px;
  font-size: 12px;
  color: #666;
}

.change-value {
  font-family: monospace;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.change-value.old {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.change-value.new {
  background-color: #f6ffed;
  color: #52c41a;
}

.history-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-user {
  font-size: 12px;
  color: #666;
}

.empty-history {
  padding: 40px 0;
  text-align: center;
}
</style>
