import{j as t,k as Oe,d as Ie,z as ye,r as w,Z as Be,q as _,x as O,s as e,h as i,b as S,c as A,v as x,a2 as Xe,e as r,i as n,F as se,t as c,K as Ye,a3 as Ke,g as pe,H as et,n as tt,a4 as st,S as Ee,m as U,T as Ue,a1 as ot,_ as xe,u as at,a5 as lt,U as fe,o as nt,A as Ce,a6 as ge,P as rt,R as it,G as ct,E as Re,O as Me,V as Te}from"./index-5218909a.js";import{r as me}from"./request-228c3d43.js";import{T as ut}from"./TeamOutlined-bfa2ee8d.js";import{S as dt}from"./SaveOutlined-064bb5bd.js";var pt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M296 250c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm184 144H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-48 458H208V148h560v320c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm440-88H728v-36.6c46.3-13.8 80-56.6 80-107.4 0-61.9-50.1-112-112-112s-112 50.1-112 112c0 50.7 33.7 93.6 80 107.4V764H520c-8.8 0-16 7.2-16 16v152c0 8.8 7.2 16 16 16h352c8.8 0 16-7.2 16-16V780c0-8.8-7.2-16-16-16zM646 620c0-27.6 22.4-50 50-50s50 22.4 50 50-22.4 50-50 50-50-22.4-50-50zm180 266H566v-60h260v60z"}}]},name:"audit",theme:"outlined"};const mt=pt;function De(p){for(var d=1;d<arguments.length;d++){var l=arguments[d]!=null?Object(arguments[d]):{},I=Object.keys(l);typeof Object.getOwnPropertySymbols=="function"&&(I=I.concat(Object.getOwnPropertySymbols(l).filter(function(C){return Object.getOwnPropertyDescriptor(l,C).enumerable}))),I.forEach(function(C){vt(p,C,l[C])})}return p}function vt(p,d,l){return d in p?Object.defineProperty(p,d,{value:l,enumerable:!0,configurable:!0,writable:!0}):p[d]=l,p}var ze=function(d,l){var I=De({},d,l.attrs);return t(Oe,De({},I,{icon:mt}),null)};ze.displayName="AuditOutlined";ze.inheritAttrs=!1;const _t=ze;var ft={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M866.9 169.9L527.1 54.1C523 52.7 517.5 52 512 52s-11 .7-15.1 2.1L157.1 169.9c-8.3 2.8-15.1 12.4-15.1 21.2v482.4c0 8.8 5.7 20.4 12.6 25.9L499.3 968c3.5 2.7 8 4.1 12.6 4.1s9.2-1.4 12.6-4.1l344.7-268.6c6.9-5.4 12.6-17 12.6-25.9V191.1c.2-8.8-6.6-18.3-14.9-21.2zM810 654.3L512 886.5 214 654.3V226.7l298-101.6 298 101.6v427.6zM402.9 528.8l-77.5 77.5a8.03 8.03 0 000 11.3l34 34c3.1 3.1 8.2 3.1 11.3 0l77.5-77.5c55.7 35.1 130.1 28.4 178.6-20.1 56.3-56.3 56.3-147.5 0-203.8-56.3-56.3-147.5-56.3-203.8 0-48.5 48.5-55.2 123-20.1 178.6zm65.4-133.3c31.3-31.3 82-31.3 113.2 0 31.3 31.3 31.3 82 0 113.2-31.3 31.3-82 31.3-113.2 0s-31.3-81.9 0-113.2z"}}]},name:"security-scan",theme:"outlined"};const gt=ft;function Ne(p){for(var d=1;d<arguments.length;d++){var l=arguments[d]!=null?Object(arguments[d]):{},I=Object.keys(l);typeof Object.getOwnPropertySymbols=="function"&&(I=I.concat(Object.getOwnPropertySymbols(l).filter(function(C){return Object.getOwnPropertyDescriptor(l,C).enumerable}))),I.forEach(function(C){yt(p,C,l[C])})}return p}function yt(p,d,l){return d in p?Object.defineProperty(p,d,{value:l,enumerable:!0,configurable:!0,writable:!0}):p[d]=l,p}var Pe=function(d,l){var I=Ne({},d,l.attrs);return t(Oe,Ne({},I,{icon:gt}),null)};Pe.displayName="SecurityScanOutlined";Pe.inheritAttrs=!1;const Le=Pe;var ht={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z"}}]},name:"tool",theme:"outlined"};const kt=ht;function je(p){for(var d=1;d<arguments.length;d++){var l=arguments[d]!=null?Object(arguments[d]):{},I=Object.keys(l);typeof Object.getOwnPropertySymbols=="function"&&(I=I.concat(Object.getOwnPropertySymbols(l).filter(function(C){return Object.getOwnPropertyDescriptor(l,C).enumerable}))),I.forEach(function(C){bt(p,C,l[C])})}return p}function bt(p,d,l){return d in p?Object.defineProperty(p,d,{value:l,enumerable:!0,configurable:!0,writable:!0}):p[d]=l,p}var $e=function(d,l){var I=je({},d,l.attrs);return t(Oe,je({},I,{icon:kt}),null)};$e.displayName="ToolOutlined";$e.inheritAttrs=!1;const St=$e,Ve=p=>me({method:"GET",url:"/permissions",params:p}),wt=p=>me({method:"GET",url:`/roles/${p}`}),Ct=(p,d)=>me({method:"PUT",url:`/roles/${p}/permissions`,data:{permissions:d}}),Ot=p=>me({method:"GET",url:`/users/${p}`}),It=(p,d)=>me({method:"PUT",url:`/users/${p}`,data:d}),xt=()=>me({method:"GET",url:"/roles"}),zt={key:0,class:"loading-container"},Pt={class:"error-container"},$t={class:"permission-editor-content"},At={class:"role-header"},Et={class:"role-avatar"},Ut={class:"role-details"},Rt={class:"role-title"},Mt={class:"role-description"},Tt={class:"role-meta"},Dt={class:"permission-stats"},Nt={class:"permissions-container"},Lt={class:"module-header"},jt={class:"module-title-section"},Vt={class:"module-icon"},Bt={class:"module-info"},Ht={class:"module-title"},qt={class:"module-subtitle"},Ft={class:"module-actions"},Gt={class:"checkbox-label"},Jt={class:"permissions-list"},Qt={class:"permissions-grid"},Zt={class:"permission-content"},Wt={class:"permission-main"},Xt={class:"permission-name"},Yt={class:"permission-details"},Kt={class:"permission-code"},es={class:"permission-description"},ts={class:"footer-actions"},ss=Ie({__name:"RolePermissionEditor",props:{open:{type:Boolean,required:!0},roleId:{type:String,required:!0}},emits:["update:open","success"],setup(p,{emit:d}){const l=p,I=d,C=ye({get:()=>l.open,set:u=>I("update:open",u)}),D=w(!1),N=w(""),h=w(null),R=w([]),k=w([]),E=w([]),B=w(!1),ne=w(!1),W=ye(()=>R.value.map(u=>u.code)),F=u=>({documents:"文档管理",templates:"模板管理",config:"配置管理",users:"用户管理",roles:"角色管理",permissions:"权限管理",audit:"审计日志",system:"系统管理"})[u]||u,L=u=>{const s=E.value.find(f=>f.module===u);return s?s.permissions.map(f=>f.code):[]},ee=u=>{const s=L(u);return s.length>0&&s.every(f=>k.value.includes(f))},oe=u=>{const s=L(u),f=s.filter(M=>k.value.includes(M)).length;return f>0&&f<s.length},ie=u=>({read:"blue",create:"green",update:"orange",delete:"red","*":"purple"})[u]||"default",g=u=>({read:"查看",create:"创建",update:"编辑",delete:"删除","*":"全部权限"})[u]||u,v=u=>({documents:Ue,templates:Ue,config:Ee,users:ot,roles:Le,permissions:Le,audit:_t,system:St})[u]||Ee,Q=u=>{u.target.checked?k.value=[...W.value]:k.value=[]},H=(u,s)=>{var y;const M=(((y=E.value.find(T=>T.module===s))==null?void 0:y.permissions)||[]).map(T=>T.code);if(u.target.checked){const T=[...new Set([...k.value,...M])];k.value=T}else k.value=k.value.filter(T=>!M.includes(T))},X=(u,s)=>{u.target.checked?k.value=[...k.value,s]:k.value=k.value.filter(f=>f!==s)},ce=u=>{const s=L(u),f=s.filter(M=>k.value.includes(M)).length;return f===0?"未选择":f===s.length?"全选":`已选 ${f}/${s.length}`},ae=async()=>{var u;if(l.roleId){D.value=!0,N.value="";try{console.log("🔍 [角色权限编辑] 开始加载数据，角色ID:",l.roleId);const s=await wt(l.roleId);console.log("✅ [角色权限编辑] 角色数据响应:",s),h.value=s.data||s,console.log("🔍 [角色权限编辑] 开始分页加载所有权限...");let f=[],M=1;const y=10;console.log("🔍 [角色权限编辑] 开始分页加载权限列表，第1页...");const T=await Ve({page:M,pageSize:y});console.log("✅ [角色权限编辑] 第一页权限响应:",T);const j=T.data||T;if(j.data&&Array.isArray(j.data)){f=[...j.data];const $=j.total||j.data.length;if(console.log(`🔍 [角色权限编辑] 第${M}页加载完成: 当前${f.length}个，总共${$}个`),f.length>=$)console.log("✅ [角色权限编辑] 第一页已包含所有权限");else{const G=Math.ceil($/y);console.log(`🔍 [角色权限编辑] 需要继续加载 ${G-1} 页权限...`);for(let J=2;J<=G;J++){console.log(`🔍 [角色权限编辑] 加载第${J}页权限...`);const z=await Ve({page:J,pageSize:y});console.log(`✅ [角色权限编辑] 第${J}页权限响应:`,z);const P=z.data||z;P.data&&Array.isArray(P.data)?(f.push(...P.data),console.log(`✅ [角色权限编辑] 第${J}页加载完成，新增${P.data.length}个权限，总计${f.length}个`)):console.warn(`⚠️ [角色权限编辑] 第${J}页数据格式异常:`,P)}console.log(`✅ [角色权限编辑] 所有权限分页加载完成，最终总数: ${f.length}`)}}else console.warn("⚠️ [角色权限编辑] 第一页权限数据格式异常:",j);console.log("🔍 [角色权限编辑] 最终权限列表，总数:",f.length),console.log("🔍 [角色权限编辑] 权限详情:",f),R.value=f;const Y=new Map;f.forEach($=>{const G=$.module||"other";Y.has(G)||Y.set(G,[]),Y.get(G).push($)}),E.value=Array.from(Y.entries()).map(([$,G])=>({module:$,displayName:F($),permissions:G.sort((J,z)=>J.name.localeCompare(z.name))})),console.log("✅ [角色权限编辑] 最终角色数据:",h.value),console.log("✅ [角色权限编辑] 最终权限模块数据:",E.value);const ue={totalPermissions:f.length,moduleCount:E.value.length,moduleStats:E.value.map($=>({module:$.module,displayName:$.displayName,count:$.permissions.length}))};console.log("📊 [角色权限编辑] 权限统计:",ue),k.value=Array.isArray((u=h.value)==null?void 0:u.permissions)?h.value.permissions:[],console.log("✅ [角色权限编辑] 已选权限:",k.value)}catch(s){console.error("❌ [角色权限编辑] 加载数据失败:",s);const f=s instanceof Error?s.message:"加载数据失败";N.value=f}finally{D.value=!1}}},le=async()=>{if(!l.roleId){U.error("角色ID不能为空");return}D.value=!0;try{console.log("🔄 [角色权限编辑] 开始保存权限配置..."),console.log("🔄 [角色权限编辑] 角色ID:",l.roleId),console.log("🔄 [角色权限编辑] 选中的权限:",k.value),await Ct(l.roleId,k.value),console.log("✅ [角色权限编辑] 权限分配成功"),U.success("权限分配成功"),I("success"),C.value=!1}catch(u){console.error("❌ [角色权限编辑] 权限分配失败:",u);const s=u instanceof Error?u.message:"未知错误";U.error(`权限分配失败: ${s}`)}finally{D.value=!1}};return Be(()=>l.open,u=>{u&&l.roleId&&ae()},{immediate:!0}),(u,s)=>{const f=_("a-spin"),M=_("a-button"),y=_("a-result"),T=_("a-avatar"),j=_("a-tag"),Y=_("a-card"),ue=_("a-divider"),$=_("a-checkbox"),G=_("a-space"),J=_("a-modal");return i(),O(J,{open:C.value,"onUpdate:open":s[1]||(s[1]=z=>C.value=z),title:h.value?`编辑角色权限 - ${h.value.displayName||h.value.name}`:"编辑角色权限",width:"900px","confirm-loading":D.value,footer:null,maskClosable:!1,centered:"",class:"role-permission-modal"},{default:e(()=>[S(" Loading状态 "),D.value?(i(),A("div",zt,[t(f,{size:"large"},{indicator:e(()=>[t(x(Xe),{style:{"font-size":"24px"},spin:""})]),_:1}),s[2]||(s[2]=r("p",{class:"loading-text"},"正在加载权限数据...",-1))])):N.value?(i(),A(se,{key:1},[S(" 错误状态 "),r("div",Pt,[t(y,{status:"error",title:N.value},{extra:e(()=>[t(M,{type:"primary",onClick:ae},{default:e(()=>s[3]||(s[3]=[n("重新加载")])),_:1,__:[3]})]),_:1},8,["title"])])],2112)):(i(),A(se,{key:2},[S(" 主要内容 "),r("div",$t,[S(" 角色信息卡片 "),t(Y,{class:"role-info-card",size:"small"},{default:e(()=>{var z,P,de,he;return[r("div",At,[r("div",Et,[t(T,{size:48,style:{backgroundColor:"#1890ff",fontSize:"18px"}},{default:e(()=>{var K,re,ke,o;return[n(c((o=(ke=((K=h.value)==null?void 0:K.displayName)||((re=h.value)==null?void 0:re.name))==null?void 0:ke.charAt(0))==null?void 0:o.toUpperCase()),1)]}),_:1})]),r("div",Ut,[r("h3",Rt,c(((z=h.value)==null?void 0:z.displayName)||((P=h.value)==null?void 0:P.name)),1),r("p",Mt,c(((de=h.value)==null?void 0:de.description)||"系统管理权限管理，拥有所有权限"),1),r("div",Tt,[t(j,{color:"blue"},{default:e(()=>{var K;return[t(x(ut)),n(" 角色ID: "+c((K=h.value)==null?void 0:K.id),1)]}),_:1}),t(j,{color:(he=h.value)!=null&&he.is_active?"green":"red"},{default:e(()=>{var K,re;return[(K=h.value)!=null&&K.is_active?(i(),O(x(Ye),{key:0})):(i(),O(x(Ke),{key:1})),n(" "+c((re=h.value)!=null&&re.is_active?"启用":"禁用"),1)]}),_:1},8,["color"])])])])]}),_:1}),S(" 权限分配区域 "),t(Y,{title:"权限分配",class:"permissions-card"},{extra:e(()=>[t(G,null,{default:e(()=>[r("span",Dt,[s[4]||(s[4]=n(" 已选择 ")),t(j,{color:"blue"},{default:e(()=>[n(c(k.value.length),1)]),_:1}),s[5]||(s[5]=n(" / 总共 ")),t(j,{color:"gray"},{default:e(()=>[n(c(W.value.length),1)]),_:1}),s[6]||(s[6]=n(" 个权限 "))]),t(ue,{type:"vertical"}),t($,{indeterminate:B.value,checked:ne.value,onChange:Q,class:"select-all-checkbox"},{default:e(()=>s[7]||(s[7]=[r("strong",null,"全选",-1)])),_:1,__:[7]},8,["indeterminate","checked"])]),_:1})]),default:e(()=>[r("div",Nt,[(i(!0),A(se,null,pe(E.value,z=>(i(),A("div",{key:z.module,class:"permission-module"},[S(" 模块头部 "),r("div",Lt,[r("div",jt,[r("div",Vt,[(i(),O(et(v(z.module))))]),r("div",Bt,[r("h4",Ht,c(z.displayName),1),r("span",qt,c(z.permissions.length)+" 个权限",1)])]),r("div",Ft,[t($,{indeterminate:oe(z.module),checked:ee(z.module),onChange:P=>H(P,z.module),class:"module-checkbox"},{default:e(()=>[r("span",Gt,c(ce(z.module)),1)]),_:2},1032,["indeterminate","checked","onChange"])])]),S(" 权限列表 "),r("div",Jt,[r("div",Qt,[(i(!0),A(se,null,pe(z.permissions,P=>(i(),A("div",{key:P.code,class:tt(["permission-item",{"permission-selected":k.value.includes(P.code)}])},[t($,{checked:k.value.includes(P.code),onChange:de=>X(de,P.code),class:"permission-checkbox"},{default:e(()=>[r("div",Zt,[r("div",Wt,[r("span",Xt,c(P.name),1),t(j,{color:ie(P.action),size:"small",class:"action-tag"},{default:e(()=>[n(c(g(P.action)),1)]),_:2},1032,["color"])]),r("div",Yt,[r("span",Kt,c(P.code),1),r("span",es,c(P.description),1)])])]),_:2},1032,["checked","onChange"])],2))),128))])])]))),128))])]),_:1}),S(" 底部操作按钮 "),r("div",ts,[t(G,{size:"large"},{default:e(()=>[t(M,{size:"large",onClick:s[0]||(s[0]=z=>C.value=!1)},{default:e(()=>[t(x(st)),s[8]||(s[8]=n(" 取消 "))]),_:1,__:[8]}),t(M,{type:"primary",size:"large",loading:D.value,onClick:le,class:"save-button"},{default:e(()=>[t(x(dt)),s[9]||(s[9]=n(" 保存权限配置 "))]),_:1,__:[9]},8,["loading"])]),_:1})])])],2112))]),_:1},8,["open","title","confirm-loading"])}}});const os=xe(ss,[["__scopeId","data-v-7ea3971a"],["__file","D:/Code/OnlyOffice/frontend/src/components/RolePermissionEditor.vue"]]),as={key:0,class:"text-center py-8"},ls={key:1,class:"text-center py-8"},ns={key:2,class:"space-y-6"},rs={style:{"margin-bottom":"16px"}},is={key:0,style:{padding:"12px",background:"#f5f5f5","border-radius":"6px"}},cs={style:{"font-size":"12px",color:"#666"}},us={style:{"margin-bottom":"16px"}},ds=Ie({__name:"UserPermissionEditor",props:{userId:{type:String,required:!1},open:{type:Boolean,required:!1,default:!1}},emits:["update:open","success"],setup(p,{emit:d}){const l=p,I=d,C=w(!1),D=w(!1),N=w(""),h=w(null),R=w([]),k=w(null),E=w(!0),B=ye({get:()=>l.open,set:g=>I("update:open",g)}),ne=ye(()=>{var g,v;return`编辑用户权限 - ${((g=h.value)==null?void 0:g.full_name)||((v=h.value)==null?void 0:v.username)||""}`}),W=ye(()=>R.value.find(g=>g.id===k.value)||null),F=async()=>{if(l.userId){C.value=!0,N.value="";try{console.log("🔍 [用户权限编辑] 开始获取用户详情, userId:",l.userId);const g=await Ot(l.userId);console.log("✅ [用户权限编辑] 用户详情API响应:",g),g!=null&&g.data?(h.value=g.data,k.value=g.data.role_id||null,E.value=g.data.status==="active",console.log("✅ [用户权限编辑] 用户数据设置完成:",{userId:l.userId,userData:h.value,selectedRole:k.value,isActive:E.value})):(N.value="获取用户详情失败：数据为空",console.error("❌ [用户权限编辑] 用户详情数据为空"))}catch(g){const v=g;N.value=`获取用户详情失败: ${v.message}`,console.error("❌ [用户权限编辑] 获取用户详情失败:",g)}finally{C.value=!1}}},L=async()=>{D.value=!0;try{console.log("🔍 [用户权限编辑] 开始加载角色列表");const g=await xt();R.value=g.data||[],console.log("✅ [用户权限编辑] 角色列表加载成功:",R.value.length,"个角色")}catch(g){console.error("❌ [用户权限编辑] 加载角色列表失败:",g),U.error("加载角色列表失败")}finally{D.value=!1}},ee=async()=>{var g,v;if(!l.userId||!h.value){console.error("❌ [用户权限编辑] 用户数据为空，无法提交");return}try{C.value=!0,console.log("📤 [用户权限编辑] 开始提交用户更新");const Q={role_id:k.value||void 0,status:E.value?"active":"inactive"};console.log("📤 [用户权限编辑] 准备提交的数据:",{userId:h.value.id,updateData:Q});const H=await It(h.value.id,Q);console.log("✅ [用户权限编辑] 用户更新成功:",H),U.success("用户权限更新成功"),I("success"),oe()}catch(Q){console.error("❌ [用户权限编辑] 用户更新失败:",Q);const H=Q,X=((v=(g=H==null?void 0:H.response)==null?void 0:g.data)==null?void 0:v.message)||(H==null?void 0:H.message)||"更新失败";U.error(`用户权限更新失败: ${X}`)}finally{C.value=!1}},oe=()=>{B.value=!1},ie=()=>{h.value=null,R.value=[],k.value=null,E.value=!0,N.value="",C.value=!1,D.value=!1};return Be(()=>l.open,g=>{g&&l.userId?(console.log("🚀 [用户权限编辑] 对话框打开，开始加载数据，userId:",l.userId),F(),L()):g||ie()},{immediate:!0}),(g,v)=>{const Q=_("a-spin"),H=_("a-result"),X=_("a-descriptions-item"),ce=_("a-descriptions"),ae=_("a-card"),le=_("a-tag"),u=_("a-select-option"),s=_("a-select"),f=_("a-switch"),M=_("a-modal");return i(),O(M,{open:B.value,"onUpdate:open":v[2]||(v[2]=y=>B.value=y),title:ne.value,width:"600px","confirm-loading":C.value,onOk:ee,onCancel:oe},{default:e(()=>[C.value?(i(),A("div",as,[t(Q,{size:"large"}),v[3]||(v[3]=r("div",{class:"mt-4 text-gray-500"},"加载用户数据中...",-1))])):N.value?(i(),A("div",ls,[t(H,{status:"error",title:N.value},null,8,["title"])])):(i(),A("div",ns,[S(" 用户基本信息 "),t(ae,{size:"small",title:"用户信息"},{default:e(()=>[t(ce,{column:2,bordered:"",size:"small"},{default:e(()=>[t(X,{label:"用户名"},{default:e(()=>{var y;return[n(c(((y=h.value)==null?void 0:y.username)||""),1)]}),_:1}),t(X,{label:"姓名"},{default:e(()=>{var y;return[n(c(((y=h.value)==null?void 0:y.full_name)||""),1)]}),_:1}),t(X,{label:"邮箱"},{default:e(()=>{var y;return[n(c(((y=h.value)==null?void 0:y.email)||""),1)]}),_:1}),t(X,{label:"部门"},{default:e(()=>{var y;return[n(c(((y=h.value)==null?void 0:y.department)||"未设置"),1)]}),_:1})]),_:1})]),_:1}),S(" 角色分配 "),t(ae,{size:"small",title:"角色分配"},{default:e(()=>[r("div",rs,[v[4]||(v[4]=r("label",{style:{display:"block","margin-bottom":"8px","font-weight":"500"}},"选择角色：",-1)),t(s,{value:k.value,"onUpdate:value":v[0]||(v[0]=y=>k.value=y),loading:D.value,placeholder:"请选择角色",style:{width:"100%"},"allow-clear":""},{default:e(()=>[(i(!0),A(se,null,pe(R.value,y=>(i(),O(u,{key:y.id,value:y.id},{default:e(()=>[n(c(y.displayName)+" ",1),t(le,{color:y.is_active?"green":"red",size:"small",style:{"margin-left":"8px"}},{default:e(()=>[n(c(y.is_active?"启用":"禁用"),1)]),_:2},1032,["color"])]),_:2},1032,["value"]))),128))]),_:1},8,["value","loading"])]),W.value?(i(),A("div",is,[r("div",cs,[v[5]||(v[5]=r("strong",null,"角色说明：",-1)),n(c(W.value.description||"无说明"),1)])])):S("v-if",!0)]),_:1}),S(" 账户状态 "),t(ae,{size:"small",title:"账户状态"},{default:e(()=>[r("div",us,[v[6]||(v[6]=r("label",{style:{display:"block","margin-bottom":"8px","font-weight":"500"}},"账户状态：",-1)),t(f,{checked:E.value,"onUpdate:checked":v[1]||(v[1]=y=>E.value=y),"checked-children":"启用","un-checked-children":"禁用"},null,8,["checked"])])]),_:1})]))]),_:1},8,["open","title","confirm-loading"])}}});const ps=xe(ds,[["__scopeId","data-v-d8b85fa2"],["__file","D:/Code/OnlyOffice/frontend/src/components/UserPermissionEditor.vue"]]),ms={class:"permissions-page"},vs={class:"user-permissions-section"},_s={class:"filter-section"},fs={style:{color:"#999","font-size":"12px"}},gs={key:1,style:{color:"#999"}},ys={key:1,style:{color:"#999"}},hs=Ie({__name:"index",setup(p){const d=at(),{hasPermission:l,permissionState:I}=lt(),C=w("roles"),D=w(!1),N=w([]),h=w(!1),R=fe({current:1,pageSize:10,total:0}),k=w([]),E=w(!1),B=fe({current:1,pageSize:10,total:0}),ne=w([]),W=w(!1),F=fe({current:1,pageSize:10,total:0}),L=fe({search:"",roleId:void 0}),ee=w(!1),oe=w(!1),ie=w(0),g=w(null),v=fe({roles:!1,permissions:!1,users:!1}),Q=[{title:"角色名称",dataIndex:"name",key:"name",width:150},{title:"显示名称",dataIndex:"displayName",key:"displayName",width:150},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"权限",key:"permissions",width:250},{title:"状态",dataIndex:"is_active",key:"status",width:80},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150},{title:"操作",key:"actions",width:150,fixed:"right"}],H=[{title:"权限名称",key:"name",width:200},{title:"权限代码",dataIndex:"code",key:"code",width:200},{title:"模块",dataIndex:"module",key:"module",width:100},{title:"操作",key:"action",width:100},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"状态",dataIndex:"is_active",key:"status",width:80},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150}],X=[{title:"用户信息",key:"user",width:200},{title:"角色",key:"role",width:120},{title:"权限",key:"permissions",width:200},{title:"状态",dataIndex:"status",key:"status",width:80},{title:"最后登录",dataIndex:"last_login_at",key:"last_login_at",width:150},{title:"操作",key:"actions",width:150,fixed:"right"}],ce=()=>{const o=["#f56a00","#7265e6","#00a2ae","#00a854","#fa541c","#eb2f96"];return o[Math.floor(Math.random()*o.length)]},ae=o=>({read:"blue",create:"green",update:"orange",delete:"red","*":"purple"})[o]||"default",le=async()=>{try{h.value=!0,console.log("🔍 [权限管理] 开始加载角色列表...");const o=await Ce.get("/roles",{params:{page:R.current,pageSize:R.pageSize}});console.log("✅ [权限管理] 角色列表加载成功:",o),N.value=o.data||[],R.total=o.total||0,v.roles=!0}catch(o){console.error("❌ [权限管理] 角色列表加载失败:",o);const a=o instanceof Error?o.message:"未知错误";U.error(`角色列表加载失败: ${a}`),v.roles=!1}finally{h.value=!1}},u=async()=>{try{E.value=!0,console.log("🔍 [权限管理] 开始加载权限列表...");const o=await Ce.get("/permissions",{params:{page:B.current,pageSize:B.pageSize}});console.log("✅ [权限管理] 权限列表加载成功:",o),k.value=o.data||[],B.total=o.total||0,v.permissions=!0}catch(o){console.error("❌ [权限管理] 权限列表加载失败:",o);const a=o instanceof Error?o.message:"未知错误";U.error(`权限列表加载失败: ${a}`),v.permissions=!1}finally{E.value=!1}},s=async()=>{try{W.value=!0,console.log("🔍 [权限管理] 开始加载用户列表（使用/users接口）...");const o={page:F.current,pageSize:F.pageSize};L.search&&(o.keyword=L.search),L.roleId&&(o.roleId=L.roleId);const a=await Ce.get("/users",{params:o});console.log("✅ [权限管理] 用户列表加载成功:",a),ne.value=a.data||[],F.total=a.total||0,v.users=!0}catch(o){console.error("❌ [权限管理] 用户列表加载失败:",o);const a=o instanceof Error?o.message:"未知错误";U.error(`用户列表加载失败: ${a}`),v.users=!1}finally{W.value=!1}},f=async o=>{R.current=o.current,R.pageSize=o.pageSize,await le()},M=async o=>{B.current=o.current,B.pageSize=o.pageSize,await u()},y=async o=>{F.current=o.current,F.pageSize=o.pageSize,await s()},T=w(!1),j=w(!1),Y=w(""),ue=w(""),$=()=>{U.info("创建角色功能正在开发中...")},G=o=>{Y.value=o.id,T.value=!0},J=o=>{U.info(`查看角色: ${o.name}`)},z=o=>{ue.value=o.id,j.value=!0},P=o=>{U.info(`查看用户详情: ${o.username}`)},de=async()=>{await le(),U.success("角色权限更新成功")},he=async()=>{await s(),U.success("用户权限更新成功")},K=async()=>{L.search="",L.roleId=void 0,F.current=1,await s()},re=async()=>{D.value=!0;try{await Promise.all([le(),u(),s()]),U.success("数据刷新成功")}catch{U.error("数据刷新失败")}finally{D.value=!1}},ke=()=>{const o=localStorage.getItem("token"),a=localStorage.getItem("userInfo");oe.value=!!o,ie.value=(o==null?void 0:o.length)||0,g.value=a?JSON.parse(a):null,ee.value=!0};return nt(async()=>{console.log("🚀 [权限管理] 页面初始化开始");try{const o=localStorage.getItem("token"),a=localStorage.getItem("userInfo");if(!o||!a){U.error("认证状态异常，请重新登录"),d.push("/login");return}console.log("✅ [权限管理] 认证状态正常，开始加载数据"),await le(),await u(),await s(),console.log("✅ [权限管理] 页面初始化完成")}catch(o){console.error("❌ [权限管理] 页面初始化失败:",o),U.error("页面初始化失败，请刷新重试")}}),(o,a)=>{const te=_("a-button"),Z=_("a-space"),Ae=_("a-avatar"),q=_("a-tag"),be=_("a-table"),Se=_("a-tab-pane"),He=_("a-input"),we=_("a-col"),qe=_("a-select-option"),Fe=_("a-select"),Ge=_("a-row"),Je=_("a-tabs"),Qe=_("a-card"),ve=_("a-descriptions-item"),Ze=_("a-descriptions"),We=_("a-modal");return i(),A("div",ms,[t(Qe,{title:"权限管理",class:"main-card"},{extra:e(()=>[t(Z,null,{default:e(()=>[x(l)(x(ge).ROLES.CREATE)?(i(),O(te,{key:0,type:"primary",onClick:$},{icon:e(()=>[t(x(rt))]),default:e(()=>[a[8]||(a[8]=n(" 新建角色 "))]),_:1,__:[8]})):S("v-if",!0),t(te,{onClick:re,loading:D.value},{icon:e(()=>[t(x(it))]),default:e(()=>[a[9]||(a[9]=n(" 刷新数据 "))]),_:1,__:[9]},8,["loading"]),x(I).isSuperAdmin?(i(),O(te,{key:1,onClick:ke,type:"dashed"},{default:e(()=>a[10]||(a[10]=[n(" 🔍 调试信息 ")])),_:1,__:[10]})):S("v-if",!0)]),_:1})]),default:e(()=>[t(Je,{activeKey:C.value,"onUpdate:activeKey":a[2]||(a[2]=m=>C.value=m),type:"card"},{default:e(()=>[S(" 角色管理 "),t(Se,{key:"roles",tab:"角色管理"},{default:e(()=>[t(be,{columns:Q,"data-source":N.value,loading:h.value,"row-key":"id",pagination:{current:R.current,pageSize:R.pageSize,total:R.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:m=>`共 ${m} 个角色`},onChange:f},{bodyCell:e(({column:m,record:b})=>[m.key==="name"?(i(),O(Z,{key:0},{default:e(()=>[t(Ae,{style:ct({backgroundColor:ce()}),size:"small"},{default:e(()=>{var V,_e;return[n(c((_e=(V=b.name)==null?void 0:V.charAt(0))==null?void 0:_e.toUpperCase()),1)]}),_:2},1032,["style"]),r("span",null,c(b.name),1)]),_:2},1024)):m.key==="permissions"?(i(),O(Z,{key:1,wrap:""},{default:e(()=>[(i(!0),A(se,null,pe((b.permissions||[]).slice(0,3),V=>(i(),O(q,{key:V,color:"blue"},{default:e(()=>[n(c(V),1)]),_:2},1024))),128)),(b.permissions||[]).length>3?(i(),O(q,{key:0,color:"default"},{default:e(()=>[n(" +"+c((b.permissions||[]).length-3),1)]),_:2},1024)):S("v-if",!0)]),_:2},1024)):m.key==="status"?(i(),O(q,{key:2,color:b.is_active?"green":"red"},{default:e(()=>[n(c(b.is_active?"启用":"禁用"),1)]),_:2},1032,["color"])):m.key==="actions"?(i(),O(Z,{key:3},{default:e(()=>[x(l)(x(ge).ROLES.UPDATE)?(i(),O(te,{key:0,type:"text",size:"small",onClick:V=>G(b)},{icon:e(()=>[t(x(Re))]),default:e(()=>[a[11]||(a[11]=n(" 编辑 "))]),_:2,__:[11]},1032,["onClick"])):S("v-if",!0),x(l)(x(ge).ROLES.READ)?(i(),O(te,{key:1,type:"text",size:"small",onClick:V=>J(b)},{icon:e(()=>[t(x(Me))]),default:e(()=>[a[12]||(a[12]=n(" 查看 "))]),_:2,__:[12]},1032,["onClick"])):S("v-if",!0)]),_:2},1024)):S("v-if",!0)]),_:1},8,["data-source","loading","pagination"])]),_:1}),S(" 权限列表 "),t(Se,{key:"permissions",tab:"权限列表"},{default:e(()=>[t(be,{columns:H,"data-source":k.value,loading:E.value,"row-key":"id",pagination:{current:B.current,pageSize:B.pageSize,total:B.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:m=>`共 ${m} 个权限`},onChange:M},{bodyCell:e(({column:m,record:b})=>[m.key==="name"?(i(),O(Z,{key:0},{default:e(()=>[t(q,{color:"blue"},{default:e(()=>[n(c(b.module),1)]),_:2},1024),r("span",null,c(b.name),1)]),_:2},1024)):m.key==="action"?(i(),O(q,{key:1,color:ae(b.action)},{default:e(()=>[n(c(b.action),1)]),_:2},1032,["color"])):m.key==="status"?(i(),O(q,{key:2,color:b.is_active?"green":"red"},{default:e(()=>[n(c(b.is_active?"启用":"禁用"),1)]),_:2},1032,["color"])):S("v-if",!0)]),_:1},8,["data-source","loading","pagination"])]),_:1}),S(" 用户权限 "),t(Se,{key:"user-permissions",tab:"用户权限"},{default:e(()=>[r("div",vs,[r("div",_s,[t(Ge,{gutter:16},{default:e(()=>[t(we,{span:6},{default:e(()=>[t(He,{value:L.search,"onUpdate:value":a[0]||(a[0]=m=>L.search=m),placeholder:"搜索用户...","allow-clear":"",onChange:s},{prefix:e(()=>[t(x(Te))]),_:1},8,["value"])]),_:1}),t(we,{span:4},{default:e(()=>[t(Fe,{value:L.roleId,"onUpdate:value":a[1]||(a[1]=m=>L.roleId=m),placeholder:"选择角色","allow-clear":"",onChange:s},{default:e(()=>[(i(!0),A(se,null,pe(N.value,m=>(i(),O(qe,{key:m.id,value:m.id},{default:e(()=>[n(c(m.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1}),t(we,{span:6},{default:e(()=>[t(Z,null,{default:e(()=>[t(te,{type:"primary",onClick:s,loading:W.value},{icon:e(()=>[t(x(Te))]),default:e(()=>[a[13]||(a[13]=n(" 搜索 "))]),_:1,__:[13]},8,["loading"]),t(te,{onClick:K},{default:e(()=>a[14]||(a[14]=[n("重置")])),_:1,__:[14]})]),_:1})]),_:1})]),_:1})]),S(" 使用/users接口替代有问题的/users/permissions接口 "),t(be,{columns:X,"data-source":ne.value,loading:W.value,"row-key":"id",pagination:{current:F.current,pageSize:F.pageSize,total:F.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:m=>`共 ${m} 个用户`},onChange:y},{bodyCell:e(({column:m,record:b})=>[m.key==="user"?(i(),O(Z,{key:0},{default:e(()=>[t(Ae,{size:"small"},{default:e(()=>{var V,_e;return[n(c((_e=(V=b.username)==null?void 0:V.charAt(0))==null?void 0:_e.toUpperCase()),1)]}),_:2},1024),r("div",null,[r("div",null,c(b.username),1),r("div",fs,c(b.email),1)])]),_:2},1024)):m.key==="role"?(i(),A(se,{key:1},[b.role_name?(i(),O(q,{key:0,color:ce()},{default:e(()=>[n(c(b.role_name),1)]),_:2},1032,["color"])):(i(),A("span",gs,"未分配角色"))],64)):m.key==="permissions"?(i(),O(Z,{key:2,wrap:""},{default:e(()=>[(i(!0),A(se,null,pe((b.role_permissions||[]).slice(0,2),V=>(i(),O(q,{key:V,size:"small",color:"blue"},{default:e(()=>[n(c(V),1)]),_:2},1024))),128)),(b.role_permissions||[]).length>2?(i(),O(q,{key:0,size:"small",color:"default"},{default:e(()=>[n(" +"+c((b.role_permissions||[]).length-2),1)]),_:2},1024)):S("v-if",!0),!b.role_permissions||b.role_permissions.length===0?(i(),A("span",ys," 无权限 ")):S("v-if",!0)]),_:2},1024)):m.key==="status"?(i(),O(q,{key:3,color:b.status==="active"?"green":"red"},{default:e(()=>[n(c(b.status==="active"?"启用":"禁用"),1)]),_:2},1032,["color"])):m.key==="actions"?(i(),O(Z,{key:4},{default:e(()=>[x(l)(x(ge).USERS.UPDATE)?(i(),O(te,{key:0,type:"text",size:"small",onClick:V=>z(b)},{icon:e(()=>[t(x(Re))]),default:e(()=>[a[15]||(a[15]=n(" 编辑权限 "))]),_:2,__:[15]},1032,["onClick"])):S("v-if",!0),x(l)(x(ge).USERS.READ)?(i(),O(te,{key:1,type:"text",size:"small",onClick:V=>P(b)},{icon:e(()=>[t(x(Me))]),default:e(()=>[a[16]||(a[16]=n(" 查看详情 "))]),_:2,__:[16]},1032,["onClick"])):S("v-if",!0)]),_:2},1024)):S("v-if",!0)]),_:1},8,["data-source","loading","pagination"])])]),_:1})]),_:1},8,["activeKey"])]),_:1}),S(" 调试信息弹窗 "),t(We,{open:ee.value,"onUpdate:open":a[3]||(a[3]=m=>ee.value=m),title:"调试信息",width:"800px",onOk:a[4]||(a[4]=m=>ee.value=!1),onCancel:a[5]||(a[5]=m=>ee.value=!1)},{default:e(()=>[t(Ze,{column:1,bordered:""},{default:e(()=>[t(ve,{label:"认证状态"},{default:e(()=>[t(q,{color:oe.value?"green":"red"},{default:e(()=>[n(c(oe.value?"已认证":"未认证"),1)]),_:1},8,["color"])]),_:1}),t(ve,{label:"Token长度"},{default:e(()=>[n(c(ie.value||0),1)]),_:1}),t(ve,{label:"用户信息"},{default:e(()=>[n(c(g.value?g.value.username:"无"),1)]),_:1}),t(ve,{label:"API状态"},{default:e(()=>[t(Z,{direction:"vertical"},{default:e(()=>[r("div",null,[a[17]||(a[17]=n(" 角色接口: ")),t(q,{color:v.roles?"green":"red"},{default:e(()=>[n(c(v.roles?"正常":"异常"),1)]),_:1},8,["color"])]),r("div",null,[a[18]||(a[18]=n(" 权限接口: ")),t(q,{color:v.permissions?"green":"red"},{default:e(()=>[n(c(v.permissions?"正常":"异常"),1)]),_:1},8,["color"])]),r("div",null,[a[19]||(a[19]=n(" 用户接口: ")),t(q,{color:v.users?"green":"red"},{default:e(()=>[n(c(v.users?"正常":"异常"),1)]),_:1},8,["color"])])]),_:1})]),_:1}),t(ve,{label:"数据统计"},{default:e(()=>[t(Z,{direction:"vertical"},{default:e(()=>[r("div",null,"角色数量: "+c(N.value.length),1),r("div",null,"权限数量: "+c(k.value.length),1),r("div",null,"用户数量: "+c(ne.value.length),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["open"]),S(" 角色权限编辑器 "),t(os,{open:T.value,"onUpdate:open":a[6]||(a[6]=m=>T.value=m),"role-id":Y.value,onSuccess:de},null,8,["open","role-id"]),S(" 用户权限编辑器 "),t(ps,{open:j.value,"onUpdate:open":a[7]||(a[7]=m=>j.value=m),"user-id":ue.value,onSuccess:he},null,8,["open","user-id"])])}}});const Cs=xe(hs,[["__scopeId","data-v-ae4bb4fd"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Permissions/index.vue"]]);export{Cs as default};
