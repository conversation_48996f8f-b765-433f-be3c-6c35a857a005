import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as jwt from 'jsonwebtoken';
import { JwtConfigService } from './jwt-config.service';
import { HybridConfigService } from './hybrid-config.service';
import { OnlyOfficeConfig } from '../../editor/interfaces/editor-config.interface';

/**
 * OnlyOffice JWT服务
 * @description 专门用于生成OnlyOffice文档服务器认证所需的JWT token
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */
@Injectable()
export class OnlyOfficeJwtService {
  private readonly logger = new Logger(OnlyOfficeJwtService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtConfigService: JwtConfigService,
    private readonly hybridConfigService: HybridConfigService,
  ) {}

  /**
   * 生成OnlyOffice JWT token
   * @param payload - JWT载荷数据
   * @returns JWT token字符串
   */
  async generateToken(payload: OnlyOfficeConfig | Record<string, unknown>): Promise<string> {
    try {
      // 获取JWT配置
      const jwtConfig = await this.jwtConfigService.getOnlyOfficeJwtConfig();

      // 获取服务器配置 - 从数据库读取
      let serverHost: string;
      let serverPort: number;
      let documentServerUrl: string;

      try {
        const appConfig = await this.hybridConfigService.getAppConfig();
        serverHost = appConfig.server.host;
        serverPort = appConfig.port;
        documentServerUrl = appConfig.onlyoffice.documentServerUrl;
      } catch (error) {
        this.logger.warn('获取服务器配置失败，使用环境变量配置', error);
        // 回退到环境变量
        serverHost = this.configService.get<string>('SERVER_HOST', 'localhost');
        serverPort = parseInt(this.configService.get<string>('PORT', '3000'), 10);
        documentServerUrl = this.configService.get<string>(
          'ONLYOFFICE_DOCUMENT_SERVER_URL',
          'http://localhost'
        );
      }

      // 构建JWT载荷
      const jwtPayload = {
        ...payload,
        iss: `http://${serverHost}:${serverPort}`, // 签发者
        aud: documentServerUrl, // 接收者
        exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
        nbf: Math.floor(Date.now() / 1000) - 60, // 提前60秒生效
        iat: Math.floor(Date.now() / 1000), // 签发时间
      };

      // 生成JWT token
      const token = jwt.sign(jwtPayload, jwtConfig.secret, {
        algorithm: jwtConfig.algorithm as jwt.Algorithm,
        header: {
          alg: jwtConfig.algorithm,
          typ: 'JWT'
        }
      });

      this.logger.log('OnlyOffice JWT令牌生成成功');
      this.logger.debug(`JWT载荷: ${JSON.stringify({
        ...jwtPayload,
        document: (jwtPayload as Record<string, unknown>).document ? { 
          ...(jwtPayload as Record<string, unknown>).document as Record<string, unknown>, 
          url: '[REDACTED]' 
        } : undefined
      })}`);

      return token;
    } catch (error) {
      this.logger.error('生成OnlyOffice JWT令牌失败:', error);
      throw new Error(`JWT令牌生成失败: ${error.message}`);
    }
  }

  /**
   * 为OnlyOffice配置签名
   * @param config - OnlyOffice配置对象
   * @returns 包含JWT token的配置对象
   */
  async signConfig(config: OnlyOfficeConfig): Promise<OnlyOfficeConfig> {
    try {
      const token = await this.generateToken(config);
      
      return {
        ...config,
        token: token
      };
    } catch (error) {
      this.logger.error('为OnlyOffice配置签名失败:', error);
      throw error;
    }
  }

  /**
   * 验证JWT token
   * @param token - 待验证的JWT token
   * @returns 验证结果和解码后的载荷
   */
  async verifyToken(token: string): Promise<{ valid: boolean; payload?: unknown; error?: string }> {
    try {
      const jwtConfig = await this.jwtConfigService.getOnlyOfficeJwtConfig();
      
      const decoded = jwt.verify(token, jwtConfig.secret, {
        algorithms: [jwtConfig.algorithm as jwt.Algorithm]
      });

      this.logger.log('JWT token验证成功');
      return { valid: true, payload: decoded };
    } catch (error) {
      this.logger.warn(`JWT token验证失败: ${error.message}`);
      return { valid: false, error: error.message };
    }
  }
} 