#!/usr/bin/env node

/**
 * FileNet文档管理系统 - 数据库表初始化脚本
 * 基于原有的filenetService.js重构
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'onlyfile_user',
  password: process.env.DB_PASSWORD || '0nlyF!le$ecure#123',
  database: process.env.DB_NAME || 'onlyfile',
  multipleStatements: true
};

async function initializeFilenetTables() {
  let connection;
  
  try {
    console.log('🚀 开始初始化FileNet文档管理系统数据库...');
    console.log(`📡 连接到数据库: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'create-filenet-tables.sql');
    console.log(`📄 读取SQL文件: ${sqlFilePath}`);
    
    const sqlContent = await fs.readFile(sqlFilePath, 'utf8');
    console.log(`📋 SQL文件大小: ${(sqlContent.length / 1024).toFixed(2)} KB`);

    // 执行SQL语句
    console.log('⚡ 开始执行SQL语句...');
    const results = await connection.query(sqlContent);
    console.log('✅ SQL执行完成');

    // 验证表是否创建成功
    console.log('🔍 验证表创建结果...');
    
    const tables = [
      'filenet_documents',
      'filenet_document_versions', 
      'filenet_access_logs',
      'filenet_document_relations',
      'filenet_system_config'
    ];

    for (const tableName of tables) {
      const [rows] = await connection.query(
        'SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?',
        [dbConfig.database, tableName]
      );
      
      if (rows[0].count > 0) {
        console.log(`✅ 表 ${tableName} 创建成功`);
      } else {
        console.log(`❌ 表 ${tableName} 创建失败`);
      }
    }

    // 验证视图是否创建成功
    console.log('🔍 验证视图创建结果...');
    
    const views = [
      'v_filenet_document_stats',
      'v_filenet_document_details'
    ];

    for (const viewName of views) {
      const [rows] = await connection.query(
        'SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?',
        [dbConfig.database, viewName]
      );
      
      if (rows[0].count > 0) {
        console.log(`✅ 视图 ${viewName} 创建成功`);
      } else {
        console.log(`❌ 视图 ${viewName} 创建失败`);
      }
    }

    // 验证配置数据是否插入成功
    console.log('🔍 验证配置数据...');
    const [configRows] = await connection.query('SELECT COUNT(*) as count FROM filenet_system_config');
    console.log(`✅ 插入配置项数量: ${configRows[0].count}`);

    // 显示统计信息
    console.log('\n📊 FileNet文档管理系统初始化完成统计:');
    console.log('  📋 数据库表: 5个');
    console.log('  👁 数据视图: 2个'); 
    console.log(`  ⚙️ 配置项: ${configRows[0].count}个`);
    console.log('  🎯 状态: 就绪可用');
    
    console.log('\n🎉 FileNet文档管理系统数据库初始化完成!');
    
  } catch (error) {
    console.error('❌ FileNet数据库初始化失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeFilenetTables();
}

module.exports = { initializeFilenetTables }; 