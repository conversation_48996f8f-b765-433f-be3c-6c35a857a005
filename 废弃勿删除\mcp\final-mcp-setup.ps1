# MCP MySQL 最终安装验证脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "    MCP MySQL 安装验证和使用指南" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 1. 验证安装
Write-Host "1. 验证安装状态..." -ForegroundColor Yellow
Write-Host ""

# 检查Node.js
$nodeVersion = node --version
Write-Host "   ✓ Node.js版本: $nodeVersion" -ForegroundColor Green

# 检查npm
$npmVersion = npm --version
Write-Host "   ✓ NPM版本: $npmVersion" -ForegroundColor Green

# 检查MCP包
$mcpPackage = npm list -g @benborla29/mcp-server-mysql --depth=0 2>$null
if ($mcpPackage -match "@benborla29/mcp-server-mysql@") {
    $version = ($mcpPackage -split "@")[-1]
    Write-Host "   ✓ MCP MySQL包已安装: $version" -ForegroundColor Green
} else {
    Write-Host "   ✗ MCP MySQL包未安装" -ForegroundColor Red
    exit 1
}

# 检查配置文件
if (Test-Path ".cursor\mcp.json") {
    Write-Host "   ✓ Cursor MCP配置文件存在" -ForegroundColor Green
} else {
    Write-Host "   ✗ Cursor MCP配置文件不存在" -ForegroundColor Red
    exit 1
}

# 2. 验证MySQL连接
Write-Host ""
Write-Host "2. 验证MySQL连接..." -ForegroundColor Yellow

$testConnection = Test-NetConnection -ComputerName "*************" -Port 3306 -WarningAction SilentlyContinue
if ($testConnection.TcpTestSucceeded) {
    Write-Host "   ✓ MySQL服务器连接正常" -ForegroundColor Green
} else {
    Write-Host "   ✗ MySQL服务器连接失败" -ForegroundColor Red
    Write-Host "   请检查MySQL服务是否运行" -ForegroundColor Yellow
}

# 3. 显示配置信息
Write-Host ""
Write-Host "3. 当前配置信息..." -ForegroundColor Yellow
$config = Get-Content ".cursor\mcp.json" | ConvertFrom-Json
$mysqlConfig = $config.mcpServers.MySQL

Write-Host "   命令: $($mysqlConfig.command)" -ForegroundColor Cyan
Write-Host "   参数: $($mysqlConfig.args -join ' ')" -ForegroundColor Cyan
Write-Host "   MySQL主机: $($mysqlConfig.env.MYSQL_HOST)" -ForegroundColor Cyan
Write-Host "   MySQL端口: $($mysqlConfig.env.MYSQL_PORT)" -ForegroundColor Cyan
Write-Host "   MySQL用户: $($mysqlConfig.env.MYSQL_USER)" -ForegroundColor Cyan
Write-Host "   MySQL数据库: $($mysqlConfig.env.MYSQL_DB)" -ForegroundColor Cyan
Write-Host "   INSERT权限: $($mysqlConfig.env.ALLOW_INSERT_OPERATION)" -ForegroundColor Cyan
Write-Host "   UPDATE权限: $($mysqlConfig.env.ALLOW_UPDATE_OPERATION)" -ForegroundColor Cyan
Write-Host "   DELETE权限: $($mysqlConfig.env.ALLOW_DELETE_OPERATION)" -ForegroundColor Cyan

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           安装验证完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "✅ MCP MySQL服务器已正确安装和配置" -ForegroundColor Green
Write-Host ""

Write-Host "📋 使用说明:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. 重启Cursor IDE" -ForegroundColor White
Write-Host "   - 完全关闭Cursor" -ForegroundColor Gray
Write-Host "   - 重新打开Cursor" -ForegroundColor Gray
Write-Host "   - 打开您的项目" -ForegroundColor Gray
Write-Host ""

Write-Host "2. 在Cursor中使用MCP MySQL" -ForegroundColor White
Write-Host "   - 在聊天中输入: @MySQL" -ForegroundColor Gray
Write-Host "   - 或者直接询问数据库相关问题" -ForegroundColor Gray
Write-Host "   - 例如: '显示onlyfile数据库中的所有表'" -ForegroundColor Gray
Write-Host "   - 例如: '@MySQL 查询users表的结构'" -ForegroundColor Gray
Write-Host ""

Write-Host "3. 检查MCP连接状态" -ForegroundColor White
Write-Host "   - 在Cursor中按 Ctrl+Shift+P" -ForegroundColor Gray
Write-Host "   - 搜索 'MCP' 相关命令" -ForegroundColor Gray
Write-Host "   - 查看MCP服务器状态" -ForegroundColor Gray
Write-Host ""

Write-Host "4. 故障排除" -ForegroundColor White
Write-Host "   - 如果@MySQL不可用，检查Cursor的输出面板" -ForegroundColor Gray
Write-Host "   - 查看MCP相关的错误日志" -ForegroundColor Gray
Write-Host "   - 确保MySQL服务器正在运行" -ForegroundColor Gray
Write-Host ""

Write-Host "🔧 高级配置:" -ForegroundColor Yellow
Write-Host ""
Write-Host "配置文件位置: .cursor\mcp.json" -ForegroundColor White
Write-Host "环境配置文件: mcp-mysql-config.env" -ForegroundColor White
Write-Host ""

Write-Host "如需修改配置，请编辑上述文件并重启Cursor。" -ForegroundColor Gray
Write-Host ""

Write-Host "🎉 安装完成！现在您可以在Cursor中使用@MySQL来访问您的数据库了。" -ForegroundColor Green
