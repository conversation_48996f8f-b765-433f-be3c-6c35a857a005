<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 明亮卡片风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
      color: #333;
      line-height: 1.6;
    }

    .app-container {
      display: flex;
      min-height: 100vh;
    }

    /* 左侧导航栏 */
    .sidebar {
      width: 280px;
      background: #ffffff;
      border-right: 2px solid #e1f5fe;
      box-shadow: 4px 0 12px rgba(33, 150, 243, 0.1);
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 1000;
    }

    .logo-area {
      padding: 24px 20px;
      text-align: center;
      background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
      color: white;
    }

    .logo-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .logo-desc {
      font-size: 12px;
      opacity: 0.9;
    }

    .navigation {
      padding: 24px 0;
    }

    .nav-section {
      margin-bottom: 32px;
    }

    .nav-section-title {
      padding: 0 20px 12px;
      font-size: 11px;
      color: #90a4ae;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .nav-link {
      display: flex;
      align-items: center;
      padding: 14px 20px;
      color: #546e7a;
      text-decoration: none;
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
      position: relative;
    }

    .nav-link:hover, .nav-link.active {
      background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
      color: #1976d2;
      border-left-color: #2196f3;
    }

    .nav-link.active::after {
      content: '';
      position: absolute;
      right: 20px;
      width: 8px;
      height: 8px;
      background: #2196f3;
      border-radius: 50%;
    }

    .nav-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      font-size: 18px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-label {
      font-size: 14px;
      font-weight: 500;
    }

    .nav-count {
      margin-left: auto;
      background: #ff5722;
      color: white;
      font-size: 10px;
      padding: 3px 7px;
      border-radius: 12px;
      min-width: 20px;
      text-align: center;
      font-weight: 600;
    }

    /* 主要内容区域 */
    .main-area {
      margin-left: 280px;
      flex: 1;
      background: transparent;
    }

    /* 顶部导航栏 */
    .top-header {
      background: #ffffff;
      padding: 16px 32px;
      border-bottom: 2px solid #e1f5fe;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header-title {
      font-size: 24px;
      font-weight: 600;
      color: #1976d2;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .breadcrumb {
      font-size: 14px;
      color: #78909c;
      margin-top: 4px;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .current-time {
      background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 100%);
      padding: 8px 16px;
      border-radius: 20px;
      border: 1px solid #c8e6c9;
      font-size: 14px;
      font-weight: 500;
      color: #2e7d32;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 12px;
      background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
      padding: 8px 16px;
      border-radius: 20px;
      border: 1px solid #ffcc02;
    }

    .user-avatar {
      width: 36px;
      height: 36px;
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    .user-details {
      display: flex;
      flex-direction: column;
    }

    .user-name {
      font-size: 14px;
      font-weight: 600;
      color: #e65100;
    }

    .user-role {
      font-size: 12px;
      color: #ff8f00;
    }

    /* 内容区域 */
    .content-wrapper {
      padding: 32px;
    }

    /* 统计卡片区域 */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-bottom: 32px;
    }

    .stat-card {
      background: #ffffff;
      border-radius: 16px;
      padding: 24px;
      border: 2px solid transparent;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
    }

    .stat-card.documents::before { background: linear-gradient(90deg, #2196f3 0%, #21cbf3 100%); }
    .stat-card.users::before { background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%); }
    .stat-card.storage::before { background: linear-gradient(90deg, #ff9800 0%, #ffc107 100%); }
    .stat-card.system::before { background: linear-gradient(90deg, #e91e63 0%, #f06292 100%); }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .stat-icon {
      width: 56px;
      height: 56px;
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: white;
    }

    .stat-icon.documents { background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%); }
    .stat-icon.users { background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%); }
    .stat-icon.storage { background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%); }
    .stat-icon.system { background: linear-gradient(135deg, #e91e63 0%, #f06292 100%); }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      padding: 4px 8px;
      border-radius: 12px;
    }

    .trend-positive {
      background: #e8f5e9;
      color: #2e7d32;
    }

    .trend-negative {
      background: #ffebee;
      color: #c62828;
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      color: #37474f;
      margin-bottom: 8px;
    }

    .stat-title {
      color: #78909c;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .stat-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #90a4ae;
    }

    /* 主要功能卡片区域 */
    .features-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 32px;
    }

    .feature-card {
      background: #ffffff;
      border-radius: 20px;
      border: 2px solid #e1f5fe;
      box-shadow: 0 6px 20px rgba(33, 150, 243, 0.1);
      overflow: hidden;
    }

    .card-header {
      padding: 24px 32px;
      border-bottom: 2px solid #f5f5f5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: #37474f;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .card-action {
      color: #2196f3;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .card-action:hover {
      background: #e3f2fd;
      color: #1976d2;
    }

    /* 快捷操作区域 */
    .actions-container {
      padding: 32px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
    }

    .action-card {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border: 2px solid #e9ecef;
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .action-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #2196f3 0%, #21cbf3 100%);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .action-card:hover {
      background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
      border-color: #2196f3;
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(33, 150, 243, 0.2);
    }

    .action-card:hover::before {
      transform: scaleX(1);
    }

    .action-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px;
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      color: #1976d2;
      transition: all 0.3s ease;
    }

    .action-card:hover .action-icon {
      background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
      color: white;
      transform: scale(1.1);
    }

    .action-title {
      font-size: 16px;
      font-weight: 600;
      color: #37474f;
      margin-bottom: 8px;
    }

    .action-subtitle {
      font-size: 12px;
      color: #78909c;
      line-height: 1.5;
    }

    /* 文档列表区域 */
    .documents-container {
      padding: 0 32px 32px;
    }

    .document-row {
      display: flex;
      align-items: center;
      gap: 20px;
      padding: 20px 0;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.3s ease;
    }

    .document-row:hover {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      margin: 0 -32px;
      padding: 20px 32px;
      border-radius: 12px;
    }

    .document-row:last-child {
      border-bottom: none;
    }

    .doc-type {
      width: 52px;
      height: 52px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      font-weight: 600;
    }

    .doc-word { background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%); }
    .doc-excel { background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%); }
    .doc-ppt { background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%); }
    .doc-pdf { background: linear-gradient(135deg, #7b1fa2 0%, #6a1b9a 100%); }

    .doc-content {
      flex: 1;
    }

    .doc-title {
      font-size: 16px;
      font-weight: 600;
      color: #37474f;
      margin-bottom: 6px;
    }

    .doc-meta {
      font-size: 14px;
      color: #78909c;
    }

    .doc-status {
      padding: 6px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      border: 1px solid;
    }

    .status-active {
      background: #e8f5e9;
      color: #2e7d32;
      border-color: #c8e6c9;
    }

    .status-shared {
      background: #e3f2fd;
      color: #1976d2;
      border-color: #bbdefb;
    }

    .status-completed {
      background: #fff3e0;
      color: #f57c00;
      border-color: #ffcc02;
    }

    .doc-actions {
      display: flex;
      gap: 8px;
    }

    .doc-button {
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid;
    }

    .btn-edit {
      background: #2196f3;
      border-color: #2196f3;
      color: white;
    }

    .btn-edit:hover {
      background: #1976d2;
      transform: translateY(-1px);
    }

    .btn-download {
      background: transparent;
      border-color: #e0e0e0;
      color: #666;
    }

    .btn-download:hover {
      background: #f5f5f5;
      border-color: #bdbdbd;
    }

    /* 侧边栏内容 */
    .sidebar-panels {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    /* 用户信息面板 */
    .user-panel {
      padding: 32px;
      text-align: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .profile-image {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-weight: 600;
      color: white;
      border: 4px solid white;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
    }

    .profile-name {
      font-size: 20px;
      font-weight: 600;
      color: #37474f;
      margin-bottom: 6px;
    }

    .profile-title {
      font-size: 14px;
      color: #78909c;
      margin-bottom: 24px;
    }

    .profile-metrics {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      padding-top: 20px;
      border-top: 1px solid #dee2e6;
    }

    .metric {
      text-align: center;
    }

    .metric-value {
      font-size: 18px;
      font-weight: 600;
      color: #37474f;
    }

    .metric-label {
      font-size: 12px;
      color: #78909c;
    }

    /* 系统状态面板 */
    .status-panel {
      padding: 24px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .status-item:last-child {
      border-bottom: none;
    }

    .status-name {
      font-size: 14px;
      color: #78909c;
      font-weight: 500;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
    }

    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }

    .status-good { background: #4caf50; }
    .status-warning { background: #ff9800; }
    .status-error { background: #f44336; }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar.open {
        transform: translateX(0);
      }
      
      .main-area {
        margin-left: 0;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .actions-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .content-wrapper {
        padding: 20px;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .actions-grid {
        grid-template-columns: 1fr;
      }
      
      .profile-metrics {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 左侧导航栏 -->
    <nav class="sidebar">
      <div class="logo-area">
        <div class="logo-title">🏢 OnlyOffice</div>
        <div class="logo-desc">企业级文档管理系统</div>
      </div>
      
      <div class="navigation">
        <div class="nav-section">
          <div class="nav-section-title">核心功能</div>
          <a href="#" class="nav-link active">
            <span class="nav-icon">🏠</span>
            <span class="nav-label">系统首页</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">📄</span>
            <span class="nav-label">文档管理</span>
            <span class="nav-count">16</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">📋</span>
            <span class="nav-label">模板管理</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">👥</span>
            <span class="nav-label">用户管理</span>
          </a>
        </div>
        
        <div class="nav-section">
          <div class="nav-section-title">系统管理</div>
          <a href="#" class="nav-link">
            <span class="nav-icon">⚙️</span>
            <span class="nav-label">系统配置</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">🔐</span>
            <span class="nav-label">权限管理</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">🔔</span>
            <span class="nav-label">OnlyOffice配置</span>
          </a>
        </div>
        
        <div class="nav-section">
          <div class="nav-section-title">数据分析</div>
          <a href="#" class="nav-link">
            <span class="nav-icon">📊</span>
            <span class="nav-label">数据报表</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">📈</span>
            <span class="nav-label">统计分析</span>
          </a>
          <a href="#" class="nav-link">
            <span class="nav-icon">📋</span>
            <span class="nav-label">系统日志</span>
          </a>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-area">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div>
          <h1 class="header-title">🏠 系统首页</h1>
          <div class="breadcrumb">首页 / 仪表板 / 系统概览</div>
        </div>
        <div class="header-right">
          <div class="current-time" id="currentTime">
            2024年12月19日 14:30
          </div>
          <div class="user-profile">
            <div class="user-avatar">管</div>
            <div class="user-details">
              <div class="user-name">系统管理员</div>
              <div class="user-role">Administrator</div>
            </div>
          </div>
        </div>
      </header>

      <!-- 内容区域 -->
      <div class="content-wrapper">
        <!-- 统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card documents">
            <div class="stat-header">
              <div class="stat-icon documents">📄</div>
              <div class="stat-trend trend-positive">↗ +18.2%</div>
            </div>
            <div class="stat-number">1,425</div>
            <div class="stat-title">文档总数</div>
            <div class="stat-info">
              <span>本月: +178</span>
              <span>活跃: 956</span>
            </div>
          </div>

          <div class="stat-card users">
            <div class="stat-header">
              <div class="stat-icon users">👥</div>
              <div class="stat-trend trend-positive">↗ +12.5%</div>
            </div>
            <div class="stat-number">92</div>
            <div class="stat-title">活跃用户</div>
            <div class="stat-info">
              <span>在线: 28</span>
              <span>本周: 74</span>
            </div>
          </div>

          <div class="stat-card storage">
            <div class="stat-header">
              <div class="stat-icon storage">💾</div>
              <div class="stat-trend trend-positive">↗ +8.7%</div>
            </div>
            <div class="stat-number">912GB</div>
            <div class="stat-title">存储使用</div>
            <div class="stat-info">
              <span>可用: 88GB</span>
              <span>使用率: 91.2%</span>
            </div>
          </div>

          <div class="stat-card system">
            <div class="stat-header">
              <div class="stat-icon system">❤️</div>
              <div class="stat-trend trend-positive">↗ +3.1%</div>
            </div>
            <div class="stat-number">99.7%</div>
            <div class="stat-title">系统健康度</div>
            <div class="stat-info">
              <span>响应: 165ms</span>
              <span>正常运行: 99.7%</span>
            </div>
          </div>
        </div>

        <!-- 主要功能区域 -->
        <div class="features-grid">
          <!-- 左侧：快捷操作和文档列表 -->
          <div>
            <!-- 快捷操作 -->
            <div class="feature-card">
              <div class="card-header">
                <h3 class="card-title">🚀 快捷操作中心</h3>
                <span class="card-action">个性化配置</span>
              </div>
              <div class="actions-container">
                <div class="actions-grid">
                  <div class="action-card">
                    <div class="action-icon">📝</div>
                    <div class="action-title">创建新文档</div>
                    <div class="action-subtitle">快速创建Word、Excel、PPT文档</div>
                  </div>
                  <div class="action-card">
                    <div class="action-icon">📤</div>
                    <div class="action-title">批量上传</div>
                    <div class="action-subtitle">支持拖拽上传多个文件</div>
                  </div>
                  <div class="action-card">
                    <div class="action-icon">📋</div>
                    <div class="action-title">模板库</div>
                    <div class="action-subtitle">使用预设模板快速创建</div>
                  </div>
                  <div class="action-card">
                    <div class="action-icon">👥</div>
                    <div class="action-title">团队协作</div>
                    <div class="action-subtitle">邀请成员协作编辑文档</div>
                  </div>
                  <div class="action-card">
                    <div class="action-icon">📊</div>
                    <div class="action-title">数据分析</div>
                    <div class="action-subtitle">查看详细的使用统计</div>
                  </div>
                  <div class="action-card">
                    <div class="action-icon">⚙️</div>
                    <div class="action-title">系统设置</div>
                    <div class="action-subtitle">配置系统参数和权限</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 最近文档 -->
            <div class="feature-card" style="margin-top: 24px;">
              <div class="card-header">
                <h3 class="card-title">📄 最近编辑的文档</h3>
                <span class="card-action">查看全部文档</span>
              </div>
              <div class="documents-container">
                <div class="document-row">
                  <div class="doc-type doc-word">W</div>
                  <div class="doc-content">
                    <div class="doc-title">企业产品需求规格说明书.docx</div>
                    <div class="doc-meta">张三 · 1小时前编辑 · 3.2MB</div>
                  </div>
                  <div class="doc-status status-active">编辑中</div>
                  <div class="doc-actions">
                    <button class="doc-button btn-edit">继续编辑</button>
                    <button class="doc-button btn-download">下载</button>
                  </div>
                </div>
                <div class="document-row">
                  <div class="doc-type doc-excel">E</div>
                  <div class="doc-content">
                    <div class="doc-title">2024年第四季度财务分析报表.xlsx</div>
                    <div class="doc-meta">李四 · 昨天 16:30 · 2.1MB</div>
                  </div>
                  <div class="doc-status status-shared">已共享</div>
                  <div class="doc-actions">
                    <button class="doc-button btn-edit">编辑</button>
                    <button class="doc-button btn-download">下载</button>
                  </div>
                </div>
                <div class="document-row">
                  <div class="doc-type doc-ppt">P</div>
                  <div class="doc-content">
                    <div class="doc-title">新产品发布会演示文稿.pptx</div>
                    <div class="doc-meta">王五 · 3天前 · 18.5MB</div>
                  </div>
                  <div class="doc-status status-completed">已完成</div>
                  <div class="doc-actions">
                    <button class="doc-button btn-edit">编辑</button>
                    <button class="doc-button btn-download">下载</button>
                  </div>
                </div>
                <div class="document-row">
                  <div class="doc-type doc-pdf">P</div>
                  <div class="doc-content">
                    <div class="doc-title">员工操作手册及培训指南.pdf</div>
                    <div class="doc-meta">赵六 · 1周前 · 12.3MB</div>
                  </div>
                  <div class="doc-status status-completed">已完成</div>
                  <div class="doc-actions">
                    <button class="doc-button btn-download">预览</button>
                    <button class="doc-button btn-download">下载</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧侧边栏 -->
          <div class="sidebar-panels">
            <!-- 用户信息 -->
            <div class="feature-card">
              <div class="user-panel">
                <div class="profile-image">管</div>
                <div class="profile-name">系统管理员</div>
                <div class="profile-title">Administrator</div>
                <div class="profile-metrics">
                  <div class="metric">
                    <div class="metric-value">178</div>
                    <div class="metric-label">我的文档</div>
                  </div>
                  <div class="metric">
                    <div class="metric-value">32</div>
                    <div class="metric-label">协作项目</div>
                  </div>
                  <div class="metric">
                    <div class="metric-value">92</div>
                    <div class="metric-label">团队成员</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统状态 -->
            <div class="feature-card">
              <div class="card-header">
                <h3 class="card-title">🔧 系统运行状态</h3>
                <span class="card-action">详细监控</span>
              </div>
              <div class="status-panel">
                <div class="status-item">
                  <span class="status-name">数据库连接</span>
                  <div class="status-indicator">
                    <span class="status-dot status-good"></span>
                    <span style="color: #4caf50;">正常</span>
                  </div>
                </div>
                <div class="status-item">
                  <span class="status-name">OnlyOffice服务</span>
                  <div class="status-indicator">
                    <span class="status-dot status-good"></span>
                    <span style="color: #4caf50;">运行中</span>
                  </div>
                </div>
                <div class="status-item">
                  <span class="status-name">FileNet连接</span>
                  <div class="status-indicator">
                    <span class="status-dot status-warning"></span>
                    <span style="color: #ff9800;">连接缓慢</span>
                  </div>
                </div>
                <div class="status-item">
                  <span class="status-name">系统负载</span>
                  <div class="status-indicator">
                    <span style="color: #37474f;">28%</span>
                  </div>
                </div>
                <div class="status-item">
                  <span class="status-name">内存使用率</span>
                  <div class="status-indicator">
                    <span style="color: #37474f;">64%</span>
                  </div>
                </div>
                <div class="status-item">
                  <span class="status-name">磁盘使用率</span>
                  <div class="status-indicator">
                    <span style="color: #ff9800;">91%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // 实时时间更新
    function updateTime() {
      const now = new Date();
      const timeString = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) + ' ' + now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
      document.getElementById('currentTime').textContent = timeString;
    }

    // 导航菜单交互
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
      });
    });

    // 快捷操作卡片交互
    document.querySelectorAll('.action-card').forEach(card => {
      card.addEventListener('click', () => {
        const title = card.querySelector('.action-title').textContent;
        console.log('执行操作:', title);
      });
    });

    // 文档操作按钮
    document.querySelectorAll('.doc-button').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('文档操作:', btn.textContent);
      });
    });

    // 移动端侧边栏切换
    function toggleSidebar() {
      const sidebar = document.querySelector('.sidebar');
      sidebar.classList.toggle('open');
    }

    // 响应式处理
    window.addEventListener('resize', () => {
      if (window.innerWidth > 1200) {
        document.querySelector('.sidebar').classList.remove('open');
      }
    });

    // 初始化
    updateTime();
    setInterval(updateTime, 60000); // 每分钟更新一次时间
  </script>
</body>
</html> 