<template>
  <div class="config-detail-compact">
    <div class="config-content-compact" ref="configContentRef">
      <!-- 权限配置 -->
      <div v-show="activeTab === 'permissions'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">🛡️</div>
            <h3 class="group-title-compact">文档权限配置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in permissionItems"
              :key="`permissions.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('permissions', item.key)"
              :is-enabled="getConfigEnabled('permissions', item.key)"
              :is-required="getConfigRequired('permissions', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('permissions', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('permissions', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 界面自定义 -->
      <div v-show="activeTab === 'customization'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">🎨</div>
            <h3 class="group-title-compact">界面和功能自定义</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in customizationItems"
              :key="`customization.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('customization', item.key)"
              :is-enabled="getConfigEnabled('customization', item.key)"
              :is-required="getConfigRequired('customization', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('customization', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('customization', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 协作编辑 -->
      <div v-show="activeTab === 'coEditing'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">👥</div>
            <h3 class="group-title-compact">协作编辑设置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in coEditingItems"
              :key="`coEditing.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('coEditing', item.key)"
              :is-enabled="getConfigEnabled('coEditing', item.key)"
              :is-required="getConfigRequired('coEditing', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('coEditing', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('coEditing', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 用户设置 -->
      <div v-show="activeTab === 'user'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">👤</div>
            <h3 class="group-title-compact">用户信息配置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in userItems"
              :key="`user.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('user', item.key)"
              :is-enabled="getConfigEnabled('user', item.key)"
              :is-required="getConfigRequired('user', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('user', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('user', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 功能特性 -->
      <div v-show="activeTab === 'features'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">⚙️</div>
            <h3 class="group-title-compact">功能特性设置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in featuresItems"
              :key="`features.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('features', item.key)"
              :is-enabled="getConfigEnabled('features', item.key)"
              :is-required="getConfigRequired('features', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('features', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('features', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 布局设置 -->
      <div v-show="activeTab === 'layout'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">📱</div>
            <h3 class="group-title-compact">布局设置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in layoutItems"
              :key="`layout.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('layout', item.key)"
              :is-enabled="getConfigEnabled('layout', item.key)"
              :is-required="getConfigRequired('layout', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('layout', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('layout', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 审阅设置 -->
      <div v-show="activeTab === 'review'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">✏️</div>
            <h3 class="group-title-compact">审阅设置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in reviewItems"
              :key="`review.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('review', item.key)"
              :is-enabled="getConfigEnabled('review', item.key)"
              :is-required="getConfigRequired('review', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('review', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('review', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 服务器设置 -->
      <div v-show="activeTab === 'server'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">🖥️</div>
            <h3 class="group-title-compact">服务器设置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in serverItems"
              :key="`server.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('server', item.key)"
              :is-enabled="getConfigEnabled('server', item.key)"
              :is-required="getConfigRequired('server', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('server', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('server', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 移动端设置 -->
      <div v-show="activeTab === 'mobile'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">📱</div>
            <h3 class="group-title-compact">移动端设置</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in mobileItems"
              :key="`mobile.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('mobile', item.key)"
              :is-enabled="getConfigEnabled('mobile', item.key)"
              :is-required="getConfigRequired('mobile', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('mobile', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('mobile', item.key, enabled)"
            />
          </div>
        </div>
      </div>

      <!-- 客户定制 -->
      <div v-show="activeTab === 'customer'" class="config-section-compact">
        <div class="config-group-compact">
          <div class="group-header-compact">
            <div class="group-icon-compact">🏢</div>
            <h3 class="group-title-compact">客户定制信息</h3>
          </div>
          <div class="config-grid-compact">
            <ConfigItem
              v-for="item in customerItems"
              :key="`customer.${item.key}`"
              :label="item.label"
              :description="item.description"
              :config-key="item.key"
              :config-value="getConfigValue('customer', item.key)"
              :is-enabled="getConfigEnabled('customer', item.key)"
              :is-required="getConfigRequired('customer', item.key)"
              :value-type="item.type"
              :options="item.options"
              @update-value="value => updateConfigValue('customer', item.key, value)"
              @update-enabled="enabled => updateConfigEnabled('customer', item.key, enabled)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, toRaw } from 'vue'
import ConfigItem from './ConfigItem.vue'

// 类型定义
export interface OnlyOfficeConfig {
  permissions?: Record<string, unknown>
  customization?: Record<string, unknown>
  coEditing?: Record<string, unknown>
  user?: Record<string, unknown>
  customer?: Record<string, unknown>
  features?: Record<string, unknown>
  layout?: Record<string, unknown>
  review?: Record<string, unknown>
  server?: Record<string, unknown>
  mobile?: Record<string, unknown>
}

interface ConfigItemDefinition {
  key: string
  label: string
  description: string
  type: 'boolean' | 'string' | 'number' | 'select'
  options?: Array<{ label: string; value: string | number | boolean }>
}

interface ConfigItemState {
  value: unknown
  enabled: boolean
  required: boolean
}

interface Props {
  templateData?: {
    id: string
    name: string
  }
  initialConfig?: Partial<OnlyOfficeConfig>
  initialConfigStates?: Record<string, Record<string, ConfigItemState>>
  activeTab: string
}

// 组件事件定义
const emit = defineEmits<{
  save: [configStates: Record<string, Record<string, ConfigItemState>>]
  reset: []
  tabChange: [tab: string]
}>()

// Props and Emits
const props = defineProps<Props>()

// 响应式数据
const configContentRef = ref<HTMLElement>()

// 配置状态存储 - 现在包含value, enabled, required三个状态
const configStates = reactive<Record<string, Record<string, ConfigItemState>>>({
  permissions: {},
  customization: {},
  features: {},
  layout: {},
  coEditing: {},
  review: {},
  user: {},
  customer: {},
  server: {},
  mobile: {},
})

// 防止重复初始化的标志
const isInitializing = ref(false)

// 配置项定义
const permissionItems: ConfigItemDefinition[] = [
  { key: 'edit', label: '编辑权限', description: '是否允许编辑文档', type: 'boolean' },
  { key: 'download', label: '下载权限', description: '是否允许下载文档', type: 'boolean' },
  { key: 'print', label: '打印权限', description: '是否允许打印文档', type: 'boolean' },
  { key: 'copy', label: '复制权限', description: '是否允许复制内容', type: 'boolean' },
  { key: 'modifyFilter', label: '筛选权限', description: '是否允许修改筛选器', type: 'boolean' },
  {
    key: 'modifyContentControl',
    label: '内容控制权限',
    description: '是否允许修改内容控制',
    type: 'boolean',
  },
  { key: 'fillForms', label: '表单填写权限', description: '是否允许填写表单', type: 'boolean' },
  { key: 'comment', label: '评论权限', description: '是否允许添加评论', type: 'boolean' },
  { key: 'chat', label: '聊天权限', description: '是否允许使用聊天功能', type: 'boolean' },
  { key: 'review', label: '审阅权限', description: '是否允许审阅文档', type: 'boolean' },
]

const customizationItems: ConfigItemDefinition[] = [
  { key: 'about', label: '关于信息', description: '是否显示关于信息', type: 'boolean' },
  { key: 'autosave', label: '自动保存', description: '是否启用自动保存', type: 'boolean' },
  { key: 'comments', label: '评论功能', description: '是否显示评论功能', type: 'boolean' },
  { key: 'compactHeader', label: '紧凑头部', description: '是否使用紧凑型头部', type: 'boolean' },
  {
    key: 'compactToolbar',
    label: '紧凑工具栏',
    description: '是否使用紧凑型工具栏',
    type: 'boolean',
  },
  { key: 'forcesave', label: '强制保存', description: '是否强制保存文档', type: 'boolean' },
  { key: 'help', label: '帮助功能', description: '是否显示帮助功能', type: 'boolean' },
  { key: 'hideRightMenu', label: '隐藏右侧菜单', description: '是否隐藏右侧菜单', type: 'boolean' },
  { key: 'hideRulers', label: '隐藏标尺', description: '是否隐藏标尺', type: 'boolean' },
  { key: 'macros', label: '宏功能', description: '是否启用宏功能', type: 'boolean' },
  { key: 'plugins', label: '插件功能', description: '是否启用插件功能', type: 'boolean' },
  { key: 'spellcheck', label: '拼写检查', description: '是否启用拼写检查', type: 'boolean' },
  {
    key: 'uiTheme',
    label: 'UI主题',
    description: '用户界面主题',
    type: 'select',
    options: [
      { label: '默认', value: 'theme-light' },
      { label: '深色', value: 'theme-dark' },
    ],
  },
  {
    key: 'unit',
    label: '测量单位',
    description: '文档中使用的测量单位',
    type: 'select',
    options: [
      { label: '厘米', value: 'cm' },
      { label: '磅', value: 'pt' },
      { label: '英寸', value: 'inch' },
    ],
  },
  { key: 'zoom', label: '缩放比例', description: '文档的默认缩放比例', type: 'number' },
]

const coEditingItems: ConfigItemDefinition[] = [
  {
    key: 'mode',
    label: '协作模式',
    description: '协作编辑的模式',
    type: 'select',
    options: [
      { label: '快速模式', value: 'fast' },
      { label: '严格模式', value: 'strict' },
    ],
  },
  { key: 'change', label: '实时更改', description: '是否启用实时更改跟踪', type: 'boolean' },
]

const userItems: ConfigItemDefinition[] = [
  { key: 'id', label: '用户ID', description: '用户的唯一标识符', type: 'string' },
  { key: 'name', label: '用户名', description: '用户显示名称', type: 'string' },
  { key: 'group', label: '用户组', description: '用户所属的组', type: 'string' },
]

const customerItems: ConfigItemDefinition[] = [
  { key: 'address', label: '地址', description: '客户地址信息', type: 'string' },
  { key: 'info', label: '信息', description: '客户附加信息', type: 'string' },
  { key: 'logo', label: 'Logo', description: '客户Logo URL', type: 'string' },
  { key: 'logoDark', label: '深色Logo', description: '深色主题下的Logo URL', type: 'string' },
  { key: 'mail', label: '邮箱', description: '客户邮箱地址', type: 'string' },
  { key: 'name', label: '名称', description: '客户名称', type: 'string' },
  { key: 'phone', label: '电话', description: '客户电话号码', type: 'string' },
  { key: 'www', label: '网站', description: '客户网站地址', type: 'string' },
]

const featuresItems: ConfigItemDefinition[] = [
  { key: 'trackChanges', label: '修订跟踪', description: '是否启用修订跟踪功能', type: 'boolean' },
  { key: 'requestclose', label: '请求关闭', description: '是否允许请求关闭文档', type: 'boolean' },
  { key: 'goback', label: '返回功能', description: '是否显示返回按钮', type: 'boolean' },
  { key: 'submitForm', label: '提交表单', description: '是否启用表单提交功能', type: 'boolean' },
]

const layoutItems: ConfigItemDefinition[] = [
  { key: 'leftMenu', label: '左侧菜单', description: '是否显示左侧菜单', type: 'boolean' },
  { key: 'rightMenu', label: '右侧菜单', description: '是否显示右侧菜单', type: 'boolean' },
  { key: 'toolbar', label: '工具栏', description: '是否显示工具栏', type: 'boolean' },
  { key: 'statusBar', label: '状态栏', description: '是否显示状态栏', type: 'boolean' },
  { key: 'header', label: '头部区域', description: '是否显示头部区域', type: 'boolean' },
]

const reviewItems: ConfigItemDefinition[] = [
  {
    key: 'reviewDisplay',
    label: '审阅显示',
    description: '审阅信息的显示方式',
    type: 'select',
    options: [
      { label: '原稿', value: 'original' },
      { label: '最终', value: 'final' },
      { label: '标记', value: 'markup' },
    ],
  },
  { key: 'trackChanges', label: '跟踪修订', description: '是否自动启用修订跟踪', type: 'boolean' },
  { key: 'hoverMode', label: '悬停模式', description: '是否启用悬停模式', type: 'boolean' },
]

const serverItems: ConfigItemDefinition[] = [
  { key: 'url', label: '服务器URL', description: 'OnlyOffice服务器地址', type: 'string' },
  { key: 'timeout', label: '超时时间', description: '请求超时时间（秒）', type: 'number' },
  { key: 'token', label: '访问令牌', description: '服务器访问令牌', type: 'string' },
]

const mobileItems: ConfigItemDefinition[] = [
  {
    key: 'forceDesktop',
    label: '强制桌面版',
    description: '移动设备是否强制使用桌面版',
    type: 'boolean',
  },
  {
    key: 'hideFileMenu',
    label: '隐藏文件菜单',
    description: '移动端是否隐藏文件菜单',
    type: 'boolean',
  },
  {
    key: 'hideRightPanel',
    label: '隐藏右侧面板',
    description: '移动端是否隐藏右侧面板',
    type: 'boolean',
  },
]

// 获取配置值的方法
const getConfigValue = (group: string, key: string): unknown => {
  return configStates[group]?.[key]?.value
}

const getConfigEnabled = (group: string, key: string): boolean => {
  return configStates[group]?.[key]?.enabled ?? true
}

const getConfigRequired = (group: string, key: string): boolean => {
  return configStates[group]?.[key]?.required ?? false
}

// 更新配置值的方法
const updateConfigValue = (group: string, key: string, value: unknown) => {
  if (!configStates[group]) {
    configStates[group] = {}
  }
  if (!configStates[group][key]) {
    configStates[group][key] = { value, enabled: true, required: false }
  } else {
    configStates[group][key].value = value
  }
}

const updateConfigEnabled = (group: string, key: string, enabled: boolean) => {
  if (!configStates[group]) {
    configStates[group] = {}
  }
  if (!configStates[group][key]) {
    configStates[group][key] = { value: null, enabled, required: false }
  } else {
    configStates[group][key].enabled = enabled
  }
}

// 初始化配置状态
const initializeConfig = () => {
  if (isInitializing.value) return
  isInitializing.value = true

  // 首先清空现有状态
  Object.keys(configStates).forEach(group => {
    configStates[group] = {}
  })

  // 如果有initialConfigStates，直接使用
  if (props.initialConfigStates) {
    Object.keys(props.initialConfigStates).forEach(group => {
      if (!configStates[group]) {
        configStates[group] = {}
      }
      Object.keys(props.initialConfigStates![group]).forEach(key => {
        configStates[group][key] = { ...props.initialConfigStates![group][key] }
      })
    })
  } else if (props.initialConfig) {
    // 兼容旧格式：从config构建configStates
    Object.keys(props.initialConfig).forEach(group => {
      const groupConfig = props.initialConfig![group as keyof OnlyOfficeConfig]
      if (groupConfig && typeof groupConfig === 'object') {
        if (!configStates[group]) {
          configStates[group] = {}
        }
        Object.keys(groupConfig).forEach(key => {
          const value = (groupConfig as Record<string, unknown>)[key]
          configStates[group][key] = {
            value,
            enabled: true, // 默认启用
            required: false, // 默认非必需
          }
        })
      }
    })
  }

  // 确保所有定义的配置项都有默认状态
  const allItems = [
    ...permissionItems.map(item => ({ group: 'permissions', ...item })),
    ...customizationItems.map(item => ({ group: 'customization', ...item })),
    ...featuresItems.map(item => ({ group: 'features', ...item })),
    ...layoutItems.map(item => ({ group: 'layout', ...item })),
    ...coEditingItems.map(item => ({ group: 'coEditing', ...item })),
    ...reviewItems.map(item => ({ group: 'review', ...item })),
    ...userItems.map(item => ({ group: 'user', ...item })),
    ...customerItems.map(item => ({ group: 'customer', ...item })),
    ...serverItems.map(item => ({ group: 'server', ...item })),
    ...mobileItems.map(item => ({ group: 'mobile', ...item })),
  ]

  allItems.forEach(item => {
    if (!configStates[item.group]) {
      configStates[item.group] = {}
    }
    if (!configStates[item.group][item.key]) {
      // 设置默认值
      let defaultValue: unknown = null
      switch (item.type) {
        case 'boolean':
          defaultValue = false
          break
        case 'number':
          defaultValue = item.key === 'zoom' ? 100 : 0
          break
        case 'select':
          defaultValue = item.options?.[0]?.value || ''
          break
        default:
          defaultValue = ''
      }

      configStates[item.group][item.key] = {
        value: defaultValue,
        enabled: false, // 默认禁用，用户需要手动启用
        required: false,
      }
    }
  })

  isInitializing.value = false
}

// 监听初始配置变化
watch(
  () => [props.initialConfig, props.initialConfigStates],
  () => {
    if (!isInitializing.value) {
      initializeConfig()
    }
  },
  { immediate: true, deep: true }
)

// 挂载时初始化
onMounted(() => {
  initializeConfig()
})

// 保存配置的方法
const saveConfig = () => {
  emit('save', toRaw(configStates))
}

// 重置配置的方法
const resetConfig = () => {
  initializeConfig()
  emit('reset')
}

// 导出方法供父组件调用
defineExpose({
  saveConfig,
  resetConfig,
  configStates: () => toRaw(configStates),
})
</script>

<style scoped>
.config-detail-compact {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.config-content-compact {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  max-height: calc(100vh - 300px); /* 减少高度限制，让内容更好填充空间 */
}

.config-section-compact {
  padding: 16px 24px 24px 24px;
  min-height: calc(100vh - 350px); /* 设置最小高度，确保内容填充空间 */
}

.config-group-compact {
  margin-bottom: 20px;
}

.group-header-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.group-icon-compact {
  width: 24px;
  height: 24px;
  background: linear-gradient(45deg, #1890ff, #40a9ff);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.group-title-compact {
  font-size: 14px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.config-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

/* 滚动条样式 */
.config-section-compact::-webkit-scrollbar {
  width: 4px;
}

.config-section-compact::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.config-section-compact::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.config-section-compact::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

@media (max-width: 768px) {
  .config-grid-compact {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
