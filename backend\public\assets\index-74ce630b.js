import{j as o,k as B,A as z,d as Xe,r as I,z as Ye,o as Ze,q as _,c as C,s as i,e as v,b as h,m as f,h as d,v as b,N as Ke,i as g,P as le,F as $,g as q,x as k,t as w,n as oe,G,H,O as et,B as tt,E as ie,Q as nt,C as re,T as se,_ as at}from"./index-5218909a.js";import{S as lt}from"./StarOutlined-b7b083e4.js";import{S as ot}from"./StopOutlined-6978af33.js";import{M as it}from"./MoreOutlined-59e0773e.js";import{U as rt}from"./UploadOutlined-25037a42.js";var st={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};const ct=st;function ce(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?Object(arguments[e]):{},l=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(a).filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable}))),l.forEach(function(s){ut(r,s,a[s])})}return r}function ut(r,e,a){return e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}var J=function(e,a){var l=ce({},e,a.attrs);return o(B,ce({},l,{icon:ct}),null)};J.displayName="BarChartOutlined";J.inheritAttrs=!1;const dt=J;var pt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V687h97.9c11.6 32.8 32 62.3 59.1 84.7 34.5 28.5 78.2 44.3 123 44.3s88.5-15.7 123-44.3c27.1-22.4 47.5-51.9 59.1-84.7H792v-63H643.6l-5.2 24.7C626.4 708.5 573.2 752 512 752s-114.4-43.5-126.5-103.3l-5.2-24.7H232V136h560v752zM320 341h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 160h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"container",theme:"outlined"};const mt=pt;function ue(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?Object(arguments[e]):{},l=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(a).filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable}))),l.forEach(function(s){vt(r,s,a[s])})}return r}function vt(r,e,a){return e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}var Q=function(e,a){var l=ue({},e,a.attrs);return o(B,ue({},l,{icon:mt}),null)};Q.displayName="ContainerOutlined";Q.inheritAttrs=!1;const ft=Q;var gt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM544 472c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v108H372c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h108v108c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V644h108c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V472z"}}]},name:"file-add",theme:"outlined"};const yt=gt;function de(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?Object(arguments[e]):{},l=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(a).filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable}))),l.forEach(function(s){_t(r,s,a[s])})}return r}function _t(r,e,a){return e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}var W=function(e,a){var l=de({},e,a.attrs);return o(B,de({},l,{icon:yt}),null)};W.displayName="FileAddOutlined";W.inheritAttrs=!1;const ht=W;var Ot={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 512h-56c-4.4 0-8 3.6-8 8v320H184V184h320c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V520c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M355.9 534.9L354 653.8c-.1 8.9 7.1 16.2 16 16.2h.4l118-2.9c2-.1 4-.9 5.4-2.3l415.9-415c3.1-3.1 3.1-8.2 0-11.3L785.4 114.3c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-415.8 415a8.3 8.3 0 00-2.3 5.6zm63.5 23.6L779.7 199l45.2 45.1-360.5 359.7-45.7 1.1.7-46.4z"}}]},name:"form",theme:"outlined"};const Ct=Ot;function pe(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?Object(arguments[e]):{},l=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(a).filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable}))),l.forEach(function(s){bt(r,s,a[s])})}return r}function bt(r,e,a){return e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}var X=function(e,a){var l=pe({},e,a.attrs);return o(B,pe({},l,{icon:Ct}),null)};X.displayName="FormOutlined";X.inheritAttrs=!1;const wt=X;var kt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};const zt=kt;function me(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?Object(arguments[e]):{},l=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(a).filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable}))),l.forEach(function(s){$t(r,s,a[s])})}return r}function $t(r,e,a){return e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}var Y=function(e,a){var l=me({},e,a.attrs);return o(B,me({},l,{icon:zt}),null)};Y.displayName="MailOutlined";Y.inheritAttrs=!1;const xt=Y;var St={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};const At=St;function ve(r){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?Object(arguments[e]):{},l=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(a).filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable}))),l.forEach(function(s){Pt(r,s,a[s])})}return r}function Pt(r,e,a){return e in r?Object.defineProperty(r,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[e]=a,r}var Z=function(e,a){var l=ve({},e,a.attrs);return o(B,ve({},l,{icon:At}),null)};Z.displayName="PlayCircleOutlined";Z.inheritAttrs=!1;const Tt=Z;class D{static async getDocumentTemplates(e){const a={limit:(e==null?void 0:e.pageSize)||10,offset:(((e==null?void 0:e.current)||1)-1)*((e==null?void 0:e.pageSize)||10),categoryId:(e==null?void 0:e.category)||"",status:"",sortBy:"updatedAt",order:"desc",keyword:(e==null?void 0:e.search)||""},l=await z.get("/document-templates",{params:a});return{list:(l.templates||[]).map(p=>({id:p.id,name:p.name,description:p.description||"",type:this.getTemplateTypeFromExtension(p.extension),category:p.category_name||p.category_id||"未分类",status:p.status==="enabled"?"active":"inactive",isDefault:!1,previewUrl:void 0,downloadUrl:`/api/document-templates/${p.id}/download`,size:p.file_size||0,createdAt:p.created_at,updatedAt:p.updated_at,createdBy:p.created_by,usageCount:0,tags:[]})),total:l.total||0,current:(e==null?void 0:e.current)||1,pageSize:(e==null?void 0:e.pageSize)||10}}static getTemplateTypeFromExtension(e){const a=(e==null?void 0:e.toLowerCase())||"";return["doc","docx","odt","rtf","txt"].includes(a)?"document":["xls","xlsx","ods","csv"].includes(a)?"spreadsheet":["ppt","pptx","odp"].includes(a)?"presentation":"document"}static async getDocumentTemplateById(e){return z.get(`/document-templates/${e}`)}static async createDocumentTemplate(e){const a=new FormData;return a.append("name",e.name),a.append("type",e.type),a.append("category",e.category),e.description&&a.append("description",e.description),e.tags&&a.append("tags",JSON.stringify(e.tags)),e.file&&a.append("file",e.file),z.post("/document-templates",a,{headers:{"Content-Type":"multipart/form-data"}})}static async updateDocumentTemplate(e,a){return z.put(`/document-templates/${e}`,a)}static async deleteDocumentTemplate(e){return z.delete(`/document-templates/${e}`)}static async createDocumentFromTemplate(e,a){return z.post(`/document-templates/${e}/create-document`,a)}static async createTemplateCategory(e){const a={name:e.name,description:e.description||"",parentId:e.parentId||null,sortOrder:0};console.log("🚀 创建分类请求数据:",a);const l=await z.post("/document-templates/categories",a);console.log("✅ 创建分类响应:",l);let s;if(l&&l.data&&l.data.id)s=l.data;else if(l&&l.id)s=l;else throw new Error("Invalid response format from create category API");return{id:s.id,name:s.name,description:s.description||"",parentId:s.parent_id,children:[],templateCount:0,createdAt:s.created_at||new Date().toISOString()}}static async getTemplateCategories(){try{const e=await z.get("/document-templates/categories/list");console.log("🔍 原始分类API响应:",e);let a=[];if(e&&Array.isArray(e.data))a=e.data;else if(e&&Array.isArray(e))a=e;else if(e&&e.success===!1)return console.warn("⚠️ API返回失败响应:",e),[];return console.log("🔍 提取的分类数据:",a),a.map(l=>({id:l.id,name:l.name,description:l.description||"",parentId:l.parent_id,children:[],templateCount:0,createdAt:l.created_at}))}catch(e){return console.error("❌ 获取分类列表失败:",e),[]}}static async getAllTemplatesOverview(){return z.get("/document-templates/overview/all")}static async searchTemplates(e){return z.get("/document-templates/search/all",{params:e})}static async downloadTemplate(e,a){return z.download(`/document-templates/${e}/download`,a)}static async previewTemplate(e){return z.get(`/document-templates/${e}/preview`)}}const It={class:"document-templates-page"},Dt={class:"content-wrapper"},Bt={class:"search-bar"},Mt={class:"category-stats"},Ft={class:"category-card"},jt={class:"category-info"},Vt={class:"category-name"},Ut={class:"category-count"},Ht={class:"category-card"},Et={class:"category-info"},Nt={class:"category-name"},Lt={class:"category-count"},Rt={class:"templates-table"},qt={key:0,class:"template-name"},Gt={class:"name-info"},Jt={class:"name"},Qt={key:0,class:"description"},Wt={class:"category-management"},Xt={class:"category-actions"},Yt={style:{display:"flex","align-items":"center",gap:"8px"}},Zt={key:0},Kt={key:1},en={style:{display:"flex","align-items":"center",gap:"8px"}},tn={style:{display:"flex","align-items":"center",gap:"8px"}},nn=Xe({__name:"index",setup(r){const e=I(!1),a=I(!1),l=I({keyword:"",categoryId:void 0,status:void 0,sortBy:"updatedAt",order:"desc"}),s=I({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`}),p=I([]),S=I([]),fe=[{title:"模板名称",key:"name",width:250,ellipsis:!0},{title:"分类",key:"category",width:120},{title:"文件大小",key:"size",width:100},{title:"状态",key:"status",width:80},{title:"使用次数",key:"usageCount",width:100},{title:"创建者",key:"creator",width:100},{title:"更新时间",key:"updateTime",width:150},{title:"操作",key:"actions",width:200,fixed:"right"}],ge=[{title:"分类名称",dataIndex:"name",key:"name",width:180},{title:"父分类",key:"parentCategory",width:120},{title:"描述",dataIndex:"description",key:"description",width:200},{title:"图标",key:"icon",width:60},{title:"颜色",key:"color",width:60},{title:"操作",key:"actions",width:120}],ye=Ye(()=>p.value.map(t=>({...t,count:S.value.filter(n=>n.category===t.id).length}))),M=async()=>{e.value=!0;try{const t={current:s.value.current,pageSize:s.value.pageSize,category:l.value.categoryId||void 0,search:l.value.keyword||void 0};console.log("📋 获取文档模板列表，参数:",t);const n=await D.getDocumentTemplates(t);console.log("📋 文档模板API响应:",n),n&&n.list?(S.value=n.list,s.value.total=n.total||n.list.length):Array.isArray(n)?(S.value=n,s.value.total=n.length):(console.warn("⚠️ 意外的API响应格式:",n),S.value=[],s.value.total=0),console.log(`✅ 成功获取 ${S.value.length} 个文档模板`)}catch(t){console.error("❌ 获取文档模板列表失败:",t),f.error("获取文档模板列表失败"),S.value=[],s.value.total=0}finally{e.value=!1}},_e=async()=>{try{console.log("📁 获取模板分类列表...");const t=await D.getTemplateCategories();console.log("📁 分类API响应:",t),Array.isArray(t)?p.value=t.map(n=>({...n,icon:"FileTextOutlined",color:"#1890ff"})):(console.warn("⚠️ 意外的分类API响应格式:",t),p.value=[{id:"contract",name:"合同模板",icon:"ContainerOutlined",color:"#1890ff",description:"",templateCount:0,createdAt:""},{id:"report",name:"报告模板",icon:"BarChartOutlined",color:"#52c41a",description:"",templateCount:0,createdAt:""},{id:"letter",name:"信函模板",icon:"MailOutlined",color:"#fa8c16",description:"",templateCount:0,createdAt:""},{id:"form",name:"表单模板",icon:"FormOutlined",color:"#722ed1",description:"",templateCount:0,createdAt:""},{id:"other",name:"其他模板",icon:"FileTextOutlined",color:"#eb2f96",description:"",templateCount:0,createdAt:""}]),console.log(`✅ 成功获取 ${p.value.length} 个模板分类`)}catch(t){console.error("❌ 获取模板分类失败:",t),f.error("获取模板分类失败"),p.value=[{id:"contract",name:"合同模板",icon:"ContainerOutlined",color:"#1890ff",description:"",templateCount:0,createdAt:""},{id:"report",name:"报告模板",icon:"BarChartOutlined",color:"#52c41a",description:"",templateCount:0,createdAt:""},{id:"letter",name:"信函模板",icon:"MailOutlined",color:"#fa8c16",description:"",templateCount:0,createdAt:""},{id:"form",name:"表单模板",icon:"FormOutlined",color:"#722ed1",description:"",templateCount:0,createdAt:""},{id:"other",name:"其他模板",icon:"FileTextOutlined",color:"#eb2f96",description:"",templateCount:0,createdAt:""}]}},he=t=>{const n=p.value.find(m=>m.id===t);return(n==null?void 0:n.name)||"未分类"},Oe=t=>{const n=p.value.find(m=>m.id===t);return(n==null?void 0:n.color)||"#666666"},E=t=>({ContainerOutlined:ft,BarChartOutlined:dt,MailOutlined:xt,FormOutlined:wt,FileTextOutlined:se})[t]||se,Ce=t=>{if(t===0)return"0 B";const n=1024,m=["B","KB","MB","GB"],y=Math.floor(Math.log(t)/Math.log(n));return parseFloat((t/Math.pow(n,y)).toFixed(2))+" "+m[y]},be=t=>t.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),F=()=>{s.value.current=1,M()},K=t=>{l.value.categoryId===t?l.value.categoryId=void 0:l.value.categoryId=t,F()},we=t=>{s.value={...s.value,...t},M()},ke=()=>{l.value={keyword:"",categoryId:void 0,status:void 0,sortBy:"updatedAt",order:"desc"},s.value.current=1,M()},ze=()=>{a.value=!0},$e=()=>{f.info("创建模板功能开发中...")},xe=()=>{f.info("导入模板功能开发中...")},Se=async t=>{try{console.log("🔍 预览模板:",t.name),f.info(`预览模板: ${t.name}`)}catch(n){console.error("❌ 预览模板失败:",n),f.error("预览模板失败")}},Ae=async t=>{try{console.log("📥 下载模板:",t.name),await D.downloadTemplate(t.id,t.name),f.success(`开始下载模板: ${t.name}`)}catch(n){console.error("❌ 下载模板失败:",n),f.error("下载模板失败")}},Pe=async t=>{try{console.log("📄 基于模板创建文档:",t.name);const n=await D.createDocumentFromTemplate(t.id,{title:`基于${t.name}的新文档`,description:`从模板"${t.name}"创建的文档`});n.editUrl?(window.open(n.editUrl,"_blank"),f.success("文档创建成功，正在打开编辑器...")):f.success("文档创建成功")}catch(n){console.error("❌ 创建文档失败:",n),f.error("创建文档失败")}},Te=t=>{f.info(`编辑模板: ${t.name}`)},Ie=t=>{f.info(`复制模板: ${t.name}`)},De=t=>{S.value.forEach(n=>{n.category===t.category&&(n.isDefault=!1)}),t.isDefault=!0,f.success(`已将 ${t.name} 设为默认模板`)},Be=t=>{const n=t.status==="active"?"inactive":"active";t.status=n,f.success(`已${n==="active"?"启用":"禁用"}模板: ${t.name}`)},Me=async t=>{try{console.log("🗑️ 删除模板:",t.name),await D.deleteDocumentTemplate(t.id),f.success(`已删除模板: ${t.name}`),M()}catch(n){console.error("❌ 删除模板失败:",n),f.error("删除模板失败")}},Fe=[{label:"文档",value:"FileTextOutlined"},{label:"容器",value:"ContainerOutlined"},{label:"柱状图",value:"BarChartOutlined"},{label:"邮件",value:"MailOutlined"},{label:"表单",value:"FormOutlined"}],je=[{label:"蓝色",value:"#1890ff"},{label:"绿色",value:"#52c41a"},{label:"橙色",value:"#fa8c16"},{label:"紫色",value:"#722ed1"},{label:"粉色",value:"#eb2f96"},{label:"青色",value:"#13c2c2"},{label:"红色",value:"#f5222d"}],Ve=()=>[{label:"无父分类（顶级分类）",value:null},...p.value.filter(t=>!t.parentId).map(t=>({label:t.name,value:t.id}))],Ue=t=>{const n=[],m=new Map;return t.forEach(y=>{y.parentId?(m.has(y.parentId)||m.set(y.parentId,[]),m.get(y.parentId).push(y)):n.push(y)}),n.forEach(y=>{y.children=m.get(y.id)||[]}),n},He=t=>{(!t.name||t.name.trim()==="")&&(t.name="新分类",f.warning("分类名称不能为空"))},Ee=async t=>{var n;try{if(!t.name||t.name.trim()===""){f.error("分类名称不能为空");return}if(t.id.startsWith("category_")){console.log("🆕 创建新分类:",t.name,"父分类:",t.parentId);const m=await D.createTemplateCategory({name:t.name.trim(),description:t.description||"",parentId:t.parentId||null}),y=p.value.findIndex(j=>j.id===t.id);y!==-1&&(p.value[y]={...m,icon:t.icon,color:t.color});const N=t.parentId?((n=p.value.find(j=>j.id===t.parentId))==null?void 0:n.name)||"未知":"顶级分类";f.success(`分类 "${t.name}" 创建成功 (${N})`)}else f.success(`分类 "${t.name}" 保存成功`)}catch(m){console.error("❌ 保存分类失败:",m),f.error("保存分类失败")}},Ne=()=>{const t={id:`category_${Date.now()}`,name:"",icon:"FileTextOutlined",color:"#1890ff",description:"",parentId:null,children:[],templateCount:0,createdAt:new Date().toISOString()};p.value.push(t)},Le=t=>{p.value.splice(t,1)},Re=async()=>{try{a.value=!1,f.success("分类设置已保存")}catch(t){console.error("❌ 保存分类失败:",t),f.error("保存分类失败")}};return Ze(()=>{console.log("🚀 初始化文档模板管理页面"),_e(),M()}),(t,n)=>{const m=_("a-button"),y=_("a-space"),N=_("a-page-header"),j=_("a-input-search"),A=_("a-col"),P=_("a-select-option"),T=_("a-select"),ee=_("a-row"),te=_("a-card"),L=_("a-tag"),qe=_("a-badge"),R=_("a-tooltip"),V=_("a-menu-item"),Ge=_("a-menu-divider"),Je=_("a-menu"),Qe=_("a-dropdown"),ne=_("a-table"),ae=_("a-input"),We=_("a-modal");return d(),C("div",It,[o(N,{title:"文档模板管理","sub-title":"管理文档内容模板，用于创建标准化文档"},{extra:i(()=>[o(y,null,{default:i(()=>[o(m,{onClick:ze},{icon:i(()=>[o(b(Ke))]),default:i(()=>[n[5]||(n[5]=g(" 分类管理 "))]),_:1,__:[5]}),o(m,{type:"primary",onClick:$e},{icon:i(()=>[o(b(le))]),default:i(()=>[n[6]||(n[6]=g(" 创建模板 "))]),_:1,__:[6]}),o(m,{onClick:xe},{icon:i(()=>[o(b(rt))]),default:i(()=>[n[7]||(n[7]=g(" 导入模板 "))]),_:1,__:[7]})]),_:1})]),_:1}),v("div",Dt,[h(" 搜索和筛选区域 "),v("div",Bt,[o(ee,{gutter:16,align:"middle"},{default:i(()=>[o(A,{span:8},{default:i(()=>[o(j,{value:l.value.keyword,"onUpdate:value":n[0]||(n[0]=u=>l.value.keyword=u),placeholder:"搜索模板名称、描述...",size:"large",onSearch:F,"allow-clear":""},null,8,["value"])]),_:1}),o(A,{span:4},{default:i(()=>[o(T,{value:l.value.categoryId,"onUpdate:value":n[1]||(n[1]=u=>l.value.categoryId=u),placeholder:"选择分类",size:"large","allow-clear":"",onChange:F},{default:i(()=>[(d(!0),C($,null,q(p.value,u=>(d(),k(P,{key:u.id,value:u.id},{default:i(()=>[g(w(u.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1}),o(A,{span:4},{default:i(()=>[o(T,{value:l.value.status,"onUpdate:value":n[2]||(n[2]=u=>l.value.status=u),placeholder:"选择状态",size:"large","allow-clear":"",onChange:F},{default:i(()=>[o(P,{value:"active"},{default:i(()=>n[8]||(n[8]=[g("启用")])),_:1,__:[8]}),o(P,{value:"inactive"},{default:i(()=>n[9]||(n[9]=[g("禁用")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1}),o(A,{span:4},{default:i(()=>[o(T,{value:l.value.sortBy,"onUpdate:value":n[3]||(n[3]=u=>l.value.sortBy=u),placeholder:"排序方式",size:"large",onChange:F},{default:i(()=>[o(P,{value:"createdAt"},{default:i(()=>n[10]||(n[10]=[g("创建时间")])),_:1,__:[10]}),o(P,{value:"updatedAt"},{default:i(()=>n[11]||(n[11]=[g("更新时间")])),_:1,__:[11]}),o(P,{value:"usageCount"},{default:i(()=>n[12]||(n[12]=[g("使用次数")])),_:1,__:[12]}),o(P,{value:"name"},{default:i(()=>n[13]||(n[13]=[g("名称")])),_:1,__:[13]})]),_:1},8,["value"])]),_:1}),o(A,{span:4},{default:i(()=>[o(m,{onClick:ke,size:"large"},{default:i(()=>n[14]||(n[14]=[g("重置")])),_:1,__:[14]})]),_:1})]),_:1})]),h(" 分类统计卡片 "),v("div",Mt,[o(ee,{gutter:16},{default:i(()=>[h(" 显示分类树结构 "),(d(!0),C($,null,q(Ue(ye.value),u=>(d(),C($,{key:u.id},[h(" 顶级分类 "),o(A,{span:4},{default:i(()=>[o(te,{size:"small",class:oe({"selected-category":l.value.categoryId===u.id}),onClick:c=>K(u.id),style:{cursor:"pointer","border-left":"4px solid #1890ff"}},{default:i(()=>[v("div",Ft,[v("div",jt,[v("div",Vt,"📁 "+w(u.name),1),v("div",Ut,w(u.count)+" 个模板",1)]),v("div",{class:"category-icon",style:G({color:u.color})},[(d(),k(H(E(u.icon||"FileTextOutlined"))))],4)])]),_:2},1032,["class","onClick"])]),_:2},1024),h(" 子分类 "),(d(!0),C($,null,q(u.children,c=>(d(),k(A,{key:c.id,span:4},{default:i(()=>[o(te,{size:"small",class:oe({"selected-category":l.value.categoryId===c.id}),onClick:x=>K(c.id),style:{cursor:"pointer","border-left":"3px solid #52c41a","margin-left":"8px"}},{default:i(()=>[v("div",Ht,[v("div",Et,[v("div",Nt,"┗ "+w(c.name),1),v("div",Lt,w(c.count||0)+" 个模板",1)]),v("div",{class:"category-icon",style:G({color:c.color})},[(d(),k(H(E(c.icon||"FileTextOutlined"))))],4)])]),_:2},1032,["class","onClick"])]),_:2},1024))),128))],64))),128))]),_:1})]),h(" 文档模板表格 "),v("div",Rt,[o(ne,{columns:fe,"data-source":S.value,loading:e.value,pagination:s.value,onChange:we,"row-key":"id",size:"middle"},{bodyCell:i(({column:u,record:c})=>[u.key==="name"?(d(),C("div",qt,[v("div",Gt,[v("div",Jt,[g(w(c.name)+" ",1),c.isDefault?(d(),k(L,{key:0,color:"blue",size:"small"},{default:i(()=>n[15]||(n[15]=[g("默认")])),_:1,__:[15]})):h("v-if",!0)]),c.description?(d(),C("div",Qt,w(c.description),1)):h("v-if",!0)])])):u.key==="category"?(d(),C($,{key:1},[h(" 分类列 "),o(L,{color:Oe(c.category)},{default:i(()=>[g(w(he(c.category)),1)]),_:2},1032,["color"])],64)):u.key==="size"?(d(),C($,{key:2},[h(" 文件大小列 "),g(w(Ce(c.size)),1)],64)):u.key==="status"?(d(),C($,{key:3},[h(" 状态列 "),o(L,{color:c.status==="active"?"green":"red"},{default:i(()=>[g(w(c.status==="active"?"启用":"禁用"),1)]),_:2},1032,["color"])],64)):u.key==="usageCount"?(d(),C($,{key:4},[h(" 使用次数列 "),o(qe,{count:c.usageCount,"number-style":{backgroundColor:"#52c41a"}},null,8,["count"])],64)):u.key==="creator"?(d(),C($,{key:5},[h(" 创建者列 "),g(w(c.createdBy),1)],64)):u.key==="updateTime"?(d(),C($,{key:6},[h(" 更新时间列 "),g(w(be(new Date(c.updatedAt))),1)],64)):u.key==="actions"?(d(),C($,{key:7},[h(" 操作列 "),o(y,null,{default:i(()=>[o(R,{title:"预览"},{default:i(()=>[o(m,{type:"text",size:"small",onClick:x=>Se(c)},{icon:i(()=>[o(b(et))]),_:2},1032,["onClick"])]),_:2},1024),o(R,{title:"下载"},{default:i(()=>[o(m,{type:"text",size:"small",onClick:x=>Ae(c)},{icon:i(()=>[o(b(tt))]),_:2},1032,["onClick"])]),_:2},1024),o(R,{title:"基于模板创建文档"},{default:i(()=>[o(m,{type:"text",size:"small",onClick:x=>Pe(c)},{icon:i(()=>[o(b(ht))]),_:2},1032,["onClick"])]),_:2},1024),o(Qe,null,{overlay:i(()=>[o(Je,null,{default:i(()=>[o(V,{onClick:x=>Te(c)},{icon:i(()=>[o(b(ie))]),default:i(()=>[n[16]||(n[16]=g(" 编辑 "))]),_:2,__:[16]},1032,["onClick"]),o(V,{onClick:x=>Ie(c)},{icon:i(()=>[o(b(nt))]),default:i(()=>[n[17]||(n[17]=g(" 复制 "))]),_:2,__:[17]},1032,["onClick"]),c.isDefault?h("v-if",!0):(d(),k(V,{key:0,onClick:x=>De(c)},{icon:i(()=>[o(b(lt))]),default:i(()=>[n[18]||(n[18]=g(" 设为默认 "))]),_:2,__:[18]},1032,["onClick"])),o(V,{onClick:x=>Be(c)},{icon:i(()=>[(d(),k(H(c.status==="active"?b(ot):b(Tt))))]),default:i(()=>[g(" "+w(c.status==="active"?"禁用":"启用"),1)]),_:2},1032,["onClick"]),o(Ge),o(V,{onClick:x=>Me(c),class:"danger-item"},{icon:i(()=>[o(b(re))]),default:i(()=>[n[19]||(n[19]=g(" 删除 "))]),_:2,__:[19]},1032,["onClick"])]),_:2},1024)]),default:i(()=>[o(m,{type:"text",size:"small"},{icon:i(()=>[o(b(it))]),_:1})]),_:2},1024)]),_:2},1024)],64)):h("v-if",!0)]),_:1},8,["data-source","loading","pagination"])])]),h(" 分类管理模态框 "),o(We,{open:a.value,"onUpdate:open":n[4]||(n[4]=u=>a.value=u),title:"分类管理",width:"800px",onOk:Re},{default:i(()=>[v("div",Wt,[v("div",Xt,[o(m,{type:"primary",onClick:Ne},{icon:i(()=>[o(b(le))]),default:i(()=>[n[20]||(n[20]=g(" 添加分类 "))]),_:1,__:[20]})]),o(ne,{columns:ge,"data-source":p.value,pagination:!1,"row-key":"id",size:"small"},{bodyCell:i(({column:u,record:c,index:x})=>[u.key==="name"?(d(),k(ae,{key:0,value:c.name,"onUpdate:value":O=>c.name=O,placeholder:"请输入分类名称",size:"small",onBlur:O=>He(c)},null,8,["value","onUpdate:value","onBlur"])):u.key==="parentCategory"?(d(),k(T,{key:1,value:c.parentId,"onUpdate:value":O=>c.parentId=O,options:Ve(),placeholder:"选择父分类",size:"small",style:{width:"100%"},"allow-clear":""},{option:i(({label:O,value:U})=>[v("div",Yt,[U?(d(),C("span",Kt,"📁")):(d(),C("span",Zt,"🏠")),v("span",null,w(O),1)])]),_:2},1032,["value","onUpdate:value","options"])):u.key==="description"?(d(),k(ae,{key:2,value:c.description,"onUpdate:value":O=>c.description=O,placeholder:"请输入分类描述",size:"small"},null,8,["value","onUpdate:value"])):u.key==="icon"?(d(),k(T,{key:3,value:c.icon,"onUpdate:value":O=>c.icon=O,size:"small",style:{width:"100%"},options:Fe},{option:i(({label:O,value:U})=>[v("div",en,[(d(),k(H(E(U)))),v("span",null,w(O),1)])]),_:2},1032,["value","onUpdate:value"])):u.key==="color"?(d(),k(T,{key:4,value:c.color,"onUpdate:value":O=>c.color=O,size:"small",style:{width:"100%"},options:je},{option:i(({label:O,value:U})=>[v("div",tn,[v("div",{style:G({backgroundColor:U,width:"16px",height:"16px",borderRadius:"4px",border:"1px solid #d9d9d9"})},null,4),v("span",null,w(O),1)])]),_:2},1032,["value","onUpdate:value"])):u.key==="actions"?(d(),k(y,{key:5},{default:i(()=>[o(m,{type:"text",size:"small",onClick:O=>Ee(c),disabled:!c.name},{icon:i(()=>[o(b(ie))]),_:2},1032,["onClick","disabled"]),o(m,{type:"text",size:"small",danger:"",onClick:O=>Le(x)},{icon:i(()=>[o(b(re))]),_:2},1032,["onClick"])]),_:2},1024)):h("v-if",!0)]),_:1},8,["data-source"])])]),_:1},8,["open"])])}}});const cn=at(nn,[["__scopeId","data-v-ee350276"],["__file","D:/Code/OnlyOffice/frontend/src/pages/Templates/index.vue"]]);export{cn as default};
