<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnlyOffice企业管理系统 - 数据密集型风格</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f5f5f5;
      color: #333;
      line-height: 1.5;
    }

    .container {
      max-width: 1600px;
      margin: 0 auto;
      padding: 16px;
    }

    /* 顶部控制台 */
    .dashboard-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      color: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .system-title {
      font-size: 24px;
      font-weight: 600;
    }

    .system-subtitle {
      font-size: 14px;
      opacity: 0.8;
      margin-top: 4px;
    }

    .header-controls {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .control-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      border-radius: 6px;
      padding: 8px 16px;
      color: white;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .control-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    /* 关键指标网格 */
    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }

    .kpi-card {
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-left: 4px solid #3498db;
      position: relative;
    }

    .kpi-card:nth-child(2) { border-left-color: #2ecc71; }
    .kpi-card:nth-child(3) { border-left-color: #e74c3c; }
    .kpi-card:nth-child(4) { border-left-color: #f39c12; }
    .kpi-card:nth-child(5) { border-left-color: #9b59b6; }
    .kpi-card:nth-child(6) { border-left-color: #34495e; }

    .kpi-icon {
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: 20px;
      opacity: 0.6;
    }

    .kpi-value {
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .kpi-label {
      font-size: 12px;
      color: #7f8c8d;
      margin-bottom: 8px;
    }

    .kpi-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 11px;
    }

    .trend-up { color: #27ae60; }
    .trend-down { color: #e74c3c; }
    .trend-stable { color: #7f8c8d; }

    /* 主要内容区域 */
    .main-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    /* 图表区域 */
    .charts-section {
      display: grid;
      grid-template-rows: auto auto;
      gap: 16px;
    }

    .chart-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ecf0f1;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }

    .chart-filter {
      display: flex;
      gap: 8px;
    }

    .filter-btn {
      padding: 4px 12px;
      border: 1px solid #bdc3c7;
      border-radius: 4px;
      background: white;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .filter-btn.active {
      background: #3498db;
      color: white;
      border-color: #3498db;
    }

    /* 模拟图表 */
    .chart-container {
      height: 200px;
      background: #f8f9fa;
      border-radius: 6px;
      position: relative;
      overflow: hidden;
    }

    .bar-chart {
      display: flex;
      align-items: end;
      justify-content: space-around;
      height: 100%;
      padding: 20px;
    }

    .bar {
      width: 20px;
      background: linear-gradient(to top, #3498db, #5dade2);
      border-radius: 2px 2px 0 0;
      position: relative;
    }

    .bar:nth-child(2) { background: linear-gradient(to top, #2ecc71, #58d68d); }
    .bar:nth-child(3) { background: linear-gradient(to top, #f39c12, #f8c471); }
    .bar:nth-child(4) { background: linear-gradient(to top, #e74c3c, #ec7063); }
    .bar:nth-child(5) { background: linear-gradient(to top, #9b59b6, #bb8fce); }

    .line-chart {
      position: relative;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 6px;
      overflow: hidden;
    }

    .line-chart::before {
      content: '';
      position: absolute;
      top: 30%;
      left: 10%;
      right: 10%;
      bottom: 20%;
      border: 2px solid white;
      border-radius: 4px;
      opacity: 0.3;
    }

    .line-chart::after {
      content: '📈 实时趋势图';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 14px;
      font-weight: 600;
    }

    /* 右侧控制面板 */
    .control-panel {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .panel-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .panel-header {
      background: #34495e;
      color: white;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 600;
    }

    .panel-content {
      padding: 16px;
    }

    /* 系统状态监控 */
    .status-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .status-item {
      text-align: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;
    }

    .status-value {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .status-value.normal { color: #27ae60; }
    .status-value.warning { color: #f39c12; }
    .status-value.error { color: #e74c3c; }

    .status-label {
      font-size: 11px;
      color: #7f8c8d;
    }

    /* 活动日志 */
    .activity-log {
      max-height: 200px;
      overflow-y: auto;
    }

    .log-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #f1f2f6;
      font-size: 12px;
    }

    .log-item:last-child {
      border-bottom: none;
    }

    .log-time {
      color: #7f8c8d;
      font-family: monospace;
      min-width: 50px;
    }

    .log-type {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .log-info { background: #3498db; }
    .log-success { background: #27ae60; }
    .log-warning { background: #f39c12; }
    .log-error { background: #e74c3c; }

    .log-message {
      flex: 1;
      color: #2c3e50;
    }

    /* 快捷操作面板 */
    .quick-actions-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .quick-action {
      background: #ecf0f1;
      border: 1px solid #bdc3c7;
      border-radius: 6px;
      padding: 12px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 12px;
    }

    .quick-action:hover {
      background: #d5dbdb;
      transform: translateY(-1px);
    }

    .action-icon {
      font-size: 16px;
      margin-bottom: 4px;
      display: block;
    }

    /* 底部数据表格 */
    .data-tables {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    .table-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .table-header {
      background: #2c3e50;
      color: white;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 600;
    }

    .data-table {
      width: 100%;
      font-size: 12px;
    }

    .data-table th,
    .data-table td {
      padding: 8px 12px;
      text-align: left;
      border-bottom: 1px solid #ecf0f1;
    }

    .data-table th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
    }

    .data-table tr:nth-child(even) {
      background: #f8f9fa;
    }

    .status-badge {
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: 500;
    }

    .badge-online { background: #d5f4e6; color: #27ae60; }
    .badge-offline { background: #fadbd8; color: #e74c3c; }
    .badge-processing { background: #fdeaa7; color: #f39c12; }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .kpi-grid {
        grid-template-columns: repeat(3, 1fr);
      }
      
      .main-grid {
        grid-template-columns: 1fr;
      }
      
      .data-tables {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 12px;
      }
      
      .kpi-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .status-grid,
      .quick-actions-grid {
        grid-template-columns: 1fr;
      }
      
      .header-content {
        flex-direction: column;
        gap: 12px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 顶部控制台 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div>
          <div class="system-title">🏢 OnlyOffice企业控制台</div>
          <div class="system-subtitle">实时监控 · 数据驱动 · 智能管理</div>
        </div>
        <div class="header-controls">
          <div class="control-btn">🔄 刷新数据</div>
          <div class="control-btn">📊 导出报表</div>
          <div class="control-btn">⚙️ 系统设置</div>
          <div class="control-btn">🔔 告警中心</div>
        </div>
      </div>
    </div>

    <!-- 关键指标 -->
    <div class="kpi-grid">
      <div class="kpi-card">
        <div class="kpi-icon">📄</div>
        <div class="kpi-value">1,247</div>
        <div class="kpi-label">文档总数</div>
        <div class="kpi-trend trend-up">↗ +12.3% 本月</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-icon">👥</div>
        <div class="kpi-value">89</div>
        <div class="kpi-label">活跃用户</div>
        <div class="kpi-trend trend-up">↗ +8.7% 本周</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-icon">🔄</div>
        <div class="kpi-value">347</div>
        <div class="kpi-label">今日操作</div>
        <div class="kpi-trend trend-down">↘ -5.2% 昨日</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-icon">💾</div>
        <div class="kpi-value">847GB</div>
        <div class="kpi-label">存储使用</div>
        <div class="kpi-trend trend-up">↗ +15.2% 本月</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-icon">⚡</div>
        <div class="kpi-value">98.5%</div>
        <div class="kpi-label">系统可用性</div>
        <div class="kpi-trend trend-stable">→ 稳定</div>
      </div>
      <div class="kpi-card">
        <div class="kpi-icon">📈</div>
        <div class="kpi-value">156ms</div>
        <div class="kpi-label">平均响应</div>
        <div class="kpi-trend trend-down">↘ -23ms</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-grid">
      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 文档使用趋势 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">📈 文档使用趋势分析</div>
            <div class="chart-filter">
              <button class="filter-btn active">7天</button>
              <button class="filter-btn">30天</button>
              <button class="filter-btn">90天</button>
            </div>
          </div>
          <div class="chart-container">
            <div class="bar-chart">
              <div class="bar" style="height: 60%;"></div>
              <div class="bar" style="height: 80%;"></div>
              <div class="bar" style="height: 45%;"></div>
              <div class="bar" style="height: 90%;"></div>
              <div class="bar" style="height: 70%;"></div>
              <div class="bar" style="height: 85%;"></div>
              <div class="bar" style="height: 95%;"></div>
            </div>
          </div>
        </div>

        <!-- 实时监控 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">📊 系统性能实时监控</div>
            <div class="chart-filter">
              <button class="filter-btn active">实时</button>
              <button class="filter-btn">1小时</button>
              <button class="filter-btn">24小时</button>
            </div>
          </div>
          <div class="chart-container">
            <div class="line-chart"></div>
          </div>
        </div>
      </div>

      <!-- 右侧控制面板 -->
      <div class="control-panel">
        <!-- 系统状态 -->
        <div class="panel-card">
          <div class="panel-header">🔧 系统状态监控</div>
          <div class="panel-content">
            <div class="status-grid">
              <div class="status-item">
                <div class="status-value normal">99.9%</div>
                <div class="status-label">数据库</div>
              </div>
              <div class="status-item">
                <div class="status-value normal">97.8%</div>
                <div class="status-label">OnlyOffice</div>
              </div>
              <div class="status-item">
                <div class="status-value warning">85.2%</div>
                <div class="status-label">FileNet</div>
              </div>
              <div class="status-item">
                <div class="status-value normal">92.1%</div>
                <div class="status-label">文件存储</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="panel-card">
          <div class="panel-header">⚡ 快捷操作</div>
          <div class="panel-content">
            <div class="quick-actions-grid">
              <div class="quick-action">
                <span class="action-icon">📝</span>
                新建文档
              </div>
              <div class="quick-action">
                <span class="action-icon">📤</span>
                批量上传
              </div>
              <div class="quick-action">
                <span class="action-icon">👥</span>
                用户管理
              </div>
              <div class="quick-action">
                <span class="action-icon">📊</span>
                生成报表
              </div>
              <div class="quick-action">
                <span class="action-icon">🔧</span>
                系统维护
              </div>
              <div class="quick-action">
                <span class="action-icon">🔒</span>
                权限配置
              </div>
            </div>
          </div>
        </div>

        <!-- 活动日志 -->
        <div class="panel-card">
          <div class="panel-header">📋 系统活动日志</div>
          <div class="panel-content">
            <div class="activity-log">
              <div class="log-item">
                <div class="log-time">14:32</div>
                <div class="log-type log-info"></div>
                <div class="log-message">用户张三上传了文档"项目计划.docx"</div>
              </div>
              <div class="log-item">
                <div class="log-time">14:28</div>
                <div class="log-type log-success"></div>
                <div class="log-message">数据库备份完成，文件大小2.3GB</div>
              </div>
              <div class="log-item">
                <div class="log-time">14:25</div>
                <div class="log-type log-warning"></div>
                <div class="log-message">FileNet连接响应时间超过阈值</div>
              </div>
              <div class="log-item">
                <div class="log-time">14:20</div>
                <div class="log-type log-info"></div>
                <div class="log-message">系统配置模板已更新</div>
              </div>
              <div class="log-item">
                <div class="log-time">14:15</div>
                <div class="log-type log-success"></div>
                <div class="log-message">新用户李四注册成功</div>
              </div>
              <div class="log-item">
                <div class="log-time">14:10</div>
                <div class="log-type log-error"></div>
                <div class="log-message">文档编辑器连接失败，已自动重试</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部数据表格 -->
    <div class="data-tables">
      <!-- 用户活动表 -->
      <div class="table-card">
        <div class="table-header">👥 活跃用户统计</div>
        <table class="data-table">
          <thead>
            <tr>
              <th>用户名</th>
              <th>部门</th>
              <th>文档数</th>
              <th>最后登录</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>张三</td>
              <td>产品部</td>
              <td>23</td>
              <td>2分钟前</td>
              <td><span class="status-badge badge-online">在线</span></td>
            </tr>
            <tr>
              <td>李四</td>
              <td>技术部</td>
              <td>45</td>
              <td>15分钟前</td>
              <td><span class="status-badge badge-online">在线</span></td>
            </tr>
            <tr>
              <td>王五</td>
              <td>财务部</td>
              <td>12</td>
              <td>1小时前</td>
              <td><span class="status-badge badge-offline">离线</span></td>
            </tr>
            <tr>
              <td>赵六</td>
              <td>市场部</td>
              <td>8</td>
              <td>3小时前</td>
              <td><span class="status-badge badge-offline">离线</span></td>
            </tr>
            <tr>
              <td>钱七</td>
              <td>人事部</td>
              <td>31</td>
              <td>30分钟前</td>
              <td><span class="status-badge badge-processing">忙碌</span></td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 文档统计表 -->
      <div class="table-card">
        <div class="table-header">📄 文档类型统计</div>
        <table class="data-table">
          <thead>
            <tr>
              <th>文档类型</th>
              <th>数量</th>
              <th>大小</th>
              <th>今日新增</th>
              <th>趋势</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Word文档</td>
              <td>562</td>
              <td>1.2GB</td>
              <td>+12</td>
              <td><span class="trend-up">↗ +8%</span></td>
            </tr>
            <tr>
              <td>Excel表格</td>
              <td>234</td>
              <td>456MB</td>
              <td>+8</td>
              <td><span class="trend-up">↗ +12%</span></td>
            </tr>
            <tr>
              <td>PowerPoint</td>
              <td>189</td>
              <td>2.8GB</td>
              <td>+3</td>
              <td><span class="trend-stable">→ 0%</span></td>
            </tr>
            <tr>
              <td>PDF文档</td>
              <td>156</td>
              <td>890MB</td>
              <td>+5</td>
              <td><span class="trend-down">↘ -3%</span></td>
            </tr>
            <tr>
              <td>其他文件</td>
              <td>106</td>
              <td>234MB</td>
              <td>+2</td>
              <td><span class="trend-up">↗ +15%</span></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    // 模拟实时数据更新
    function updateKPIs() {
      const kpiValues = document.querySelectorAll('.kpi-value');
      kpiValues.forEach(value => {
        // 模拟数据变化
        if (Math.random() > 0.7) {
          value.style.transform = 'scale(1.05)';
          setTimeout(() => {
            value.style.transform = 'scale(1)';
          }, 200);
        }
      });
    }

    // 筛选按钮交互
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const parent = btn.parentElement;
        parent.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
      });
    });

    // 快捷操作点击
    document.querySelectorAll('.quick-action').forEach(action => {
      action.addEventListener('click', () => {
        console.log('执行操作:', action.textContent.trim());
      });
    });

    // 控制按钮点击
    document.querySelectorAll('.control-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        console.log('控制操作:', btn.textContent.trim());
      });
    });

    // 模拟实时数据更新
    setInterval(updateKPIs, 5000);
    
    // 模拟新日志添加
    function addNewLog() {
      const logContainer = document.querySelector('.activity-log');
      const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      const messages = [
        '文档"财务报表.xlsx"被编辑',
        '用户孙八登录系统',
        '系统自动备份完成',
        '新模板"合同模板v2.0"已创建'
      ];
      const types = ['log-info', 'log-success', 'log-warning'];
      
      const newLog = document.createElement('div');
      newLog.className = 'log-item';
      newLog.innerHTML = `
        <div class="log-time">${time}</div>
        <div class="log-type ${types[Math.floor(Math.random() * types.length)]}"></div>
        <div class="log-message">${messages[Math.floor(Math.random() * messages.length)]}</div>
      `;
      
      logContainer.insertBefore(newLog, logContainer.firstChild);
      
      // 保持最多显示10条
      const logs = logContainer.querySelectorAll('.log-item');
      if (logs.length > 10) {
        logs[logs.length - 1].remove();
      }
    }

    // 每30秒添加新日志
    setInterval(addNewLog, 30000);
  </script>
</body>
</html> 