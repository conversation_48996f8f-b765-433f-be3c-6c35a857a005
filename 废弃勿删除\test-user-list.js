const https = require('https');
const http = require('http');

// 创建忽略SSL错误的agent
const agent = new http.Agent({
  rejectUnauthorized: false
});

async function testUserListAPI() {
  console.log('🔄 测试用户列表API...');
  
  try {
    // 首先进行登录获取JWT token
    console.log('1. 登录获取JWT token...');
    const loginResponse = await makeRequest('POST', '/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('登录响应:', loginResponse);
    
    if (!loginResponse.success || !loginResponse.data?.accessToken) {
      throw new Error('登录失败或未获取到token');
    }
    
    const token = loginResponse.data.accessToken;
    console.log('✅ 登录成功，获取到token:', token.substring(0, 20) + '...');
    
    // 使用token访问用户列表
    console.log('\n2. 使用token获取用户列表...');
    const userListResponse = await makeRequest('GET', '/api/users', null, {
      'Authorization': `Bearer ${token}`
    });
    
    console.log('用户列表响应:', JSON.stringify(userListResponse, null, 2));
    
    if (userListResponse.success) {
      console.log('✅ 用户列表获取成功！');
      console.log(`📊 总用户数: ${userListResponse.data.total}`);
      console.log(`📄 当前页用户数: ${userListResponse.data.data.length}`);
      
      // 显示用户信息
      if (userListResponse.data.data.length > 0) {
        console.log('\n👥 用户列表:');
        userListResponse.data.data.forEach((user, index) => {
          console.log(`  ${index + 1}. ${user.username} (${user.full_name || '无姓名'})`);
          console.log(`     角色: ${user.role_name || '无角色'}`);
          console.log(`     权限: ${user.role_permissions ? user.role_permissions.join(', ') : '无权限'}`);
          console.log('     ---------------');
        });
      }
    } else {
      console.log('❌ 用户列表获取失败:', userListResponse.message);
      console.log('错误详情:', userListResponse.error);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      agent: agent
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const jsonResponse = JSON.parse(responseData);
          resolve(jsonResponse);
        } catch (error) {
          reject(new Error(`解析响应失败: ${responseData}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 运行测试
testUserListAPI(); 