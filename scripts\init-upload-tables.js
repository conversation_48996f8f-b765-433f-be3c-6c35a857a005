/**
 * 初始化上传文件表数据库脚本
 */
const fs = require('fs').promises;
const path = require('path');
const db = require('../services/database');

async function initUploadTables() {
    try {
        console.log('开始初始化上传文件表...');

        // 读取并执行SQL脚本
        const sqlPath = path.join(__dirname, 'create-upload-tables.sql');
        const sql = await fs.readFile(sqlPath, 'utf8');

        // 将SQL分割为单独的语句
        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

        console.log(`执行 ${statements.length} 个SQL语句...`);

        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i].trim();
            if (statement) {
                try {
                    console.log(`执行第 ${i + 1} 个语句...`);
                    await db.query(statement);
                } catch (error) {
                    // 忽略表已存在的错误
                    if (error.code !== 'ER_TABLE_EXISTS_ERROR' && !error.message.includes('already exists')) {
                        console.error(`执行第 ${i + 1} 个语句时出错:`, error.message);
                        throw error;
                    } else {
                        console.log(`跳过第 ${i + 1} 个语句（表已存在或其他非致命错误）`);
                    }
                }
            }
        }

        // 验证初始化结果
        const uploadFilesCount = await db.queryOne('SELECT COUNT(*) as count FROM uploaded_files');
        console.log('上传文件表初始化完成！');
        console.log(`- 上传文件记录数量: ${uploadFilesCount.count}`);

        // 检查表是否创建成功
        const tables = await db.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name IN ('uploaded_files', 'file_download_logs', 'file_access_stats')
            ORDER BY table_name
        `);
        
        console.log('\n已创建的表:');
        tables.forEach(table => {
            console.log(`- ${table.table_name}`);
        });

    } catch (error) {
        console.error('初始化上传文件表失败:', error);
        process.exit(1);
    } finally {
        console.log('上传文件表初始化脚本执行完成');
    }
}

// 如果是直接运行此脚本
if (require.main === module) {
    initUploadTables();
}

module.exports = initUploadTables; 