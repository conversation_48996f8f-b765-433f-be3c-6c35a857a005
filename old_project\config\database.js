/**
 * 数据库配置文件
 */
module.exports = {
    host: '*************',
    port: 3306,
    user: 'onlyfile_user',
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile',
    connectionLimit: 10,
    waitForConnections: true,
    queueLimit: 0,
    // 启用预处理语句，防止SQL注入
    namedPlaceholders: true,
    // 处理中文字符
    charset: 'utf8mb4',
    // 连接池配置
    pool: {
        min: 5,
        max: 20,
        // 空闲超时（毫秒）
        idleTimeoutMillis: 30000,
        // 连接超时（毫秒）
        createTimeoutMillis: 30000,
        // 获取连接超时（毫秒）
        acquireTimeoutMillis: 30000
    }
}; 