<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>系统仪表板 - 现代化设计 (版本A)</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 24px;
    }

    /* 欢迎区域 */
    .welcome-section {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .welcome-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .welcome-text h1 {
      font-size: 32px;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 8px;
    }

    .welcome-text p {
      color: #666;
      font-size: 16px;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
    }

    .user-info h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .user-info p {
      color: #666;
      font-size: 14px;
    }

    /* 快速操作 */
    .quick-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-top: 24px;
    }

    .action-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #e5e5e7;
      position: relative;
      overflow: hidden;
    }

    .action-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
    }

    .action-card:nth-child(1)::before { background: linear-gradient(90deg, #667eea, #764ba2); }
    .action-card:nth-child(2)::before { background: linear-gradient(90deg, #f093fb, #f5576c); }
    .action-card:nth-child(3)::before { background: linear-gradient(90deg, #4facfe, #00f2fe); }
    .action-card:nth-child(4)::before { background: linear-gradient(90deg, #43e97b, #38f9d7); }

    .action-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }

    .action-icon {
      font-size: 32px;
      margin-bottom: 12px;
    }

    .action-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .action-desc {
      font-size: 13px;
      color: #666;
    }

    /* 统计概览 */
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
      margin-bottom: 24px;
    }

    .stat-card {
      background: white;
      border-radius: 20px;
      padding: 28px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
      position: relative;
      overflow: hidden;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
    }

    .stat-info h3 {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .stat-number {
      font-size: 36px;
      font-weight: 700;
      color: #333;
      line-height: 1;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: white;
    }

    .stat-icon.blue { background: linear-gradient(135deg, #667eea, #764ba2); }
    .stat-icon.green { background: linear-gradient(135deg, #43e97b, #38f9d7); }
    .stat-icon.orange { background: linear-gradient(135deg, #fa709a, #fee140); }
    .stat-icon.purple { background: linear-gradient(135deg, #a855f7, #3b82f6); }

    .stat-trend {
      margin-top: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .trend-indicator {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .trend-up {
      background: #d4edda;
      color: #155724;
    }

    .trend-down {
      background: #f8d7da;
      color: #721c24;
    }

    .trend-stable {
      background: #d1ecf1;
      color: #0c5460;
    }

    /* 内容区域 */
    .content-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
      margin-bottom: 24px;
    }

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .sidebar-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    /* 最近文档 */
    .recent-docs {
      background: white;
      border-radius: 20px;
      padding: 28px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .view-all {
      color: #667eea;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
    }

    .doc-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .doc-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .doc-item:hover {
      background: #f8f9fa;
    }

    .doc-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: white;
      flex-shrink: 0;
    }

    .doc-icon.word { background: #2b5797; }
    .doc-icon.excel { background: #217346; }
    .doc-icon.ppt { background: #d24726; }
    .doc-icon.pdf { background: #dc3545; }

    .doc-details {
      flex: 1;
    }

    .doc-name {
      font-size: 15px;
      font-weight: 500;
      margin-bottom: 4px;
      color: #333;
    }

    .doc-meta {
      font-size: 13px;
      color: #666;
    }

    .doc-time {
      font-size: 12px;
      color: #999;
      text-align: right;
    }

    /* 活动时间线 */
    .activity-timeline {
      background: white;
      border-radius: 20px;
      padding: 28px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
    }

    .timeline-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .timeline-item {
      display: flex;
      gap: 16px;
      position: relative;
    }

    .timeline-item:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 15px;
      top: 32px;
      bottom: -20px;
      width: 2px;
      background: #e5e5e7;
    }

    .timeline-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: white;
      flex-shrink: 0;
      z-index: 1;
    }

    .timeline-icon.create { background: #667eea; }
    .timeline-icon.edit { background: #43e97b; }
    .timeline-icon.share { background: #fa709a; }
    .timeline-icon.comment { background: #a855f7; }

    .timeline-content {
      flex: 1;
      padding-top: 4px;
    }

    .timeline-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
      color: #333;
    }

    .timeline-desc {
      font-size: 13px;
      color: #666;
      margin-bottom: 4px;
    }

    .timeline-time {
      font-size: 12px;
      color: #999;
    }

    /* 系统状态 */
    .system-status {
      background: white;
      border-radius: 20px;
      padding: 28px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
    }

    .status-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
    }

    .status-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .status-indicator.online { background: #43e97b; }
    .status-indicator.offline { background: #dc3545; }
    .status-indicator.warning { background: #ffc107; }

    .status-label {
      font-size: 14px;
      color: #333;
    }

    .status-value {
      font-size: 14px;
      font-weight: 500;
      color: #667eea;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .content-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 16px;
      }
      
      .welcome-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
      }
      
      .quick-actions {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .stats-overview {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-header">
        <div class="welcome-text">
          <h1>📊 系统仪表板</h1>
          <p>欢迎回来！这里是您的工作概览</p>
        </div>
        <div class="user-profile">
          <div class="avatar">管</div>
          <div class="user-info">
            <h3>管理员</h3>
            <p>系统管理员</p>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <div class="action-card">
          <div class="action-icon">📄</div>
          <div class="action-title">创建文档</div>
          <div class="action-desc">新建Word/Excel/PPT文档</div>
        </div>
        <div class="action-card">
          <div class="action-icon">📤</div>
          <div class="action-title">文件上传</div>
          <div class="action-desc">上传本地文件到系统</div>
        </div>
        <div class="action-card">
          <div class="action-icon">👥</div>
          <div class="action-title">协作空间</div>
          <div class="action-desc">查看团队协作文档</div>
        </div>
        <div class="action-card">
          <div class="action-icon">⚙️</div>
          <div class="action-title">系统设置</div>
          <div class="action-desc">管理系统配置</div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-info">
            <h3>总文档数</h3>
            <div class="stat-number">1,245</div>
          </div>
          <div class="stat-icon blue">📄</div>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator trend-up">
            ↗ +12.5%
          </div>
          <span>较上月增长</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-info">
            <h3>活跃用户</h3>
            <div class="stat-number">89</div>
          </div>
          <div class="stat-icon green">👥</div>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator trend-up">
            ↗ +8.2%
          </div>
          <span>较上周增长</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-info">
            <h3>存储使用</h3>
            <div class="stat-number">78%</div>
          </div>
          <div class="stat-icon orange">💾</div>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator trend-stable">
            → 稳定
          </div>
          <span>存储健康</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-info">
            <h3>系统可用性</h3>
            <div class="stat-number">99.9%</div>
          </div>
          <div class="stat-icon purple">🛡️</div>
        </div>
        <div class="stat-trend">
          <div class="trend-indicator trend-up">
            ↗ +0.1%
          </div>
          <span>运行稳定</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
      <div class="main-content">
        <!-- 最近文档 -->
        <div class="recent-docs">
          <div class="section-header">
            <h2 class="section-title">最近文档</h2>
            <a href="#" class="view-all">查看全部 →</a>
          </div>
          <div class="doc-list">
            <div class="doc-item">
              <div class="doc-icon word">📄</div>
              <div class="doc-details">
                <div class="doc-name">项目需求分析文档v2.0</div>
                <div class="doc-meta">张三 • 2.4 MB</div>
              </div>
              <div class="doc-time">2小时前</div>
            </div>
            <div class="doc-item">
              <div class="doc-icon excel">📊</div>
              <div class="doc-details">
                <div class="doc-name">Q4财务报表汇总</div>
                <div class="doc-meta">李四 • 1.8 MB</div>
              </div>
              <div class="doc-time">5小时前</div>
            </div>
            <div class="doc-item">
              <div class="doc-icon ppt">🎨</div>
              <div class="doc-details">
                <div class="doc-name">产品发布会演示PPT</div>
                <div class="doc-meta">王五 • 15.2 MB</div>
              </div>
              <div class="doc-time">1天前</div>
            </div>
            <div class="doc-item">
              <div class="doc-icon pdf">📋</div>
              <div class="doc-details">
                <div class="doc-name">用户操作手册v2.0</div>
                <div class="doc-meta">赵六 • 8.9 MB</div>
              </div>
              <div class="doc-time">2天前</div>
            </div>
            <div class="doc-item">
              <div class="doc-icon word">📄</div>
              <div class="doc-details">
                <div class="doc-name">会议纪要模板</div>
                <div class="doc-meta">孙七 • 456 KB</div>
              </div>
              <div class="doc-time">3天前</div>
            </div>
          </div>
        </div>
      </div>

      <div class="sidebar-content">
        <!-- 活动时间线 -->
        <div class="activity-timeline">
          <div class="section-header">
            <h2 class="section-title">最近活动</h2>
          </div>
          <div class="timeline-list">
            <div class="timeline-item">
              <div class="timeline-icon create">➕</div>
              <div class="timeline-content">
                <div class="timeline-title">创建了新文档</div>
                <div class="timeline-desc">项目需求分析文档v2.0</div>
                <div class="timeline-time">2小时前</div>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-icon edit">✏️</div>
              <div class="timeline-content">
                <div class="timeline-title">编辑了文档</div>
                <div class="timeline-desc">Q4财务报表汇总</div>
                <div class="timeline-time">5小时前</div>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-icon share">📤</div>
              <div class="timeline-content">
                <div class="timeline-title">分享了文档</div>
                <div class="timeline-desc">产品发布会演示PPT</div>
                <div class="timeline-time">1天前</div>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-icon comment">💬</div>
              <div class="timeline-content">
                <div class="timeline-title">添加了评论</div>
                <div class="timeline-desc">用户操作手册v2.0</div>
                <div class="timeline-time">2天前</div>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-icon create">➕</div>
              <div class="timeline-content">
                <div class="timeline-title">上传了文件</div>
                <div class="timeline-desc">会议纪要模板</div>
                <div class="timeline-time">3天前</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="system-status">
          <div class="section-header">
            <h2 class="section-title">系统状态</h2>
          </div>
          <div class="status-list">
            <div class="status-item">
              <div class="status-info">
                <div class="status-indicator online"></div>
                <div class="status-label">OnlyOffice服务</div>
              </div>
              <div class="status-value">正常</div>
            </div>
            <div class="status-item">
              <div class="status-info">
                <div class="status-indicator online"></div>
                <div class="status-label">数据库连接</div>
              </div>
              <div class="status-value">正常</div>
            </div>
            <div class="status-item">
              <div class="status-info">
                <div class="status-indicator warning"></div>
                <div class="status-label">存储空间</div>
              </div>
              <div class="status-value">78%</div>
            </div>
            <div class="status-item">
              <div class="status-info">
                <div class="status-indicator online"></div>
                <div class="status-label">FileNet连接</div>
              </div>
              <div class="status-value">正常</div>
            </div>
            <div class="status-item">
              <div class="status-info">
                <div class="status-indicator online"></div>
                <div class="status-label">API网关</div>
              </div>
              <div class="status-value">正常</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 快速操作卡片点击
    document.querySelectorAll('.action-card').forEach(card => {
      card.addEventListener('click', () => {
        const title = card.querySelector('.action-title').textContent;
        console.log('执行快速操作:', title);
      });
    });

    // 文档项点击
    document.querySelectorAll('.doc-item').forEach(item => {
      item.addEventListener('click', () => {
        const name = item.querySelector('.doc-name').textContent;
        console.log('打开文档:', name);
      });
    });

    // 统计卡片交互
    document.querySelectorAll('.stat-card').forEach(card => {
      card.addEventListener('click', () => {
        const title = card.querySelector('h3').textContent;
        console.log('查看详细统计:', title);
      });
    });

    // 实时更新数据模拟
    setInterval(() => {
      const activeUsers = document.querySelector('.stat-card:nth-child(2) .stat-number');
      const currentValue = parseInt(activeUsers.textContent);
      const newValue = currentValue + Math.floor(Math.random() * 3) - 1;
      if (newValue > 0) {
        activeUsers.textContent = newValue;
      }
    }, 5000);
  </script>
</body>
</html> 