import { Logger } from '@nestjs/common';
import { DatabaseService } from '../modules/database/services/database.service';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

/**
 * 用户数据接口
 */
interface UserData {
  username: string;
  password: string;
  email: string;
  realName: string;
  role: string;
  status: string;
}

/**
 * 用户初始化脚本
 * 
 * @description 用于初始化系统管理员用户
 * <AUTHOR> Team
 * @since 2024-12-19
 */

const logger = new Logger('UserInit');

/**
 * 默认用户配置
 */
const DEFAULT_USERS = [
  {
    username: 'admin',
    password: 'password123',
    email: '<EMAIL>',
    realName: '系统管理员',
    role: 'admin',
    status: 'active',
  },
  {
    username: 'editor',
    password: 'password123',
    email: '<EMAIL>',
    realName: '编辑员',
    role: 'editor',
    status: 'active',
  },
  {
    username: 'viewer',
    password: 'password123',
    email: '<EMAIL>',
    realName: '查看员',
    role: 'viewer',
    status: 'active',
  },
];

/**
 * 初始化用户
 */
async function initUsers(databaseService: DatabaseService): Promise<void> {
  logger.log('开始初始化用户...');

  try {
    // 检查用户表是否存在
    const userTableExists = await checkUserTableExists(databaseService);
    if (!userTableExists) {
      await createUserTable(databaseService);
    }

    // 创建默认用户
    for (const userData of DEFAULT_USERS) {
      await createUserIfNotExists(databaseService, userData);
    }

    logger.log('用户初始化完成');
  } catch (error) {
    logger.error('用户初始化失败:', error);
    throw error;
  }
}

/**
 * 检查用户表是否存在
 */
async function checkUserTableExists(databaseService: DatabaseService): Promise<boolean> {
  const query = `
    SELECT COUNT(*) as count 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'users'
  `;
  
  const result = await databaseService.query(query) as Array<{ count: number }>;
  return result[0].count > 0;
}

/**
 * 创建用户表
 */
async function createUserTable(databaseService: DatabaseService): Promise<void> {
  logger.log('创建用户表...');
  
  const createTableQuery = `
    CREATE TABLE users (
      id VARCHAR(36) PRIMARY KEY,
      username VARCHAR(50) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      email VARCHAR(100) UNIQUE NOT NULL,
      real_name VARCHAR(100),
      role VARCHAR(20) DEFAULT 'viewer',
      status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
      last_login_at DATETIME NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_username (username),
      INDEX idx_email (email),
      INDEX idx_role (role),
      INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await databaseService.query(createTableQuery);
  logger.log('用户表创建成功');
}

/**
 * 创建用户（如果不存在）
 */
async function createUserIfNotExists(
  databaseService: DatabaseService,
  userData: UserData
): Promise<void> {
  // 检查用户是否已存在
  const existingUser = await databaseService.query(
    'SELECT id FROM users WHERE username = ? OR email = ?',
    [userData.username, userData.email]
  );

  if (existingUser.length > 0) {
    logger.log(`用户 ${userData.username} 已存在，跳过创建`);
    return;
  }

  // 生成用户ID和加密密码
  const userId = uuidv4();
  const hashedPassword = await bcrypt.hash(userData.password, 10);

  // 创建用户
  const insertQuery = `
    INSERT INTO users (
      id, username, password, email, real_name, role, status, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  `;

  await databaseService.query(insertQuery, [
    userId,
    userData.username,
    hashedPassword,
    userData.email,
    userData.realName,
    userData.role,
    userData.status,
  ]);

  logger.log(`用户 ${userData.username} (${userData.realName}) 创建成功`);
}

/**
 * 主函数
 */
export async function main(): Promise<void> {
  const databaseService = new DatabaseService();
  
  try {
    // 数据库服务会在构造时自动初始化
    await databaseService.onModuleInit();
    
    // 确保数据库连接
    await databaseService.query('SELECT 1');
    logger.log('数据库连接成功');
    
    // 初始化用户
    await initUsers(databaseService);
    
    logger.log('用户初始化脚本执行完成');
    process.exit(0);
  } catch (error) {
    logger.error('用户初始化脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
} 