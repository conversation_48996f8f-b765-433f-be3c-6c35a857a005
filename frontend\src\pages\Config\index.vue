<template>
  <div class="config-templates-page">
    <a-page-header title="OnlyOffice用户模板" sub-title="管理OnlyOffice编辑器的用户配置模板">
      <template #extra>
        <a-space>
          <a-button @click="handleRefresh">
            <template #icon>
              <reload-outlined />
            </template>
            刷新模板
          </a-button>
          <a-button type="primary" @click="handleCreateTemplate">
            <template #icon>
              <plus-outlined />
            </template>
            创建用户模板
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-wrapper">
      <!-- 搜索和筛选 -->
      <div class="search-bar">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchQuery"
              placeholder="搜索用户模板名称..."
              @search="handleSearch"
            />
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="selectedStatus"
              placeholder="模板状态"
              allow-clear
              @change="handleStatusChange"
            >
              <a-select-option value="active">启用</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="selectedType"
              placeholder="模板类型"
              allow-clear
              @change="handleTypeChange"
            >
              <a-select-option value="permissions">权限配置</a-select-option>
              <a-select-option value="customization">界面定制</a-select-option>
              <a-select-option value="collaboration">协作功能</a-select-option>
              <a-select-option value="security">安全设置</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button @click="handleReset">重置筛选</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 用户模板统计 -->
      <div class="config-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="用户模板总数"
                :value="configTemplates.length"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <setting-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="默认模板"
                :value="defaultTemplatesCount"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <star-filled />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="启用模板"
                :value="activeTemplatesCount"
                :value-style="{ color: '#fa8c16' }"
              >
                <template #prefix>
                  <check-circle-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="最近更新"
                :value="recentUpdatesCount"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <clock-circle-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 用户模板列表 -->
      <a-table
        :columns="configTemplateColumns"
        :data-source="filteredConfigTemplates"
        :loading="loading"
        :pagination="{
          pageSize: 10,
          showTotal: (total: number) => `共 ${total} 个用户模板`,
          showSizeChanger: true,
          showQuickJumper: true,
        }"
        row-key="id"
        class="config-templates-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="template-name">
              <a-avatar shape="square" :style="{ backgroundColor: getTypeColor(record.type) }">
                <template #icon>
                  <component :is="getTypeIcon(record.type)" />
                </template>
              </a-avatar>
              <div class="name-info">
                <div class="name">
                  {{ record.name }}
                  <a-tag v-if="record.isDefault" color="gold" size="small">
                    <template #icon>
                      <star-filled />
                    </template>
                    默认
                  </a-tag>
                </div>
                <div class="description">
                  {{ record.description }}
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              <template #icon>
                <component :is="getTypeIcon(record.type)" />
              </template>
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status === 'active' ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'usageCount'">
            <a-tooltip :title="`被应用 ${record.usageCount} 次`">
              <a-badge :count="record.usageCount" :number-style="{ backgroundColor: '#52c41a' }" />
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'lastModified'">
            {{ formatDate(record.updatedAt) }}
          </template>
          <template v-else-if="column.key === 'creator'">
            <a-tag>{{ record.creator || '系统' }}</a-tag>
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="primary" size="small" @click="handleEditConfig(record)">
                <template #icon>
                  <edit-outlined />
                </template>
                详细配置
              </a-button>
              <a-button type="text" size="small" @click="handlePreview(record)">
                <template #icon>
                  <eye-outlined />
                </template>
                预览
              </a-button>
              <a-button type="text" size="small" @click="handleExport(record)">
                <template #icon>
                  <download-outlined />
                </template>
                导出
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleDuplicate(record)">
                      <copy-outlined />
                      复制配置
                    </a-menu-item>
                    <a-menu-item @click="handleSetDefault(record)" v-if="!record.isDefault">
                      <star-outlined />
                      设为默认
                    </a-menu-item>
                    <a-menu-item @click="handleToggleStatus(record)">
                      <template v-if="record.status === 'active'">
                        <stop-outlined />
                        禁用
                      </template>
                      <template v-else>
                        <play-circle-outlined />
                        启用
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item class="danger-item" @click="handleDelete(record)">
                      <delete-outlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small">
                  <template #icon>
                    <more-outlined />
                  </template>
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建用户模板对话框 -->
    <a-modal
      v-model:open="createModalVisible"
      title="创建用户模板"
      :width="600"
      @ok="handleCreateSubmit"
    >
      <a-form :model="createForm" layout="vertical">
        <a-form-item label="模板名称" required>
          <a-input v-model:value="createForm.name" placeholder="请输入用户模板名称" />
        </a-form-item>

        <a-form-item label="模板描述">
          <a-textarea v-model:value="createForm.description" placeholder="请输入用户模板描述" />
        </a-form-item>

        <a-form-item label="模板类型" required>
          <a-select v-model:value="createForm.type" placeholder="选择模板类型">
            <a-select-option value="permissions">权限配置</a-select-option>
            <a-select-option value="customization">界面定制</a-select-option>
            <a-select-option value="collaboration">协作功能</a-select-option>
            <a-select-option value="security">安全设置</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="基于模板">
          <a-select
            v-model:value="createForm.baseTemplate"
            placeholder="选择基础模板（可选）"
            allow-clear
          >
            <a-select-option
              v-for="template in configTemplates"
              :key="template.id"
              :value="template.id"
            >
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-checkbox v-model:checked="createForm.setAsDefault">设为默认配置</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  StarFilled,
  StarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EditOutlined,
  EyeOutlined,
  DownloadOutlined,
  CopyOutlined,
  DeleteOutlined,
  MoreOutlined,
  StopOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons-vue'

// 类型定义
interface ConfigTemplate {
  id: string
  name: string
  description: string
  type: 'permissions' | 'customization' | 'collaboration' | 'security'
  status: 'active' | 'inactive'
  isDefault: boolean
  usageCount: number
  creator: string
  createdAt: Date
  updatedAt: Date
}

interface CreateTemplateForm {
  name: string
  description: string
  type: string
  baseTemplate: string
  setAsDefault: boolean
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedStatus = ref<string | undefined>()
const selectedType = ref<string | undefined>()
const createModalVisible = ref(false)

// 创建表单
const createForm = ref<CreateTemplateForm>({
  name: '',
  description: '',
  type: '',
  baseTemplate: '',
  setAsDefault: false,
})

// 用户模板数据
const configTemplates = ref<ConfigTemplate[]>([
  {
    id: '1',
    name: '默认用户模板',
    description: '标准的OnlyOffice用户模板，包含基本的权限和界面设置',
    type: 'permissions',
    status: 'active',
    isDefault: true,
    usageCount: 245,
    creator: '系统管理员',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-12-01'),
  },
  {
    id: '2',
    name: '高级用户模板',
    description: '具有完整权限的用户模板，适用于管理员和高级用户',
    type: 'permissions',
    status: 'active',
    isDefault: false,
    usageCount: 89,
    creator: '张三',
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2024-11-20'),
  },
  {
    id: '3',
    name: '只读用户模板',
    description: '只读权限模板，适用于文档查看和审阅',
    type: 'permissions',
    status: 'active',
    isDefault: false,
    usageCount: 156,
    creator: '李四',
    createdAt: new Date('2024-03-01'),
    updatedAt: new Date('2024-10-15'),
  },
  {
    id: '4',
    name: '企业定制模板',
    description: '定制化的企业用户模板，包含品牌元素和专用功能',
    type: 'customization',
    status: 'active',
    isDefault: false,
    usageCount: 67,
    creator: '王五',
    createdAt: new Date('2024-04-10'),
    updatedAt: new Date('2024-09-25'),
  },
  {
    id: '5',
    name: '协作用户模板',
    description: '启用高级协作功能的用户模板，支持实时协作和评论',
    type: 'collaboration',
    status: 'active',
    isDefault: false,
    usageCount: 123,
    creator: '赵六',
    createdAt: new Date('2024-05-20'),
    updatedAt: new Date('2024-08-30'),
  },
])

// 表格列定义
const configTemplateColumns = [
  {
    title: '模板名称',
    key: 'name',
    dataIndex: 'name',
    width: 300,
  },
  {
    title: '模板类型',
    key: 'type',
    dataIndex: 'type',
    width: 120,
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '使用次数',
    key: 'usageCount',
    dataIndex: 'usageCount',
    width: 100,
  },
  {
    title: '最后修改',
    key: 'lastModified',
    dataIndex: 'updatedAt',
    width: 150,
  },
  {
    title: '创建者',
    key: 'creator',
    dataIndex: 'creator',
    width: 120,
  },
  {
    title: '操作',
    key: 'actions',
    width: 300,
  },
]

// 计算属性
const filteredConfigTemplates = computed(() => {
  let filtered = configTemplates.value

  if (searchQuery.value) {
    filtered = filtered.filter(
      template =>
        template.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(template => template.status === selectedStatus.value)
  }

  if (selectedType.value) {
    filtered = filtered.filter(template => template.type === selectedType.value)
  }

  return filtered
})

const defaultTemplatesCount = computed(
  () => configTemplates.value.filter(template => template.isDefault).length
)

const activeTemplatesCount = computed(
  () => configTemplates.value.filter(template => template.status === 'active').length
)

const recentUpdatesCount = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  return configTemplates.value.filter(template => template.updatedAt > oneWeekAgo).length
})

// 方法
const getTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    permissions: 'CheckCircleOutlined',
    customization: 'SettingOutlined',
    collaboration: 'EditOutlined',
    security: 'EyeOutlined',
  }
  return iconMap[type] || 'SettingOutlined'
}

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    permissions: '#52c41a',
    customization: '#1890ff',
    collaboration: '#fa8c16',
    security: '#f5222d',
  }
  return colorMap[type] || '#d9d9d9'
}

const getTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    permissions: '权限配置',
    customization: '界面定制',
    collaboration: '协作功能',
    security: '安全设置',
  }
  return nameMap[type] || '未知类型'
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 事件处理
const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('用户模板已刷新')
  }, 1000)
}

const handleCreateTemplate = () => {
  createModalVisible.value = true
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleStatusChange = () => {
  // 筛选逻辑已在计算属性中处理
}

const handleTypeChange = () => {
  // 筛选逻辑已在计算属性中处理
}

const handleReset = () => {
  searchQuery.value = ''
  selectedStatus.value = undefined
  selectedType.value = undefined
}

const handleEditConfig = (record: ConfigTemplate) => {
  router.push(`/onlyoffice-config/edit/${record.id}`)
}

const handlePreview = (record: ConfigTemplate) => {
  message.info(`预览模板: ${record.name}`)
}

const handleExport = (record: ConfigTemplate) => {
  message.info(`导出模板: ${record.name}`)
}

const handleDuplicate = (record: ConfigTemplate) => {
  message.info(`复制模板: ${record.name}`)
}

const handleSetDefault = (record: ConfigTemplate) => {
  message.success(`已将 ${record.name} 设为默认模板`)
}

const handleToggleStatus = (record: ConfigTemplate) => {
  const newStatus = record.status === 'active' ? 'inactive' : 'active'
  record.status = newStatus
  message.success(`模板状态已更新为${newStatus === 'active' ? '启用' : '禁用'}`)
}

const handleDelete = (record: ConfigTemplate) => {
  message.error(`删除模板: ${record.name}`)
}

const handleCreateSubmit = () => {
  message.success('用户模板创建成功')
  createModalVisible.value = false
  createForm.value = {
    name: '',
    description: '',
    type: '',
    baseTemplate: '',
    setAsDefault: false,
  }
}

// onMounted已移除 - 当前无需初始化逻辑
</script>

<style scoped>
.config-templates-page {
  padding: 16px;
}

.content-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
}

.search-bar {
  margin-bottom: 16px;
}

.config-stats {
  margin-bottom: 24px;
}

.template-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.name-info .name {
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-info .description {
  color: #666;
  font-size: 12px;
}

.config-templates-table {
  margin-top: 16px;
}

.danger-item {
  color: #ff4d4f !important;
}
</style>
