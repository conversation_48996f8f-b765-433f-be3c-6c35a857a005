import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionService } from '../../users/services/permission.service';

/**
 * 权限守卫装饰器元数据键
 */
export const PERMISSIONS_KEY = 'permissions';

/**
 * 权限守卫
 * 
 * @description 用于检查用户是否具有访问特定API端点的权限
 * @guard PermissionGuard
 * <AUTHOR> Team
 * @since 2024-12-19
 */
@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private permissionService: PermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果没有设置权限要求，则允许访问
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 如果用户未认证，拒绝访问
    if (!user || !user.sub) {
      throw new ForbiddenException('用户未认证');
    }

    try {
      // 检查用户权限
      const checkResult = await this.permissionService.checkPermissions({
        userId: user.sub,
        permissions: requiredPermissions,
        context: {
          request: {
            method: request.method,
            url: request.url,
            params: request.params,
            query: request.query,
          },
        },
      });

      // 如果用户没有所需的任何权限，拒绝访问
      if (!checkResult.has_any) {
        throw new ForbiddenException(`访问被拒绝，需要以下权限之一: ${requiredPermissions.join(', ')}`);
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      
      // 权限检查过程中发生错误，记录日志并拒绝访问
      console.error('权限检查过程中发生错误:', error);
      throw new ForbiddenException('权限检查失败');
    }
  }
} 