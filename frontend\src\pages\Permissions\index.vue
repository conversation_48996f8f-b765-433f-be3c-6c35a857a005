<template>
  <div class="permissions-page">
    <a-card title="权限管理" class="main-card">
      <template #extra>
        <a-space>
          <a-button
            v-if="hasPermission(PERMISSIONS.ROLES.CREATE)"
            type="primary"
            @click="createRole"
          >
            <template #icon>
              <plus-outlined />
            </template>
            新建角色
          </a-button>
          <a-button @click="refreshData" :loading="globalLoading">
            <template #icon>
              <reload-outlined />
            </template>
            刷新数据
          </a-button>
          <a-button v-if="permissionState.isSuperAdmin" @click="debugInfo" type="dashed">
            🔍 调试信息
          </a-button>
        </a-space>
      </template>

      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 角色管理 -->
        <a-tab-pane key="roles" tab="角色管理">
          <a-table
            :columns="roleColumns"
            :data-source="roles"
            :loading="roleLoading"
            row-key="id"
            :pagination="{
              current: rolePagination.current,
              pageSize: rolePagination.pageSize,
              total: rolePagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 个角色`,
            }"
            @change="handleRoleTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <a-space>
                  <a-avatar :style="{ backgroundColor: getRandomColor() }" size="small">
                    {{ record.name?.charAt(0)?.toUpperCase() }}
                  </a-avatar>
                  <span>{{ record.name }}</span>
                </a-space>
              </template>

              <template v-else-if="column.key === 'permissions'">
                <a-space wrap>
                  <a-tag
                    v-for="permission in (record.permissions || []).slice(0, 3)"
                    :key="permission"
                    color="blue"
                  >
                    {{ permission }}
                  </a-tag>
                  <a-tag v-if="(record.permissions || []).length > 3" color="default">
                    +{{ (record.permissions || []).length - 3 }}
                  </a-tag>
                </a-space>
              </template>

              <template v-else-if="column.key === 'status'">
                <a-tag :color="record.is_active ? 'green' : 'red'">
                  {{ record.is_active ? '启用' : '禁用' }}
                </a-tag>
              </template>

              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    v-if="hasPermission(PERMISSIONS.ROLES.UPDATE)"
                    type="text"
                    size="small"
                    @click="editRole(record)"
                  >
                    <template #icon>
                      <edit-outlined />
                    </template>
                    编辑
                  </a-button>
                  <a-button
                    v-if="hasPermission(PERMISSIONS.ROLES.READ)"
                    type="text"
                    size="small"
                    @click="viewRole(record)"
                  >
                    <template #icon>
                      <eye-outlined />
                    </template>
                    查看
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 权限列表 -->
        <a-tab-pane key="permissions" tab="权限列表">
          <a-table
            :columns="permissionColumns"
            :data-source="permissions"
            :loading="permissionLoading"
            row-key="id"
            :pagination="{
              current: permissionPagination.current,
              pageSize: permissionPagination.pageSize,
              total: permissionPagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 个权限`,
            }"
            @change="handlePermissionTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <a-space>
                  <a-tag color="blue">{{ record.module }}</a-tag>
                  <span>{{ record.name }}</span>
                </a-space>
              </template>

              <template v-else-if="column.key === 'action'">
                <a-tag :color="getActionColor(record.action)">
                  {{ record.action }}
                </a-tag>
              </template>

              <template v-else-if="column.key === 'status'">
                <a-tag :color="record.is_active ? 'green' : 'red'">
                  {{ record.is_active ? '启用' : '禁用' }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 用户权限 -->
        <a-tab-pane key="user-permissions" tab="用户权限">
          <div class="user-permissions-section">
            <div class="filter-section">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-input
                    v-model:value="userFilters.search"
                    placeholder="搜索用户..."
                    allow-clear
                    @change="loadUserPermissions"
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="userFilters.roleId"
                    placeholder="选择角色"
                    allow-clear
                    @change="loadUserPermissions"
                  >
                    <a-select-option v-for="role in roles" :key="role.id" :value="role.id">
                      {{ role.name }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-space>
                    <a-button
                      type="primary"
                      @click="loadUserPermissions"
                      :loading="userPermissionLoading"
                    >
                      <template #icon>
                        <search-outlined />
                      </template>
                      搜索
                    </a-button>
                    <a-button @click="resetUserFilters">重置</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 使用/users接口替代有问题的/users/permissions接口 -->
            <a-table
              :columns="userColumns"
              :data-source="users"
              :loading="userPermissionLoading"
              row-key="id"
              :pagination="{
                current: userPagination.current,
                pageSize: userPagination.pageSize,
                total: userPagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total: number) => `共 ${total} 个用户`,
              }"
              @change="handleUserTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'user'">
                  <a-space>
                    <a-avatar size="small">{{
                      record.username?.charAt(0)?.toUpperCase()
                    }}</a-avatar>
                    <div>
                      <div>{{ record.username }}</div>
                      <div style="color: #999; font-size: 12px">{{ record.email }}</div>
                    </div>
                  </a-space>
                </template>

                <template v-else-if="column.key === 'role'">
                  <a-tag v-if="record.role_name" :color="getRandomColor()">
                    {{ record.role_name }}
                  </a-tag>
                  <span v-else style="color: #999">未分配角色</span>
                </template>

                <template v-else-if="column.key === 'permissions'">
                  <a-space wrap>
                    <a-tag
                      v-for="permission in (record.role_permissions || []).slice(0, 2)"
                      :key="permission"
                      size="small"
                      color="blue"
                    >
                      {{ permission }}
                    </a-tag>
                    <a-tag
                      v-if="(record.role_permissions || []).length > 2"
                      size="small"
                      color="default"
                    >
                      +{{ (record.role_permissions || []).length - 2 }}
                    </a-tag>
                    <span
                      v-if="!record.role_permissions || record.role_permissions.length === 0"
                      style="color: #999"
                    >
                      无权限
                    </span>
                  </a-space>
                </template>

                <template v-else-if="column.key === 'status'">
                  <a-tag :color="record.status === 'active' ? 'green' : 'red'">
                    {{ record.status === 'active' ? '启用' : '禁用' }}
                  </a-tag>
                </template>

                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button
                      v-if="hasPermission(PERMISSIONS.USERS.UPDATE)"
                      type="text"
                      size="small"
                      @click="editUserPermissions(record)"
                    >
                      <template #icon>
                        <edit-outlined />
                      </template>
                      编辑权限
                    </a-button>
                    <a-button
                      v-if="hasPermission(PERMISSIONS.USERS.READ)"
                      type="text"
                      size="small"
                      @click="viewUserDetail(record)"
                    >
                      <template #icon>
                        <eye-outlined />
                      </template>
                      查看详情
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 调试信息弹窗 -->
    <a-modal
      v-model:open="debugModalVisible"
      title="调试信息"
      width="800px"
      @ok="debugModalVisible = false"
      @cancel="debugModalVisible = false"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="认证状态">
          <a-tag :color="hasToken ? 'green' : 'red'">
            {{ hasToken ? '已认证' : '未认证' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="Token长度">
          {{ tokenLength || 0 }}
        </a-descriptions-item>
        <a-descriptions-item label="用户信息">
          {{ userInfo ? userInfo.username : '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="API状态">
          <a-space direction="vertical">
            <div>
              角色接口:
              <a-tag :color="apiStatus.roles ? 'green' : 'red'">{{
                apiStatus.roles ? '正常' : '异常'
              }}</a-tag>
            </div>
            <div>
              权限接口:
              <a-tag :color="apiStatus.permissions ? 'green' : 'red'">{{
                apiStatus.permissions ? '正常' : '异常'
              }}</a-tag>
            </div>
            <div>
              用户接口:
              <a-tag :color="apiStatus.users ? 'green' : 'red'">{{
                apiStatus.users ? '正常' : '异常'
              }}</a-tag>
            </div>
          </a-space>
        </a-descriptions-item>
        <a-descriptions-item label="数据统计">
          <a-space direction="vertical">
            <div>角色数量: {{ roles.length }}</div>
            <div>权限数量: {{ permissions.length }}</div>
            <div>用户数量: {{ users.length }}</div>
          </a-space>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 角色权限编辑器 -->
    <RolePermissionEditor
      v-model:open="roleEditorVisible"
      :role-id="currentEditingRoleId"
      @success="onRoleEditSuccess"
    />

    <!-- 用户权限编辑器 -->
    <UserPermissionEditor
      v-model:open="userEditorVisible"
      :user-id="currentEditingUserId"
      @success="onUserEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
} from '@ant-design/icons-vue'
import ApiService from '@/services/api'
import RolePermissionEditor from '@/components/RolePermissionEditor.vue'
import UserPermissionEditor from '@/components/UserPermissionEditor.vue'
import { usePermissions, PERMISSIONS } from '@/composables/usePermissions'

// 路由
const router = useRouter()

// 权限控制
const { hasPermission, permissionState } = usePermissions()

// 响应式数据
const activeTab = ref('roles')
const globalLoading = ref(false)

// 角色相关
const roles = ref<Role[]>([])
const roleLoading = ref(false)
const rolePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 权限相关
const permissions = ref<Permission[]>([])
const permissionLoading = ref(false)
const permissionPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 用户相关 - 使用/users接口替代有问题的/users/permissions接口
const users = ref<User[]>([])
const userPermissionLoading = ref(false)
const userPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 筛选条件
const userFilters = reactive({
  search: '',
  roleId: undefined,
})

// 调试相关
const debugModalVisible = ref(false)
const hasToken = ref(false)
const tokenLength = ref(0)
const userInfo = ref<{ username: string } | null>(null)
const apiStatus = reactive({
  roles: false,
  permissions: false,
  users: false,
})

// 类型定义
interface Role {
  id: string
  name: string
  displayName?: string
  description?: string
  permissions?: string[]
  is_active: boolean
  createdAt?: string
  updatedAt?: string
}

interface Permission {
  id: string
  name: string
  code: string
  module: string
  action: string
  description?: string
  is_active: boolean
  createdAt?: string
  updatedAt?: string
}

interface User {
  id: string
  username: string
  email?: string
  fullName?: string
  role_name?: string
  role_permissions?: string[]
  status: 'active' | 'inactive'
  last_login_at?: string
}

interface ApiResponse<T = unknown> {
  success?: boolean
  data: T[]
  total: number
  message?: string
}

interface TableChangeEvent {
  current: number
  pageSize: number
  total?: number
}

// 表格列定义
const roleColumns = [
  { title: '角色名称', dataIndex: 'name', key: 'name', width: 150 },
  { title: '显示名称', dataIndex: 'displayName', key: 'displayName', width: 150 },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '权限', key: 'permissions', width: 250 },
  { title: '状态', dataIndex: 'is_active', key: 'status', width: 80 },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', width: 150 },
  { title: '操作', key: 'actions', width: 150, fixed: 'right' },
]

const permissionColumns = [
  { title: '权限名称', key: 'name', width: 200 },
  { title: '权限代码', dataIndex: 'code', key: 'code', width: 200 },
  { title: '模块', dataIndex: 'module', key: 'module', width: 100 },
  { title: '操作', key: 'action', width: 100 },
  { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '状态', dataIndex: 'is_active', key: 'status', width: 80 },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', width: 150 },
]

const userColumns = [
  { title: '用户信息', key: 'user', width: 200 },
  { title: '角色', key: 'role', width: 120 },
  { title: '权限', key: 'permissions', width: 200 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80 },
  { title: '最后登录', dataIndex: 'last_login_at', key: 'last_login_at', width: 150 },
  { title: '操作', key: 'actions', width: 150, fixed: 'right' },
]

// 工具函数
const getRandomColor = () => {
  const colors = ['#f56a00', '#7265e6', '#00a2ae', '#00a854', '#fa541c', '#eb2f96']
  return colors[Math.floor(Math.random() * colors.length)]
}

const getActionColor = (action: string) => {
  const mapping: Record<string, string> = {
    read: 'blue',
    create: 'green',
    update: 'orange',
    delete: 'red',
    '*': 'purple',
  }
  return mapping[action] || 'default'
}

// API调用函数
const loadRoles = async () => {
  try {
    roleLoading.value = true
    console.log('🔍 [权限管理] 开始加载角色列表...')

    const response = (await ApiService.get('/roles', {
      params: {
        page: rolePagination.current,
        pageSize: rolePagination.pageSize,
      },
    })) as ApiResponse<Role>

    console.log('✅ [权限管理] 角色列表加载成功:', response)

    roles.value = response.data || []
    rolePagination.total = response.total || 0
    apiStatus.roles = true
  } catch (error: unknown) {
    console.error('❌ [权限管理] 角色列表加载失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    message.error(`角色列表加载失败: ${errorMessage}`)
    apiStatus.roles = false
  } finally {
    roleLoading.value = false
  }
}

const loadPermissions = async () => {
  try {
    permissionLoading.value = true
    console.log('🔍 [权限管理] 开始加载权限列表...')

    const response = (await ApiService.get('/permissions', {
      params: {
        page: permissionPagination.current,
        pageSize: permissionPagination.pageSize,
      },
    })) as ApiResponse<Permission>

    console.log('✅ [权限管理] 权限列表加载成功:', response)

    permissions.value = response.data || []
    permissionPagination.total = response.total || 0
    apiStatus.permissions = true
  } catch (error: unknown) {
    console.error('❌ [权限管理] 权限列表加载失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    message.error(`权限列表加载失败: ${errorMessage}`)
    apiStatus.permissions = false
  } finally {
    permissionLoading.value = false
  }
}

// 使用/users接口替代有问题的/users/permissions接口
const loadUserPermissions = async () => {
  try {
    userPermissionLoading.value = true
    console.log('🔍 [权限管理] 开始加载用户列表（使用/users接口）...')

    const params: Record<string, unknown> = {
      page: userPagination.current,
      pageSize: userPagination.pageSize,
    }

    if (userFilters.search) {
      params.keyword = userFilters.search
    }

    if (userFilters.roleId) {
      params.roleId = userFilters.roleId
    }

    const response = (await ApiService.get('/users', { params })) as ApiResponse<User>

    console.log('✅ [权限管理] 用户列表加载成功:', response)

    users.value = response.data || []
    userPagination.total = response.total || 0
    apiStatus.users = true
  } catch (error: unknown) {
    console.error('❌ [权限管理] 用户列表加载失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    message.error(`用户列表加载失败: ${errorMessage}`)
    apiStatus.users = false
  } finally {
    userPermissionLoading.value = false
  }
}

// 表格操作
const handleRoleTableChange = async (pagination: TableChangeEvent) => {
  rolePagination.current = pagination.current
  rolePagination.pageSize = pagination.pageSize
  await loadRoles()
}

const handlePermissionTableChange = async (pagination: TableChangeEvent) => {
  permissionPagination.current = pagination.current
  permissionPagination.pageSize = pagination.pageSize
  await loadPermissions()
}

const handleUserTableChange = async (pagination: TableChangeEvent) => {
  userPagination.current = pagination.current
  userPagination.pageSize = pagination.pageSize
  await loadUserPermissions()
}

// 编辑器状态
const roleEditorVisible = ref(false)
const userEditorVisible = ref(false)
const currentEditingRoleId = ref<string>('')
const currentEditingUserId = ref<string>('')

// 操作函数
const createRole = () => {
  message.info('创建角色功能正在开发中...')
}

const editRole = (role: Role) => {
  currentEditingRoleId.value = role.id
  roleEditorVisible.value = true
}

const viewRole = (role: Role) => {
  message.info(`查看角色: ${role.name}`)
}

const editUserPermissions = (user: User) => {
  currentEditingUserId.value = user.id
  userEditorVisible.value = true
}

const viewUserDetail = (user: User) => {
  message.info(`查看用户详情: ${user.username}`)
}

// 编辑器回调
const onRoleEditSuccess = async () => {
  await loadRoles()
  message.success('角色权限更新成功')
}

const onUserEditSuccess = async () => {
  await loadUserPermissions()
  message.success('用户权限更新成功')
}

const resetUserFilters = async () => {
  userFilters.search = ''
  userFilters.roleId = undefined
  userPagination.current = 1
  await loadUserPermissions()
}

const refreshData = async () => {
  globalLoading.value = true
  try {
    await Promise.all([loadRoles(), loadPermissions(), loadUserPermissions()])
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    globalLoading.value = false
  }
}

const debugInfo = () => {
  // 更新调试信息
  const token = localStorage.getItem('token')
  const userInfoStr = localStorage.getItem('userInfo')

  hasToken.value = !!token
  tokenLength.value = token?.length || 0
  userInfo.value = userInfoStr ? JSON.parse(userInfoStr) : null

  debugModalVisible.value = true
}

// 初始化
onMounted(async () => {
  console.log('🚀 [权限管理] 页面初始化开始')

  try {
    // 验证认证状态
    const token = localStorage.getItem('token')
    const userInfo = localStorage.getItem('userInfo')

    if (!token || !userInfo) {
      message.error('认证状态异常，请重新登录')
      router.push('/login')
      return
    }

    console.log('✅ [权限管理] 认证状态正常，开始加载数据')

    // 串行加载数据，避免并发问题
    await loadRoles()
    await loadPermissions()
    await loadUserPermissions()

    console.log('✅ [权限管理] 页面初始化完成')
  } catch (error: unknown) {
    console.error('❌ [权限管理] 页面初始化失败:', error)
    message.error('页面初始化失败，请刷新重试')
  }
})
</script>

<style scoped>
.permissions-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px - 64px - 48px);
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.user-permissions-section {
  padding: 16px 0;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-tag) {
  margin-bottom: 4px;
}
</style>
