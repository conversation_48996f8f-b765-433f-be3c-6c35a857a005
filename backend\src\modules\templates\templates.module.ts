import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { DocumentTemplateController } from './controllers/document-template.controller';
import { DocumentTemplateService } from './services/document-template.service';
import { DatabaseModule } from '../database/database.module';
import { FilenetModule } from '../filenet/filenet.module';
import * as multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// 确保上传目录存在
const uploadDir = path.resolve(process.cwd(), 'uploads/templates');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

/**
 * 文档模板模块
 * 
 * 提供FileNet文档模板的管理功能
 * 专门管理合同模板等实际文档文件
 * 区别于OnlyOffice配置模板
 * 
 * @class TemplatesModule
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 2.0.0 (NestJS版)
 */
@Module({
  imports: [
    DatabaseModule,
    FilenetModule,
    MulterModule.register({
      storage: multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, uploadDir); // 模板文件专用目录
        },
        filename: function (req, file, cb) {
          // 使用UUID生成唯一文件名
          const uniqueId = uuidv4();
          const extension = path.extname(file.originalname);
          cb(null, `template-${Date.now()}-${uniqueId}${extension}`);
        }
      }),
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB 文件大小限制
      },
      fileFilter: (req, file, cb) => {
        // 只允许文档类型文件
        const allowedMimes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'text/plain',
          'application/rtf'
        ];
        
        if (allowedMimes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error('不支持的文件类型，只允许文档类型文件'), false);
        }
      }
    }),
  ],
  controllers: [DocumentTemplateController],
  providers: [DocumentTemplateService],
  exports: [DocumentTemplateService],
})
export class TemplatesModule {} 