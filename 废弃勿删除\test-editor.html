<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice编辑器测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 10px 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .info {
            background-color: #ecf0f1;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .success {
            border-left-color: #27ae60;
        }
        .error {
            border-left-color: #e74c3c;
        }
        #placeholder {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #666;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>OnlyOffice编辑器保存功能测试</h1>
        <p>测试新后端架构下的文档编辑和保存功能</p>
    </div>

    <div class="info">
        <h3>📝 测试说明</h3>
        <p>1. 页面会自动加载一个测试文档到OnlyOffice编辑器</p>
        <p>2. 在编辑器中进行任何修改（如输入文字、修改格式等）</p>
        <p>3. 观察编辑器的保存状态指示器</p>
        <p>4. 文档应该能自动保存，不出现"无法保存"的错误</p>
    </div>

    <div id="status-panel">
        <button onclick="loadEditor()">重新加载编辑器</button>
        <button onclick="checkStatus()">检查保存状态</button>
        <button onclick="showConfig()">显示配置信息</button>
    </div>

    <div id="placeholder">
        <div>
            <p>点击"重新加载编辑器"按钮开始测试</p>
            <p>确保新后端服务正在运行在 http://*************:3000</p>
        </div>
    </div>

    <div id="messages"></div>

    <script>
        let docEditor = null;
        let currentConfig = null;
        const API_BASE = 'http://*************:3000';
        const TEST_DOC_ID = '00c536f3-a7a7-45d0-b646-dd8390faf425'; // 从诊断脚本获取的测试文档ID

        function addMessage(text, type = 'info') {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${text}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }

        async function loadEditor() {
            try {
                addMessage('开始加载编辑器配置...', 'info');
                
                // 获取配置
                const response = await fetch(`${API_BASE}/api/editor/${TEST_DOC_ID}/config`);
                if (!response.ok) {
                    throw new Error(`配置获取失败: ${response.status}`);
                }
                
                const result = await response.json();
                currentConfig = result.success ? result.data : result;
                
                addMessage('配置获取成功', 'success');
                addMessage(`文档: ${currentConfig.document.title}`, 'info');
                addMessage(`回调URL: ${currentConfig.editorConfig.callbackUrl}`, 'info');
                
                // 清空容器
                document.getElementById('placeholder').innerHTML = '';
                
                // 加载OnlyOffice API脚本
                if (!window.DocsAPI) {
                    const script = document.createElement('script');
                    script.src = currentConfig.apiUrl;
                    script.onload = () => {
                        addMessage('OnlyOffice API脚本加载成功', 'success');
                        initializeEditor();
                    };
                    script.onerror = () => {
                        addMessage('OnlyOffice API脚本加载失败', 'error');
                    };
                    document.head.appendChild(script);
                } else {
                    initializeEditor();
                }
                
            } catch (error) {
                addMessage(`加载失败: ${error.message}`, 'error');
                console.error('加载编辑器失败:', error);
            }
        }

        function initializeEditor() {
            try {
                addMessage('初始化OnlyOffice编辑器...', 'info');
                
                const config = {
                    document: currentConfig.document,
                    documentType: currentConfig.documentType,
                    editorConfig: {
                        ...currentConfig.editorConfig,
                        // 添加事件处理
                        customization: {
                            ...currentConfig.editorConfig.customization,
                            about: false,
                            feedback: false
                        }
                    },
                    events: {
                        onAppReady: function() {
                            addMessage('✅ 编辑器已准备就绪，可以开始编辑', 'success');
                            addMessage('请在文档中进行一些修改来测试保存功能', 'info');
                        },
                        onDocumentStateChange: function(event) {
                            if (event.data === true) {
                                addMessage('📝 检测到文档修改，等待自动保存...', 'info');
                            }
                        },
                        onSave: function(event) {
                            addMessage('💾 文档已自动保存', 'success');
                        },
                        onError: function(event) {
                            addMessage(`❌ 编辑器错误: ${JSON.stringify(event.data)}`, 'error');
                        },
                        onInfo: function(event) {
                            addMessage(`ℹ️ 编辑器信息: ${JSON.stringify(event.data)}`, 'info');
                        }
                    }
                };

                if (currentConfig.token) {
                    config.token = currentConfig.token;
                }
                
                console.log('编辑器配置:', config);
                
                docEditor = new DocsAPI.DocEditor("placeholder", config);
                addMessage('编辑器初始化完成', 'success');
                
            } catch (error) {
                addMessage(`编辑器初始化失败: ${error.message}`, 'error');
                console.error('编辑器初始化失败:', error);
            }
        }

        async function checkStatus() {
            try {
                addMessage('检查文档保存状态...', 'info');
                
                const response = await fetch(`${API_BASE}/api/editor/${TEST_DOC_ID}/status`);
                if (!response.ok) {
                    throw new Error(`状态检查失败: ${response.status}`);
                }
                
                const result = await response.json();
                addMessage(`保存状态: ${result.status}`, 'success');
                addMessage(`文件名: ${result.fileName}`, 'info');
                addMessage(`版本: ${result.version}`, 'info');
                
            } catch (error) {
                addMessage(`状态检查失败: ${error.message}`, 'error');
            }
        }

        function showConfig() {
            if (currentConfig) {
                addMessage('当前配置信息:', 'info');
                addMessage(`文档URL: ${currentConfig.document.url}`, 'info');
                addMessage(`回调URL: ${currentConfig.editorConfig.callbackUrl}`, 'info');
                addMessage(`文档密钥: ${currentConfig.document.key}`, 'info');
                addMessage(`编辑权限: ${currentConfig.document.permissions.edit}`, 'info');
                console.log('完整配置:', currentConfig);
            } else {
                addMessage('未加载配置信息', 'error');
            }
        }

        // 页面加载时的提示
        addMessage('页面已加载，点击"重新加载编辑器"开始测试', 'info');
    </script>
</body>
</html> 