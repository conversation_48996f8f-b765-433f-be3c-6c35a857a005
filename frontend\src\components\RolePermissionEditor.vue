<!-- 角色权限编辑器组件 -->
<template>
  <a-modal
    v-model:open="visible"
    :title="role ? `编辑角色权限 - ${role.displayName || role.name}` : '编辑角色权限'"
    width="900px"
    :confirm-loading="loading"
    :footer="null"
    :maskClosable="false"
    centered
    class="role-permission-modal"
  >
    <!-- Loading状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large">
        <template #indicator>
          <loading-outlined style="font-size: 24px" spin />
        </template>
      </a-spin>
      <p class="loading-text">正在加载权限数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <a-result status="error" :title="error">
        <template #extra>
          <a-button type="primary" @click="loadData">重新加载</a-button>
        </template>
      </a-result>
    </div>

    <!-- 主要内容 -->
    <div v-else class="permission-editor-content">
      <!-- 角色信息卡片 -->
      <a-card class="role-info-card" size="small">
        <div class="role-header">
          <div class="role-avatar">
            <a-avatar :size="48" :style="{ backgroundColor: '#1890ff', fontSize: '18px' }">
              {{ (role?.displayName || role?.name)?.charAt(0)?.toUpperCase() }}
            </a-avatar>
          </div>
          <div class="role-details">
            <h3 class="role-title">{{ role?.displayName || role?.name }}</h3>
            <p class="role-description">
              {{ role?.description || '系统管理权限管理，拥有所有权限' }}
            </p>
            <div class="role-meta">
              <a-tag color="blue">
                <team-outlined />
                角色ID: {{ role?.id }}
              </a-tag>
              <a-tag :color="role?.is_active ? 'green' : 'red'">
                <check-circle-outlined v-if="role?.is_active" />
                <close-circle-outlined v-else />
                {{ role?.is_active ? '启用' : '禁用' }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 权限分配区域 -->
      <a-card title="权限分配" class="permissions-card">
        <template #extra>
          <a-space>
            <span class="permission-stats">
              已选择 <a-tag color="blue">{{ selectedPermissions.length }}</a-tag> / 总共
              <a-tag color="gray">{{ allPermissionCodes.length }}</a-tag> 个权限
            </span>
            <a-divider type="vertical" />
            <a-checkbox
              :indeterminate="selectAllIndeterminate"
              :checked="isAllSelected"
              @change="onCheckAllChange"
              class="select-all-checkbox"
            >
              <strong>全选</strong>
            </a-checkbox>
          </a-space>
        </template>

        <div class="permissions-container">
          <div v-for="module in permissionModules" :key="module.module" class="permission-module">
            <!-- 模块头部 -->
            <div class="module-header">
              <div class="module-title-section">
                <div class="module-icon">
                  <component :is="getModuleIcon(module.module)" />
                </div>
                <div class="module-info">
                  <h4 class="module-title">{{ module.displayName }}</h4>
                  <span class="module-subtitle">{{ module.permissions.length }} 个权限</span>
                </div>
              </div>
              <div class="module-actions">
                <a-checkbox
                  :indeterminate="getModuleIndeterminate(module.module)"
                  :checked="getModuleChecked(module.module)"
                  @change="(e: CheckboxChangeEvent) => onModuleCheckChange(e, module.module)"
                  class="module-checkbox"
                >
                  <span class="checkbox-label">
                    {{ getModuleSelectionText(module.module) }}
                  </span>
                </a-checkbox>
              </div>
            </div>

            <!-- 权限列表 -->
            <div class="permissions-list">
              <div class="permissions-grid">
                <div
                  v-for="permission in module.permissions"
                  :key="permission.code"
                  class="permission-item"
                  :class="{ 'permission-selected': selectedPermissions.includes(permission.code) }"
                >
                  <a-checkbox
                    :checked="selectedPermissions.includes(permission.code)"
                    @change="(e: CheckboxChangeEvent) => onPermissionChange(e, permission.code)"
                    class="permission-checkbox"
                  >
                    <div class="permission-content">
                      <div class="permission-main">
                        <span class="permission-name">{{ permission.name }}</span>
                        <a-tag
                          :color="getActionColor(permission.action)"
                          size="small"
                          class="action-tag"
                        >
                          {{ getActionDisplayName(permission.action) }}
                        </a-tag>
                      </div>
                      <div class="permission-details">
                        <span class="permission-code">{{ permission.code }}</span>
                        <span class="permission-description">{{ permission.description }}</span>
                      </div>
                    </div>
                  </a-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 底部操作按钮 -->
      <div class="footer-actions">
        <a-space size="large">
          <a-button size="large" @click="visible = false">
            <close-outlined />
            取消
          </a-button>
          <a-button
            type="primary"
            size="large"
            :loading="loading"
            @click="savePermissions"
            class="save-button"
          >
            <save-outlined />
            保存权限配置
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  SecurityScanOutlined,
  AuditOutlined,
  ToolOutlined,
  LoadingOutlined,
  TeamOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SaveOutlined,
} from '@ant-design/icons-vue'
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface'
import {
  getRoleById,
  getPermissions,
  assignRolePermissions,
  type Role,
  type Permission,
} from '@/api/permissions'

// 模块权限接口
interface ModulePermissions {
  module: string
  displayName: string
  permissions: Permission[]
}

// Props
interface Props {
  open: boolean
  roleId: string
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const visible = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})
const loading = ref(false)
const error = ref('')
const role = ref<Role | null>(null)
const permissions = ref<Permission[]>([])
const selectedPermissions = ref<string[]>([])
const permissionModules = ref<ModulePermissions[]>([])
const selectAllIndeterminate = ref(false)
const isAllSelected = ref(false)

// Computed
const allPermissionCodes = computed(() => {
  return permissions.value.map(p => p.code)
})

// Methods
const getModuleDisplayName = (module: string) => {
  const moduleNames: Record<string, string> = {
    documents: '文档管理',
    templates: '模板管理',
    config: '配置管理',
    users: '用户管理',
    roles: '角色管理',
    permissions: '权限管理',
    audit: '审计日志',
    system: '系统管理',
  }
  return moduleNames[module] || module
}

const getModulePermissions = (module: string) => {
  const moduleData = permissionModules.value.find(m => m.module === module)
  return moduleData ? moduleData.permissions.map(p => p.code) : []
}

const getModuleChecked = (module: string) => {
  const modulePermissions = getModulePermissions(module)
  return (
    modulePermissions.length > 0 &&
    modulePermissions.every(code => selectedPermissions.value.includes(code))
  )
}

const getModuleIndeterminate = (module: string) => {
  const modulePermissions = getModulePermissions(module)
  const selectedCount = modulePermissions.filter(code =>
    selectedPermissions.value.includes(code)
  ).length
  return selectedCount > 0 && selectedCount < modulePermissions.length
}

const getActionColor = (action: string) => {
  const mapping: Record<string, string> = {
    read: 'blue',
    create: 'green',
    update: 'orange',
    delete: 'red',
    '*': 'purple',
  }
  return mapping[action] || 'default'
}

const getActionDisplayName = (action: string) => {
  const actionNames: Record<string, string> = {
    read: '查看',
    create: '创建',
    update: '编辑',
    delete: '删除',
    '*': '全部权限',
  }
  return actionNames[action] || action
}

const getModuleIcon = (module: string) => {
  const iconMapping: Record<string, typeof FileTextOutlined> = {
    documents: FileTextOutlined,
    templates: FileTextOutlined,
    config: SettingOutlined,
    users: UserOutlined,
    roles: SecurityScanOutlined,
    permissions: SecurityScanOutlined,
    audit: AuditOutlined,
    system: ToolOutlined,
  }
  return iconMapping[module] || SettingOutlined
}

const onCheckAllChange = (e: CheckboxChangeEvent) => {
  if (e.target.checked) {
    selectedPermissions.value = [...allPermissionCodes.value]
  } else {
    selectedPermissions.value = []
  }
}

const onModuleCheckChange = (e: CheckboxChangeEvent, module: string) => {
  const modulePermissions =
    permissionModules.value.find(m => m.module === module)?.permissions || []
  const modulePermissionCodes = modulePermissions.map(p => p.code)

  if (e.target.checked) {
    // 选中该模块的所有权限
    const newSelected = [...new Set([...selectedPermissions.value, ...modulePermissionCodes])]
    selectedPermissions.value = newSelected
  } else {
    // 取消选中该模块的所有权限
    selectedPermissions.value = selectedPermissions.value.filter(
      code => !modulePermissionCodes.includes(code)
    )
  }
}

const onPermissionChange = (e: CheckboxChangeEvent, permissionCode: string) => {
  if (e.target.checked) {
    selectedPermissions.value = [...selectedPermissions.value, permissionCode]
  } else {
    selectedPermissions.value = selectedPermissions.value.filter(code => code !== permissionCode)
  }
}

const getModuleSelectionText = (module: string) => {
  const modulePermissions = getModulePermissions(module)
  const selectedCount = modulePermissions.filter(code =>
    selectedPermissions.value.includes(code)
  ).length

  if (selectedCount === 0) {
    return '未选择'
  } else if (selectedCount === modulePermissions.length) {
    return '全选'
  } else {
    return `已选 ${selectedCount}/${modulePermissions.length}`
  }
}

const loadData = async () => {
  if (!props.roleId) return

  loading.value = true
  error.value = ''

  try {
    console.log('🔍 [角色权限编辑] 开始加载数据，角色ID:', props.roleId)

    // 先加载角色信息
    const roleResponse = await getRoleById(props.roleId)
    console.log('✅ [角色权限编辑] 角色数据响应:', roleResponse)
    role.value = roleResponse.data || roleResponse

    // 分页加载所有权限 - 修复分页逻辑，使用串行加载确保正确执行
    console.log('🔍 [角色权限编辑] 开始分页加载所有权限...')

    // 逐页加载所有权限
    let allPermissionsList: Permission[] = []
    let currentPage = 1
    const pageSize = 10

    // 先获取第一页
    console.log('🔍 [角色权限编辑] 开始分页加载权限列表，第1页...')
    const firstPageResponse = await getPermissions({
      page: currentPage,
      pageSize: pageSize,
    })

    console.log('✅ [角色权限编辑] 第一页权限响应:', firstPageResponse)

    const firstPageData = firstPageResponse.data || firstPageResponse

    if (firstPageData.data && Array.isArray(firstPageData.data)) {
      allPermissionsList = [...firstPageData.data]
      const totalPermissions = firstPageData.total || firstPageData.data.length

      console.log(
        `🔍 [角色权限编辑] 第${currentPage}页加载完成: 当前${allPermissionsList.length}个，总共${totalPermissions}个`
      )

      // 检查是否还有更多页
      if (allPermissionsList.length >= totalPermissions) {
        console.log('✅ [角色权限编辑] 第一页已包含所有权限')
      } else {
        // 计算剩余页数并逐页加载
        const totalPages = Math.ceil(totalPermissions / pageSize)
        console.log(`🔍 [角色权限编辑] 需要继续加载 ${totalPages - 1} 页权限...`)

        for (let page = 2; page <= totalPages; page++) {
          console.log(`🔍 [角色权限编辑] 加载第${page}页权限...`)

          const pageResponse = await getPermissions({ page, pageSize })
          console.log(`✅ [角色权限编辑] 第${page}页权限响应:`, pageResponse)

          const pageData = pageResponse.data || pageResponse
          if (pageData.data && Array.isArray(pageData.data)) {
            allPermissionsList.push(...pageData.data)
            console.log(
              `✅ [角色权限编辑] 第${page}页加载完成，新增${pageData.data.length}个权限，总计${allPermissionsList.length}个`
            )
          } else {
            console.warn(`⚠️ [角色权限编辑] 第${page}页数据格式异常:`, pageData)
          }
        }

        console.log(
          `✅ [角色权限编辑] 所有权限分页加载完成，最终总数: ${allPermissionsList.length}`
        )
      }
    } else {
      console.warn('⚠️ [角色权限编辑] 第一页权限数据格式异常:', firstPageData)
    }

    console.log('🔍 [角色权限编辑] 最终权限列表，总数:', allPermissionsList.length)
    console.log('🔍 [角色权限编辑] 权限详情:', allPermissionsList)

    // 设置所有权限数据
    permissions.value = allPermissionsList

    // 按模块分组权限
    const moduleMap = new Map<string, Permission[]>()
    allPermissionsList.forEach(permission => {
      const module = permission.module || 'other'
      if (!moduleMap.has(module)) {
        moduleMap.set(module, [])
      }
      moduleMap.get(module)!.push(permission)
    })

    // 转换为数组格式，添加displayName
    permissionModules.value = Array.from(moduleMap.entries()).map(([module, permissions]) => ({
      module,
      displayName: getModuleDisplayName(module),
      permissions: permissions.sort((a, b) => a.name.localeCompare(b.name)),
    }))

    console.log('✅ [角色权限编辑] 最终角色数据:', role.value)
    console.log('✅ [角色权限编辑] 最终权限模块数据:', permissionModules.value)

    // 权限统计日志
    const stats = {
      totalPermissions: allPermissionsList.length,
      moduleCount: permissionModules.value.length,
      moduleStats: permissionModules.value.map(m => ({
        module: m.module,
        displayName: m.displayName,
        count: m.permissions.length,
      })),
    }
    console.log('📊 [角色权限编辑] 权限统计:', stats)

    // 设置已选权限（从角色数据中获取）
    selectedPermissions.value = Array.isArray(role.value?.permissions) ? role.value.permissions : []

    console.log('✅ [角色权限编辑] 已选权限:', selectedPermissions.value)
  } catch (err: unknown) {
    console.error('❌ [角色权限编辑] 加载数据失败:', err)
    const errorMessage = err instanceof Error ? err.message : '加载数据失败'
    error.value = errorMessage
  } finally {
    loading.value = false
  }
}

const savePermissions = async () => {
  if (!props.roleId) {
    message.error('角色ID不能为空')
    return
  }

  loading.value = true
  try {
    console.log('🔄 [角色权限编辑] 开始保存权限配置...')
    console.log('🔄 [角色权限编辑] 角色ID:', props.roleId)
    console.log('🔄 [角色权限编辑] 选中的权限:', selectedPermissions.value)

    await assignRolePermissions(props.roleId, selectedPermissions.value)

    console.log('✅ [角色权限编辑] 权限分配成功')
    message.success('权限分配成功')

    emit('success')
    visible.value = false
  } catch (err: unknown) {
    console.error('❌ [角色权限编辑] 权限分配失败:', err)
    const errorMessage = err instanceof Error ? err.message : '未知错误'
    message.error(`权限分配失败: ${errorMessage}`)
  } finally {
    loading.value = false
  }
}

// Watch
watch(
  () => props.open,
  newValue => {
    if (newValue && props.roleId) {
      loadData()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
/* 角色权限编辑器样式 */
.role-permission-modal :deep(.ant-modal-content) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  overflow: hidden;
}

.role-permission-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

.role-permission-modal :deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.role-permission-modal :deep(.ant-modal-close) {
  color: white;
}

.role-permission-modal :deep(.ant-modal-close:hover) {
  color: #f0f0f0;
}

.role-permission-modal :deep(.ant-modal-body) {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* Loading状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  margin: 20px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

/* 错误状态 */
.error-container {
  margin: 20px;
  background: white;
  border-radius: 8px;
  padding: 20px;
}

/* 主要内容区域 */
.permission-editor-content {
  padding: 20px;
  space-y: 20px;
}

/* 角色信息卡片 */
.role-info-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  background: white;
}

.role-info-card :deep(.ant-card-body) {
  padding: 20px;
}

.role-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.role-avatar {
  flex-shrink: 0;
}

.role-details {
  flex: 1;
}

.role-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.role-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.role-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 权限分配卡片 */
.permissions-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  background: white;
}

.permissions-card :deep(.ant-card-head) {
  border-bottom: 2px solid #f0f0f0;
  padding: 16px 20px;
}

.permissions-card :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.permissions-card :deep(.ant-card-body) {
  padding: 0;
}

/* 权限统计 */
.permission-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

.select-all-checkbox {
  font-weight: 600;
}

/* 权限容器 */
.permissions-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 20px;
}

/* 权限模块 */
.permission-module {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
  transition: all 0.3s ease;
}

.permission-module:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

/* 模块头部 */
.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e8e8e8;
}

.module-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.module-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  color: white;
  font-size: 16px;
}

.module-info {
  display: flex;
  flex-direction: column;
}

.module-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.module-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.module-actions {
  display: flex;
  align-items: center;
}

.module-checkbox {
  font-weight: 500;
}

.checkbox-label {
  color: #1890ff;
  font-size: 14px;
}

/* 权限列表 */
.permissions-list {
  padding: 20px;
  background: white;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

/* 权限项 */
.permission-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.permission-item:hover {
  border-color: #1890ff;
  background: #f6f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.permission-selected {
  border-color: #1890ff;
  background: #f6f9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.permission-checkbox {
  width: 100%;
}

.permission-checkbox :deep(.ant-checkbox) {
  margin-right: 8px;
}

/* 权限内容 */
.permission-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.permission-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.permission-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  flex: 1;
}

.action-tag {
  flex-shrink: 0;
  font-size: 12px;
  border-radius: 4px;
}

.permission-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.permission-code {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  align-self: flex-start;
}

.permission-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 底部操作按钮 */
.footer-actions {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-top: 1px solid #e8e8e8;
  margin-top: 20px;
  border-radius: 0 0 12px 12px;
}

.save-button {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border: none;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
}

.save-button:hover {
  background: linear-gradient(135deg, #389e0d, #237804);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permissions-grid {
    grid-template-columns: 1fr;
  }

  .module-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .permission-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 滚动条样式 */
.permissions-container::-webkit-scrollbar {
  width: 6px;
}

.permissions-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.permissions-container::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 3px;
}

.permissions-container::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 动画效果 */
.permission-module {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.permission-item {
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 深色主题兼容性 */
@media (prefers-color-scheme: dark) {
  .role-permission-modal :deep(.ant-modal-content) {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .permission-module {
    background: #3a3a3a;
    border-color: #4a4a4a;
  }

  .module-header {
    background: linear-gradient(135deg, #4a4a4a 0%, #5a5a5a 100%);
  }

  .permission-item {
    background: #3a3a3a;
    border-color: #4a4a4a;
  }

  .permission-item:hover {
    background: #4a4a4a;
  }
}
</style>
