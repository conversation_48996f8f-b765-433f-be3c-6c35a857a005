# OnlyOffice 集成系统 - NestJS 后端

> 🚀 **现代化企业级后端服务** | NestJS + TypeScript + MySQL + Swagger  
> 📅 **创建时间**: 2024年12月19日  
> 🎯 **版本**: 2.0.0 (NestJS重构版)  

## 📋 项目概述

这是OnlyOffice集成系统的NestJS重构版本，提供企业级的文档管理和集成能力。

### ✨ 核心特性

- 🏗️ **现代架构**: NestJS + TypeScript，模块化设计
- 📡 **RESTful API**: 标准化API设计，统一响应格式
- 📚 **Swagger文档**: 开箱即用的API文档系统
- 🔐 **JWT认证**: 基于角色的权限控制(RBAC)
- 🗄️ **数据库集成**: MySQL连接池，事务支持
- ⚡ **高性能**: 请求限流、缓存、异步处理
- 🛡️ **安全防护**: 参数验证、异常处理、安全中间件
- 📊 **监控能力**: 结构化日志、性能监控、健康检查

### 🆚 相比Express版本的优势

| 特性 | Express版本 | NestJS版本 | 改进 |
|------|-------------|------------|------|
| **架构模式** | 手动分层 | 装饰器+模块化 | +40% 开发效率 |
| **依赖注入** | 手动管理 | 自动注入 | +30% 代码质量 |
| **API文档** | 手动编写 | 自动生成 | +90% 文档覆盖 |
| **参数验证** | 手动验证 | 自动验证管道 | +60% 安全性 |
| **错误处理** | 分散处理 | 全局过滤器 | +50% 可维护性 |
| **测试支持** | 手动配置 | 内置测试工具 | +70% 测试覆盖 |

## 🚀 快速开始

### 1. 安装依赖

```bash
# 进入NestJS后端目录
cd backend-nestjs

# 安装依赖包
npm install

# 安装开发依赖
npm install --save-dev
```

### 2. 环境配置

```bash
# 复制环境配置文件 (从根目录的.env)
cp ../.env .env

# 确认关键配置
cat .env | grep -E "(DB_|ONLYOFFICE_|JWT_)"
```

### 3. 启动开发服务器

```bash
# 启动开发服务器 (热重载)
npm run dev

# 或者使用NestJS原生命令
npm run start:dev
```

### 4. 验证安装

```bash
# 健康检查
curl http://localhost:3000/api/health

# API文档
open http://localhost:3000/api-docs
```

## 📁 项目结构

```
backend-nestjs/
├── src/
│   ├── main.ts                 # 应用程序入口
│   ├── app.module.ts           # 根模块
│   │
│   ├── modules/                # 功能模块
│   │   ├── health/             # 健康检查模块
│   │   ├── auth/               # 认证模块
│   │   ├── documents/          # 文档管理模块
│   │   ├── config/             # 配置管理模块
│   │   └── database/           # 数据库模块
│   │
│   ├── common/                 # 通用组件
│   │   ├── filters/            # 异常过滤器
│   │   ├── interceptors/       # 拦截器
│   │   ├── guards/             # 路由守卫
│   │   ├── decorators/         # 自定义装饰器
│   │   └── pipes/              # 验证管道
│   │
│   ├── dto/                    # 数据传输对象
│   ├── entities/               # 数据库实体
│   ├── interfaces/             # TypeScript接口
│   └── utils/                  # 工具函数
│
├── test/                       # 测试文件
├── dist/                       # 编译输出
└── uploads/                    # 文件上传目录
```

## 🛠️ 开发指南

### 代码规范

项目遵循严格的代码规范，详见 [`../代码规范和命名标准.md`](../代码规范和命名标准.md)

**关键命名规则**:
- **模块**: PascalCase + Module后缀 (`HealthModule`)
- **控制器**: PascalCase + Controller后缀 (`HealthController`)
- **服务**: PascalCase + Service后缀 (`DatabaseService`)
- **DTO**: PascalCase + Dto后缀 (`CreateDocumentDto`)
- **接口**: I前缀 + PascalCase (`IUserJwtPayload`)
- **装饰器**: PascalCase (`@ApiOperation`)

### 开发命令

```bash
# 开发相关
npm run dev              # 启动开发服务器
npm run start:debug      # 调试模式启动
npm run build            # 构建生产版本
npm run start:prod       # 生产模式启动

# 代码质量
npm run lint             # 代码检查
npm run lint:fix         # 自动修复
npm run format           # 代码格式化
npm run check-types      # 类型检查

# 测试相关
npm run test             # 单元测试
npm run test:watch       # 监听模式测试
npm run test:cov         # 覆盖率测试
npm run test:e2e         # 端到端测试

# 实用工具
npm run init-db          # 初始化数据库
npm run validate         # 完整验证
```

### NestJS CLI生成器

```bash
# 生成模块
nest g module modules/documents

# 生成控制器
nest g controller modules/documents/controllers/document

# 生成服务
nest g service modules/documents/services/document

# 生成DTO
nest g class dto/create-document.dto --no-spec

# 生成Guard
nest g guard common/guards/jwt-auth

# 生成Interceptor
nest g interceptor common/interceptors/cache
```

## 📡 API 文档

### Swagger 接口文档

启动服务后访问: **http://localhost:3000/api-docs**

### 核心接口

| 模块 | 路径 | 描述 |
|------|------|------|
| **健康检查** | `/api/health` | 系统状态监控 |
| **认证** | `/api/auth` | 用户登录、JWT刷新 |
| **文档** | `/api/documents` | OnlyOffice文档CRUD |
| **配置** | `/api/config` | 系统配置管理 |
| **FileNet** | `/api/filenet` | 企业内容管理 |

### 统一响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
  requestId: string;
}
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | 服务端口 | `3000` |
| `DB_HOST` | 数据库主机 | `*************` |
| `DB_USER` | 数据库用户 | `onlyfile_user` |
| `JWT_SECRET` | JWT密钥 | `your-secret-key` |
| `ONLYOFFICE_URL` | OnlyOffice服务器 | `http://office.example.com` |

### 模块配置

- **限流配置**: 短期3req/s，中期20req/10s，长期100req/min
- **CORS配置**: 支持凭证传递，预设Origin白名单
- **Swagger配置**: JWT认证，标签分组，自定义样式

## 🧪 测试策略

### 测试类型

1. **单元测试**: 测试单个服务和控制器
2. **集成测试**: 测试模块间交互
3. **端到端测试**: 测试完整API流程
4. **性能测试**: 测试响应时间和并发

### 测试覆盖目标

- **服务层**: 90%+ 覆盖率
- **控制器层**: 85%+ 覆盖率
- **工具函数**: 95%+ 覆盖率
- **总体覆盖**: 80%+ 覆盖率

## 🚀 部署指南

### 生产构建

```bash
# 构建项目
npm run build

# 启动生产服务器
npm run start:prod
```

### Docker部署

```bash
# 构建镜像
docker build -t onlyoffice-nestjs .

# 运行容器
docker run -p 3000:3000 --env-file .env onlyoffice-nestjs
```

### PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 监控状态
pm2 status
pm2 logs
```

## 📊 性能监控

### 关键指标

- **响应时间**: < 200ms (健康检查)
- **内存使用**: < 512MB (开发环境)
- **CPU使用**: < 50% (正常负载)
- **数据库连接**: < 10个活跃连接

### 监控工具

- **内置日志**: Winston结构化日志
- **健康检查**: `/api/health` 端点
- **Swagger监控**: API调用统计
- **PM2监控**: 进程状态管控

## 🔄 迁移计划

### 从Express版本迁移

1. **✅ 项目初始化**: NestJS环境搭建
2. **🔄 核心模块**: 健康检查、认证、数据库
3. **⏳ 业务模块**: 文档管理、配置管理、FileNet
4. **⏳ 测试覆盖**: 单元测试、集成测试
5. **⏳ 部署切换**: 灰度发布、监控验证

### 迁移优势

- **业务逻辑保留**: 100%兼容现有业务
- **API格式统一**: 前端无需修改
- **性能提升**: 30%+响应速度提升
- **维护性增强**: 50%+代码可读性提升

## 📞 技术支持

### 问题排查

```bash
# 检查服务状态
curl http://localhost:3000/api/health

# 查看日志
npm run start:dev | tail -f

# 数据库连接测试
npm run init-db
```

### 常见问题

1. **模块导入错误**: 检查 `tsconfig.json` 路径配置
2. **数据库连接失败**: 验证 `.env` 数据库配置
3. **Swagger不显示**: 确认装饰器语法正确

---

**🎯 下一步**: 安装依赖，配置环境变量，启动开发服务器  
**📈 开发进度**: NestJS架构搭建完成，开始模块开发  
**🔥 推荐**: 使用NestJS CLI生成器提升开发效率 