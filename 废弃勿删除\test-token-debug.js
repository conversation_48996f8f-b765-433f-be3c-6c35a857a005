const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

// 从环境变量或直接设置数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: 3306,
  user: 'onlyoffice_user',
  password: 'SecureP@ssw0rd123',
  database: 'onlyoffice_db',
  charset: 'utf8mb4'
};

async function testTokenAndDatabase() {
  try {
    console.log('🔍 开始测试Token和数据库查询...');
    
    // 模拟一个JWT token（这里需要用实际的token）
    const samplePayload = {
      sub: 'user-admin',
      username: 'admin',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
      iss: 'onlyoffice-system',
      aud: 'onlyoffice-users'
    };
    
    console.log('📝 模拟Token Payload:', samplePayload);
    
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查询用户
    const userQuery = `
      SELECT 
        u.id,
        u.username,
        u.email,
        u.full_name as fullName,
        u.last_login_at as lastLoginAt,
        ur.id as role_id,
        ur.name as role_name,
        ur.display_name as role_display_name,
        ur.permissions as role_permissions,
        ur.color as role_color
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.id = ? AND u.deleted_at IS NULL
    `;
    
    console.log('📝 执行用户查询:', userQuery.replace(/\s+/g, ' ').trim());
    console.log('📝 查询参数:', [samplePayload.sub]);
    
    const [userResults] = await connection.execute(userQuery, [samplePayload.sub]);
    
    console.log('✅ 查询结果:', {
      resultCount: userResults.length,
      data: userResults
    });
    
    if (userResults.length === 0) {
      console.error('❌ 用户不存在，检查数据库中的用户ID:');
      
      const [allUsers] = await connection.execute('SELECT id, username, email, deleted_at FROM users LIMIT 10');
      console.log('🔍 数据库中的所有用户:', allUsers);
    } else {
      console.log('✅ 找到用户:', userResults[0]);
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testTokenAndDatabase(); 