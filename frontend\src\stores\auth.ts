import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import type { LoginResponse, UserInfo } from '@/types/api.types'
import { api } from '@/services/api' // 直接使用axios实例
import { useRouter } from 'vue-router'
import { usePermissions } from '@/composables/usePermissions'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)
  const router = useRouter()
  const { initPermissions, clearPermissions } = usePermissions()

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => userInfo.value?.role === 'admin')

  // 初始化用户信息
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo && savedUserInfo !== 'undefined' && savedUserInfo.trim() !== '') {
      try {
        userInfo.value = JSON.parse(savedUserInfo)

        // 强制重新获取权限，不从localStorage恢复
        console.log('🔄 [Auth] 用户信息已恢复，强制重新获取权限')
        if (userInfo.value?.id && token.value) {
          loadUserPermissions()
            .then(() => {
              console.log('✅ [Auth] 权限重新加载成功')
            })
            .catch(error => {
              console.error('❌ [Auth] 权限重新加载失败:', error)
              // 如果权限获取失败，使用空权限，但不登出
              initPermissions([], [])
            })
        } else {
          console.warn('⚠️ [Auth] 用户信息或token缺失，使用空权限')
          initPermissions([], [])
        }
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        localStorage.removeItem('userPermissions')
        localStorage.removeItem('userRoles')
        userInfo.value = null
        token.value = null
        clearPermissions()
      }
    }
  }

  // 处理认证过期
  const handleAuthExpired = (showMessage = true) => {
    console.log('🚨 处理认证过期事件')

    // 清除本地状态
    token.value = null
    userInfo.value = null

    // 清除本地存储和权限信息
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('userPermissions')
    localStorage.removeItem('userRoles')

    // 清除权限系统状态
    clearPermissions()

    // 显示提示消息
    if (showMessage) {
      message.warning('登录状态已过期，请重新登录')
    }

    // 跳转到登录页面
    if (router.currentRoute.value.path !== '/login') {
      router.push('/login')
    }
  }

  // 登录
  const login = async (credentials: { username: string; password: string }) => {
    loading.value = true
    try {
      console.log('开始登录请求:', credentials.username)
      // 直接使用axios实例，不通过ApiService包装
      const response = await api.post<{ success: boolean; data: LoginResponse; message: string }>(
        '/auth/login',
        credentials
      )
      const result = response.data

      console.log('登录响应:', result)

      // 检查响应包装格式
      if (!result || !result.success || !result.data) {
        throw new Error(result.message || '登录响应数据格式不正确')
      }

      const data = result.data

      // 🔧 修复Token保存逻辑 - 优先使用accessToken
      const tokenValue = data.accessToken || data.token
      if (tokenValue) {
        token.value = tokenValue
        localStorage.setItem('token', tokenValue)
        console.log('✅ JWT Token已保存到localStorage:', tokenValue.substring(0, 30) + '...')
      } else {
        throw new Error('响应中未包含accessToken或token字段')
      }

      // 🔧 修复用户信息保存逻辑 - 优先使用user字段
      const userInfoValue = data.user || data.userInfo
      if (userInfoValue) {
        userInfo.value = userInfoValue
        localStorage.setItem('userInfo', JSON.stringify(userInfoValue))
        console.log('✅ 用户信息已保存:', userInfoValue.username)
      } else {
        throw new Error('响应中未包含user或userInfo字段')
      }

      message.success('登录成功！')

      // 获取用户权限并初始化权限系统
      await loadUserPermissions()

      return data
    } catch (error) {
      console.error('登录详细错误:', error)
      message.error('登录失败，请检查用户名和密码')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注销
  const logout = async () => {
    loading.value = true
    try {
      console.log('🚪 开始用户登出流程')

      // 1. 调用后端登出API（可选，用于服务端清理）
      try {
        await api.post('/auth/logout')
        console.log('✅ 后端登出API调用成功')
      } catch (error) {
        console.log('⚠️ 后端登出API调用失败，继续本地清理:', error)
        // 即使后端API失败，也继续本地清理
      }

      // 2. 清除本地状态
      token.value = null
      userInfo.value = null

      // 3. 清除本地存储和权限信息
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('userPermissions')
      localStorage.removeItem('userRoles')

      // 4. 清除权限系统状态
      clearPermissions()

      console.log('✅ 用户登出成功')
      message.success('退出登录成功')

      // 4. 跳转到登录页
      router.push('/login')
    } catch (error) {
      console.error('登出过程出错:', error)
      // 即使出错也要强制清理
      token.value = null
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      router.push('/login')
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const response = await api.get<UserInfo>('/auth/me')
      userInfo.value = response.data
      localStorage.setItem('userInfo', JSON.stringify(response.data))
      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能token已过期，执行注销
      handleAuthExpired()
      throw error
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await api.post<{ token: string }>('/auth/refresh')
      token.value = response.data.token
      localStorage.setItem('token', response.data.token)
      return response.data.token
    } catch (error) {
      console.error('刷新token失败:', error)
      handleAuthExpired()
      throw error
    }
  }

  // 强制登出（用于测试）
  const forceLogout = () => {
    console.log('🔥 强制清除认证状态 - 用于测试')

    // 清除状态
    token.value = ''
    userInfo.value = null

    // 清除本地存储
    localStorage.clear()

    console.log('✅ 认证状态已清除，请重新登录')
  }

  // 测试重新登录
  const testReLogin = async () => {
    console.log('🧪 测试重新登录流程')

    // 先强制登出
    forceLogout()

    // 延迟一下，然后重新登录
    setTimeout(async () => {
      try {
        await login({ username: 'admin', password: 'admin123' })
        console.log('✅ 重新登录成功')
      } catch (error) {
        console.error('❌ 重新登录失败:', error)
      }
    }, 1000)
  }

  /**
   * 获取用户权限并初始化权限系统
   */
  const loadUserPermissions = async () => {
    try {
      console.log('🔐 [Auth] 开始获取用户权限...')

      // 获取当前用户权限
      const response = await api.get('/permissions/user/my')
      const result = response.data

      console.log('🔐 [Auth] 权限API响应:', result)

      if (result.success && result.data) {
        const permissions: string[] = result.data.permissions || []
        const roles: string[] = result.data.roles || []

        // 初始化权限系统
        initPermissions(permissions, roles)

        // 保存权限到localStorage
        localStorage.setItem('userPermissions', JSON.stringify(permissions))
        localStorage.setItem('userRoles', JSON.stringify(roles))

        console.log('✅ [Auth] 权限初始化成功:', {
          permissions: permissions.length,
          roles: roles.length,
          permissionList: permissions,
          roleList: roles,
        })

        return { permissions, roles }
      } else {
        console.warn('⚠️ [Auth] 权限API响应格式异常:', result)
        // 初始化空权限
        initPermissions([], [])
        return { permissions: [], roles: [] }
      }
    } catch (error: unknown) {
      console.error('❌ [Auth] 获取用户权限失败:', error)

      // 检查是否是认证错误
      if (error instanceof Error && 'response' in error) {
        const httpError = error as { response?: { status: number } }
        if (httpError.response?.status === 401) {
          console.warn('🚨 [Auth] 权限获取时认证失败，执行登出')
          handleAuthExpired(false)
          return { permissions: [], roles: [] }
        }
      }

      // 其他错误时初始化空权限，但不登出
      console.warn('⚠️ [Auth] 权限获取失败，使用空权限继续')
      initPermissions([], [])
      return { permissions: [], roles: [] }
    }
  }

  // 初始化
  initUserInfo()

  return {
    // 状态
    token,
    userInfo,
    loading,
    // 计算属性
    isAuthenticated,
    isAdmin,
    // 方法
    login,
    logout,
    getCurrentUser,
    refreshToken,
    handleAuthExpired,
    forceLogout,
    testReLogin,
    loadUserPermissions,
  }
})
