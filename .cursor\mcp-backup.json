{"mcpServers": {"mysql": {"command": "D:\\Program Files\\nodejs\\node.exe", "env": {"ALLOW_INSERT_OPERATION": "true", "MYSQL_PORT": "3306", "ALLOW_DELETE_OPERATION": "false", "MYSQL_USER": "onlyfile_user", "MYSQL_PASS": "0nlyF!le$ecure#123", "ALLOW_UPDATE_OPERATION": "true", "PATH": "D:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Windows\\System32;C:\\Windows", "MYSQL_HOST": "*************", "MYSQL_ENABLE_LOGGING": "true", "MYSQL_DB": "onlyfile", "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"}, "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@benborla29\\mcp-server-mysql\\dist\\index.js"]}}}