import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsInt,
  IsEnum,
  MinLength,
  MaxLength,
  Min,
  Max,
} from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * 创建角色DTO
 */
export class CreateRoleDto {
  @ApiProperty({
    description: '角色名称',
    example: 'project_manager',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @ApiProperty({
    description: '角色显示名称',
    example: '项目经理',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  display_name: string;

  @ApiPropertyOptional({
    description: '角色描述',
    example: '负责项目管理和团队协调',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '权限列表',
    type: [String],
    example: ['documents.read', 'documents.create', 'templates.read'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({
    description: '角色是否启用',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: '排序序号',
    example: 10,
    minimum: 0,
    maximum: 999,
  })
  @IsInt()
  @Min(0)
  @Max(999)
  @IsOptional()
  sort_order?: number;
}

/**
 * 更新角色DTO
 */
export class UpdateRoleDto {
  @ApiPropertyOptional({
    description: '角色名称',
    example: 'project_manager',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({
    description: '角色显示名称',
    example: '项目经理',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  @MinLength(2)
  @MaxLength(100)
  display_name?: string;

  @ApiPropertyOptional({
    description: '角色描述',
    example: '负责项目管理和团队协调',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '权限列表',
    type: [String],
    example: ['documents.read', 'documents.create', 'templates.read'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({
    description: '角色是否启用',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: '排序序号',
    example: 10,
    minimum: 0,
    maximum: 999,
  })
  @IsInt()
  @Min(0)
  @Max(999)
  @IsOptional()
  sort_order?: number;
}

/**
 * 角色查询DTO
 */
export class RoleQueryDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
  })
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 1000,
  })
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(1000)
  @IsOptional()
  pageSize?: number;

  @ApiPropertyOptional({
    description: '搜索关键词',
    example: '管理员',
  })
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: '角色状态',
    example: 'active',
    enum: ['active', 'inactive'],
  })
  @IsEnum(['active', 'inactive'])
  @IsOptional()
  status?: 'active' | 'inactive';

  @ApiPropertyOptional({
    description: '是否系统角色',
    example: false,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  @IsOptional()
  isSystem?: boolean;

  @ApiPropertyOptional({
    description: '排序字段',
    example: 'sort_order',
    enum: ['name', 'display_name', 'created_at', 'sort_order', 'user_count'],
  })
  @IsEnum(['name', 'display_name', 'created_at', 'sort_order', 'user_count'])
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({
    description: '排序方向',
    example: 'ASC',
    enum: ['ASC', 'DESC'],
  })
  @IsEnum(['ASC', 'DESC'])
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * 分配权限DTO
 */
export class AssignPermissionsDto {
  @ApiProperty({
    description: '权限列表',
    type: [String],
    example: ['documents.*', 'templates.read', 'config.read'],
  })
  @IsArray()
  @IsString({ each: true })
  permissions: string[];
}

/**
 * 角色响应DTO
 */
export class RoleResponseDto {
  @ApiProperty({
    description: '角色ID',
    example: 'role-uuid-123',
  })
  id: string;

  @ApiProperty({
    description: '角色名称',
    example: 'project_manager',
  })
  name: string;

  @ApiProperty({
    description: '角色显示名称',
    example: '项目经理',
  })
  display_name: string;

  @ApiPropertyOptional({
    description: '角色描述',
    example: '负责项目管理和团队协调',
  })
  description?: string;

  @ApiProperty({
    description: '权限列表',
    type: [String],
    example: ['documents.read', 'documents.create', 'templates.read'],
  })
  permissions: string[];

  @ApiProperty({
    description: '是否系统角色',
    example: false,
  })
  is_system: boolean;

  @ApiProperty({
    description: '角色是否启用',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: '排序序号',
    example: 10,
  })
  sort_order: number;

  @ApiPropertyOptional({
    description: '创建者ID',
    example: 'user-uuid-123',
  })
  created_by?: string;

  @ApiProperty({
    description: '创建时间',
    example: '2024-12-19T10:30:00Z',
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: '更新者ID',
    example: 'user-uuid-123',
  })
  updated_by?: string;

  @ApiProperty({
    description: '更新时间',
    example: '2024-12-19T10:30:00Z',
  })
  updated_at: Date;

  @ApiPropertyOptional({
    description: '使用此角色的用户数量',
    example: 5,
  })
  user_count?: number;
}

/**
 * 角色列表响应DTO
 */
export class RoleListResponseDto {
  @ApiProperty({
    description: '角色列表',
    type: [RoleResponseDto],
  })
  data: RoleResponseDto[];

  @ApiProperty({
    description: '总数',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: '总页数',
    example: 3,
  })
  totalPages: number;
}

/**
 * 权限响应DTO
 */
export class PermissionResponseDto {
  @ApiProperty({
    description: '权限ID',
    example: 'perm-uuid-123',
  })
  id: string;

  @ApiProperty({
    description: '权限代码',
    example: 'documents.read',
  })
  code: string;

  @ApiProperty({
    description: '权限名称',
    example: '查看文档',
  })
  name: string;

  @ApiPropertyOptional({
    description: '权限描述',
    example: '查看和下载文档',
  })
  description?: string;

  @ApiProperty({
    description: '所属模块',
    example: 'documents',
  })
  module: string;

  @ApiPropertyOptional({
    description: '资源标识',
    example: 'document',
  })
  resource?: string;

  @ApiPropertyOptional({
    description: '操作类型',
    example: 'read',
  })
  action?: string;

  @ApiProperty({
    description: '是否启用',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: '排序序号',
    example: 10,
  })
  sort_order: number;

  @ApiProperty({
    description: '创建时间',
    example: '2024-12-19T10:30:00Z',
  })
  created_at: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-12-19T10:30:00Z',
  })
  updated_at: Date;
} 