const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置（使用.env文件中的配置）
const dbConfig = {
    host: '*************',
    port: 3306,
    user: 'onlyfile_user', 
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
    // 移除 multipleStatements，改为分步执行
};

async function initUserTables() {
    let connection;
    
    try {
        console.log('🔄 连接数据库...');
        console.log(`📍 数据库配置: ${dbConfig.user}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        console.log('🔄 读取SQL脚本...');
        const sqlScript = fs.readFileSync(path.join(__dirname, 'scripts/create-user-tables.sql'), 'utf8');
        console.log('✅ SQL脚本读取成功');
        
        console.log('🔄 分步执行SQL语句...');
        // 将SQL脚本分割成独立的语句
        const statements = sqlScript
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📋 共有 ${statements.length} 条SQL语句需要执行`);
        
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            if (statement.toLowerCase().includes('create table')) {
                const tableName = statement.match(/create table if not exists\s+(\w+)/i)?.[1];
                console.log(`🔄 创建表: ${tableName || 'unknown'}`);
            } else if (statement.toLowerCase().includes('insert')) {
                console.log(`🔄 插入数据...`);
            } else if (statement.toLowerCase().includes('alter table')) {
                console.log(`🔄 修改表结构...`);
            } else if (statement.toLowerCase().includes('create or replace view')) {
                console.log(`🔄 创建视图...`);
            }
            
            try {
                await connection.execute(statement);
            } catch (error) {
                console.warn(`⚠️ 语句执行失败 (可能已存在): ${error.message.substring(0, 100)}...`);
                // 继续执行下一条语句，不中断整个过程
            }
        }
        
        console.log('✅ SQL语句执行完成');
        
        // 验证表是否创建成功
        console.log('🔄 验证表结构...');
        const [tables] = await connection.execute(`
            SHOW TABLES LIKE 'user%' 
        `);
        
        console.log('📋 创建的用户相关表:');
        tables.forEach(table => {
            console.log(`  - ${Object.values(table)[0]}`);
        });
        
        // 验证默认用户
        console.log('🔄 验证默认用户...');
        const [users] = await connection.execute(`
            SELECT u.username, u.full_name, r.display_name as role_name, u.status 
            FROM users u 
            LEFT JOIN user_roles r ON u.role_id = r.id 
            WHERE u.username IN ('admin', 'demo')
        `);
        
        console.log('👤 创建的默认用户:');
        users.forEach(user => {
            console.log(`  - ${user.username} (${user.full_name}) - ${user.role_name} - ${user.status}`);
        });
        
        console.log('🎉 用户管理系统初始化完成！');
        console.log('📝 默认管理员账户: admin / admin123');
        console.log('📝 默认演示账户: demo / admin123');
        
    } catch (error) {
        console.error('❌ 初始化失败:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.error('💡 请检查数据库连接配置和数据库服务是否启动');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('💡 请检查数据库用户名和密码是否正确');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.error('💡 请检查数据库名称是否正确');
        }
        
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 执行初始化
initUserTables(); 