"use strict";
exports.id = 0;
exports.ids = null;
exports.modules = {

/***/ 10:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.envConfig = void 0;
exports.validateConfig = validateConfig;
exports.printConfig = printConfig;
exports.initializeConfig = initializeConfig;
const dotenv_1 = __webpack_require__(11);
const path_1 = __webpack_require__(12);
const fs_1 = __webpack_require__(13);
const PROJECT_ROOT = (0, path_1.join)(__dirname, '../../../');
const ENV_FILE_PATH = (0, path_1.join)(PROJECT_ROOT, '.env');
if ((0, fs_1.existsSync)(ENV_FILE_PATH)) {
    const result = (0, dotenv_1.config)({ path: ENV_FILE_PATH });
    if (result.error) {
        console.error(`[EnvConfig] 加载环境变量文件失败: ${ENV_FILE_PATH}`, result.error);
    }
    else {
        console.log(`[EnvConfig] ✅ 成功加载环境变量: ${ENV_FILE_PATH}`);
    }
}
else {
    console.warn(`[EnvConfig] ⚠️ 环境变量文件不存在: ${ENV_FILE_PATH}`);
}
function getEnvValue(key, defaultValue) {
    return process.env[key] || defaultValue || '';
}
function getEnvNumber(key, defaultValue) {
    const value = process.env[key];
    return value ? parseInt(value, 10) : defaultValue;
}
function getEnvBoolean(key, defaultValue) {
    const value = process.env[key];
    if (!value)
        return defaultValue;
    return value.toLowerCase() === 'true';
}
function getEnvArray(key, defaultValue = []) {
    const value = process.env[key];
    if (!value)
        return defaultValue;
    return value.split(',').map(item => item.trim().replace(/^["']|["']$/g, ''));
}
function cleanPassword(value) {
    return value.replace(/^["']|["']$/g, '');
}
exports.envConfig = {
    node_env: getEnvValue('NODE_ENV', 'development'),
    port: getEnvNumber('PORT', 3000),
    frontend_port: getEnvNumber('FRONTEND_PORT', 8080),
    database: {
        host: getEnvValue('DB_HOST', 'localhost'),
        port: getEnvNumber('DB_PORT', 3306),
        name: getEnvValue('DB_NAME', 'onlyfile'),
        user: getEnvValue('DB_USER', 'root'),
        password: cleanPassword(getEnvValue('DB_PASSWORD', '')),
    },
    jwt: {
        secret: cleanPassword(getEnvValue('JWT_SECRET', 'API-Auth-R7CpjZbbfls7v0wIV3cH78qvMo9KrZQV')),
        expiresIn: getEnvValue('JWT_EXPIRES_IN', '24h'),
    },
    cors: {
        origin: getEnvValue('CORS_ORIGIN', 'http://localhost:8080'),
        allowedOrigins: getEnvArray('ALLOWED_ORIGINS', ['http://localhost:3000', 'http://localhost:8080']),
    },
    onlyoffice: {
        serverUrl: getEnvValue('ONLYOFFICE_SERVER_URL', 'http://localhost/'),
        documentServerUrl: getEnvValue('ONLYOFFICE_DOCUMENT_SERVER_URL', 'http://localhost/'),
        documentPort: getEnvNumber('ONLYOFFICE_DOCUMENT_PORT', 80),
        secretKey: cleanPassword(getEnvValue('ONLYOFFICE_SECRET_KEY', '')),
    },
    filenet: {
        host: getEnvValue('FILENET_HOST', 'localhost'),
        port: getEnvNumber('FILENET_PORT', 8090),
        username: getEnvValue('FILENET_USERNAME', ''),
        password: getEnvValue('FILENET_PASSWORD', ''),
        defaultFolder: getEnvValue('FILENET_DEFAULT_FOLDER', ''),
        defaultDocClass: getEnvValue('FILENET_DEFAULT_DOC_CLASS', 'SimpleDocument'),
        defaultSourceType: getEnvValue('FILENET_DEFAULT_SOURCE_TYPE', 'MaxOffice'),
        defaultBizTag: getEnvValue('FILENET_DEFAULT_BIZ_TAG', 'office_file'),
    },
    storage: {
        uploadPath: getEnvValue('UPLOAD_PATH', './uploads'),
        tmpPath: getEnvValue('TMP_PATH', './tmp'),
        maxFileSize: getEnvValue('MAX_FILE_SIZE', '50MB'),
        allowedFileTypes: getEnvArray('ALLOWED_FILE_TYPES', ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt']),
    },
    cache: {
        type: getEnvValue('CACHE_TYPE', 'memory'),
        ttl: getEnvNumber('CACHE_TTL', 3600),
        maxSize: getEnvNumber('CACHE_MAX_SIZE', 100),
    },
    redis: {
        host: getEnvValue('REDIS_HOST', 'localhost'),
        port: getEnvNumber('REDIS_PORT', 6379),
        password: getEnvValue('REDIS_PASSWORD', ''),
        db: getEnvNumber('REDIS_DB', 0),
    },
    logging: {
        level: getEnvValue('LOG_LEVEL', 'info'),
        dir: getEnvValue('LOG_DIR', './logs'),
        maxSize: getEnvValue('LOG_MAX_SIZE', '10m'),
        maxFiles: getEnvNumber('LOG_MAX_FILES', 7),
    },
    monitoring: {
        enabled: getEnvBoolean('ENABLE_PERFORMANCE_MONITORING', true),
        slowQueryThreshold: getEnvNumber('SLOW_QUERY_THRESHOLD', 1000),
        requestTimeout: getEnvNumber('REQUEST_TIMEOUT', 30000),
    },
    security: {
        rateLimitWindowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 900000),
        rateLimitMaxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
        enableSecurityHeaders: getEnvBoolean('ENABLE_SECURITY_HEADERS', true),
    },
    development: {
        hotReload: getEnvBoolean('HOT_RELOAD', true),
        debugMode: getEnvBoolean('DEBUG_MODE', true),
        apiDocsEnabled: getEnvBoolean('API_DOCS_ENABLED', true),
        swaggerEndpoint: getEnvValue('SWAGGER_ENDPOINT', '/api-docs'),
    },
    server: {
        host: getEnvValue('SERVER_HOST', 'localhost'),
        callbackUrl: cleanPassword(getEnvValue('CALLBACK_URL', 'http://localhost:3000/api/editor/callback')),
    },
};
function validateConfig() {
    const requiredFields = [
        'database.host',
        'database.name',
        'database.user',
        'jwt.secret',
    ];
    const missingFields = [];
    requiredFields.forEach(field => {
        const value = getNestedValue(exports.envConfig, field);
        if (!value || value === '') {
            missingFields.push(field);
        }
    });
    if (missingFields.length > 0) {
        throw new Error(`[EnvConfig] 缺少必需的配置项: ${missingFields.join(', ')}`);
    }
    console.log('[EnvConfig] ✅ 配置验证通过');
}
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
}
function printConfig() {
    const configCopy = JSON.parse(JSON.stringify(exports.envConfig));
    if (configCopy.database?.password)
        configCopy.database.password = '***隐藏***';
    if (configCopy.jwt?.secret)
        configCopy.jwt.secret = '***隐藏***';
    if (configCopy.onlyoffice?.secretKey)
        configCopy.onlyoffice.secretKey = '***隐藏***';
    if (configCopy.filenet?.password)
        configCopy.filenet.password = '***隐藏***';
    if (configCopy.redis?.password)
        configCopy.redis.password = '***隐藏***';
    console.log('[EnvConfig] 📋 当前配置:', JSON.stringify(configCopy, null, 2));
}
function initializeConfig() {
    try {
        validateConfig();
        if (exports.envConfig.development.debugMode) {
            printConfig();
        }
        console.log(`[EnvConfig] 🚀 配置初始化完成 - 环境: ${exports.envConfig.node_env}`);
    }
    catch (error) {
        console.error('[EnvConfig] ❌ 配置初始化失败:', error);
        throw error;
    }
}
exports["default"] = exports.envConfig;


/***/ })

};
exports.runtime =
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("a619cbb9566039c682f2")
/******/ })();
/******/ 
/******/ }
;