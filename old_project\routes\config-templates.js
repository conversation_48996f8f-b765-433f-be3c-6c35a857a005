/**
 * 配置模板管理路由
 */
const express = require('express');
const router = express.Router();
const configTemplateService = require('../services/configTemplateService');
const { apiError } = require('../middleware/error');

/**
 * 获取配置分组元数据
 * GET /api/config-templates/meta/groups
 * 注意：这个路由必须在 /:id 路由之前定义
 */
router.get('/meta/groups', async (req, res, next) => {
    try {
        const groups = configTemplateService.getConfigGroups();
        res.json({
            success: true,
            data: groups,
            message: '获取配置分组信息成功'
        });
    } catch (error) {
        next(apiError('获取配置分组信息失败', 500, error));
    }
});

/**
 * 获取默认配置模板
 * GET /api/config-templates/default
 */
router.get('/default', async (req, res, next) => {
    try {
        const template = await configTemplateService.getDefaultTemplate();
        res.json({
            success: true,
            data: template,
            message: '获取默认配置模板成功'
        });
    } catch (error) {
        next(apiError('获取默认配置模板失败', 500, error));
    }
});

/**
 * 导入配置模板
 * POST /api/config-templates/import
 */
router.post('/import', async (req, res, next) => {
    try {
        const importData = req.body;

        // 验证导入数据格式
        if (!importData.name || !importData.items) {
            return next(apiError('导入数据格式不正确', 400));
        }

        // 检查是否存在同名模板
        try {
            await configTemplateService.getTemplateByName(importData.name);
            return res.status(409).json({
                success: false,
                message: '同名模板已存在，请修改模板名称后重试'
            });
        } catch (error) {
            // 模板不存在，可以继续导入
        }

        const template = await configTemplateService.createTemplate({
            name: importData.name,
            description: importData.description || '导入的配置模板',
            is_default: false,
            items: importData.items
        });

        res.status(201).json({
            success: true,
            data: template,
            message: '配置模板导入成功'
        });
    } catch (error) {
        next(apiError('导入配置模板失败', 500, error));
    }
});

/**
 * 获取所有配置模板列表
 * GET /api/config-templates
 */
router.get('/', async (req, res, next) => {
    try {
        const templates = await configTemplateService.getAllTemplates();
        res.json({
            success: true,
            data: templates,
            message: '获取配置模板列表成功'
        });
    } catch (error) {
        next(apiError('获取配置模板列表失败', 500, error));
    }
});

/**
 * 创建新的配置模板
 * POST /api/config-templates
 */
router.post('/', async (req, res, next) => {
    try {
        const templateData = req.body;

        // 基本验证
        if (!templateData.name) {
            return next(apiError('模板名称不能为空', 400));
        }

        if (!templateData.items || !Array.isArray(templateData.items)) {
            return next(apiError('配置项列表不能为空', 400));
        }

        const template = await configTemplateService.createTemplate(templateData);
        res.status(201).json({
            success: true,
            data: template,
            message: '配置模板创建成功'
        });
    } catch (error) {
        next(apiError('创建配置模板失败', 500, error));
    }
});

/**
 * 复制配置模板
 * POST /api/config-templates/:id/copy
 */
router.post('/:id/copy', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        const { newName, newDescription } = req.body;

        if (!newName) {
            return next(apiError('新模板名称不能为空', 400));
        }

        // 获取原模板
        const originalTemplate = await configTemplateService.getTemplateById(templateId);

        // 创建新模板数据
        const newTemplateData = {
            name: newName,
            description: newDescription || `${originalTemplate.description} (副本)`,
            is_default: false,
            items: originalTemplate.items.map(item => ({
                config_group: item.config_group,
                config_key: item.config_key,
                config_value: item.config_value,
                value_type: item.value_type,
                is_enabled: item.is_enabled,
                is_required: item.is_required,
                description: item.description
            }))
        };

        const newTemplate = await configTemplateService.createTemplate(newTemplateData);
        res.status(201).json({
            success: true,
            data: newTemplate,
            message: '配置模板复制成功'
        });
    } catch (error) {
        next(apiError('复制配置模板失败', 500, error));
    }
});

/**
 * 预览配置模板效果
 * POST /api/config-templates/:id/preview
 */
router.post('/:id/preview', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        const { fileId, overrides = {} } = req.body;

        if (!fileId) {
            return next(apiError('文件ID不能为空', 400));
        }

        const editorConfig = await configTemplateService.buildEditorConfig(templateId, fileId, overrides);
        res.json({
            success: true,
            data: {
                templateId,
                fileId,
                config: editorConfig,
                overrides
            },
            message: '配置预览生成成功'
        });
    } catch (error) {
        next(apiError('生成配置预览失败', 500, error));
    }
});

/**
 * 导出配置模板
 * GET /api/config-templates/:id/export
 */
router.get('/:id/export', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        const template = await configTemplateService.getTemplateById(templateId);

        // 清理导出数据，移除数据库相关字段
        const exportData = {
            name: template.name,
            description: template.description,
            version: '1.0',
            exported_at: new Date().toISOString(),
            items: template.items.map(item => ({
                config_group: item.config_group,
                config_key: item.config_key,
                config_value: item.config_value,
                value_type: item.value_type,
                is_enabled: item.is_enabled,
                is_required: item.is_required,
                description: item.description
            }))
        };

        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="${template.name}-config-template.json"`);
        res.json(exportData);
    } catch (error) {
        next(apiError('导出配置模板失败', 500, error));
    }
});

/**
 * 设置默认配置模板
 * POST /api/config-templates/:id/set-default
 */
router.post('/:id/set-default', async (req, res, next) => {
    try {
        const templateId = req.params.id;

        // 清除其他默认模板
        await configTemplateService.clearDefaultTemplates();

        // 设置新的默认模板
        await configTemplateService.setDefaultTemplate(templateId);

        res.json({
            success: true,
            message: '设置默认模板成功'
        });
    } catch (error) {
        next(apiError('设置默认模板失败', 500, error));
    }
});

/**
 * 获取单个配置模板
 * GET /api/config-templates/:id
 */
router.get('/:id', async (req, res, next) => {
    try {
        const templateId = req.params.id;

        let template;
        // 判断传入的是ID还是名称
        if (templateId.includes('-') || templateId.length >= 32) {
            // 看起来像ID
            template = await configTemplateService.getTemplateById(templateId);
        } else {
            // 看起来像名称
            template = await configTemplateService.getTemplateByName(templateId);
        }

        res.json({
            success: true,
            data: template,
            message: '获取配置模板成功'
        });
    } catch (error) {
        next(apiError('获取配置模板失败', 404, error));
    }
});

/**
 * 更新配置模板
 * PUT /api/config-templates/:id
 */
router.put('/:id', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        const templateData = req.body;

        // 验证必要字段
        if (!templateData.name) {
            return next(apiError('模板名称不能为空', 400));
        }

        if (!templateData.items || !Array.isArray(templateData.items)) {
            return next(apiError('配置项列表不能为空', 400));
        }

        const template = await configTemplateService.updateTemplate(templateId, templateData);
        res.json({
            success: true,
            data: template,
            message: '配置模板更新成功'
        });
    } catch (error) {
        next(apiError('更新配置模板失败', 500, error));
    }
});

/**
 * 删除配置模板
 * DELETE /api/config-templates/:id
 */
router.delete('/:id', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        const result = await configTemplateService.deleteTemplate(templateId);
        res.json({
            success: true,
            data: result,
            message: '配置模板删除成功'
        });
    } catch (error) {
        next(apiError('删除配置模板失败', 500, error));
    }
});

module.exports = router; 