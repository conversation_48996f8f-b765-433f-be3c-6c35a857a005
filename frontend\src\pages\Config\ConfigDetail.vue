<template>
  <div class="config-detail-page">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item>
        <router-link to="/onlyoffice-config">OnlyOffice用户模板</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>用户模板配置详情</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      title="OnlyOffice用户模板配置"
      sub-title="配置用户模板的详细参数和选项"
      @back="handleGoBack"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleTestConnection">
            <template #icon>
              <api-outlined />
            </template>
            测试连接
          </a-button>
          <a-button @click="handleExportConfig">
            <template #icon>
              <download-outlined />
            </template>
            导出配置
          </a-button>
          <a-button type="primary" @click="handleSaveConfig">
            <template #icon>
              <save-outlined />
            </template>
            保存配置
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-wrapper">
      <a-tabs v-model:activeKey="activeTab" type="card" :items="tabItems" />

      <!-- 基础配置 -->
      <div v-if="activeTab === 'basic'" class="tab-content">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="服务器配置" size="small">
              <a-form layout="vertical">
                <a-form-item label="Document Server地址">
                  <a-input
                    v-model:value="basicConfig.serverUrl"
                    placeholder="http://localhost:8080"
                  />
                </a-form-item>
                <a-form-item label="API端口">
                  <a-input-number
                    v-model:value="basicConfig.apiPort"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="JWT密钥">
                  <a-input-password
                    v-model:value="basicConfig.jwtSecret"
                    placeholder="请输入JWT密钥"
                  />
                </a-form-item>
                <a-form-item label="连接超时时间(秒)">
                  <a-input-number
                    v-model:value="basicConfig.timeout"
                    :min="1"
                    :max="300"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="编辑器配置" size="small">
              <a-form layout="vertical">
                <a-form-item label="默认语言">
                  <a-select v-model:value="basicConfig.language" style="width: 100%">
                    <a-select-option value="zh-CN">中文简体</a-select-option>
                    <a-select-option value="en-US">English</a-select-option>
                    <a-select-option value="ja-JP">日本語</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="编辑器模式">
                  <a-radio-group v-model:value="basicConfig.mode">
                    <a-radio-button value="edit">编辑模式</a-radio-button>
                    <a-radio-button value="view">查看模式</a-radio-button>
                    <a-radio-button value="review">审阅模式</a-radio-button>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="自动保存间隔(秒)">
                  <a-slider
                    v-model:value="basicConfig.autoSaveInterval"
                    :min="10"
                    :max="300"
                    :marks="{ 10: '10s', 60: '1min', 300: '5min' }"
                  />
                </a-form-item>
                <a-form-item label="界面主题">
                  <a-select v-model:value="basicConfig.theme" style="width: 100%">
                    <a-select-option value="default">默认主题</a-select-option>
                    <a-select-option value="dark">深色主题</a-select-option>
                    <a-select-option value="light">浅色主题</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="编辑器宽度">
                  <a-radio-group v-model:value="basicConfig.width">
                    <a-radio-button value="100%">全宽</a-radio-button>
                    <a-radio-button value="90%">90%</a-radio-button>
                    <a-radio-button value="80%">80%</a-radio-button>
                  </a-radio-group>
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 权限配置 -->
      <div v-if="activeTab === 'permissions'" class="tab-content">
        <a-row :gutter="24">
          <a-col :span="16">
            <a-card title="用户权限配置" size="small">
              <a-table
                :columns="permissionColumns"
                :data-source="permissionConfig.userRoles"
                :pagination="false"
                bordered
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <a-tag :color="getRoleColor(record.role)">
                      {{ record.name }}
                    </a-tag>
                  </template>
                  <template
                    v-else-if="
                      ['canEdit', 'canComment', 'canShare', 'canDownload', 'canPrint'].includes(
                        column.key
                      )
                    "
                  >
                    <a-switch v-model:checked="record[column.key]" size="small" />
                  </template>
                </template>
              </a-table>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card title="文档保护设置" size="small">
              <a-form layout="vertical">
                <a-form-item label="密码保护">
                  <a-switch v-model:checked="permissionConfig.passwordProtection" />
                </a-form-item>
                <a-form-item label="水印设置">
                  <a-switch v-model:checked="permissionConfig.watermark.enabled" />
                  <a-input
                    v-if="permissionConfig.watermark.enabled"
                    v-model:value="permissionConfig.watermark.text"
                    placeholder="水印文本"
                    style="margin-top: 8px"
                  />
                </a-form-item>
                <a-form-item label="下载限制">
                  <a-switch v-model:checked="permissionConfig.downloadRestriction" />
                </a-form-item>
                <a-form-item label="打印限制">
                  <a-switch v-model:checked="permissionConfig.printRestriction" />
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 界面定制 -->
      <div v-if="activeTab === 'customization'" class="tab-content">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="工具栏配置" size="small">
              <a-form layout="vertical">
                <a-form-item label="显示工具栏">
                  <a-checkbox-group v-model:value="customizationConfig.toolbar.items">
                    <a-row :gutter="[16, 8]">
                      <a-col :span="12" v-for="tool in toolbarItems" :key="tool.key">
                        <a-checkbox :value="tool.key">{{ tool.label }}</a-checkbox>
                      </a-col>
                    </a-row>
                  </a-checkbox-group>
                </a-form-item>
                <a-form-item label="工具栏位置">
                  <a-radio-group v-model:value="customizationConfig.toolbar.position">
                    <a-radio-button value="top">顶部</a-radio-button>
                    <a-radio-button value="bottom">底部</a-radio-button>
                  </a-radio-group>
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="界面元素配置" size="small">
              <a-form layout="vertical">
                <a-form-item label="界面元素">
                  <a-checkbox-group v-model:value="customizationConfig.ui.elements">
                    <a-row :gutter="[16, 8]">
                      <a-col :span="24" v-for="element in uiElements" :key="element.key">
                        <a-checkbox :value="element.key">{{ element.label }}</a-checkbox>
                      </a-col>
                    </a-row>
                  </a-checkbox-group>
                </a-form-item>
              </a-form>
            </a-card>

            <a-card title="品牌定制" size="small" style="margin-top: 16px">
              <a-form layout="vertical">
                <a-form-item label="公司Logo">
                  <a-upload
                    v-model:file-list="customizationConfig.branding.logoFileList"
                    :before-upload="() => false"
                    list-type="picture-card"
                    :max-count="1"
                  >
                    <div v-if="customizationConfig.branding.logoFileList.length < 1">
                      <plus-outlined />
                      <div style="margin-top: 8px">上传Logo</div>
                    </div>
                  </a-upload>
                </a-form-item>
                <a-form-item label="主题色">
                  <a-input v-model:value="customizationConfig.branding.primaryColor" type="color" />
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 协作功能 -->
      <div v-if="activeTab === 'collaboration'" class="tab-content">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="实时协作配置" size="small">
              <a-form layout="vertical">
                <a-form-item label="启用实时协作">
                  <a-switch v-model:checked="collaborationConfig.realtime.enabled" />
                </a-form-item>
                <a-form-item label="最大协作用户数">
                  <a-input-number
                    v-model:value="collaborationConfig.realtime.maxUsers"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="协作超时时间(分钟)">
                  <a-input-number
                    v-model:value="collaborationConfig.realtime.timeout"
                    :min="5"
                    :max="240"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="自动保存冲突解决">
                  <a-select
                    v-model:value="collaborationConfig.realtime.conflictResolution"
                    style="width: 100%"
                  >
                    <a-select-option value="merge">自动合并</a-select-option>
                    <a-select-option value="prompt">提示用户</a-select-option>
                    <a-select-option value="latest">最新优先</a-select-option>
                  </a-select>
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="评论和审阅设置" size="small">
              <a-form layout="vertical">
                <a-form-item label="启用评论功能">
                  <a-switch v-model:checked="collaborationConfig.comments.enabled" />
                </a-form-item>
                <a-form-item label="匿名评论">
                  <a-switch v-model:checked="collaborationConfig.comments.allowAnonymous" />
                </a-form-item>
                <a-form-item label="启用修订跟踪">
                  <a-switch v-model:checked="collaborationConfig.tracking.enabled" />
                </a-form-item>
                <a-form-item label="修订显示模式">
                  <a-radio-group v-model:value="collaborationConfig.tracking.displayMode">
                    <a-radio-button value="markup">标记模式</a-radio-button>
                    <a-radio-button value="final">最终模式</a-radio-button>
                    <a-radio-button value="original">原始模式</a-radio-button>
                  </a-radio-group>
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 安全设置 -->
      <div v-if="activeTab === 'security'" class="tab-content">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="访问控制" size="small">
              <a-form layout="vertical">
                <a-form-item label="启用JWT认证">
                  <a-switch v-model:checked="securityConfig.jwt.enabled" />
                </a-form-item>
                <a-form-item label="JWT过期时间(小时)" v-if="securityConfig.jwt.enabled">
                  <a-input-number
                    v-model:value="securityConfig.jwt.expiration"
                    :min="1"
                    :max="168"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="IP访问限制">
                  <a-switch v-model:checked="securityConfig.ipRestriction.enabled" />
                </a-form-item>
                <a-form-item label="允许的IP地址" v-if="securityConfig.ipRestriction.enabled">
                  <a-textarea
                    v-model:value="securityConfig.ipRestriction.allowedIPs"
                    placeholder="请输入IP地址，每行一个"
                    :rows="4"
                  />
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="文件安全" size="small">
              <a-form layout="vertical">
                <a-form-item label="文件加密">
                  <a-switch v-model:checked="securityConfig.fileEncryption.enabled" />
                </a-form-item>
                <a-form-item label="加密算法" v-if="securityConfig.fileEncryption.enabled">
                  <a-select
                    v-model:value="securityConfig.fileEncryption.algorithm"
                    style="width: 100%"
                  >
                    <a-select-option value="AES-256">AES-256</a-select-option>
                    <a-select-option value="AES-128">AES-128</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="安全审计">
                  <a-switch v-model:checked="securityConfig.audit.enabled" />
                </a-form-item>
                <a-form-item label="审计日志保留天数" v-if="securityConfig.audit.enabled">
                  <a-input-number
                    v-model:value="securityConfig.audit.retentionDays"
                    :min="1"
                    :max="365"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ApiOutlined,
  DownloadOutlined,
  SaveOutlined,
  PlusOutlined,
  SafetyCertificateOutlined,
  SettingOutlined,
  TeamOutlined,
  SafetyOutlined,
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const activeTab = ref('basic')
const configTemplate = ref<{
  id: string
  name: string
  description: string
  type: string
  status: string
  isDefault: boolean
} | null>(null)

// 基础配置
const basicConfig = reactive({
  serverUrl: 'http://localhost:8080',
  apiPort: 8080,
  jwtSecret: '',
  timeout: 120,
  language: 'zh-CN',
  mode: 'edit',
  autoSaveInterval: 60,
  theme: 'default',
  width: '100%',
})

// 权限配置
const permissionConfig = reactive({
  userRoles: [
    {
      name: '管理员',
      role: 'admin',
      canEdit: true,
      canComment: true,
      canShare: true,
      canDownload: true,
      canPrint: true,
    },
    {
      name: '编辑者',
      role: 'editor',
      canEdit: true,
      canComment: true,
      canShare: false,
      canDownload: false,
      canPrint: false,
    },
    {
      name: '审阅者',
      role: 'reviewer',
      canEdit: false,
      canComment: true,
      canShare: false,
      canDownload: false,
      canPrint: false,
    },
    {
      name: '查看者',
      role: 'viewer',
      canEdit: false,
      canComment: false,
      canShare: false,
      canDownload: false,
      canPrint: false,
    },
  ],
  passwordProtection: false,
  watermark: {
    enabled: false,
    text: '内部文档，禁止外传',
  },
  downloadRestriction: false,
  printRestriction: false,
})

// 界面定制配置
const customizationConfig = reactive({
  toolbar: {
    items: ['file', 'edit', 'insert', 'layout'],
    position: 'top',
  },
  ui: {
    elements: ['statusBar', 'leftPanel', 'rightPanel', 'header'],
  },
  branding: {
    logoFileList: [],
    primaryColor: '#1890ff',
  },
})

// 协作功能配置
const collaborationConfig = reactive({
  realtime: {
    enabled: true,
    maxUsers: 10,
    timeout: 60,
    conflictResolution: 'merge',
  },
  comments: {
    enabled: true,
    allowAnonymous: false,
  },
  tracking: {
    enabled: true,
    displayMode: 'markup',
  },
})

// 安全设置配置
const securityConfig = reactive({
  jwt: {
    enabled: true,
    expiration: 24,
  },
  ipRestriction: {
    enabled: false,
    allowedIPs: '',
  },
  fileEncryption: {
    enabled: false,
    algorithm: 'AES-256',
  },
  audit: {
    enabled: true,
    retentionDays: 30,
  },
})

// 标签页配置
const tabItems = computed(() => [
  {
    key: 'basic',
    tab: '基础配置',
    icon: SettingOutlined,
  },
  {
    key: 'permissions',
    tab: '权限配置',
    icon: SafetyCertificateOutlined,
  },
  {
    key: 'customization',
    tab: '界面定制',
    icon: ApiOutlined,
  },
  {
    key: 'collaboration',
    tab: '协作功能',
    icon: TeamOutlined,
  },
  {
    key: 'security',
    tab: '安全设置',
    icon: SafetyOutlined,
  },
])

// 工具栏选项
const toolbarItems = [
  { key: 'file', label: '文件操作' },
  { key: 'edit', label: '编辑功能' },
  { key: 'insert', label: '插入内容' },
  { key: 'layout', label: '页面布局' },
  { key: 'references', label: '引用工具' },
  { key: 'review', label: '审阅工具' },
  { key: 'view', label: '视图选项' },
  { key: 'plugins', label: '插件功能' },
]

// 界面元素选项
const uiElements = [
  { key: 'statusBar', label: '状态栏' },
  { key: 'leftPanel', label: '左侧面板' },
  { key: 'rightPanel', label: '右侧面板' },
  { key: 'header', label: '头部标题' },
  { key: 'ruler', label: '标尺' },
  { key: 'toolbar', label: '工具栏' },
]

// 权限表格列定义
const permissionColumns = [
  { title: '角色名称', key: 'name', dataIndex: 'name', width: 120 },
  { title: '编辑权限', key: 'canEdit', align: 'center', width: 100 },
  { title: '评论权限', key: 'canComment', align: 'center', width: 100 },
  { title: '分享权限', key: 'canShare', align: 'center', width: 100 },
  { title: '下载权限', key: 'canDownload', align: 'center', width: 100 },
  { title: '打印权限', key: 'canPrint', align: 'center', width: 100 },
]

// 方法
const handleGoBack = () => {
  router.push('/onlyoffice-config')
}

const loadConfigTemplate = () => {
  const templateId = route.params.id as string

  // 模拟加载配置模板数据
  const templates = [
    {
      id: '1',
      name: '默认编辑器配置',
      description: '标准的OnlyOffice编辑器配置，包含基本的权限和界面设置',
      type: 'permissions',
      status: 'active',
      isDefault: true,
    },
    {
      id: '2',
      name: '高级权限配置',
      description: '具有完整权限的配置模板，适用于管理员和高级用户',
      type: 'permissions',
      status: 'active',
      isDefault: false,
    },
  ]

  configTemplate.value = templates.find(t => t.id === templateId) || templates[0]
}

const handleTestConnection = async () => {
  try {
    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 1500))
    message.success('连接测试成功！Document Server 正常运行')
  } catch (error) {
    message.error('连接测试失败，请检查服务器配置')
  }
}

const handleExportConfig = () => {
  const configData = {
    basic: basicConfig,
    permissions: permissionConfig,
    customization: customizationConfig,
    collaboration: collaborationConfig,
    security: securityConfig,
  }

  const dataStr = JSON.stringify(configData, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'onlyoffice-user-template-config.json'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  message.success('配置已成功导出')
}

const handleSaveConfig = async () => {
  try {
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1500))
    message.success('用户模板配置已保存成功')
  } catch (error) {
    message.error('保存配置失败，请重试')
  }
}

const getRoleColor = (role: string) => {
  const colorMap: Record<string, string> = {
    admin: '#f50',
    editor: '#2db7f5',
    reviewer: '#87d068',
    viewer: '#108ee9',
  }
  return colorMap[role] || '#d9d9d9'
}

onMounted(() => {
  // 页面初始化逻辑
  console.log('OnlyOffice用户模板配置页面已加载')
  loadConfigTemplate()
})
</script>

<style scoped>
.config-detail-page {
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 64px - 48px); /* 减去顶部导航栏、底部页脚和内容区padding */
  padding: 0 16px 16px;
}

.content-wrapper {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-top: 16px;
}

.tab-content {
  margin-top: 16px;
}

.toolbar-checkbox-group .ant-col {
  margin-bottom: 8px;
}

.config-card {
  margin-bottom: 16px;
}

.config-card:last-child {
  margin-bottom: 0;
}
</style>
