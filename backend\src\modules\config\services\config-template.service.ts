import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';

/**
 * 配置模板数据库记录接口
 */
interface ConfigTemplateRow {
  id: string;
  name: string;
  description?: string;
  is_default: number | boolean;
  is_active: number | boolean;
  created_at: Date | string;
  updated_at: Date | string;
}

/**
 * 配置项数据库记录接口
 */
interface ConfigItemRecord {
  config_group: string;
  config_key: string;
  config_value: string;
  value_type: string;
  is_enabled?: boolean | number;
  is_required?: boolean | number;
  description?: string;
}

/**
 * 配置项状态接口
 */
export interface ConfigItemState {
  value: unknown;
  enabled: boolean;
  required: boolean;
}

/**
 * 配置状态映射接口
 */
export interface ConfigStates {
  [groupKey: string]: {
    [configKey: string]: ConfigItemState;
  };
}

/**
 * 配置对象接口
 */
export interface ConfigObject {
  [groupKey: string]: {
    [configKey: string]: unknown;
  };
}

/**
 * 配置模板服务
 * 提供OnlyOffice配置模板的管理功能
 * 迁移自原有的configTemplateService.js
 */
@Injectable()
export class ConfigTemplateService {
  constructor(private databaseService: DatabaseService) {}

  /**
   * 获取所有配置模板
   */
  async getAllTemplates() {
    const sql = `
      SELECT id, name, description, is_default, is_active, created_at, updated_at
      FROM config_templates 
      ORDER BY is_default DESC, created_at DESC
    `;
    const templates = await this.databaseService.query(sql) as unknown as ConfigTemplateRow[];
    
    // 确保布尔值字段被正确转换
    return templates.map(template => ({
      ...template,
      isDefault: Boolean(template.is_default),
      isActive: Boolean(template.is_active),
      createdAt: template.created_at,
      updatedAt: template.updated_at
    }));
  }

  /**
   * 根据ID获取配置模板
   */
  async getTemplateById(templateId: string) {
    const sql = `
      SELECT id, name, description, is_default, is_active, created_at, updated_at
      FROM config_templates 
      WHERE id = ?
    `;
    const template = await this.databaseService.queryOne(sql, [templateId]) as unknown as ConfigTemplateRow | null;
    
    if (!template) {
      return null;
    }
    
    // 转换数据格式
    return {
      ...template,
      isDefault: Boolean(template.is_default),
      isActive: Boolean(template.is_active),
      createdAt: template.created_at,
      updatedAt: template.updated_at
    };
  }

  /**
   * 获取默认配置模板
   */
  async getDefaultTemplate() {
    const sql = `
      SELECT id, name, description, is_default, is_active, created_at, updated_at
      FROM config_templates 
      WHERE is_default = TRUE AND is_active = TRUE
      LIMIT 1
    `;
    return await this.databaseService.queryOne(sql);
  }

  /**
   * 获取配置模板的完整配置
   */
  async getTemplateConfig(templateId: string) {
    // 获取模板基本信息
    const template = await this.getTemplateById(templateId);
    if (!template) {
      throw new Error('配置模板不存在');
    }

    // 获取所有配置项（包括禁用的）
    const configItemsSql = `
      SELECT config_group, config_key, config_value, value_type, is_enabled, is_required, description
      FROM config_template_items 
      WHERE template_id = ?
      ORDER BY config_group, config_key
    `;
    const configItems = await this.databaseService.query(configItemsSql, [templateId]) as unknown as ConfigItemRecord[];

    // 组装配置对象，保留启用状态信息
    const { config, configStates } = this.buildConfigObjectWithStates(configItems);

    return {
      template,
      config,
      configStates
    };
  }

  /**
   * 获取用于OnlyOffice的有效配置（仅包含启用的配置项）
   */
  async getEffectiveConfig(templateId: string) {
    // 获取模板基本信息
    const template = await this.getTemplateById(templateId);
    if (!template) {
      throw new Error('配置模板不存在');
    }

    // 获取启用的配置项
    const configItemsSql = `
      SELECT config_group, config_key, config_value, value_type, is_enabled, is_required, description
      FROM config_template_items 
      WHERE template_id = ? AND is_enabled = TRUE
      ORDER BY config_group, config_key
    `;
    const configItems = await this.databaseService.query(configItemsSql, [templateId]) as unknown as ConfigItemRecord[];

    // 组装配置对象（仅包含值）
    const config = this.buildConfigObject(configItems);

    return {
      template,
      config
    };
  }

  /**
   * 创建新的配置模板
   */
  async createTemplate(templateData: Record<string, unknown>) {
    const { name, description, configItems = [] } = templateData;
    const items = Array.isArray(configItems) ? configItems : [];
    
    // 生成新的模板ID
    const templateId = this.generateUUID();
    
    // 开始事务
    return await this.databaseService.transaction(async (connection) => {
      // 插入模板基本信息
      const insertTemplateSql = `
        INSERT INTO config_templates (id, name, description, is_default, is_active, created_at, updated_at)
        VALUES (?, ?, ?, FALSE, TRUE, NOW(), NOW())
      `;
      await connection.query(insertTemplateSql, [templateId, name, description]);

      // 插入配置项
      if (items.length > 0) {
        for (const item of items) {
          // 假设item有必要的属性，类型检查在运行时进行
          const configItem = item as Record<string, unknown>;
          const itemId = `${templateId}-${configItem.config_group}-${configItem.config_key}`;
          const insertItemSql = `
            INSERT INTO config_template_items 
            (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `;
          await connection.query(insertItemSql, [
            itemId, templateId, configItem.config_group, configItem.config_key, 
            configItem.config_value, configItem.value_type || 'string', 
            configItem.is_enabled !== false, configItem.is_required || false, 
            configItem.description || ''
          ]);
        }
      }

      return templateId;
    });
  }

  /**
   * 更新配置模板
   */
  async updateTemplate(templateId: string, templateData: Record<string, unknown>) {
    const { name, description, configItems, configStates } = templateData;
    
    return await this.databaseService.transaction(async (connection) => {
      // 更新模板基本信息
      if (name || description) {
        const updateTemplateSql = `
          UPDATE config_templates 
          SET name = COALESCE(?, name), description = COALESCE(?, description), updated_at = NOW()
          WHERE id = ?
        `;
        await connection.query(updateTemplateSql, [name, description, templateId]);
      }

      // 更新配置项（支持新的配置状态格式）
      if (configStates) {
        const states = configStates as ConfigStates;
        // 删除现有配置项
        const deleteItemsSql = 'DELETE FROM config_template_items WHERE template_id = ?';
        await connection.query(deleteItemsSql, [templateId]);

        // 从configStates重建配置项
        for (const groupKey of Object.keys(states)) {
          const group = states[groupKey];
          for (const configKey of Object.keys(group)) {
            const itemState = group[configKey];
            const itemId = `${templateId}-${groupKey}-${configKey}`;
            
            // 确定值类型
            let valueType = 'string';
            let configValue = itemState.value;
            
            if (typeof itemState.value === 'boolean') {
              valueType = 'boolean';
              configValue = String(itemState.value);
            } else if (typeof itemState.value === 'number') {
              valueType = 'number';
              configValue = String(itemState.value);
            } else if (typeof itemState.value === 'object') {
              valueType = 'object';
              configValue = JSON.stringify(itemState.value);
            }
            
            const insertItemSql = `
              INSERT INTO config_template_items 
              (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description, created_at, updated_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            `;
            await connection.query(insertItemSql, [
              itemId, templateId, groupKey, configKey, 
              configValue, valueType, 
              itemState.enabled !== false, itemState.required || false, 
              `${groupKey}.${configKey}配置项`
            ]);
          }
        }
      } else if (configItems && Array.isArray(configItems) && configItems.length > 0) {
        // 兼容旧格式
        // 删除现有配置项
        const deleteItemsSql = 'DELETE FROM config_template_items WHERE template_id = ?';
        await connection.query(deleteItemsSql, [templateId]);

        // 插入新配置项
        for (const item of configItems) {
          const configItem = item as Record<string, unknown>;
          const itemId = `${templateId}-${configItem.config_group}-${configItem.config_key}`;
          const insertItemSql = `
            INSERT INTO config_template_items 
            (id, template_id, config_group, config_key, config_value, value_type, is_enabled, is_required, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `;
          await connection.query(insertItemSql, [
            itemId, templateId, configItem.config_group, configItem.config_key, 
            configItem.config_value, configItem.value_type || 'string', 
            configItem.is_enabled !== false, configItem.is_required || false, 
            configItem.description || ''
          ]);
        }
      }
    });
  }

  /**
   * 删除配置模板（软删除）
   */
  async deleteTemplate(templateId: string) {
    const sql = `
      UPDATE config_templates 
      SET is_active = FALSE, updated_at = NOW()
      WHERE id = ?
    `;
    await this.databaseService.query(sql, [templateId]);
  }

  /**
   * 设置默认配置模板
   */
  async setDefaultTemplate(templateId: string) {
    return await this.databaseService.transaction(async (connection) => {
      // 取消所有模板的默认状态
      const resetDefaultSql = 'UPDATE config_templates SET is_default = FALSE WHERE is_default = TRUE';
      await connection.query(resetDefaultSql);

      // 设置新的默认模板
      const setDefaultSql = 'UPDATE config_templates SET is_default = TRUE, updated_at = NOW() WHERE id = ?';
      await connection.query(setDefaultSql, [templateId]);
    });
  }

  /**
   * 将配置项数组转换为配置对象
   */
  private buildConfigObject(configItems: ConfigItemRecord[]): ConfigObject {
    const config: ConfigObject = {};

    for (const item of configItems) {
      const { config_group, config_key, config_value, value_type } = item;

      // 初始化配置组
      if (!config[config_group]) {
        config[config_group] = {};
      }

      // 转换值类型
      let value: unknown = config_value;
      switch (value_type) {
        case 'boolean':
          value = config_value === 'true';
          break;
        case 'number':
          value = Number(config_value);
          break;
        case 'object':
        case 'array':
          try {
            value = JSON.parse(config_value);
          } catch (error) {
            console.warn('[ConfigTemplate] JSON解析失败:', error instanceof Error ? error.message : String(error));
            value = config_value;
          }
          break;
        default:
          value = config_value;
      }

      config[config_group][config_key] = value;
    }

    return config;
  }

  /**
   * 将配置项数组转换为配置对象和状态信息
   */
  private buildConfigObjectWithStates(configItems: ConfigItemRecord[]): { config: ConfigObject; configStates: ConfigStates } {
    const config: ConfigObject = {};
    const configStates: ConfigStates = {};

    for (const item of configItems) {
      const { config_group, config_key, config_value, value_type, is_enabled, is_required } = item;

      // 初始化配置组
      if (!config[config_group]) {
        config[config_group] = {};
        configStates[config_group] = {};
      }

      // 转换值类型
      let value: unknown = config_value;
      switch (value_type) {
        case 'boolean':
          value = config_value === 'true';
          break;
        case 'number':
          value = Number(config_value);
          break;
        case 'object':
        case 'array':
          try {
            value = JSON.parse(config_value);
          } catch (error) {
            console.warn('[ConfigTemplate] JSON解析失败:', error instanceof Error ? error.message : String(error));
            value = config_value;
          }
          break;
        default:
          value = config_value;
      }

      // 设置配置值（始终设置，即使被禁用）
      config[config_group][config_key] = value;

      // 设置配置状态
      configStates[config_group][config_key] = {
        value: value,
        enabled: is_enabled === 1 || is_enabled === true,
        required: is_required === 1 || is_required === true
      };
    }

    return { config, configStates };
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
} 