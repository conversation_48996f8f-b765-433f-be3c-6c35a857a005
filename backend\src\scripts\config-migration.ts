import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DatabaseService } from '../modules/database/services/database.service';
import { HybridConfigService } from '../modules/config/services/hybrid-config.service';
import { SystemConfigService } from '../modules/config/services/system-config.service';

/**
 * 配置项接口
 */
interface ConfigItem {
  setting_key: string;
  setting_value: string;
}

/**
 * 配置迁移和验证脚本
 * 
 * 用途：
 * 1. 验证数据库配置系统是否正常工作
 * 2. 检查配置的迁移状态
 * 3. 提供配置诊断信息
 * 
 * <AUTHOR> Team
 * @since 2024-12-19
 * @version 1.0.0
 */

async function runConfigMigration() {
  console.log('🚀 开始配置迁移和验证...\n');

  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error', 'warn'],
  });

  try {
    const databaseService = app.get(DatabaseService);
    const hybridConfigService = app.get(HybridConfigService);
    const systemConfigService = app.get(SystemConfigService);

    // 1. 检查数据库连接
    console.log('📋 1. 检查数据库连接...');
    const dbConnected = await databaseService.testConnection();
    console.log(`   数据库连接: ${dbConnected ? '✅ 成功' : '❌ 失败'}`);

    if (!dbConnected) {
      throw new Error('数据库连接失败，无法继续配置验证');
    }

    // 2. 检查system_settings表
    console.log('\n📋 2. 检查system_settings表...');
    try {
      const settingsCount = await databaseService.query(
        'SELECT COUNT(*) as count FROM system_settings'
      ) as Array<{ count: number }>;
      console.log(`   配置项数量: ${settingsCount[0].count}`);

      if (settingsCount[0].count === 0) {
        console.log('   ⚠️  配置表为空，尝试初始化默认配置...');
        await systemConfigService.resetToDefaults();
        console.log('   ✅ 默认配置初始化完成');
      }
    } catch (error) {
      console.log(`   ❌ system_settings表检查失败: ${error.message}`);
      throw error;
    }

    // 3. 测试混合配置服务
    console.log('\n📋 3. 测试混合配置服务...');
    try {
      const configStats = await hybridConfigService.getConfigStats();
      console.log(`   总配置项: ${configStats.total}`);
      console.log(`   数据库配置: ${configStats.fromDatabase} (${Math.round((configStats.fromDatabase / configStats.total) * 100)}%)`);
      console.log(`   环境变量配置: ${configStats.fromEnv} (${Math.round((configStats.fromEnv / configStats.total) * 100)}%)`);
      console.log(`   缓存年龄: ${Math.floor(configStats.cacheAge / (1000 * 60))} 分钟`);
    } catch (error) {
      console.log(`   ❌ 混合配置服务测试失败: ${error.message}`);
    }

    // 4. 获取完整配置并验证
    console.log('\n📋 4. 验证配置读取...');
    try {
      const appConfig = await hybridConfigService.getAppConfig();
      
      // 验证关键配置
      const criticalConfigs = {
        'OnlyOffice服务器': appConfig.onlyoffice.serverUrl,
        'FileNet主机': appConfig.filenet.host,
        'JWT密钥': appConfig.jwt.secret ? '***已设置***' : '未设置',
        'OnlyOffice JWT': appConfig.onlyoffice.secretKey ? '***已设置***' : '未设置',
        '数据库主机': appConfig.database.host,
        '上传路径': appConfig.storage.uploadPath
      };

      console.log('   关键配置检查:');
      for (const [key, value] of Object.entries(criticalConfigs)) {
        console.log(`     ${key}: ${value}`);
      }
    } catch (error) {
      console.log(`   ❌ 配置读取失败: ${error.message}`);
    }

    // 5. 验证配置来源
    console.log('\n📋 5. 检查配置来源...');
    try {
      const sources = await hybridConfigService.getConfigSources();
      const databaseConfigs = Object.entries(sources)
        .filter(([_, source]) => source === 'database')
        .map(([key, _]) => key);
      
      const envConfigs = Object.entries(sources)
        .filter(([_, source]) => source === 'env')
        .map(([key, _]) => key);

      console.log(`   数据库配置 (${databaseConfigs.length}项):`);
      databaseConfigs.slice(0, 5).forEach(key => console.log(`     - ${key}`));
      if (databaseConfigs.length > 5) {
        console.log(`     ... 还有 ${databaseConfigs.length - 5} 项`);
      }

      console.log(`   环境变量配置 (${envConfigs.length}项):`);
      envConfigs.slice(0, 5).forEach(key => console.log(`     - ${key}`));
      if (envConfigs.length > 5) {
        console.log(`     ... 还有 ${envConfigs.length - 5} 项`);
      }
    } catch (error) {
      console.log(`   ❌ 配置来源检查失败: ${error.message}`);
    }

    // 6. 测试特定配置的获取
    console.log('\n📋 6. 测试特定配置的获取...');
    try {
      const testConfigs = [
        'jwt.onlyoffice.secret',
        'onlyoffice.server_url',
        'filenet.host',
        'storage.upload_path'
      ];

      for (const configKey of testConfigs) {
        try {
          const config = await systemConfigService.getConfig(configKey);
          console.log(`   ${configKey}: ✅ 已找到 (值: ${config.setting_value.substring(0, 20)}...)`);
        } catch {
          console.log(`   ${configKey}: ❌ 未找到`);
        }
      }
    } catch (error) {
      console.log(`   ❌ 特定配置测试失败: ${error.message}`);
    }

    // 7. 生成配置报告
    console.log('\n📋 7. 生成配置迁移报告...');
    const allConfigs = await systemConfigService.getAllConfigs();
    
    const configGroups = allConfigs.reduce((groups: Record<string, ConfigItem[]>, config: ConfigItem) => {
      const group = config.setting_key.split('.')[0];
      if (!groups[group]) groups[group] = [];
      groups[group].push(config);
      return groups;
    }, {} as Record<string, ConfigItem[]>);

    console.log('\n📊 配置分组统计:');
    Object.entries(configGroups).forEach(([group, configs]: [string, ConfigItem[]]) => {
      console.log(`   ${group}: ${configs.length} 项配置`);
    });

    console.log('\n✅ 配置迁移和验证完成！');
    
    // 输出使用建议
    console.log('\n💡 使用建议:');
    console.log('1. 访问 http://localhost:3000/api/config/hybrid/check 查看配置检查报告');
    console.log('2. 访问 http://localhost:3000/api/config/hybrid/stats 查看配置统计');
    console.log('3. 访问 http://localhost:3000/api/config/hybrid/sources 查看配置来源');
    console.log('4. 使用 POST http://localhost:3000/api/config/hybrid/refresh-cache 刷新配置缓存');

  } catch (error) {
    console.error('\n❌ 配置迁移验证失败:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runConfigMigration()
    .then(() => {
      console.log('\n🎉 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 脚本执行失败:', error);
      process.exit(1);
    });
}

export { runConfigMigration }; 