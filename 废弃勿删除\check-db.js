const http = require('http');

// 测试获取所有配置
function checkAllConfigs() {
  const options = {
    hostname: '*************',
    port: 3000,
    path: '/api/system-config/all',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  console.log('🔍 检查数据库中的配置数据...');

  const req = http.request(options, (res) => {
    let data = '';
    
    console.log(`📊 响应状态码: ${res.statusCode}`);

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        console.log('\n✅ 获取配置成功!');
        
        if (jsonData.data && jsonData.data.length > 0) {
          console.log(`📈 数据库中共有 ${jsonData.data.length} 个配置项`);
          console.log('\n🎯 前5个配置项:');
          jsonData.data.slice(0, 5).forEach((config, index) => {
            console.log(`${index + 1}. ${config.setting_key} = "${config.setting_value}"`);
            console.log(`   更新时间: ${config.updated_at || '未设置'}`);
            console.log(`   描述: ${config.description || '无描述'}`);
            console.log('');
          });
          
          // 检查有updated_at字段的配置
          const withUpdatedAt = jsonData.data.filter(c => c.updated_at);
          console.log(`🕒 有更新时间的配置项: ${withUpdatedAt.length} 个`);
          
          if (withUpdatedAt.length > 0) {
            console.log('📅 最近更新的配置:');
            withUpdatedAt
              .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
              .slice(0, 3)
              .forEach((config, index) => {
                console.log(`${index + 1}. ${config.setting_key} (${config.updated_at})`);
              });
          }
        } else {
          console.log('❌ 数据库中没有配置数据！');
          console.log('💡 可能需要运行数据库初始化脚本');
        }
      } catch (error) {
        console.log('\n❌ JSON解析失败:', error.message);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ 请求失败:', error.message);
  });

  req.end();
}

console.log('🚀 开始数据库检查...\n');
checkAllConfigs(); 