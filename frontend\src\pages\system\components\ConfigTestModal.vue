<template>
  <a-modal
    :open="visible"
    title="配置连接测试"
    :width="600"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="emit('update:visible', $event)"
  >
    <div v-if="config">
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="配置项">
          <span class="config-key">{{ config.setting_key }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="当前值">
          <span class="config-value">{{ maskSensitiveValue(config.setting_value) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="描述">
          {{ config.description }}
        </a-descriptions-item>
      </a-descriptions>

      <div class="test-section">
        <a-button
          type="primary"
          @click="runTest"
          :loading="testing"
          :disabled="!config.setting_value"
        >
          <ExperimentOutlined />
          开始测试
        </a-button>
      </div>

      <div v-if="testResult" class="test-result">
        <a-alert
          :type="testResult.success ? 'success' : 'error'"
          :message="testResult.success ? '连接测试成功' : '连接测试失败'"
          :description="testResult.message"
          show-icon
        />

        <div v-if="testResult.details" class="test-details">
          <h4>详细信息:</h4>
          <pre>{{ JSON.stringify(testResult.details, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { ExperimentOutlined } from '@ant-design/icons-vue'
import type { ConfigTestResult } from '@/types/system-config'

interface ConfigItem {
  setting_key: string
  setting_value: string
  description?: string
}

interface Props {
  visible: boolean
  config: ConfigItem | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const testing = ref(false)
const testResult = ref<ConfigTestResult | null>(null)

watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      testResult.value = null
    }
  }
)

const maskSensitiveValue = (value: string) => {
  if (!props.config) return value

  const key = props.config.setting_key.toLowerCase()
  if (key.includes('password') || key.includes('secret')) {
    return '*'.repeat(value.length)
  }
  return value
}

const runTest = async () => {
  if (!props.config) return

  testing.value = true
  testResult.value = null

  try {
    // 根据配置类型执行不同的测试
    let result: ConfigTestResult

    if (props.config.setting_key.includes('database')) {
      result = await testDatabaseConnection()
    } else if (props.config.setting_key.includes('redis')) {
      result = await testRedisConnection()
    } else if (props.config.setting_key.includes('filenet')) {
      result = await testFileNetConnection()
    } else if (props.config.setting_key.includes('onlyoffice')) {
      result = await testOnlyOfficeConnection()
    } else {
      result = await testGenericConnection()
    }

    testResult.value = result

    if (result.success) {
      message.success('测试成功')
    } else {
      message.error('测试失败')
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : '测试过程中发生错误'
    testResult.value = {
      success: false,
      message: errorMessage,
      details: {
        error: String(error),
      },
    }
    message.error('测试失败')
  } finally {
    testing.value = false
  }
}

const testDatabaseConnection = async (): Promise<ConfigTestResult> => {
  // 模拟数据库连接测试
  await new Promise(resolve => setTimeout(resolve, 1000))
  return {
    success: true,
    message: '数据库连接正常',
    details: {
      host: '*************',
      port: 3306,
      database: 'onlyfile',
      responseTime: '15ms',
    },
  }
}

const testRedisConnection = async (): Promise<ConfigTestResult> => {
  // 模拟Redis连接测试
  await new Promise(resolve => setTimeout(resolve, 800))
  return {
    success: true,
    message: 'Redis连接正常',
    details: {
      host: 'localhost',
      port: 6379,
      pingResponse: 'PONG',
    },
  }
}

const testFileNetConnection = async (): Promise<ConfigTestResult> => {
  // 模拟FileNet连接测试
  await new Promise(resolve => setTimeout(resolve, 1500))
  return {
    success: false,
    message: 'FileNet服务器连接超时',
    details: {
      host: '*************',
      port: 8090,
      timeout: '30s',
    },
  }
}

const testOnlyOfficeConnection = async (): Promise<ConfigTestResult> => {
  // 模拟OnlyOffice连接测试
  await new Promise(resolve => setTimeout(resolve, 1200))
  return {
    success: true,
    message: 'OnlyOffice文档服务器连接正常',
    details: {
      serverUrl: 'http://*************/',
      version: '7.5.1',
      status: 'healthy',
    },
  }
}

const testGenericConnection = async (): Promise<ConfigTestResult> => {
  // 通用连接测试
  await new Promise(resolve => setTimeout(resolve, 500))
  return {
    success: true,
    message: '配置项验证通过',
    details: {
      value: props.config?.setting_value,
      validation: 'passed',
    },
  }
}

const handleOk = () => {
  emit('update:visible', false)
  emit('close')
}

const handleCancel = () => {
  emit('update:visible', false)
  emit('close')
}
</script>

<style scoped>
.config-key {
  font-family: monospace;
  color: #1890ff;
}

.config-value {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.test-section {
  margin: 20px 0;
  text-align: center;
}

.test-result {
  margin-top: 20px;
}

.test-details {
  margin-top: 16px;
}

.test-details h4 {
  margin-bottom: 8px;
}

.test-details pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
