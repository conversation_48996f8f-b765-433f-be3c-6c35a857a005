-- FileNet文档管理系统 - 数据库表结构
-- 基于原有的filenetService.js重构
-- 创建时间: 2024-12-19

-- ================================================
-- 1. FileNet文档主表
-- ================================================
CREATE TABLE IF NOT EXISTS `filenet_documents` (
  `id` varchar(36) NOT NULL COMMENT '文档唯一标识UUID',
  `fn_doc_id` varchar(255) NOT NULL COMMENT 'FileNet系统文档ID',
  `original_name` varchar(500) NOT NULL COMMENT '原始文件名',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `mime_type` varchar(255) DEFAULT NULL COMMENT '文件MIME类型',
  `extension` varchar(50) DEFAULT NULL COMMENT '文件扩展名',
  `version` int DEFAULT 1 COMMENT '版本号',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件SHA-256哈希值',
  `created_by` varchar(100) DEFAULT 'anonymous' COMMENT '创建用户',
  `last_modified_by` varchar(100) DEFAULT 'anonymous' COMMENT '最后修改用户',
  `template_id` varchar(36) DEFAULT NULL COMMENT '关联的配置模板ID',
  `folder_path` varchar(1000) DEFAULT NULL COMMENT 'FileNet文件夹路径',
  `doc_class` varchar(255) DEFAULT 'SimpleDocument' COMMENT 'FileNet文档类',
  `source_type` varchar(100) DEFAULT 'MaxOffice' COMMENT '来源类型',
  `biz_tag` varchar(100) DEFAULT 'office_file' COMMENT '业务标签',
  `description` text DEFAULT NULL COMMENT '文档描述',
  `keywords` text DEFAULT NULL COMMENT '关键词',
  `properties` json DEFAULT NULL COMMENT '扩展属性(JSON格式)',
  `uploaded_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` boolean DEFAULT FALSE COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_fn_doc_id` (`fn_doc_id`),
  KEY `idx_file_hash` (`file_hash`),
  KEY `idx_original_name` (`original_name`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_extension` (`extension`),
  KEY `idx_template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FileNet文档主表';

-- ================================================
-- 2. FileNet文档版本表
-- ================================================
CREATE TABLE IF NOT EXISTS `filenet_document_versions` (
  `id` varchar(36) NOT NULL COMMENT '版本记录ID',
  `fn_doc_id` varchar(255) NOT NULL COMMENT 'FileNet系统文档ID',
  `version` int NOT NULL COMMENT '版本号',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件SHA-256哈希值',
  `version_comment` text DEFAULT NULL COMMENT '版本说明',
  `created_by` varchar(100) DEFAULT 'anonymous' COMMENT '创建版本的用户',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '版本创建时间',
  `is_current` boolean DEFAULT FALSE COMMENT '是否为当前版本',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_fn_doc_version` (`fn_doc_id`, `version`),
  KEY `idx_fn_doc_id` (`fn_doc_id`),
  KEY `idx_version` (`version`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_current` (`is_current`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FileNet文档版本记录表';

-- ================================================
-- 3. FileNet文档访问日志表
-- ================================================
CREATE TABLE IF NOT EXISTS `filenet_access_logs` (
  `id` varchar(36) NOT NULL COMMENT '访问记录ID',
  `fn_doc_id` varchar(255) NOT NULL COMMENT 'FileNet系统文档ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型(upload/download/view/delete/update)',
  `user_id` varchar(100) DEFAULT 'anonymous' COMMENT '操作用户',
  `user_ip` varchar(45) DEFAULT NULL COMMENT '用户IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理信息',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求唯一标识',
  `duration_ms` int DEFAULT NULL COMMENT '操作耗时(毫秒)',
  `status` varchar(20) DEFAULT 'success' COMMENT '操作状态(success/failed/error)',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_fn_doc_id` (`fn_doc_id`),
  KEY `idx_action` (`action`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FileNet文档访问日志表';

-- ================================================
-- 4. FileNet文档关联关系表
-- ================================================
CREATE TABLE IF NOT EXISTS `filenet_document_relations` (
  `id` varchar(36) NOT NULL COMMENT '关联记录ID',
  `parent_doc_id` varchar(255) NOT NULL COMMENT '父文档FileNet ID',
  `child_doc_id` varchar(255) NOT NULL COMMENT '子文档FileNet ID',
  `relation_type` varchar(50) NOT NULL COMMENT '关联类型(attachment/reference/version/conversion)',
  `created_by` varchar(100) DEFAULT 'anonymous' COMMENT '创建关联的用户',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_active` boolean DEFAULT TRUE COMMENT '关联是否有效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_parent_child_type` (`parent_doc_id`, `child_doc_id`, `relation_type`),
  KEY `idx_parent_doc_id` (`parent_doc_id`),
  KEY `idx_child_doc_id` (`child_doc_id`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FileNet文档关联关系表';

-- ================================================
-- 5. FileNet系统配置表
-- ================================================
CREATE TABLE IF NOT EXISTS `filenet_system_config` (
  `id` varchar(36) NOT NULL COMMENT '配置ID',
  `config_key` varchar(255) NOT NULL COMMENT '配置键名',
  `config_value` text DEFAULT NULL COMMENT '配置值',
  `config_type` varchar(50) DEFAULT 'string' COMMENT '配置类型(string/number/boolean/json)',
  `description` text DEFAULT NULL COMMENT '配置说明',
  `is_encrypted` boolean DEFAULT FALSE COMMENT '是否加密存储',
  `created_by` varchar(100) DEFAULT 'system' COMMENT '创建者',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_active` boolean DEFAULT TRUE COMMENT '是否启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FileNet系统配置表';

-- ================================================
-- 6. 创建视图：文档统计概览
-- ================================================
CREATE OR REPLACE VIEW `v_filenet_document_stats` AS
SELECT 
  DATE(uploaded_at) as upload_date,
  COUNT(*) as total_documents,
  COUNT(DISTINCT created_by) as unique_users,
  SUM(file_size) as total_size_bytes,
  ROUND(SUM(file_size) / 1024 / 1024, 2) as total_size_mb,
  COUNT(CASE WHEN is_deleted = FALSE THEN 1 END) as active_documents,
  COUNT(CASE WHEN is_deleted = TRUE THEN 1 END) as deleted_documents
FROM filenet_documents 
GROUP BY DATE(uploaded_at)
ORDER BY upload_date DESC;

-- ================================================
-- 7. 创建视图：文档详细信息
-- ================================================
CREATE OR REPLACE VIEW `v_filenet_document_details` AS
SELECT 
  fd.id,
  fd.fn_doc_id,
  fd.original_name,
  fd.file_size,
  ROUND(fd.file_size / 1024 / 1024, 2) as file_size_mb,
  fd.mime_type,
  fd.extension,
  fd.version,
  fd.file_hash,
  fd.created_by,
  fd.last_modified_by,
  fd.uploaded_at,
  fd.is_deleted,
  COALESCE(versions.version_count, 0) as total_versions,
  COALESCE(access_logs.access_count, 0) as total_access_count,
  latest_access.latest_access_time
FROM filenet_documents fd
LEFT JOIN (
  SELECT fn_doc_id, COUNT(*) as version_count 
  FROM filenet_document_versions 
  GROUP BY fn_doc_id
) versions ON fd.fn_doc_id = versions.fn_doc_id
LEFT JOIN (
  SELECT fn_doc_id, COUNT(*) as access_count 
  FROM filenet_access_logs 
  WHERE status = 'success'
  GROUP BY fn_doc_id
) access_logs ON fd.fn_doc_id = access_logs.fn_doc_id
LEFT JOIN (
  SELECT fn_doc_id, MAX(created_at) as latest_access_time
  FROM filenet_access_logs 
  WHERE status = 'success'
  GROUP BY fn_doc_id
) latest_access ON fd.fn_doc_id = latest_access.fn_doc_id;

-- ================================================
-- 8. 插入默认配置数据
-- ================================================
INSERT IGNORE INTO `filenet_system_config` (`id`, `config_key`, `config_value`, `config_type`, `description`) VALUES
('filenet-config-001', 'default_folder', '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}', 'string', 'FileNet默认文件夹ID'),
('filenet-config-002', 'default_doc_class', 'SimpleDocument', 'string', 'FileNet默认文档类'),
('filenet-config-003', 'default_source_type', 'MaxOffice', 'string', '默认来源类型'),
('filenet-config-004', 'default_biz_tag', 'office_file', 'string', '默认业务标签'),
('filenet-config-005', 'max_file_size_mb', '50', 'number', '最大文件大小限制(MB)'),
('filenet-config-006', 'allowed_extensions', '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt', 'string', '允许的文件扩展名'),
('filenet-config-007', 'enable_version_control', 'true', 'boolean', '是否启用版本控制'),
('filenet-config-008', 'enable_access_log', 'true', 'boolean', '是否启用访问日志'),
('filenet-config-009', 'connection_timeout_ms', '60000', 'number', 'FileNet连接超时时间(毫秒)'),
('filenet-config-010', 'retry_count', '3', 'number', '失败重试次数');

-- ================================================
-- 完成信息
-- ================================================
SELECT 'FileNet文档管理系统数据库表创建完成!' as message,
       NOW() as created_at,
       '表数量: 5个主表 + 2个视图 + 默认配置数据' as details; 