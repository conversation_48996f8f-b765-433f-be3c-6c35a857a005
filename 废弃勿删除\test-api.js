const http = require('http');

async function testLogin() {
  const data = JSON.stringify({
    username: 'admin',
    password: 'admin123'
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log('状态码:', res.statusCode);
        console.log('响应头:', res.headers);
        console.log('响应数据:', responseData);
        resolve(responseData);
      });
    });

    req.on('error', (error) => {
      console.error('请求错误:', error);
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

async function testHealth() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/health',
    method: 'GET'
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log('健康检查状态码:', res.statusCode);
        console.log('健康检查响应:', responseData);
        resolve(responseData);
      });
    });

    req.on('error', (error) => {
      console.error('健康检查错误:', error);
      reject(error);
    });

    req.end();
  });
}

async function runTests() {
  try {
    console.log('=== 开始API测试 ===');
    
    console.log('\n1. 测试健康检查...');
    await testHealth();
    
    console.log('\n2. 测试用户登录...');
    await testLogin();
    
    console.log('\n=== 测试完成 ===');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

runTests(); 